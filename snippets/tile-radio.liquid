
{%- comment -%}

  title: 'Topper'
  icon: image
  description: '25% of Total Food'
  radio_value: 'feeding'
  radio_name: 'feeding'
  required: true
  radio_index: 1

{%- endcomment -%}

{%- if icon == blank -%}
  {%- assign icon = section.settings.calculator_icon -%}
{%- endif -%}

{%- if radio_value == blank -%}
  {%- assign radio_value = title | handle -%}
{%- endif -%}

{%- if radio_name == blank -%}
  {%- assign radio_name = title -%}
{%- endif -%}

{%- capture radio_id -%}{{ section.id }}--{{ radio_name }}-{{ radio_index }}{%- endcapture -%}

<input 
  class="tile-radio-input visually-hidden" 
  type="radio" 
  name="{{ radio_name }}" 
  id="{{ radio_id }}" 
  value="{{ radio_value }}"
  {% if required == true %}required{% endif %}
>

<label class="tile-radio" for="{{ radio_id }}">

  {%- if icon -%}
    
    <span class="tile-radio__icon">
      {%- if icon.width -%}
        {{- icon | image_url: width: icon.width | image_tag: loading: 'lazy', widths: '150' -}}
      {%- else -%}
        <img src="{{ icon }}" alt="" loading="lazy">
      {%- endif -%}
    </span>

  {%- endif -%}

  {%- capture content -%}

    {%- if title -%}
      <span class="tile-radio__title text--small text--strong">{{ title }}</span>
    {%- endif -%}

    {%- if description -%}
      <span class="tile-radio__description text--xsmall">{{ description }}</span>
    {%- endif -%}

  {%- endcapture -%}

  {%- if content != blank -%}
    <span class="tile-radio__content">
      {{ content }}
    </span>
  {%- endif -%}

</label>