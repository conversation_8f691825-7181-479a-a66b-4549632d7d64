{%- liquid 

  if customer.metafields.custom.veterinarian != blank
    assign customer_vet = customer.metafields.custom.veterinarian_rec
  endif

-%}

{%- capture settings_json -%}
  {
    {%- if customer -%}
      "customer": {
        "first_name": "{{ customer.first_name }}",
        "last_name": "{{ customer.last_name }}",
        "email": "{{ customer.email }}"
      }
    {%- endif -%}
  }
{%- endcapture -%}

<cart-vet-partner class="cart-vet-partner" section="{{ section.id }}" data-vet="{{ customer_vet }}" {% if settings_json != "" %}data-settings='{{ settings_json | strip_newlines }}'{% endif %}>

  <collapsible-content class="collapsible cart-vet-partner__inner" id="{{ section.id }}--cart-vet-partners-inner" {% if customer_vet != blank %}{% else %}open{% endif %}>
    <div class="cart-vet-partner__info">
      <collapsible-content id="{{ section.id }}--cart-vet-notice" class="collapsible cart-vet-notice">
        <div class="cart-vet-partner__notice">
          <div class="cart-vet-partner__notice-icon">
            {% render 'icon-whos-your-vet' %}
          </div>
          <div class="cart-vet-partner__notice-text">
            <p class="subheading heading heading--xsmall">{{ settings.veterinarians_widget_title }}</p>
            <div class="text--xsmall">
              {{ settings.veterinarians_widget_description }}
            </div>
            <button type="button" is="toggle-button" class="link text--xsmall" aria-controls="{{ section.id }}--cart-vet-notice" data-action="close" title="{{ 'general.accessibility.close' | t | escape }}">Close</button>
          </div>
        </div>
      </collapsible-content>
    </div>
    <div class="cart-vet-partner__selector">
      <form class="cart-vet-partner__selector-form" class="" action="" id="{{ section.id }}--cart-vet-partner-form">
        <styled-select searchable observe class="vet-partner-selector-wrapper"> 
          <label for="{{ section.id }}--cart-vet-partner-select" class="visually-hidden">Select your Vet</label>
          <select 
            id="{{ section.id }}--cart-vet-partner-select" 
            name="attributes[Vet]" 
            class="cart-vet-partner-select select nice-select--position-top text--xsmall"
            data-vet-select-list
            data-sub-option-input 
            data-sub-option-input-reveal-value="Not Listed" 
          >

            <option value="" data-null-value selected disabled>Select your Vet</option>

            {%- if settings.veterinarians_metaobject == true -%}

              {%- assign vets = metaobjects['veterinarian'].values -%}

              {%- if settings.veterinarians_metaobject_use_states == true -%}

                {% comment %} States {% endcomment %}
                {%- assign veterinarian_list_states = '' -%}
                {%- for vet in vets -%}
                  {%- unless veterinarian_list_states contains vet.state.value.name -%}
                    {%- if veterinarian_list_states == '' -%}
                      {%- assign veterinarian_list_states = vet.state.value.name -%}
                    {%- else -%}
                      {%- assign veterinarian_list_states = veterinarian_list_states | append: ", " | append: vet.state.value.name -%}
                    {%- endif -%}
                  {%- endunless -%}
                {%- endfor -%}
                {%- assign vet_states = veterinarian_list_states | split: ", " | sort -%}

                {%- for vet in vets -%}
                  {%- if vet.state.value.name == blank -%}
                    <option value="{{ vet.name }}">
                      <span>{{ vet.name }}</span>
                    </option>
                  {%- endif -%}
                {%- endfor -%}

                {%- for state in vet_states -%}
                  <option value="{{ state }}" class="select__optgroup" disabled>{{ state }}</option>
                  {% for vet in vets %}
                    {%- if vet.state.value.name == state -%}
                      <option value="{{ vet.name }}" optgroup="{{ vet.state.value.name }}" {% if customer_vet contains option_stripped %}selected{% endif %}>
                        <span>{{ vet.name }}</span>
                      </option>
                    {%- endif -%}
                  {%- endfor -%}
                {% endfor %}

              {%- else -%}

                {%- for vet in vets -%}
                  <option value="{{ vet.name }}" optgroup="{{ vet.state.value.name }}" {% if customer_vet contains option_stripped %}selected{% endif %}>
                    <span>{{ vet.name }}</span>
                    {% comment %} <span class="hidden">{{ state }}</span> {% endcomment %}
                  </option>
                {%- endfor -%}

              {%- endif -%}

            {%- else -%}
              {%- assign options_array = settings.quiz_veterinarians | newline_to_br | split: '<br />' -%}
              {% for option in options_array %}
                {%- assign option_stripped = option | strip -%}
                <option value="{{ option_stripped }}" {% if customer_vet contains option_stripped %}selected{% endif %}>{{ option }}</option>
              {% endfor %}
            {%- endif -%}

          </select>
        </styled-select>
        <div class="text--xxsmall text--subdued text--center unlisted-vet-container" hidden>
          <strong>{{ 'vet_partners.not_listed.unlisted_vet' | t }}:</strong>
          <span data-unlisted-vet-details></span>
          <button type="button" is="toggle-button" class="link color--danger" aria-controls="{{ section.id }}--vet-notlisted" aria-expanded="false">{{ 'vet_partners.not_listed.change' | t }}</button>
        </div>
        <button type="button" is="loader-button" class="cart-vet-partner__selector-button button button--small button--full button--highlight" data-button-update-vet>{{ 'vet_partners.not_listed.submit_vet_details' | t }}        </button>
      </form>
    </div>
    <span class="cart-vet-partner__product-notice text--xxsmall color--danger">
      {{ settings.veterinarians_widget_message }} <button type="button" is="toggle-button" class="strong link" aria-controls="{{ section.id }}--cart-vet-notice" cart-vet-notice aria-expanded="false">{{ 'vet_partners.not_listed.more_info' | t }}      </button>
    </span>
  </collapsible-content>
  <div class="cart-vet-partner__vet">
    <collapsible-content class="collapsible cart-vet-text" id="{{ section.id }}--cart-vet-text" {% if customer_vet != blank %}open{% endif %} animate-items>
      <span class="cart-vet-partner__vet-text text--xxsmall">
        {{ 'vet_partners.not_listed.my_veterinarian' | t }} <strong data-vet-name>{{ customer_vet }}</strong>.   
        <button type="button" is="toggle-button" class="strong link color--danger" {% if auto_close_vet_text == true %}close-vet-text{% endif %} aria-controls="{{ section.id }}--cart-vet-partners-inner" aria-expanded="{% if customer_vet %}false{% else %}true{% endif %}">Not your vet?</button>
      </span>
    </collapsible-content>
  </div>
</cart-vet-partner>