{%- assign color_label_list = 'general.label.color' | t | replace: ', ', ',' | downcase | split: ',' -%}
{%- assign color_white_label = 'general.label.white' | t | downcase -%}
{%- assign size_label_list = 'general.label.size' | t | replace: ', ', ',' | downcase | split: ',' -%}
{%- assign product_form_id = 'product-form-' | append: section.id | append: '-' | append: product.id -%}

<div class="product-form">
  {%- for block in section.blocks -%}
    {%- case block.type -%}
      {%- comment -%}
      ----------------------------------------------------------------------------------------------------------------
      APP BLOCK TYPE
      ----------------------------------------------------------------------------------------------------------------
      {%- endcomment -%}
      {%- when '@app' -%}
        {%- render block -%}

      {%- comment -%}
      ----------------------------------------------------------------------------------------------------------------
      VARIANT PICKER BLOCK TYPE
      ----------------------------------------------------------------------------------------------------------------
      {%- endcomment -%}
      {%- when 'variant_picker' -%}
        {%- unless product.has_only_default_variant or block.settings.hide_sold_out_variants and product.available == false -%}
          {%- assign size_chart_page = block.settings.size_chart_page -%}
          {%- assign found_size_option = false -%}

          <product-variants handle="{{ product.handle }}" form-id="{{ product_form_id }}" {% if update_url %}update-url{% endif %} {% if block.settings.hide_sold_out_variants %}hide-sold-out-variants{% endif %} class="product-form__variants" {{ block.shopify_attributes }}>
            {%- for option in product.options_with_values -%}
              {%- assign option_downcase = option.name | downcase -%}
              {%- capture option_id -%}option-{{ section.id }}-{{ template.suffix }}-{{ product.id }}-{{ forloop.index }}{%- endcapture -%}

              {%- assign selector_type = block.settings.selector_mode -%}
              {%- assign variant_image_options = block.settings.variant_image_options | replace: ', ', ',' | downcase | split: ',' -%}

              {%- assign swatch_count = option.values | map: 'swatch' | compact | size -%}

              {%- if swatch_count > 0 and block.settings.color_mode == 'swatch' %}
                {%- assign selector_type = 'swatch' -%}
              {%- endif -%}

              {% if swatch_count == 0 and color_label_list contains option_downcase and block.settings.color_mode != 'none' %}
                {%- assign selector_type = 'swatch' -%}
              {%- endif -%}

              {%- if variant_image_options contains option_downcase -%}
                {%- assign selector_type = 'variant_image' -%}
              {%- endif -%}

              <div class="product-form__option-selector" data-selector-type="{{ selector_type | replace: '_', '-' | escape }}">
                <div class="product-form__option-info">
                  <span class="product-form__option-name">{{ option.name }}:</span>

                  {%- if selector_type != 'dropdown' -%}
                    <span id="{{ option_id }}-value" class="product-form__option-value">{{ option.selected_value }}</span>
                  {%- endif -%}

                  {%- if size_chart_page != blank and size_label_list contains option_downcase -%}
                    {%- assign found_size_option = true -%}
                    <button type="button" is="toggle-button" class="product-form__option-link link text--subdued hidden-phone" aria-controls="product-{{ section.id }}-{{ product.id }}-size-chart-drawer" aria-expanded="false">{{ 'product.general.size_chart' | t }}</button>
                    <button type="button" is="toggle-button" class="product-form__option-link link text--subdued hidden-tablet-and-up" aria-controls="product-{{ section.id }}-{{ product.id }}-size-chart-popover" aria-expanded="false">{{ 'product.general.size_chart' | t }}</button>
                  {%- endif -%}
                </div>

                {%- case selector_type -%}
                  {%- when 'variant_image' -%}
                    <div class="variant-swatch-list">
                      {%- for value in option.values -%}
                        {%- assign image = value.variant.featured_media | default: value.variant.product.featured_media -%}

                        <div class="variant-swatch">
                          <input class="variant-swatch__radio visually-hidden" type="radio" name="option{{ option.position }}" form="{{ product_form_id }}" id="{{ option_id }}-{{ forloop.index }}" value="{{ value | escape }}" data-bind-value="{{ option_id }}-value" {% if value == option.selected_value %}checked="checked"{% endif %} aria-label="{{ value | escape }}">
                          <label class="variant-swatch__item" for="{{ option_id }}-{{ forloop.index }}">
                            <span class="visually-hidden">{{ value }}</span>

                            {{- image | image_url: width: image.width | image_tag: loading: 'lazy', sizes: '(max-width: 740px) 64px, 72px', sizes: '64,72,128,144,192,216', class: 'variant-swatch__image' -}}
                          </label>
                        </div>
                      {%- endfor -%}
                    </div>

                  {%- when 'swatch' -%}
                    <div class="color-swatch-list">
                      {%- assign color_swatch_config = settings.color_swatch_config | newline_to_br | split: '<br />' -%}

                      {%- for value in option.values -%}
                        {%- assign color_value_downcase = value | downcase -%}

                        <div class="color-swatch {% if color_white_label == color_value_downcase %}color-swatch--white{% endif %}">
                          <input class="color-swatch__radio visually-hidden" type="radio" name="option{{ option.position }}" form="{{ product_form_id }}" id="{{ option_id }}-{{ forloop.index }}" value="{{ value | escape }}" {% if value == option.selected_value %}checked="checked"{% endif %} data-bind-value="{{ option_id }}-value">
                          <label class="color-swatch__item" for="{{ option_id }}-{{ forloop.index }}" style="{% render 'color-swatch-style', swatch: value.swatch, color_swatch_config: color_swatch_config, value: value %}">
                            <span class="visually-hidden">{{ value }}</span>
                          </label>
                        </div>
                      {%- endfor -%}
                    </div>

                  {%- when 'block' -%}
                    <div class="block-swatch-list">
                      {%- for value in option.values -%}
                        <div class="block-swatch">
                          <input class="block-swatch__radio visually-hidden" type="radio" name="option{{ option.position }}" form="{{ product_form_id }}" id="{{ option_id }}-{{ forloop.index }}" value="{{ value | escape }}" {% if value == option.selected_value %}checked="checked"{% endif %} data-bind-value="{{ option_id }}-value">
                          <label class="block-swatch__item" for="{{ option_id }}-{{ forloop.index }}">{{ value }}</label>
                        </div>
                      {%- endfor -%}
                    </div>

                  {%- when 'dropdown' -%}
                    <div class="select-wrapper">
                      <combo-box initial-focus-selector="[aria-selected='true']" id="{{ option_id }}-combo-box" class="combo-box">
                        <span class="combo-box__overlay"></span>

                        <header class="combo-box__header">
                          <p class="combo-box__title heading h6">{{ option.name }}</p>

                          <button type="button" class="combo-box__close-button tap-area" data-action="close" title="{{ 'general.accessibility.close' | t | escape }}">
                            {%- render 'icon' with 'close' -%}
                          </button>
                        </header>

                        <div class="combo-box__option-list" role="listbox">
                          {%- for value in option.values -%}
                            <button type="button" role="option" class="combo-box__option-item" value="{{ value | escape }}" aria-selected="{% if value == option.selected_value %}true{% else %}false{% endif %}">{{ value }}</button>
                          {%- endfor -%}
                        </div>

                        <select class="visually-hidden" name="option{{ option.position }}" form="{{ product_form_id }}" data-bind-value="{{ option_id }}-value" aria-label="{{ option.name | escape }}">
                          {%- for value in option.values -%}
                            <option value="{{ value | escape }}" {% if value == option.selected_value %}selected{% endif %}>{{ value }}</option>
                          {%- endfor -%}
                        </select>
                      </combo-box>

                      <button type="button" is="toggle-button" class="select" aria-expanded="false" aria-haspopup="listbox" aria-controls="{{ option_id }}-combo-box">
                        <span id="{{ option_id }}-value" class="select__selected-value">{{ option.selected_value }}</span>
                        {%- render 'icon' with 'chevron' -%}
                      </button>
                    </div>
                {%- endcase -%}
              </div>
            {%- endfor -%}

            <noscript>
              <label class="input__block-label" for="product-select-{{ section.id }}-{{ product.id }}">{{ 'product.form.variant' | t }}</label>

              <div class="select-wrapper">
                <select class="select" autocomplete="off" id="product-select-{{ section.id }}-{{ product.id }}" name="id" form="{{ product_form_id }}">
                  {%- for variant in product.variants -%}
                    <option {% if variant == product.selected_or_first_available_variant %}selected="selected"{% endif %} {% unless variant.available %}disabled="disabled"{% endunless %} value="{{ variant.id }}" data-sku="{{ variant.sku }}">{{ variant.title }} - {{ variant.price | money }}</option>
                  {%- endfor -%}
                </select>

                {%- render 'icon' with 'chevron' -%}
              </div>
            </noscript>
          </product-variants>
        {%- endunless -%}

      {%- comment -%}
      ----------------------------------------------------------------------------------------------------------------
      LINE ITEM PROPERTY
      ----------------------------------------------------------------------------------------------------------------
      {%- endcomment -%}
      {%- when 'line_item_property' -%}
        {%- if block.settings.label != blank -%}
          {%- case block.settings.type -%}
            {%- when 'text' -%}
              {%- if block.settings.allow_long_text -%}
                <div class="product-form__line-item-property" {{ block.shopify_attributes }}>
                  <label class="input__block-label" for="line-item-{{ section.id }}-{{ block.id }}">{{ block.settings.label | escape }}:</label>
                  <textarea {% if block.settings.required %}required{% endif %} id="line-item-{{ section.id }}-{{ block.id }}" form="{{ product_form_id }}" class="input__field input__field--textarea" name="properties[{{ block.settings.label | escape }}]"></textarea>
                </div>
              {% else %}
                <div class="product-form__line-item-property" {{ block.shopify_attributes }}>
                  <label class="input__block-label" for="line-item-{{ section.id }}-{{ block.id }}">{{ block.settings.label | escape }}:</label>
                  <input {% if block.settings.required %}required{% endif %} id="line-item-{{ section.id }}-{{ block.id }}" form="{{ product_form_id }}" type="text" class="input__field" name="properties[{{ block.settings.label | escape }}]">
                </div>
              {%- endif -%}

            {%- when 'checkbox' -%}
              <div class="product-form__line-item-property" {{ block.shopify_attributes }}>
                <div class="checkbox-container">
                  <input type="hidden" form="{{ product_form_id }}" class="checkbox" name="properties[{{ block.settings.label | escape }}]" value="{{ block.settings.unchecked_value | escape }}">
                  <input type="checkbox" form="{{ product_form_id }}" class="checkbox" name="properties[{{ block.settings.label | escape }}]" id="line-item-{{ section.id }}-{{ block.id }}" value="{{ block.settings.checked_value | escape }}">
                  <label for="line-item-{{ section.id }}-{{ block.id }}">{{ block.settings.label | escape }}</label>
                </div>
              </div>
          {%- endcase -%}
        {%- endif -%}

      {%- comment -%}
      ----------------------------------------------------------------------------------------------------------------
      TEXT
      ----------------------------------------------------------------------------------------------------------------
      {%- endcomment -%}
      {%- when 'text' -%}
        {% if block.settings.text != blank %}
          <div class="product-form__text" {{ block.shopify_attributes }}>
            {{- block.settings.text -}}
          </div>
        {%- endif -%}

      {%- comment -%}
      ----------------------------------------------------------------------------------------------------------------
      IMAGE
      ----------------------------------------------------------------------------------------------------------------
      {%- endcomment -%}
      {%- when 'image' -%}
      {% if block.settings.image != blank %}
        <div class="product-form__image product-form__image--{{ block.settings.image_alignment }}" {{ block.shopify_attributes }}>
          {%- capture image_sizes -%}{{ block.settings.image_width }}, {{ block.settings.image_width | times: 2 }}, {{ block.settings.image_width | times: 3 }}{%- endcapture -%}
          {%- capture sizes_attribute -%}{{ block.settings.image_width }}px{%- endcapture -%}
          {%- capture style_attribute -%}max-width: {{ block.settings.image_width }}px{%- endcapture -%}
          {{- block.settings.image | image_url: width: block.settings.image.width | image_tag: loading: 'lazy', sizes: sizes_attribute, widths: image_sizes, style: style_attribute -}}
        </div>
      {%- endif -%}

      {%- comment -%}
      ----------------------------------------------------------------------------------------------------------------
      BUTTON
      ----------------------------------------------------------------------------------------------------------------
      {%- endcomment -%}
      {%- when 'button' -%}
        {% if block.settings.link != blank and block.settings.text != blank %}
          <div class="product-form__button" {{ block.shopify_attributes }}>
            {%- if block.settings.background == 'rgba(0,0,0,0)' -%}
              {%- assign button_background = settings.primary_button_background -%}
            {%- else -%}
              {%- assign button_background = block.settings.background -%}
            {%- endif -%}

            {%- if block.settings.text_color == 'rgba(0,0,0,0)' -%}
              {%- assign button_text_color = settings.primary_button_text_color -%}
            {%- else -%}
              {%- assign button_text_color = block.settings.text_color -%}
            {%- endif -%}

            {%- capture button_background_rgb -%}{{ button_background.red }}, {{ button_background.green }}, {{ button_background.blue }}{%- endcapture -%}
            {%- capture button_text_color_rgb -%}{{ button_text_color.red }}, {{ button_text_color.green }}, {{ button_text_color.blue }}{%- endcapture -%}

            <a href="{{ block.settings.link }}" class="button button--primary {% if block.settings.stretch %}button--full{% endif %}" style="--primary-button-background: {{ button_background_rgb }}; --primary-button-text-color: {{ button_text_color_rgb }}">{{ block.settings.text | escape }}</a>
          </div>
        {%- endif -%}

      {%- comment -%}
      ----------------------------------------------------------------------------------------------------------------
      CUSTOM LIQUID
      ----------------------------------------------------------------------------------------------------------------
      {%- endcomment -%}
      {%- when 'liquid' -%}
        {% if block.settings.liquid != blank %}
          <div class="product-form__custom-liquid" {{ block.shopify_attributes }}>
            {{- block.settings.liquid -}}
          </div>
        {%- endif -%}

      {%- comment -%}
      ----------------------------------------------------------------------------------------------------------------
      QUANTITY SELECTOR
      ----------------------------------------------------------------------------------------------------------------
      {%- endcomment -%}
      {%- when 'quantity_selector' -%}
        {%- if request.page_type != 'password' and product.available -%}
          <div class="product-form__quantity" {{ block.shopify_attributes }}>
            <span class="product-form__quantity-label">{{ 'product.form.quantity' | t }}:</span>

            <quantity-selector class="quantity-selector">
              <button type="button" class="quantity-selector__button">
                <span class="visually-hidden">{{ 'cart.general.decrease_quantity' | t }}</span>
                {%- render 'icon' with 'minus-big' -%}
              </button>

              <input type="text" form="{{ product_form_id }}" is="input-number" class="quantity-selector__input" inputmode="numeric" name="quantity" autocomplete="off" min="1" value="1" size="2" aria-label="{{ 'product.form.quantity' | t | escape }}">

              <button type="button" class="quantity-selector__button">
                <span class="visually-hidden">{{ 'cart.general.increase_quantity' | t }}</span>
                {%- render 'icon' with 'plus-big' -%}
              </button>
            </quantity-selector>
          </div>
        {%- endif -%}

      {%- comment -%}
      ----------------------------------------------------------------------------------------------------------------
      PRODUCT DESCRIPTION
      ----------------------------------------------------------------------------------------------------------------
      {%- endcomment -%}
      {%- when 'description' -%}
        {%- if product.description != blank -%}
          <div class="product-form__description rte" {{ block.shopify_attributes }}>
            {{- product.description -}}
          </div>
        {%- endif -%}

      {%- comment -%}
      ----------------------------------------------------------------------------------------------------------------
      INVENTORY
      ----------------------------------------------------------------------------------------------------------------
      {%- endcomment -%}
      {%- when 'inventory' -%}
        {%- if product.template_suffix != 'pre-order' and product.available or product.selected_or_first_available_variant.incoming -%}
          <product-inventory form-id="{{ product_form_id }}" {% unless product.selected_or_first_available_variant.available or product.selected_or_first_available_variant.incoming %}hidden{% endunless %} class="product-form__inventory-wrapper" {{ block.shopify_attributes }}>
            {%- if product.selected_or_first_available_variant.available -%}
              {%- if product.selected_or_first_available_variant.inventory_management and product.selected_or_first_available_variant.inventory_policy == 'deny' and product.selected_or_first_available_variant.inventory_quantity <= block.settings.low_inventory_threshold and block.settings.low_inventory_threshold > 0 -%}
                <span class="inventory inventory--low">{{ 'product.form.low_stock_with_quantity_count' | t: count: product.selected_or_first_available_variant.inventory_quantity }}</span>
              {%- else -%}
                {%- if product.selected_or_first_available_variant.inventory_policy == 'continue' and product.selected_or_first_available_variant.inventory_quantity <= 0 and product.selected_or_first_available_variant.requires_shipping -%}
                  {%- if product.selected_or_first_available_variant.incoming and product.selected_or_first_available_variant.next_incoming_date -%}
                    {%- capture next_incoming_date -%}{{ product.selected_or_first_available_variant.next_incoming_date | date: format: 'date' }}{%- endcapture -%}
                    <span class="inventory inventory--low">{{ 'product.form.incoming_stock' | t: next_incoming_date: next_incoming_date }}</span>
                  {%- else -%}
                    <span class="inventory inventory--low">{{ 'product.form.oversell_stock' | t }}</span>
                  {%- endif -%}
                {%- else -%}
                  <span class="inventory inventory--high">{{ 'product.form.in_stock' | t }}</span>
                {%- endif -%}
              {%- endif -%}
            {%- elsif product.selected_or_first_available_variant.incoming -%}
              {%- if product.selected_or_first_available_variant.next_incoming_date -%}
                {%- capture next_incoming_date -%}{{ product.selected_or_first_available_variant.next_incoming_date | date: format: 'date' }}{%- endcapture -%}
                <span class="inventory inventory--low">{{ 'product.form.incoming_stock' | t: next_incoming_date: next_incoming_date }}</span>
              {%- else -%}
                <span class="inventory inventory--low">{{ 'product.form.oversell_stock' | t }}</span>
              {%- endif -%}
            {%- endif -%}

            <script type="application/json">
              {
                {%- for variant in product.variants -%}
                  {%- capture inventory_message -%}
                    {%- if variant.available -%}
                      {%- if variant.inventory_management and variant.inventory_policy == 'deny' and variant.inventory_quantity <= block.settings.low_inventory_threshold and block.settings.low_inventory_threshold > 0 -%}
                        <span class="inventory inventory--low">{{ 'product.form.low_stock_with_quantity_count' | t: count: variant.inventory_quantity }}</span>
                      {%- else -%}
                        {%- if variant.inventory_policy == 'continue' and variant.inventory_quantity <= 0 and variant.requires_shipping -%}
                          {%- if variant.incoming and variant.next_incoming_date -%}
                            {%- capture next_incoming_date -%}{{ variant.next_incoming_date | date: format: 'date' }}{%- endcapture -%}
                            <span class="inventory inventory--low">{{ 'product.form.incoming_stock' | t: next_incoming_date: next_incoming_date }}</span>
                          {%- else -%}
                            <span class="inventory inventory--low">{{ 'product.form.oversell_stock' | t }}</span>
                          {%- endif -%}
                        {%- else -%}
                          <span class="inventory inventory--high">{{ 'product.form.in_stock' | t }}</span>
                        {%- endif -%}
                      {%- endif -%}
                    {%- elsif variant.incoming -%}
                      {%- if variant.next_incoming_date -%}
                        {%- capture next_incoming_date -%}{{ variant.next_incoming_date | date: format: 'date' }}{%- endcapture -%}
                        <span class="inventory inventory--low">{{ 'product.form.incoming_stock' | t: next_incoming_date: next_incoming_date }}</span>
                      {%- else -%}
                        <span class="inventory inventory--low">{{ 'product.form.oversell_stock' | t }}</span>
                      {%- endif -%}
                    {%- endif -%}
                  {%- endcapture -%}

                  "{{ variant.id }}": {{ inventory_message | json }}{% unless forloop.last %},{% endunless %}
                {%- endfor -%}
              }
            </script>
          </product-inventory>
        {%- endif -%}

      {%- comment -%}
      ----------------------------------------------------------------------------------------------------------------
      BUY BUTTONS
      ----------------------------------------------------------------------------------------------------------------
      {%- endcomment -%}
      {%- when 'buy_buttons' -%}
        {%- if request.page_type != 'password' -%}
          {%- assign recipient_feature_active = false -%}

          {%- if product.gift_card? and block.settings.show_gift_card_recipient -%}
            {%- assign recipient_feature_active = true -%}
          {%- endif -%}

          <div class="product-form__buy-buttons" {{ block.shopify_attributes }}>
            {%- form 'product', product, is: 'product-form', id: product_form_id -%}
              {%- if recipient_feature_active -%}
                <gift-card-recipient class="gift-card-recipient">
                  <div class="input input--checkbox">
                    <div class="checkbox-container">
                      <input type="checkbox" class="checkbox" name="properties[__shopify_send_gift_card_to_recipient]" id="product-{{ section.id }}-{{ product.id }}-send-gift-card-to-recipient">
                      <label for="product-{{ section.id }}-{{ product.id }}-send-gift-card-to-recipient" class="text--subdued">{{ 'gift_card.recipient.checkbox' | t }}</label>
                    </div>
                  </div>

                  <div class="gift-card-recipient__fields js:hidden">
                    <div class="input">
                      <input id="product-{{ section.id }}-{{ product.id }}-recipient-email" type="email" class="input__field input__field--text" name="properties[Recipient email]" required value="{{ form.email }}">
                      <label for="product-{{ section.id }}-{{ product.id }}-recipient-email" class="input__label">{{ 'gift_card.recipient.email_label' | t }}</label>
                    </div>

                    <div class="input">
                      <input id="product-{{ section.id }}-{{ product.id }}-recipient-name" type="text" class="input__field input__field--text" name="properties[Recipient name]" value="{{ form.name }}">
                      <label for="product-{{ section.id }}-{{ product.id }}-recipient-name" class="input__label">{{ 'gift_card.recipient.name_label' | t }}</label>
                    </div>

                    <div class="input">
                      <input type="hidden" name="properties[__shopify_offset]" value="" disabled>
                      <input id="product-{{ section.id }}-{{ product.id }}-send-on" type="date" class="input__field input__field--text" pattern="\d{4}-\d{2}-\d{2}" name="properties[Send on]" value="{{ form.send_on }}">
                      <label for="product-{{ section.id }}-{{ product.id }}-send-on" class="input__label">{{ 'gift_card.recipient.send_on_label' | t }}</label>
                    </div>

                    <div class="input">
                      <textarea id="product-{{ section.id }}-{{ product.id }}-recipient-message" rows="4" class="input__field input__field--textarea" name="properties[Message]">{{ form.message }}</textarea>
                      <label for="product-{{ section.id }}-{{ product.id }}-recipient-message" class="input__label">{{ 'gift_card.recipient.message_label' | t }}</label>
                    </div>
                  </div>
                </gift-card-recipient>
              {%- endif -%}

              <input type="hidden" disabled name="id" value="{{ product.selected_or_first_available_variant.id }}">

              <product-payment-container form-id="{{ product_form_id }}" {% if update_url %}id="MainPaymentContainer"{% endif %} class="product-form__payment-container" {{ block.shopify_attributes }}>
                <button id="AddToCart" type="submit" is="loader-button" {% unless block.settings.show_payment_button and template.suffix != 'quick-buy-popover' %}data-use-primary{% endunless %} data-product-add-to-cart-button data-button-content="{% if product.template_suffix == 'pre-order' %}{{ 'product.form.pre_order' | t | escape }}{% else %}{{ 'product.form.add_to_cart' | t | escape }}{% endif %}" class="product-form__add-button button button--large {% unless product.selected_or_first_available_variant.available %}button--ternary{% else %}{% if block.settings.show_payment_button and template.suffix != 'quick-buy-popover' %}button--secondary{% else %}button--primary{% endif %}{% endunless %} button--full" {% unless product.selected_or_first_available_variant.available %}disabled{% endunless %}>
                  {%- if product.selected_or_first_available_variant.available -%}
                    {%- if product.template_suffix == 'pre-order' -%}
                      {{- 'product.form.pre_order' | t -}}
                    {%- else -%}
                      {{- 'product.form.add_to_cart' | t -}}
                    {%- endif -%}
                  {%- else -%}
                    {{- 'product.form.sold_out' | t -}}
                  {%- endif -%}
                </button>

                {%- if block.settings.show_payment_button and recipient_feature_active == false and template.suffix != 'quick-buy-popover' -%}
                  {{ form | payment_button }}

                  {%- unless product.selected_or_first_available_variant.available -%}
                    <style>
                      #shopify-section-{{ section.id }} .shopify-payment-button {
                        display: none;
                      }
                    </style>
                  {%- endunless -%}
                {%- endif -%}
              </product-payment-container>
            {%- endform -%}
          </div>
        {%- endif -%}

        {%- if template.suffix != 'quick-buy-popover' -%}
          <store-pickup form-id="{{ product_form_id }}" class="product-form__store-availability-container">
            {%- render 'store-availability', product_variant: product.selected_or_first_available_variant -%}
          </store-pickup>
        {%- endif -%}
    {%- endcase -%}
  {%- endfor -%}

  {%- comment -%}
  IMPLEMENTATION NOTE: under rare circumstances, merchant may want to show selectors but hide the add to cart button. This
  is however problematic as elements changed based on this. So if we detect there is no buy buttons block, we add an empty one
  {%- endcomment -%}

  {%- assign buy_buttons_block = section.blocks | where: 'type', 'buy_buttons' | first -%}

  {%- if buy_buttons_block == blank -%}
    {%- form 'product', product, is: 'product-form', id: product_form_id -%}
      <input type="hidden" disabled name="id" value="{{ product.selected_or_first_available_variant.id }}">
    {%- endform -%}
  {%- endif -%}
</div>

{%- if template.suffix == 'quick-buy-drawer' -%}
  <div class="product-form__view-details">
    <a href="{{ product.url }}" class="link text--subdued">{{ 'product.general.view_details' | t }}</a>
  </div>
{%- endif -%}

{%- comment -%}
IMPLEMENTATION NOTE: if during the iteration of the options we have found an option matching a size chart, we add it here
{%- endcomment -%}

{%- if found_size_option and size_chart_page != blank -%}
  {%- comment -%}Drawer is for tablet and desktop{%- endcomment -%}
  <drawer-content id="product-{{ section.id }}-{{ product.id }}-size-chart-drawer" class="drawer drawer--large hidden-phone">
    <span class="drawer__overlay"></span>

    <header class="drawer__header">
      <p class="drawer__title heading h4">{{ size_chart_page.title }}</p>

      <button type="button" class="drawer__close-button tap-area" data-action="close" title="{{ 'general.accessibility.close' | t | escape }}">
        {%- render 'icon' with 'close' -%}
      </button>
    </header>

    <div class="drawer__content drawer__content--padded-start">
      <div class="rte">
        {{- size_chart_page.content -}}
      </div>
    </div>
  </drawer-content>

  {%- comment -%}Popover is for mobile{%- endcomment -%}
  <popover-content id="product-{{ section.id }}-{{ product.id }}-size-chart-popover" class="popover hidden-lap-and-up">
    <span class="popover__overlay"></span>

    <header class="popover__header">
      <p class="popover__title heading h6">{{ size_chart_page.title }}</p>

      <button type="button" class="popover__close-button tap-area tap-area--large" data-action="close" title="{{ 'general.accessibility.close' | t | escape }}">
        {%- render 'icon' with 'close' -%}
      </button>
    </header>

    <div class="popover__content">
      <div class="rte">
        {{- size_chart_page.content -}}
      </div>
    </div>
  </popover-content>
{%- endif -%}
