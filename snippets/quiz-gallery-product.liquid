{% if forloop == blank %}
  {% assign index = "$INDEX" %}
{% else %}
  {% assign index = forloop.index %}
{% endif %}

{% if product_id == blank %}
  {% assign product_id = product.id | default: '$PRODUCT_ID' %}
{% endif %}

{% if product_title == blank %}
  {% assign product_title = product.title | default: '$PRODUCT_TITLE' %}
{% endif %}

{% if product_color == blank %}
  {% assign product_color = product.metafields.quiz.product_color | default: '$PRODUCT_COLOR' %}
{% endif %}

{% if product_short_description == blank %}
  {% if product %}
    {% if product.metafields.custom.short_description %}
      {% assign product_short_description = product.metafields.custom.short_description %}
    {% else %}
      {% assign product_short_description = '' %}
    {% endif %}
  {% else %}
    {% assign product_short_description = '$PRODUCT_SHORT_DESCRIPTION' %}
  {% endif %}
{% endif %}


{% if first_variant_id == blank %}
  {% assign first_variant_id = product.variants.first.id | default: "$VARIANT_ID_FIRST" %}
{% endif %}

{% if last_variant_id == blank %}
  {% assign last_variant_id = product.variants.last.id | default: "$VARIANT_ID_LAST" %}
{% endif %}

{% if variant_price == blank %}
  {% assign variant_price = product.selected_or_first_available_variant.price | default: '$VARIANT_PRICE' %}
{% endif %}

{% if variant_compare_price == blank %}
  {% if product.selected_or_first_available_variant.compare_at_price %}
    {% assign variant_compare_price = product.selected_or_first_available_variant.compare_at_price | default: '$VARIANT_PRICE_COMPARE' %}
  {% else %}
    {% assign variant_compare_price = variant_price | default: '$VARIANT_PRICE_COMPARE'  %}
  {% endif %}
{% endif %}

{% if variant_data == blank %}
  {% if product %}
    {% capture variant_data %}
      {{- product.variants.first | json -}}
    {% endcapture %}
  {% else %}
    {%- capture variant_object -%}
      { "price": {{- variant_price -}} }
    {%- endcapture -%}
    {% assign variant_data = variant_object %}
  {% endif %}
{% endif %}

{% if variant_calories == blank %}
  {% assign variant_calories = variant.metafields.quiz.calories | default: '$VARIANT_METAFIELD_CALORIES' %}
{% endif %}

{% if results_image_url == blank %}

  {% if product %}
    
    {% if product.metafields.custom.quiz_results_image %}
      {% assign results_image_url = product.metafields.custom.quiz_results_image | image_url %}
    {% else %}
      {% assign results_image_url = product.images.first | image_url %}
    {% endif %}
  {% else %}
    {% assign results_image_url = '$PRODUCT_IMAGE_URL' %}
  {% endif %}

{%- endif -%}

{% if show_price == true %}
  
  {% capture price_display %}

    <div class="price-list">

      {%- if variant_compare_price > variant_price -%}
        
        <span class="price price--highlight">
          <span class="visually-hidden">{{ 'product.general.sale_price' | t }}</span>

          {%- if variant_price != "$PRODUCT_PRICE" -%}
            {{- variant_price | money -}}
          {%- else -%}
            {{- variant_price -}}
          {%- endif -%}

        </span>

        <span class="price price--compare">
          <span class="visually-hidden">{{ 'product.general.regular_price' | t }}</span>

          {%- if variant_compare_price != "$VARIANT_PRICE_COMPARE " -%}
            {{- variant_compare_price | money -}}
          {%- else -%}
            {{- variant_compare_price -}}
          {%- endif -%}
          
        </span>

      {%- else -%}

        <span class="price">
          <span class="visually-hidden">{{ 'product.general.sale_price' | t }}</span>

          {%- if variant_price != "$PRODUCT_PRICE" -%}
            {{- variant_price | money -}}
          {%- else -%}
            {{- variant_price -}}
          {%- endif -%}
        </span>

      {%- endif -%}
      
    </div>

  {% endcapture %}
  
{% endif %}

<quiz-results-product class="gallery__item quiz-results-product" data-quiz-results-product data-variant-id="{{ first_variant_id }}" data-variant-id-last="{{ last_variant_id }}" data-variant-data='{{ variant_data }}' data-calories='{{ variant_calories }}' style="--quiz-results-product-color: {{ product_color }}">

  <div class="quiz-results-product__inner">

    <div class="gallery__header quiz-results-product__header">

      {% unless hide_details == true %}
        <div class="quiz-ribbon quiz-ribbon--number">
          <div class="quiz-ribbon__top">
            <span>
              <span>{{ index }}</span>
            </span>
          </div>
          <div class="quiz-ribbon__bottom">
            {% render 'quiz-tag-ribbon' %}
          </div>
        </div>
      {% endunless %}

      <img src="{{ results_image_url }}" alt="{{ product_title }}" loading="lazy" class="gallery__image quiz-results-product__image">

      <div class="quiz-ribbon quiz-ribbon--recommended">
        <div class="quiz-ribbon__top">
          <span>
            <svg width="22" height="20" viewBox="0 0 22 20" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M11 0L13.4697 7.60081H21.4616L14.996 12.2984L17.4656 19.8992L11 15.2016L4.53436 19.8992L7.00402 12.2984L0.538379 7.60081H8.53035L11 0Z" fill="#2E2E2E"/>
            </svg>
          </span>
        </div>
        <div class="quiz-ribbon__bottom">
          {% render 'quiz-tag-ribbon' %}
        </div>
      </div> 

    </div>

    <figure class="quiz-results-product__details">

      <h4 class="heading h3">{{ product_title }}</h4>

      {%- if product_short_description != blank -%}
        <div class="quiz-results-product__caption rte">
          <p>{{- product_short_description -}}</p>
        </div>
        <div class="quiz-results-product__tags">
          <div class="quiz-results-tag quiz-results-tag--recommended">
            <div class="quiz-results-tag__inner">{{ 'quiz.general.recommended' | t }}</div>
          </div>
          <div class="quiz-results-tag quiz-results-tag--soldout">
            <div class="quiz-results-tag__inner">{{ 'quiz.general.soldout' | t }}</div>
          </div>
        </div>
      {%- endif -%}

      <div class="quiz-results-product__view-details text--large text-align--center">
        
        {% unless hide_details == true %}
          <button type="button" is="toggle-button" class="link link--strong link--animated" aria-controls="quiz-popup--recipe-{{ product_id }}" aria-expanded="false">{{ 'quiz.results.view_details' | t }}</button>
        {% endunless %}

      </div>

    </figure>
  
  </div>

  <div class="quiz-results-product__footer">

    {% if show_price == true %}
      <div class="quiz-results-product__footer-price">
        {{ price_display }}
      </div>
    {% endif %}

    <div class="quiz-results-product__footer-cta">

      <button class="quiz-results-product-button quiz-results-product-button--add-to-cart quiz-results-product__checkbox-button" type="button" data-quiz-results-product-checkbox>

        <div class="quiz-results-product__checkbox">
          <div class="quiz-results-product__checkbox-input">
            
          </div>
          <div class="quiz-results-product__checkbox-label">
            {{ 'quiz.quiz_results_product.add_to_cart' | t }}
          </div>
        </div>

      </button>

      <button disabled class="quiz-results-product-button quiz-results-product-button--soldout quiz-results-product__checkbox-button" type="button">
        <div class="quiz-results-product__checkbox">
          <div class="quiz-results-product__checkbox-label">
            {{ 'quiz.general.soldout' | t }}
          </div>
        </div>
      </button>

    </div>

  </div>

</quiz-results-product>