{%- comment -%}V4.6.0 - November 2022
This file is system-genererated by Discount Ninja and should not be modified or stored in a source control system.
Do not modify or remove this snippet, we reserve the right to overwrite it at any moment to add improvements or new features so that customizations you made might be lost.
{%- endcomment -%}

{% comment %}
Add css to the head
{% endcomment %}
<link rel="preload" href="{{ 'limoniapps-discountninja.css' | asset_url }}" as="style" onload="this.onload=null;this.rel='stylesheet'">
<noscript><link rel="stylesheet" href="{{ 'limoniapps-discountninja.css' | asset_url }}"></noscript>

{% comment %}
Save token for later use and register event listener
{% endcomment %}
<script type="text/javascript">
try { var a = window.location.href.replace(window.location.hash, ""); var b = [], hash; var c = a.slice(a.indexOf('?') + 1).split('&'); for (var i = 0; i < c.length; i++) {hash = c[i].split('='); b.push(hash[0]); b[hash[0]] = hash[1];} var d = b["token"]; var e = b["discountcode"]; var f = 'limoniapps-discountninja-'; if (d) sessionStorage.setItem(f + 'savedtoken', d); if (e) sessionStorage.setItem(f + 'saveddiscountcode', e); } catch (e) { var a = 0; }
try { if(null==EventTarget.prototype.original_limoniAppsDiscountNinja_addEventListener){EventTarget.prototype.original_limoniAppsDiscountNinja_addEventListener=EventTarget.prototype.addEventListener,EventTarget.prototype.addEventListener=function(n,i,t){this.limoniAppsDiscountNinjaAllHandlers=this.limoniAppsDiscountNinjaAllHandlers||[],this.limoniAppsDiscountNinjaAllHandlers.push({typ:n,fn:i,opt:t}),this.original_limoniAppsDiscountNinja_addEventListener(n,i,t)}} } catch (e) { var a = 0; }
</script>