{%- comment -%}
Add Facebook and Pinterest Open Graph meta tags to product pages
for friendly Facebook sharing and Pinterest pinning.

More info Open Graph meta tags
- https://developers.facebook.com/docs/opengraph/using-objects/
- https://developers.pinterest.com/rich_pins/

Use the Facebook Open Graph Debugger for validation (and cache clearing)
- https://developers.facebook.com/tools/debug

Validate your Pinterest rich pins
- https://developers.pinterest.com/tools/url-debugger/
{%- endcomment -%}

{%- if request.page_type == 'product' -%}
  {%- comment %}<locksmith:7baf>{% endcomment -%}
    {%- capture var %}{% render 'locksmith-variables', variable: 'transparent', hiding_scope: 'resource' %}{% endcapture %}{% if var == 'true' %}<meta property="og:type" content="product">{% endif -%}
    {%- comment %}original: <meta property="og:type" content="product">{%- endcomment %}
  {%- comment %}</locksmith:7baf>{% endcomment -%}
  {%- comment %}<locksmith:264d>{% endcomment -%}
    {%- capture var %}{% render 'locksmith-variables', variable: 'transparent', hiding_scope: 'resource' %}{% endcapture %}{% if var == 'true' %}<meta property="og:title" content="{{ product.title | strip_html | escape }}">{% endif -%}
    {%- comment %}original: <meta property="og:title" content="{{ product.title | strip_html | escape }}">{%- endcomment %}
  {%- comment %}</locksmith:264d>{% endcomment -%}
  <meta property="product:price:amount" content="{{ product.selected_or_first_available_variant.price | money_without_currency | strip_html | escape }}">
  <meta property="product:price:currency" content="{{ cart.currency.iso_code }}">
{%- elsif request.page_type == 'article' -%}
  {%- comment %}<locksmith:1a38>{% endcomment -%}
    {%- capture var %}{% render 'locksmith-variables', variable: 'transparent', hiding_scope: 'resource' %}{% endcapture %}{% if var == 'true' %}<meta property="og:type" content="article">{% endif -%}
    {%- comment %}original: <meta property="og:type" content="article">{%- endcomment %}
  {%- comment %}</locksmith:1a38>{% endcomment -%}
  {%- comment %}<locksmith:1657>{% endcomment -%}
    {%- capture var %}{% render 'locksmith-variables', variable: 'transparent', hiding_scope: 'resource' %}{% endcapture %}{% if var == 'true' %}<meta property="og:title" content="{{ article.title | strip_html | escape }}">{% endif -%}
    {%- comment %}original: <meta property="og:title" content="{{ article.title | strip_html | escape }}">{%- endcomment %}
  {%- comment %}</locksmith:1657>{% endcomment -%}
{%- elsif request.page_type == 'collection' -%}
  {%- comment %}<locksmith:8691>{% endcomment -%}
    {%- capture var %}{% render 'locksmith-variables', variable: 'transparent', hiding_scope: 'resource' %}{% endcapture %}{% if var == 'true' %}<meta property="og:type" content="website">{% endif -%}
    {%- comment %}original: <meta property="og:type" content="website">{%- endcomment %}
  {%- comment %}</locksmith:8691>{% endcomment -%}
  {%- comment %}<locksmith:5b2a>{% endcomment -%}
    {%- capture var %}{% render 'locksmith-variables', variable: 'transparent', hiding_scope: 'resource' %}{% endcapture %}{% if var == 'true' %}<meta property="og:title" content="{{ collection.title | strip_html | escape }}">{% endif -%}
    {%- comment %}original: <meta property="og:title" content="{{ collection.title | strip_html | escape }}">{%- endcomment %}
  {%- comment %}</locksmith:5b2a>{% endcomment -%}
{%- else -%}
  {%- comment %}<locksmith:140a>{% endcomment -%}
    {%- capture var %}{% render 'locksmith-variables', variable: 'transparent', hiding_scope: 'resource' %}{% endcapture %}{% if var == 'true' %}<meta property="og:type" content="website">{% endif -%}
    {%- comment %}original: <meta property="og:type" content="website">{%- endcomment %}
  {%- comment %}</locksmith:140a>{% endcomment -%}
  {%- comment %}<locksmith:8a9d>{% endcomment -%}
    {%- capture var %}{% render 'locksmith-variables', variable: 'transparent', hiding_scope: 'resource' %}{% endcapture %}{% if var == 'true' %}<meta property="og:title" content="{{ page_title | escape }}">{% endif -%}
    {%- comment %}original: <meta property="og:title" content="{{ page_title | escape }}">{%- endcomment %}
  {%- comment %}</locksmith:8a9d>{% endcomment -%}
{%- endif -%}

{%- if page_image -%}
  {%- comment %}<locksmith:00db>{% endcomment -%}
    {%- capture var %}{% render 'locksmith-variables', variable: 'transparent', hiding_scope: 'resource' %}{% endcapture %}{% if var == 'true' %}<meta property="og:image" content="http:{{ page_image | img_url: 'master' }}">{% endif -%}
    {%- comment %}original: <meta property="og:image" content="http:{{ page_image | img_url: 'master' }}">{%- endcomment %}
  {%- comment %}</locksmith:00db>{% endcomment -%}
  {%- comment %}<locksmith:8c37>{% endcomment -%}
    {%- capture var %}{% render 'locksmith-variables', variable: 'transparent', hiding_scope: 'resource' %}{% endcapture %}{% if var == 'true' %}<meta property="og:image:secure_url" content="https:{{ page_image | img_url: 'master' }}">{% endif -%}
    {%- comment %}original: <meta property="og:image:secure_url" content="https:{{ page_image | img_url: 'master' }}">{%- endcomment %}
  {%- comment %}</locksmith:8c37>{% endcomment -%}
  <meta property="og:image:width" content="{{ page_image.width }}">
  <meta property="og:image:height" content="{{ page_image.height }}">
{%- endif -%}

{%- if page_description -%}
  {%- comment %}<locksmith:4829>{% endcomment -%}
    {%- capture var %}{% render 'locksmith-variables', variable: 'transparent', hiding_scope: 'resource' %}{% endcapture %}{% if var == 'true' %}<meta property="og:description" content="{{ page_description | escape }}">{% endif -%}
    {%- comment %}original: <meta property="og:description" content="{{ page_description | escape }}">{%- endcomment %}
  {%- comment %}</locksmith:4829>{% endcomment -%}
{%- endif -%}

<meta property="og:url" content="{{ canonical_url }}">
<meta property="og:site_name" content="{{ shop.name }}">

{%- comment -%}
This snippet renders meta data needed to create a Twitter card
for products and articles.

Your cards must be approved by Twitter to be activated
- https://dev.twitter.com/docs/cards/validation/validator

More information:
- https://dev.twitter.com/cards/types/summary
{%- endcomment -%}

<meta name="twitter:card" content="summary">

{%- if request.page_type == 'product' -%}
  {%- comment %}<locksmith:da4e>{% endcomment -%}
    {%- capture var %}{% render 'locksmith-variables', variable: 'transparent', hiding_scope: 'resource' %}{% endcapture %}{% if var == 'true' %}<meta name="twitter:title" content="{{ product.title | escape }}">{% endif -%}
    {%- comment %}original: <meta name="twitter:title" content="{{ product.title | escape }}">{%- endcomment %}
  {%- comment %}</locksmith:da4e>{% endcomment -%}
  {%- comment %}<locksmith:2828>{% endcomment -%}
    {%- capture var %}{% render 'locksmith-variables', variable: 'transparent', hiding_scope: 'resource' %}{% endcapture %}{% if var == 'true' %}<meta name="twitter:description" content="{{ product.description | strip_html | truncatewords: 140, '' | escape }}">{% endif -%}
    {%- comment %}original: <meta name="twitter:description" content="{{ product.description | strip_html | truncatewords: 140, '' | escape }}">{%- endcomment %}
  {%- comment %}</locksmith:2828>{% endcomment -%}
{%- elsif request.page_type == 'article' -%}
  {%- comment %}<locksmith:95e8>{% endcomment -%}
    {%- capture var %}{% render 'locksmith-variables', variable: 'transparent', hiding_scope: 'resource' %}{% endcapture %}{% if var == 'true' %}<meta name="twitter:title" content="{{ article.title }}">{% endif -%}
    {%- comment %}original: <meta name="twitter:title" content="{{ article.title }}">{%- endcomment %}
  {%- comment %}</locksmith:95e8>{% endcomment -%}
  {%- comment %}<locksmith:53de>{% endcomment -%}
    {%- capture var %}{% render 'locksmith-variables', variable: 'transparent', hiding_scope: 'resource' %}{% endcapture %}{% if var == 'true' %}<meta name="twitter:description" content="{{ article.excerpt_or_content | strip_html | truncatewords: 140, '' | escape }}">{% endif -%}
    {%- comment %}original: <meta name="twitter:description" content="{{ article.excerpt_or_content | strip_html | truncatewords: 140, '' | escape }}">{%- endcomment %}
  {%- comment %}</locksmith:53de>{% endcomment -%}
{%- elsif request.page_type == 'collection' -%}
  {%- comment %}<locksmith:ccdc>{% endcomment -%}
    {%- capture var %}{% render 'locksmith-variables', variable: 'transparent', hiding_scope: 'resource' %}{% endcapture %}{% if var == 'true' %}<meta name="twitter:title" content="{{ collection.title }}">{% endif -%}
    {%- comment %}original: <meta name="twitter:title" content="{{ collection.title }}">{%- endcomment %}
  {%- comment %}</locksmith:ccdc>{% endcomment -%}
  {%- comment %}<locksmith:39f4>{% endcomment -%}
    {%- capture var %}{% render 'locksmith-variables', variable: 'transparent', hiding_scope: 'resource' %}{% endcapture %}{% if var == 'true' %}<meta name="twitter:description" content="{{ collection.description | strip_html | truncatewords: 140, '' | escape }}">{% endif -%}
    {%- comment %}original: <meta name="twitter:description" content="{{ collection.description | strip_html | truncatewords: 140, '' | escape }}">{%- endcomment %}
  {%- comment %}</locksmith:39f4>{% endcomment -%}
{%- else -%}
  {%- comment %}<locksmith:249c>{% endcomment -%}
    {%- capture var %}{% render 'locksmith-variables', variable: 'transparent', hiding_scope: 'resource' %}{% endcapture %}{% if var == 'true' %}<meta name="twitter:title" content="{{ page_title | escape }}">{% endif -%}
    {%- comment %}original: <meta name="twitter:title" content="{{ page_title | escape }}">{%- endcomment %}
  {%- comment %}</locksmith:249c>{% endcomment -%}
  {%- comment %}<locksmith:74b7>{% endcomment -%}
    {%- capture var %}{% render 'locksmith-variables', variable: 'transparent', hiding_scope: 'resource' %}{% endcapture %}{% if var == 'true' %}<meta name="twitter:description" content="{{ page_description | default: page_title | escape }}">{% endif -%}
    {%- comment %}original: <meta name="twitter:description" content="{{ page_description | default: page_title | escape }}">{%- endcomment %}
  {%- comment %}</locksmith:74b7>{% endcomment -%}
{%- endif -%}

{%- if page_image -%}
  {%- comment %}<locksmith:6754>{% endcomment -%}
    {%- capture var %}{% render 'locksmith-variables', variable: 'transparent', hiding_scope: 'resource' %}{% endcapture %}{% if var == 'true' %}<meta name="twitter:image" content="https:{{ page_image | img_url: '1200x1200', crop: 'center' }}">{% endif -%}
    {%- comment %}original: <meta name="twitter:image" content="https:{{ page_image | img_url: '1200x1200', crop: 'center' }}">{%- endcomment %}
  {%- comment %}</locksmith:6754>{% endcomment -%}
  <meta name="twitter:image:alt" content="{{ page_image.alt | escape }}">
{%- endif -%}