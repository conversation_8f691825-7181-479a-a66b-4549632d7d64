{% if dog_name != blank %}
  {% assign dog_name_last_character = dog_name | slice: -1 %}
  {% if dog_name_last_character == "s" %}
    {% assign dog_name_possessive = dog_name | append: "'" %}
  {% else %}
    {% assign dog_name_possessive = dog_name | append: "'s" %}
  {% endif %}
{% endif %}

<quiz-results-dog data-dog="{{ dog_index | default: "$DOGINDEX" }}" data-dog-name="{{ dog_name | default: "$DOGNAME" }}" class="quiz-results-dog--active" limit="{{ product_recommendation_limit | default: "$PRODUCT_RECOMMENDATION_LIMIT" }}">

  <div class="quiz-results-dog__header">

    <div class="container">

      <h3 class="quiz-results-dog__title heading h1">
        <span data-dog-name-possessive>{{ dog_name_possessive | default: "$DOGNAME_POSSESSIVE" }}</span> Starter Box
      </h3>

      <div class="quiz-results-dog__content">
        <p class="initial-recommendation">
          We recommend starting with our <strong data-initial-recommendation-product>{{ product_recommendation | default: "$PRODUCT_RECOMMENDATION" }}</strong> recipe<span data-initial-recommendation-prompt></span>.
        </p> 
        <p>Our Recipe Starter Box includes enough food for a 10 day transition.</p>
      </div>

      <p class="heading heading--small text--subdued">Choose {{ product_recommendation_limit | default: "$PRODUCT_RECOMMENDATION_LIMIT" }} of our recipes <button is="toggle-button" aria-controls="quiz-popup--recipes" aria-expanded="false" style="vertical-align: top;">{% render 'question-mark' %}</button></p>
    
    </div>
  
  </div>

  <quiz-results-products class="gallery quiz-results-products">

    <scrollable-content {% unless section.settings.show_arrows %}draggable{% endunless %} class="gallery__list-wrapper {% if section.blocks.size >= 3 %}is-scrollable{% endif %} hide-scrollbar">
      
      <div class="container">
        
        <div class="gallery__list quiz-results__list">

          {% comment %} Products are loaded via AJAX. {% endcomment %}

          {%- comment -%}
            {% if collection %}
              {%- for product in collection.products -%}
                {% render 'quiz-gallery-product', product: product, forloop: forloop %}
              {%- endfor -%}
            {% endif %}
           {%- endcomment -%}
        
        </div>

      </div>

    </scrollable-content>

    {% comment %}
    <prev-next-buttons class="gallery__prev-next-buttons prev-next-buttons">
      <button class="gallery__arrow prev-next-button prev-next-button--prev">
        <span class="visually-hidden">{{ 'general.accessibility.previous' | t }}</span>
        {%- render 'icon' with 'nav-arrow-left', direction_aware: true -%}
      </button>

      <button class="gallery__arrow prev-next-button prev-next-button--next">
        <span class="visually-hidden">{{ 'general.accessibility.next' | t }}</span>
        {%- render 'icon' with 'nav-arrow-right', direction_aware: true -%}
      </button>
    </prev-next-buttons>

    <div class="gallery__progress-bar-wrapper container">
      <span class="gallery__progress-bar progress-bar" style="--divider: {{ product_recommendation_number | default: "$PRODUCT_RECOMMENDATION_NUMBER" }}"></span>
    </div> 
    {% endcomment %}

  </quiz-results-products>

</quiz-results-dog>