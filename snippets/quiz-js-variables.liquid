<script>
  window.quizVariables = {
    images: {
      {% if settings.quiz_dog_image_1 != blank %}
        dogImage1: '{{ settings.quiz_dog_image_1 | image_url }}',
      {% endif %}
      {% if settings.quiz_overlay_loading_gif != blank %}
        dogImage1: '{{ settings.quiz_overlay_loading_gif | image_url }}',
      {% endif %}
    },
    discounts: {
      {% if settings.quiz_free_shipping_discount_code != blank %}
        code: "{{ settings.quiz_free_shipping_discount_code }}"
      {% endif %}
    },
    {% if customer %}
    customer: {
      id: {{ customer.id }},
      email: "{{ customer.email }}"
    },
    {% endif %}
    locations: {
      post_data: '{{ settings.quiz_bookmark_post_data }}',
      quiz_account: '{{ settings.quiz_bookmark_quiz_account }}',
      quiz: '{{ settings.quiz_bookmark_quiz }}',
      results: '{{ settings.quiz_bookmark_results }}'
    },
    components: {
      navigation: {
        start_over: "{{ 'quiz.general.start_over' | t }}",
        back: "{{ 'quiz.general.back' | t }}"
      },
      quizResultsProduct: {
        add_to_cart: "{{ 'quiz.quiz_results_product.add_to_cart' | t }}",
        selected: "{{ 'quiz.quiz_results_product.selected' | t }}"
      }
    },
    steps: {
      dog: `{% render 'quiz-step--dog-content' %}`
    },
    collections: {
      hpu: {
        id: {{ settings.quiz_collection_hpu.id }},
        message: "{{ settings.quiz_collection_hpu_message }}"
      },
      hpr: {
        id: {{ settings.quiz_collection_hpr.id }},
        message: "{{ settings.quiz_collection_hpr_message }}"
      },
      hpo: {
        id: {{ settings.quiz_collection_hpo.id }},
        message: "{{ settings.quiz_collection_hpo_message }}"
      },
      hau: {
        id: {{ settings.quiz_collection_hau.id }},
        message: "{{ settings.quiz_collection_hau_message }}"
      },
      har: {
        id: {{ settings.quiz_collection_har.id }},
        message: "{{ settings.quiz_collection_har_message }}"
      },
      hao: {
        id: {{ settings.quiz_collection_hao.id }},
        message: "{{ settings.quiz_collection_hao_message }}"
      },
      hsu: {
        id: {{ settings.quiz_collection_hsu.id }},
        message: "{{ settings.quiz_collection_hsu_message }}"
      },
      hsr: {
        id: {{ settings.quiz_collection_hsr.id }},
        message: "{{ settings.quiz_collection_hsr_message }}"
      },
      hso: {
        id: {{ settings.quiz_collection_hso.id }},
        message: "{{ settings.quiz_collection_hso_message }}"
      },
      allergies: {
        id: {{ settings.quiz_collection_allergies.id }},
        message: "{{ settings.quiz_collection_allergies_message }}"
      },
      heart: {
        id: {{ settings.quiz_collection_heart.id }},
        message: "{{ settings.quiz_collection_heart_message }}"
      },
      liver: {
        id: {{ settings.quiz_collection_liver.id }},
        message: "{{ settings.quiz_collection_liver_message }}"
      },
      kidney: {
        id: {{ settings.quiz_collection_kidney.id }},
        message: "{{ settings.quiz_collection_kidney_message }}"
      },
      pancreatitis: {
        id: {{ settings.quiz_collection_pancreatitis.id }},
        message: "{{ settings.quiz_collection_pancreatitis_message }}"
      },
      cancer: {
        id: {{ settings.quiz_collection_cancer.id }},
        message: "{{ settings.quiz_collection_cancer_message }}"
      },
      calcium_oxolate: {
        id: {{ settings.quiz_collection_calcium_oxolate.id }},
        message: "{{ settings.quiz_collection_calcium_oxolate_message }}"
      },
      urate_stones: {
        id: {{ settings.quiz_collection_urate_stones.id }},
        message: "{{ settings.quiz_collection_urate_stones_message }}"
      },
      kidney_pancreatitis: {
        id: {{ settings.quiz_collection_kidney_pancreatitis.id }},
        message: "{{ settings.quiz_collection_kidney_pancreatitis_message }}"
      },
      gi: {
        id: {{ settings.quiz_collection_gi.id }},
        message: "{{ settings.quiz_collection_gi_message }}"
      },
      joint: {
        id: {{ settings.quiz_collection_joint.id }},
        message: "{{ settings.quiz_collection_joint_message }}"
      },
      grain_intolerance: {
        id: {{ settings.quiz_collection_grain_intolerance.id }},
        message: "{{ settings.quiz_collection_grain_intolerance_message }}"
      }
    },
    results: {
      initial_recommendation_text: "{{ 'quiz.results.initial_recommendation_text' | t }}",
      dog: `{% render 'quiz-results-dog' %}`,
      dog_condition_not_listed: `{% render 'quiz-results-dog--notlisted' %}`,
      results_product: `{% render 'quiz-gallery-product' %}`,
      results_product_modal: `{% render 'quiz-product-modal' %}`
    },
    boxes: {
      days_in_box: {{ settings.quiz_starter_boxes_days }}
    }
  }
</script>