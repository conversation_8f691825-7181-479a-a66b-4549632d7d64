{%- comment -%}V4.6.0 - November 2022
This file is system-genererated by Discount Ninja and should not be modified or stored in a source control system.
Do not modify or remove this snippet, we reserve the right to overwrite it at any moment to add improvements or new features so that customizations you made might be lost.
{%- endcomment -%}

<script>
  {%- comment -%}Initialize object{%- endcomment -%}
  window.discountNinjaContext = { Version: "4.7.0", Shop: "{{shop.permanent_domain}}", ApiVersion: "5", ShopCurrency: "{{shop.currency}}", PresentmentCurrency: "{{cart.currency.iso_code}}", PresentmentCurrencyMoney: "{{ 677789 | money | replace: '"', "'" }}", PresentmentCurrencyMoneyWithCurrency: "{{ 677789 | money_with_currency | replace: '"', "'" }}", PresentmentCurrencyMoneyWithoutCurrency: "{{ 677789 | money_without_currency | replace: '"', "'" }}", PresentmentCurrencyMoneyWithoutTrailingZeros: "{{ 677789 | money_without_trailing_zeros | replace: '"', "'" }}", EnabledCurrencies: [], MoneyFormat: "{{shop.money_format | replace: '"', "'"}}", MoneyWithCurrencyFormat: "{{shop.money_with_currency_format | replace: '"', "'"}}", Customer: null, Cart: {{ cart | json }}, Product: null, CollectionProducts: [], ProductVariants: null, Labels: { From: "{{ 'discount_ninja.from' | t | replace: '"', "'" }}", Here: "{{ 'discount_ninja.here' | t | replace: '"', "'" }}", Free: "{{ 'discount_ninja.free' | t | replace: '"', "'" }}", SoldOut: "{{ 'discount_ninja.sold_out' | t | replace: '"', "'" }}" }, Settings: {% include 'limoniapps-discountninja-context-settings' %}, Flags: {% include 'limoniapps-discountninja-context-flags' %}, Status: {% include 'limoniapps-discountninja-context-status' %}, Signature: {% include 'limoniapps-discountninja-context-signature' %} };
  
  {%- comment -%}If we are on a collection page, get the details of the products in the collection{%- endcomment -%}
  {%- if collection and discountninjacollection == nil -%}{%- assign discountninjacollection = collection -%}{%- endif -%}   
  {%- if discountninjacollection -%}
      {%- for collectionProduct in discountninjacollection.products limit: 200 -%}
          window.discountNinjaContext.CollectionProducts.push({ Collections: {{ collectionProduct.collections | map: 'handle' | default: '[[--NONE--]]' | join: ',' | json }}, CollectionIds: "{{collectionProduct.collections | map: 'id' | default: '[[--NONE--]]' | join: ',' }}", Available: {{ collectionProduct.available }}, Handle: "{{ collectionProduct.handle }}", Price: {{ collectionProduct.price_max | default: 0 }}, CompareAtPrice: {{ collectionProduct.compare_at_price_max | default: 0 }}, PriceVaries: {{ collectionProduct.price_varies | default: false }}, PriceMin: {{ collectionProduct.price_min | default: 0 }}, Tags: {{ collectionProduct.tags | default: '[[--NONE--]]' | join: ', ' | json }} });
      {%- endfor -%}
  {%- endif -%}

  {%- comment -%}Add customer details if logged in{%- endcomment -%}
  {%- if customer -%}
      window.discountNinjaContext.Customer = { Id: {{ customer.id | default: 0 }}, NumberOfOrders: {{ customer.orders | size | default: 0 }}, Email: "{{ customer.email | default: '' | remove: '"' }}", Tags: {{ customer.tags | json }}, FirstName: "{{ customer.first_name | default: '' | remove: '"' }}", LastName: "{{ customer.last_name | default: '' | remove: '"' }}" };
  {%- endif -%} 

  {%- comment -%}If we are on a product page, get information about the product and its variants{%- endcomment -%}   
  {%- if product and discountninjaproduct == nil -%}{%- assign discountninjaproduct = product -%}{%- endif -%}
  {%- if discountninjaproduct -%}
      {%- if discountninjaproduct.selected_or_first_available_variant.id -%}
          {%- assign variantid = discountninjaproduct.selected_or_first_available_variant.id -%}
      {%- else -%}
          {%- assign variantid = 'null' -%}
      {%- endif -%}

      {%- comment -%}If we are on a product page, add the id and first or selected variantid{%- endcomment -%}
      window.discountNinjaContext.Product = {};
      window.discountNinjaContext.Product.Id = {{ discountninjaproduct.id | default: 0 }};
	  window.discountNinjaContext.Product.Handle = '{{ discountninjaproduct.handle | default: '' }}';
      window.discountNinjaContext.Product.VariantId = {{ variantid }};
      window.discountNinjaContext.Product.Tags = {{ discountninjaproduct.tags | default: '[[--NONE--]]' | join: ',' | json }};
      window.discountNinjaContext.Product.Available = {{ discountninjaproduct.available }};
     
      {%- comment -%}If we are on a product page, add the collection info and the featured image{%- endcomment -%}
      window.discountNinjaContext.Product.Collections = {{ discountninjaproduct.collections | map: 'handle' | default: '[[--NONE--]]' | join: ',' | json }};
      window.discountNinjaContext.Product.CollectionIds = '{{ discountninjaproduct.collections | map: 'id' | default: '[[--NONE--]]' | join: ',' }}';
      window.discountNinjaContext.Product.FeaturedImage = {{ discountninjaproduct.featured_image | img_url: '500x' | json }};

      {%- comment -%}If we are on a product page, initialize the list of product variants{%- endcomment -%}
      window.discountNinjaContext.ProductVariants = [];

      {%- comment -%}Loop through each variant of the product (maximum 200) {%- endcomment -%}
      {%- for discountninjaProductVariant in discountninjaproduct.variants limit: 200 -%}
          {%- comment -%}Add inventory quantity if it is tracked{%- endcomment -%}
          {%- assign inventory_quantity = discountninjaProductVariant.inventory_quantity -%}
		  {%- assign inventory_available = true -%}
          {%- if inventory_quantity == nil or inventory_quantity <= 0 -%}
              {%- assign inventory_quantity_below_zero = 1 -%}
          {%- else -%}
              {%- assign inventory_quantity_below_zero = 0 -%}
          {%- endif -%}
          {%- if discountninjaProductVariant.inventory_management and discountninjaProductVariant.inventory_policy == 'deny' -%}
              {%- assign inventory_quantity_apply = 1 -%}
          {%- else -%}
              {%- assign inventory_quantity_apply = 0 -%}
          {%- endif -%}
          {%- if inventory_quantity_below_zero == 1 and inventory_quantity_apply == 1 -%}
              {%- assign inventory_available = false -%}
          {%- endif -%}      
       
          {%- comment -%}Get price and compare at price of the variant{%- endcomment -%}
          {%- assign discountninja_variant_price = discountninjaProductVariant.price -%}
          {%- assign discountninja_compare_at_or_product_price = discountninjaProductVariant.compare_at_price -%}   
      
          {%- comment -%}If compare at price if not available, default to price{%- endcomment -%}
          {%- if discountninja_compare_at_or_product_price == nil or discountninja_compare_at_or_product_price < discountninja_variant_price -%}
              {%- assign discountninja_compare_at_or_product_price = discountninja_variant_price -%}
          {%- endif -%}
      
          {%- comment -%}Add to array{%- endcomment -%}
          window.discountNinjaContext.ProductVariants.push({ VariantId: {{ discountninjaProductVariant.id | default: 0 }}, Price: {{ discountninja_variant_price | default: 0 }}, CompareAtPrice: {{ discountninja_compare_at_or_product_price | default: 0 }}, Inventory: {{ inventory_quantity | default: 0 }}, Available: {{ inventory_available }}, IsSubscriptionProduct:  {%- if discountninjaProductVariant.selling_plan_allocations.size > 0 -%}true{%- else -%}false{%- endif -%} });
      {%- endfor -%}
  {%- endif -%}
  document.dispatchEvent(
    new CustomEvent('discountNinjaContext:load')
  );
</script>