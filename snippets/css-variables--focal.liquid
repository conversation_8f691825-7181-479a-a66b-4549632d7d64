{%- assign heading_font_italic = settings.quiz_heading_font | font_modify: 'style', 'italic' -%}

{%- assign quiz_text_font_italic = settings.quiz_text_font | font_modify: 'style', 'italic' -%}
{%- assign quiz_text_font_bold = settings.quiz_text_font | font_modify: 'weight', '800' -%}

{%- unless quiz_text_font_bold -%}
{%- assign quiz_text_font_bold = settings.quiz_text_font | font_modify: 'weight', '700' -%}
{%- endunless -%}

{%- unless quiz_text_font_bold -%}
{%- assign quiz_text_font_bold = settings.quiz_text_font | font_modify: 'weight', 'bolder' -%}
{%- endunless -%}

{%- assign quiz_text_font_bold_italic = quiz_text_font_bold | font_modify: 'style', 'italic' -%}

{% style %}

:root {

/*  ========== Layout ========== */

---container-gutter--desktop: 30px;
---container-gutter--mobile: 20px;

/*  ========== Type ========== */

/*  ------------------------------
    Fonts
    ------------------------------ */

---font-family-body: var(--text-font-family);
---font-style-body: normal;
---font-weight-body: 300;
---font-weight-body--bold: 800;

/* ---font-family-heading: var(--heading-font-family); */
---font-family-heading: 'cubano', sans-serif;
---font-style-heading: normal;
---font-weight-heading: 400;
---font-weight-heading--bold: 700;
---font-weight-heading--bolder: 700;

---font-weight--product-title: 600;

---font-weight--product-price: 600;
---font-weight--product-price--compare: 600;
---font-weight--product-price--sale: 600;

/*  ------------------------------
    Typography
    ------------------------------ */

/* ----- Font Size ----- */

/* Site */

---font-size-h0--mobile: 48px;
---font-size-h0--desktop: 72px;
---font-size-h1--mobile: 48px;
---font-size-h1--desktop: 64px;
---font-size-h2--mobile: 44px;
---font-size-h2--desktop: 56px;
---font-size-h3--mobile: 32px;
---font-size-h3--desktop: 40px;
---font-size-h4--mobile: 28px;
---font-size-h4--desktop: 32px;
---font-size-h5--mobile: 24px;
---font-size-h5--desktop: 28px;
---font-size-h6--mobile: 14px;
---font-size-h6--desktop: 16px;

---font-size-subheading-small--mobile: 14px;
---font-size-subheading-small--desktop: 16px;
---font-size-subheading--mobile: 18px;
---font-size-subheading--desktop: 20px;

---font-size-subheading-large--mobile: 14px;
---font-size-subheading-large--desktop: 16px;

---font-size-body--mobile: 16px;
---font-size-body--desktop: 18px;
---font-size-body-xs--mobile: 12px;
---font-size-body-xs--desktop: 14px;
---font-size-body-small--mobile: 14px;
---font-size-body-small--desktop: 16px;
---font-size-body-large--mobile: 20px;
---font-size-body-large--desktop: 22px;

---font-size-product-title--mobile: 14px;
---font-size-product-title--desktop: 16px;
---font-size-product-title--major--mobile: 20px;
---font-size-product-title--major--desktop: 24px;

---font-size-price--major--mobile: 18px;
---font-size-price--major--desktop: 24px;

---font-size-label-radio--mobile: 18px;
---font-size-label-radio--desktop: 24px;

---font-size-button--mobile: 16px;
---font-size-button--desktop: 18px;
---font-size-button-large--mobile: 18px;
---font-size-button-large--desktop: 20px;

/*
Line Height
*/

---line-height-heading--mobile: 1.6;
---line-height-heading--desktop: 1.6;
---line-height-heading--mobile: 1.1;
---line-height-heading--desktop: 1.1;
---line-height-body--mobile: 1.6;
---line-height-body--desktop: 1.6;

/* ----- Letter Spacing ----- */

---letter-spacing-heading--mobile: 0;
---letter-spacing-heading--desktop: 0;

---letter-spacing-subheading--mobile: 0.05em;
---letter-spacing-subheading--desktop: 0.05em;

---letter-spacing-body--mobile: inherit;
---letter-spacing-body--desktop: inherit;

---letter-spacing-title--mobile: inherit;
---letter-spacing-title--desktop: inherit;
---letter-spacing-product-name--mobile: inherit;
---letter-spacing-product-name--desktop: inherit;

/*  ========== Colors ========== */

---color--white: {{ settings.color__white }};
---color--black: {{ settings.color__black }};

/*  ------------------------------
    Brand
    ------------------------------ */

---color--brand-1: {{ settings.color__brand__1 }}; /* Main Yellow */
---color--brand-2: {{ settings.color__brand__2 }}; /* Main Black */
---color--brand-3: {{ settings.color__brand__3 }}; /* Dark Gray */
---color--brand-4: {{ settings.color__brand__4 }}; /* Medium Gray */
---color--brand-5: {{ settings.color__brand__5 }}; /* Light Gray */
---color--brand-6: {{ settings.color__brand__6 }}; /* White */
---color--brand-7: {{ settings.color__brand__7 }}; /* Off Yellow */

---color--brand-1--rgb: {{ settings.color__brand__1.red }}, {{ settings.color__brand__1.green }}, {{ settings.color__brand__1.blue }};
---color--brand-2--rgb: {{ settings.color__brand__2.red }}, {{ settings.color__brand__2.green }}, {{ settings.color__brand__2.blue }};
---color--brand-3--rgb: {{ settings.color__brand__3.red }}, {{ settings.color__brand__3.green }}, {{ settings.color__brand__3.blue }};
---color--brand-4--rgb: {{ settings.color__brand__4.red }}, {{ settings.color__brand__4.green }}, {{ settings.color__brand__4.blue }};
---color--brand-5--rgb: {{ settings.color__brand__5.red }}, {{ settings.color__brand__5.green }}, {{ settings.color__brand__5.blue }};
---color--brand-6--rgb: {{ settings.color__brand__6.red }}, {{ settings.color__brand__6.green }}, {{ settings.color__brand__6.blue }};
---color--brand-7--rgb: {{ settings.color__brand__7.red }}, {{ settings.color__brand__7.green }}, {{ settings.color__brand__7.blue }};

/*  ------------------------------
    Color System
    ------------------------------ */

/* ----- Base Colors ----- */

---color--default: {{ settings.color__default }};
---color--default--light: {{ settings.color__default__light }};
---color--default--dark: {{ settings.color__default__dark }};

---color--highlight: {{ settings.color__highlight }};
---color--highlight--light: {{ settings.color__highlight__light }};
---color--highlight--dark: {{ settings.color__highlight__dark }};

---color--primary: {{ settings.color__primary }};
---color--primary--light: {{ settings.color__primary__light }};
---color--primary--dark: {{ settings.color__primary__dark }};

---color--secondary: {{ settings.color__secondary }};
---color--secondary--light: {{ settings.color__secondary__light }};
---color--secondary--dark: {{ settings.color__secondary__dark }};

---color--tertiary: {{ settings.color__tertiary }};
---color--tertiary--light: {{ settings.color__tertiary__light }};
---color--tertiary--dark: {{ settings.color__tertiary__dark }};

---color--disabled: {{ settings.color__disabled }};
---color--disabled--light: {{ settings.color__disabled__light }};
---color--disabled--dark: {{ settings.color__disabled__dark }};

---color--success: {{ settings.color__success }};
---color--success--light: {{ settings.color__success__light }};
---color--success--dark: {{ settings.color__success__dark }};

---color--warning: {{ settings.color__warning }};
---color--warning--light: {{ settings.color__warning__light }};
---color--warning--dark: {{ settings.color__warning__dark }};

---color--danger: {{ settings.color__danger }};
---color--danger--light: {{ settings.color__danger__light }};
---color--danger--dark: {{ settings.color__danger__dark }};

---color--info: {{ settings.color__info }};
---color--info--light: {{ settings.color__info__light }};
---color--info--dark: {{ settings.color__info__dark }};

---color--default--rgb: {{ settings.color__default.red }}, {{ settings.color__default.green }}, {{ settings.color__default.blue }};
---color--default--light--rgb: {{ settings.color__default__light.red }}, {{ settings.color__default__light.green }}, {{ settings.color__default__light.blue }};
---color--default--dark--rgb: {{ settings.color__default__dark.red }}, {{ settings.color__default__dark.green }}, {{ settings.color__default__dark.blue }};

---color--highlight--rgb: {{ settings.color__highlight.red }}, {{ settings.color__highlight.green }}, {{ settings.color__highlight.blue }};
---color--highlight--light--rgb: {{ settings.color__highlight__light.red }}, {{ settings.color__highlight__light.green }}, {{ settings.color__highlight__light.blue }};
---color--highlight--dark--rgb: {{ settings.color__highlight__dark.red }}, {{ settings.color__highlight__dark.green }}, {{ settings.color__highlight__dark.blue }};

---color--primary--rgb: {{ settings.color__primary.red }}, {{ settings.color__primary.green }}, {{ settings.color__primary.blue }};
---color--primary--light--rgb: {{ settings.color__primary__light.red }}, {{ settings.color__primary__light.green }}, {{ settings.color__primary__light.blue }};
---color--primary--dark--rgb: {{ settings.color__primary__dark.red }}, {{ settings.color__primary__dark.green }}, {{ settings.color__primary__dark.blue }};

---color--secondary--rgb: {{ settings.color__secondary.red }}, {{ settings.color__secondary.green }}, {{ settings.color__secondary.blue }};
---color--secondary--light--rgb: {{ settings.color__secondary__light.red }}, {{ settings.color__secondary__light.green }}, {{ settings.color__secondary__light.blue }};
---color--secondary--dark--rgb: {{ settings.color__secondary__dark.red }}, {{ settings.color__secondary__dark.green }}, {{ settings.color__secondary__dark.blue }};

---color--tertiary--rgb: {{ settings.color__tertiary.red }}, {{ settings.color__tertiary.green }}, {{ settings.color__tertiary.blue }};
---color--tertiary--light--rgb: {{ settings.color__tertiary__light.red }}, {{ settings.color__tertiary__light.green }}, {{ settings.color__tertiary__light.blue }};
---color--tertiary--dark--rgb: {{ settings.color__tertiary__dark.red }}, {{ settings.color__tertiary__dark.green }}, {{ settings.color__tertiary__dark.blue }};

---color--disabled--rgb: {{ settings.color__disabled.red }}, {{ settings.color__disabled.green }}, {{ settings.color__disabled.blue }};
---color--disabled--light--rgb: {{ settings.color__disabled__light.red }}, {{ settings.color__disabled__light.green }}, {{ settings.color__disabled__light.blue }};
---color--disabled--dark--rgb: {{ settings.color__disabled__dark.red }}, {{ settings.color__disabled__dark.green }}, {{ settings.color__disabled__dark.blue }};

---color--success--rgb: {{ settings.color__success.red }}, {{ settings.color__success.green }}, {{ settings.color__success.blue }};
---color--success--light--rgb: {{ settings.color__success__light.red }}, {{ settings.color__success__light.green }}, {{ settings.color__success__light.blue }};
---color--success--dark--rgb: {{ settings.color__success__dark.red }}, {{ settings.color__success__dark.green }}, {{ settings.color__success__dark.blue }};

---color--warning--rgb: {{ settings.color__warning.red }}, {{ settings.color__warning.green }}, {{ settings.color__warning.blue }};
---color--warning--light--rgb: {{ settings.color__warning__light.red }}, {{ settings.color__warning__light.green }}, {{ settings.color__warning__light.blue }};
---color--warning--dark--rgb: {{ settings.color__warning__dark.red }}, {{ settings.color__warning__dark.green }}, {{ settings.color__warning__dark.blue }};

---color--danger--rgb: {{ settings.color__danger.red }}, {{ settings.color__danger.green }}, {{ settings.color__danger.blue }};
---color--danger--light--rgb: {{ settings.color__danger__light.red }}, {{ settings.color__danger__light.green }}, {{ settings.color__danger__light.blue }};
---color--danger--dark--rgb: {{ settings.color__danger__dark.red }}, {{ settings.color__danger__dark.green }}, {{ settings.color__danger__dark.blue }};

---color--info--rgb: {{ settings.color__info.red }}, {{ settings.color__info.green }}, {{ settings.color__info.blue }};
---color--info--light--rgb: {{ settings.color__info__light.red }}, {{ settings.color__info__light.green }}, {{ settings.color__info__light.blue }};
---color--info--dark--rgb: {{ settings.color__info__dark.red }}, {{ settings.color__info__dark.green }}, {{ settings.color__info__dark.blue }};

/* ----- Background Colors ----- */

---background-color--primary: {{ settings.background_color__primary }};
---background-color--secondary: {{ settings.background_color__secondary }};
---background-color--tertiary: {{ settings.background_color__tertiary }};

---background-color--default: {{ settings.background_color__default }};
---background-color--disabled: {{ settings.background_color__disabled }};
---background-color--success: {{ settings.background_color__success }};
---background-color--warning: {{ settings.background_color__warning }};
---background-color--danger: {{ settings.background_color__danger }};
---background-color--info: {{ settings.background_color__info }};

---background-color--highlight--rgb: {{ settings.background_color__highlight.red }}, {{ settings.background_color__highlight.green }}, {{ settings.background_color__highlight.blue }};
---background-color--primary--rgb: {{ settings.background_color__primary.red }}, {{ settings.background_color__primary.green }}, {{ settings.background_color__primary.blue }};
---background-color--secondary--rgb: {{ settings.background_color__secondary.red }}, {{ settings.background_color__secondary.green }}, {{ settings.background_color__secondary.blue }};
---background-color--tertiary--rgb: {{ settings.background_color__tertiary.red }}, {{ settings.background_color__tertiary.green }}, {{ settings.background_color__tertiary.blue }};

---background-color--default--rgb: {{ settings.background_color__default.red }}, {{ settings.background_color__default.green }}, {{ settings.background_color__default.blue }};
---background-color--disabled--rgb: {{ settings.background_color__disabled.red }}, {{ settings.background_color__disabled.green }}, {{ settings.background_color__disabled.blue }};
---background-color--success--rgb: {{ settings.background_color__success.red }}, {{ settings.background_color__success.green }}, {{ settings.background_color__success.blue }};
---background-color--warning--rgb: {{ settings.background_color__warning.red }}, {{ settings.background_color__warning.green }}, {{ settings.background_color__warning.blue }};
---background-color--danger--rgb: {{ settings.background_color__danger.red }}, {{ settings.background_color__danger.green }}, {{ settings.background_color__danger.blue }};
---background-color--info--rgb: {{ settings.background_color__info.red }}, {{ settings.background_color__info.green }}, {{ settings.background_color__info.blue }};


/*  ------------------------------
    Content Colors
    ------------------------------ */

---background-color--body: {{ settings.background_color_body }};

---background-color--content-1: {{ settings.background_color_content_1 }};
---background-color--content-2: {{ settings.background_color_content_2 }};
---background-color--content-3: {{ settings.background_color_content_3 }};

---background-color--content-1--rgb: {{ settings.background_color_content_1.red }}, {{ settings.background_color_content_1.green }}, {{ settings.background_color_content_1.blue }};
---background-color--content-2--rgb: {{ settings.background_color_content_2.red }}, {{ settings.background_color_content_2.green }}, {{ settings.background_color_content_2.blue }};
---background-color--content-3--rgb: {{ settings.background_color_content_3.red }}, {{ settings.background_color_content_3.green }}, {{ settings.background_color_content_3.blue }};

---background-color--content-reversed-1: {{ settings.background_color_content_reversed_1 }};
---background-color--content-reversed-2: {{ settings.background_color_content_reversed_2 }};
---background-color--content-reversed-3: {{ settings.background_color_content_reversed_3 }};

---background-color--content-reversed-1--rgb: {{ settings.background_color_content_reversed_1.red }}, {{ settings.background_color_content_reversed_1.green }}, {{ settings.background_color_content_reversed_1.blue }};
---background-color--content-reversed-2--rgb: {{ settings.background_color_content_reversed_2.red }}, {{ settings.background_color_content_reversed_2.green }}, {{ settings.background_color_content_reversed_2.blue }};
---background-color--content-reversed-3--rgb: {{ settings.background_color_content_reversed_3.red }}, {{ settings.background_color_content_reversed_3.green }}, {{ settings.background_color_content_reversed_3.blue }};



---color-text: {{ settings.color_text }};
---color-text--dark: {{ settings.color_text_dark }};
---color-text--light: {{ settings.color_body_light }};
---color-text--reversed: {{ settings.color_body_reversed }};
---color-text--reversed-strong: {{ settings.color_body_reversed_strong }};

---color-text--rgb: {{ settings.color_text.red }}, {{ settings.color_text.green }}, {{ settings.color_text.blue }};
---color-text--dark--rgb: {{ settings.color_text_dark.red }}, {{ settings.color_text_dark.green }}, {{ settings.color_text_dark.blue }};
---color-text--light--rgb: {{ settings.color_body_light.red }}, {{ settings.color_body_light.green }}, {{ settings.color_body_light.blue }};
---color-text--reversed--rgb: {{ settings.color_body_reversed.red }}, {{ settings.color_body_reversed.green }}, {{ settings.color_body_reversed.blue }};
---color-text--reversed-strong--rgb: {{ settings.color_body_reversed_strong.red }}, {{ settings.color_body_reversed_strong.green }}, {{ settings.color_body_reversed_strong.blue }};

---color-heading-1: var(---color--primary);
---color-heading-2: var(---color--secondary);
---color-heading-3: var(---color--tertiary);

---color-heading-1--rgb: var(---color--primary--rgb);
---color-heading-2--rgb: var(---color--secondary--rgb);
---color-heading-3--rgb: var(---color--tertiary--rgb);

---color-products-title: {{ settings.color_product_title }};

---color-price: {{ settings.color_prices }};
---color-price--sale: {{ settings.color_prices_sale }};
---color-price--compare: {{ settings.color_prices_compare }};

---color-link: {{ settings.color_link }};
---color-link--rgb: {{ settings.color_link.red }}, {{ settings.color_link.green }}, {{ settings.color_link.blue }};
---color-link--hover: {{ settings.color_link | lighten: 10 }};

---color-reviews: {{ settings.color_reviews }};
---color-reviews--rgb: {{ settings.color_reviews.red }}, {{ settings.color_reviews.green }}, {{ settings.color_reviews.blue }};

---banner-overlay-background: {{ settings.overlay_color }};


/*  ========== Components ========== */

---border-radius--general: 8px;
---border-radius--inputs: 0px;

---color-line: {{ settings.color_line }};
---color-line--light: {{ settings.color_line_light }};
---color-line--dark: {{ settings.color_line_dark }};

---color-line--rgb: {{ settings.color_line.red }}, {{ settings.color_line.green }}, {{ settings.color_line.blue }};
---color-line--light--rgb: {{ settings.color_line_light.red }}, {{ settings.color_line_light.green }}, {{ settings.color_line_light.blue }};
---color-line--dark--rgb: {{ settings.color_line_dark.red }}, {{ settings.color_line_dark.green }}, {{ settings.color_line_dark.blue }};

---color-icon: var(---color--primary);

---stamp-width--mobile: 60px;
---stamp-width--desktop: 150px;

/*  ------------------------------
    Form Elements
    ------------------------------ */

---button-color: #F5FBFD;
---button-radius: var(--button-border-radius);
---button-min-width: {{ settings.button_min_width | append: 'px' }};
---button-letter-spacing: 0.2em;

---input-color: var(---color-text--dark);
---input-background: var(---background-color--content-1);
---input-border-radius: {{ settings.input_border_radius | append: 'px' }};
---input-min-width: {{ settings.input_min_width | append: 'px' }};
---input-letter-spacing: {{ settings.input_letter_spacing | append: 'em' }};

/*  ------------------------------
    Effects
    ------------------------------ */

// ---shadow--banner-title: 0px 4px 17px rgba(0, 0, 0, 0.25);

---shadow--modal: 0px 4px 30px rgba(0, 0, 0, 0.1);
---shadow--section: 0px 0px 7px rgba(0, 0, 0, 0.1);
---shadow--section-inner: inset 0px 0px 10px rgba(0, 0, 0, 0.1);

---transition-duration--general: 0.25s;


/*  ========== Vectors ========== */

// ---logo-header: url('{{ "logo_header.svg" | asset_url }}');
// ---logo-footer: url('{{ "logo_footer.svg" | asset_url }}');


/*  ========== Images ========== */

---pattern-wood: url('{{ "wood-texture--tiled.jpg" | file_url }}');
// ---pattern-blue-size: 150px;

// ---illustration-bio: url('{{ "illustration-bio.png" | file_url }}');
---icon--chevron: url('{{ "icon-chevron.png" | asset_url }}');

---cart-overlay-background: url('{{ settings.quiz_cart_background | img_url: "master" }}');

/*  ========== Animations ========== */

---transition-hover: cubic-bezier(0.4, 0, 0.22, 1);

    /*  ========== THEME OVERRIDES  ========== */ 

/* Form related */
--form-input-field-height: 48px;
--form-input-gap: 32px;
--form-submit-margin: 24px;

/*  ========== THEME OVERRIDES  ========== */

--button-height: 50px;
--button-height--large: 60px;

/* Typography */ 

/* --heading-font-family: {{ settings.quiz_heading_font.family }}, {{ settings.quiz_heading_font.fallback_families }}; */
--heading-font-family: 'cubano', sans-serif;
--heading-font-weight: 400;
--heading-font-style: {{ settings.quiz_heading_font.style }};
/* --heading-text-transform: {{ settings.heading_text_transform }}; */
--text-font-family: {{ settings.quiz_text_font.family }}, {{ settings.quiz_text_font.fallback_families }};
--text-font-weight: {{ settings.quiz_text_font.weight }};
--text-font-style: {{ settings.quiz_text_font.style }};
--text-font-bold-weight: {{ quiz_text_font_bold.weight | default: '700' }};

--heading-xxsmall-font-size: {% if settings.heading_font_size == 'small' %}10px{% else %}11px{% endif %};
--heading-xsmall-font-size: {% if settings.heading_font_size == 'small' %}10px{% else %}11px{% endif %};
--heading-small-font-size: {% if settings.heading_font_size == 'small' %}11px{% elsif settings.heading_font_size == 'medium' %}12px{% else %}13px{% endif %};
--heading-large-font-size: {% if settings.heading_font_size == 'small' %}32px{% elsif settings.heading_font_size == 'medium' %}36px{% else %}40px{% endif %};
--heading-h1-font-size: {% if settings.heading_font_size == 'small' %}32px{% elsif settings.heading_font_size == 'medium' %}36px{% else %}40px{% endif %};
--heading-h2-font-size: {% if settings.heading_font_size == 'small' %}28px{% elsif settings.heading_font_size == 'medium' %}30px{% else %}32px{% endif %};
--heading-h3-font-size: {% if settings.heading_font_size == 'small' %}26px{% elsif settings.heading_font_size == 'medium' %}26px{% else %}28px{% endif %};
--heading-h4-font-size: {% if settings.heading_font_size == 'small' %}22px{% elsif settings.heading_font_size == 'medium' %}24px{% else %}26px{% endif %};
--heading-h5-font-size: {% if settings.heading_font_size == 'small' %}18px{% elsif settings.heading_font_size == 'medium' %}20px{% else %}22px{% endif %};
--heading-h6-font-size: {% if settings.heading_font_size == 'small' %}16px{% elsif settings.heading_font_size == 'medium' %}16px{% else %}18px{% endif %};

--text-font-weight: var(---font-weight-body);
--text-font-style: var(---font-style-body);
--text-font-bold-weight: var(---font-weight-body--bold);

/* --border-color: var(---color-line--rgb); */
/* --border-color-darker: var(---color-line--dark--rgb); */

--product-low-stock-text-color: var(---color--warning--rgb);
--product-no-stock-text-color: var(---color--danger--rgb);
--product-in-stock-text-color: var(---color--success--rgb);

--vertical-breather-double: {% if settings.vertical_spacing == 'small' %}48px{% elsif settings.vertical_spacing == 'medium' %}72px{% else %}96px{% endif %};

}
 
@media screen and (min-width: 1200px) {

    :root {
        
        --vertical-breather-double: {% if settings.vertical_spacing == 'small' %}96px{% elsif settings.vertical_spacing == 'medium' %}128px{% else %}160px{% endif %};
        
        --button-height: 56px;
        --button-height--large: 68px;

    }

}

@media screen and (min-width: 1600px) {

    :root {
        --vertical-breather-double: {% if settings.vertical_spacing == 'small' %}96px{% elsif settings.vertical_spacing == 'medium' %}128px{% else %}180px{% endif %};
    }
 
}


.quiz {

    /* Quiz */

    ---font-size-h0--mobile: 48px;
    ---font-size-h0--desktop: 72px;
    ---font-size-h1--mobile: 44px;
    ---font-size-h1--desktop: 56px;
    ---font-size-h2--mobile: 32px;
    ---font-size-h2--desktop: 40px;
    ---font-size-h3--mobile: 28px;
    ---font-size-h3--desktop: 32px;
    ---font-size-h4--mobile: 24px;
    ---font-size-h4--desktop: 28px;
    ---font-size-h5--mobile: 14px;
    ---font-size-h5--desktop: 16px;
    ---font-size-h6--mobile: 12px;
    ---font-size-h6--desktop: 14px;
    
}

/*  ========== @Font-Face Rules ========== */

  /* Typography (heading) */
  {{ settings.quiz_heading_font | font_face: font_display: 'swap' }}

  {%- if quiz_heading_font_italic -%}
    {{ quiz_heading_font_italic | font_face: font_display: 'swap' }}
  {%- endif -%}

  /* Typography (body) */
  {{ settings.quiz_text_font | font_face: font_display: 'swap' }}

  {%- if quiz_text_font_italic -%}
    {{ quiz_text_font_italic | font_face: font_display: 'swap' }}
  {%- endif -%}

  {%- if quiz_text_font_bold -%}
    {{ quiz_text_font_bold | font_face: font_display: 'swap' }}
  {%- endif -%}

  {%- if quiz_text_font_bold_italic -%}
    {{ quiz_text_font_bold_italic | font_face: font_display: 'swap' }}
  {%- endif -%}

{% endstyle %}
