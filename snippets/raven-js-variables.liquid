{% liquid

  assign rid_dog_1_name = "ryvlujU"
  assign rid_dog_1_sex = "-HBpk9w"
  assign rid_dog_1_age_in_months = "O9VV3EU"
  assign rid_dog_1_breed = "115pDDE"
  assign rid_dog_1_neutered = "WboOZbA"
  assign rid_dog_1_weight_profile = "efcgyIk"
  assign rid_dog_1_weight = "iYURqbk"
  assign rid_dog_1_ideal_weight = "o-5P8YU"
  assign rid_dog_1_activity_level = "8E21LS8"
  assign rid_dog_1_has_health_issue = "pDOIf2E"
  assign rid_dog_1_prescription_diet = "AtlElpg"

  assign rid_dog_2_name = "5JGITAE"
  assign rid_dog_2_sex = "1p4s898"
  assign rid_dog_2_age_in_months = "V2Je9a4"
  assign rid_dog_2_breed = "My8sS-8"
  assign rid_dog_2_neutered = "qkkoRSY"
  assign rid_dog_2_weight_profile = "2WUkN7A"
  assign rid_dog_2_weight = "LQlCf0o"
  assign rid_dog_2_ideal_weight = "rJVmOi8"
  assign rid_dog_2_activity_level = "u5ZraC0"
  assign rid_dog_2_has_health_issue = "oHYH2bM"
  assign rid_dog_2_prescription_diet = "nPi7UBQ"

  assign rid_dog_3_name = "MNP_mZU"
  assign rid_dog_3_sex = "JClcTho"
  assign rid_dog_3_age_in_months = "Obkvb4k"
  assign rid_dog_3_breed = "Ets2NhU"
  assign rid_dog_3_neutered = "opOoGMc"
  assign rid_dog_3_weight_profile = "Gir6T2c"
  assign rid_dog_3_weight = "434A2Hg"
  assign rid_dog_3_ideal_weight = "8xOmOsg"
  assign rid_dog_3_activity_level = "Sm5So9s"
  assign rid_dog_3_has_health_issue = "3L-eXqc"
  assign rid_dog_3_prescription_diet = "SDur_3E"

  assign rid_dog_4_name = "UDS0TGM"
  assign rid_dog_4_sex = "4L-8Eu8"
  assign rid_dog_4_age_in_months = "AIwUFWQ"
  assign rid_dog_4_breed = "FDCmw6M"
  assign rid_dog_4_neutered = "pW8_9Vc"
  assign rid_dog_4_weight_profile = "VcQ_FLw"
  assign rid_dog_4_weight = "zFM9lAg"
  assign rid_dog_4_ideal_weight = "ed5mdl4"
  assign rid_dog_4_activity_level = "3KdPcLY"
  assign rid_dog_4_has_health_issue = "Yr-4Ogw"
  assign rid_dog_4_prescription_diet = "lR7wgj8"

  assign rid_dog_5_name = "4nVSDIw"
  assign rid_dog_5_sex = "3H7v7BI"
  assign rid_dog_5_age_in_months = "W5vpSxI"
  assign rid_dog_5_breed = "BS8R9h8"
  assign rid_dog_5_neutered = "9RBqcQ4"
  assign rid_dog_5_weight_profile = "chv9c18"
  assign rid_dog_5_weight = "fLTMSYQ"
  assign rid_dog_5_ideal_weight = "8VDp6m4"
  assign rid_dog_5_activity_level = "Bi94uRE"
  assign rid_dog_5_has_health_issue = "xdqLr94"
  assign rid_dog_5_prescription_diet = "JbJIf1E"

  assign rid_dog_6_name = "w2ZxObs"
  assign rid_dog_6_sex = "KmacxWE"
  assign rid_dog_6_age_in_months = "UY3b8Rk"
  assign rid_dog_6_breed = "5ITMrfI"
  assign rid_dog_6_neutered = "QchmTnA"
  assign rid_dog_6_weight_profile = "-lAobeE"
  assign rid_dog_6_weight = "yGl0vjk"
  assign rid_dog_6_ideal_weight = "SCKtuXs"
  assign rid_dog_6_activity_level = "socMAYM"
  assign rid_dog_6_has_health_issue = "o3joacs"
  assign rid_dog_6_prescription_diet = "YQRnM6s"

  assign rid_dog_7_name = "_lHhDjs"
  assign rid_dog_7_sex = "Cm6ymrc"
  assign rid_dog_7_age_in_months = "WW6mPf4"
  assign rid_dog_7_breed = "IhgeSQY"
  assign rid_dog_7_neutered = "ikkxBnk"
  assign rid_dog_7_weight_profile = "ArfkTzU"
  assign rid_dog_7_weight = "N-M02LU"
  assign rid_dog_7_ideal_weight = "nt6ED40"
  assign rid_dog_7_activity_level = "NdNj-t4"
  assign rid_dog_7_has_health_issue = "zj3HJD8"
  assign rid_dog_7_prescription_diet = "z8bX4Us"

  assign rid_dog_8_name = "XtUxIW0"
  assign rid_dog_8_sex = "TOu48iw"
  assign rid_dog_8_age_in_months = "ezNKW3s"
  assign rid_dog_8_breed = "dn_pB5A"
  assign rid_dog_8_neutered = "x2um_ks"
  assign rid_dog_8_weight_profile = "cIazOuE"
  assign rid_dog_8_weight = "p0VTJ3U"
  assign rid_dog_8_ideal_weight = "s_wYX1I"
  assign rid_dog_8_activity_level = "NgyhOD4"
  assign rid_dog_8_has_health_issue = "bLzX4XA"
  assign rid_dog_8_prescription_diet = "JKzCCYk"

  assign rid_dog_9_name = "SdiHTEI"
  assign rid_dog_9_sex = "qeMSirg"
  assign rid_dog_9_age_in_months = "x0GW4Vo"
  assign rid_dog_9_breed = "-n-uu_A"
  assign rid_dog_9_neutered = "Gw8N8j0"
  assign rid_dog_9_weight_profile = "6_PiYTM"
  assign rid_dog_9_weight = "rjLzBOQ"
  assign rid_dog_9_ideal_weight = "QQt9P1w"
  assign rid_dog_9_activity_level = "08j-bFw"
  assign rid_dog_9_has_health_issue = "vLU5Cms"
  assign rid_dog_9_prescription_diet = "Odg38JM"

  assign rid_dog_10_name = "4Xn5Uvg"
  assign rid_dog_10_sex = "qkg-zOo"
  assign rid_dog_10_age_in_months = "pImxjUw"
  assign rid_dog_10_breed = "mDinMv0"
  assign rid_dog_10_neutered = "XH9frkk"
  assign rid_dog_10_weight_profile = "il6R0_w"
  assign rid_dog_10_weight = "yo26S_U"
  assign rid_dog_10_ideal_weight = "uhtkCm0"
  assign rid_dog_10_activity_level = "H_ZjFXA"
  assign rid_dog_10_has_health_issue = "xkjtwII"
  assign rid_dog_10_prescription_diet = "zaV18jI"

  assign rid_customer_veterinarian = "cUyl0eI",
  assign rid_customer_veterinarian_rec = "ACiFQfc"
  
  assign unlisted_vet_hospital_name = "13jOa88"
  assign unlisted_vet_vet_name = "R-BUn2U"
  assign unlisted_vet_contact_details = "Azs29oI"

%} 

<script>

var FieldsRaven = {
  {% comment %} secret: {{ shop.metafields.fields_raven.api_secret }}, {% endcomment %}
  endpoints: {
    create_single: '/apps/raven/create_metafield',
    create_multiple: '/apps/raven/create_multiple_metafields',
  },
  definitions: {
    dog_1_name: {% render 'raven-mac-gen', resource_id: nil, raven_id: rid_dog_1_name %},
    dog_1_sex: {% render 'raven-mac-gen', resource_id: nil, raven_id: rid_dog_1_sex %},
    dog_1_age_in_months: {% render 'raven-mac-gen', resource_id: nil, raven_id: rid_dog_1_age_in_months %},
    dog_1_breed: {% render 'raven-mac-gen', resource_id: nil, raven_id: rid_dog_1_breed %},
    dog_1_neutered: {% render 'raven-mac-gen', resource_id: nil, raven_id: rid_dog_1_neutered %},
    dog_1_weight_profile: {% render 'raven-mac-gen', resource_id: nil, raven_id: rid_dog_1_weight_profile %},
    dog_1_weight: {% render 'raven-mac-gen', resource_id: nil, raven_id: rid_dog_1_weight %},
    dog_1_ideal_weight: {% render 'raven-mac-gen', resource_id: nil, raven_id: rid_dog_1_ideal_weight %},
    dog_1_activity_level: {% render 'raven-mac-gen', resource_id: nil, raven_id: rid_dog_1_activity_level %},
    dog_1_has_health_issue: {% render 'raven-mac-gen', resource_id: nil, raven_id: rid_dog_1_has_health_issue %},
    dog_1_prescription_diet: {% render 'raven-mac-gen', resource_id: nil, raven_id: rid_dog_1_prescription_diet %},
    dog_2_name: {% render 'raven-mac-gen', resource_id: nil, raven_id: rid_dog_2_name %},
    dog_2_sex: {% render 'raven-mac-gen', resource_id: nil, raven_id: rid_dog_2_sex %},
    dog_2_age_in_months: {% render 'raven-mac-gen', resource_id: nil, raven_id: rid_dog_2_age_in_months %},
    dog_2_breed: {% render 'raven-mac-gen', resource_id: nil, raven_id: rid_dog_2_breed %},
    dog_2_neutered: {% render 'raven-mac-gen', resource_id: nil, raven_id: rid_dog_2_neutered %},
    dog_2_weight_profile: {% render 'raven-mac-gen', resource_id: nil, raven_id: rid_dog_2_weight_profile %},
    dog_2_weight: {% render 'raven-mac-gen', resource_id: nil, raven_id: rid_dog_2_weight %},
    dog_2_ideal_weight: {% render 'raven-mac-gen', resource_id: nil, raven_id: rid_dog_2_ideal_weight %},
    dog_2_activity_level: {% render 'raven-mac-gen', resource_id: nil, raven_id: rid_dog_2_activity_level %},
    dog_2_has_health_issue: {% render 'raven-mac-gen', resource_id: nil, raven_id: rid_dog_2_has_health_issue %},
    dog_2_prescription_diet: {% render 'raven-mac-gen', resource_id: nil, raven_id: rid_dog_2_prescription_diet %},
    dog_3_name: {% render 'raven-mac-gen', resource_id: nil, raven_id: rid_dog_3_name %},
    dog_3_sex: {% render 'raven-mac-gen', resource_id: nil, raven_id: rid_dog_3_sex %},
    dog_3_age_in_months: {% render 'raven-mac-gen', resource_id: nil, raven_id: rid_dog_3_age_in_months %},
    dog_3_breed: {% render 'raven-mac-gen', resource_id: nil, raven_id: rid_dog_3_breed %},
    dog_3_neutered: {% render 'raven-mac-gen', resource_id: nil, raven_id: rid_dog_3_neutered %},
    dog_3_weight_profile: {% render 'raven-mac-gen', resource_id: nil, raven_id: rid_dog_3_weight_profile %},
    dog_3_weight: {% render 'raven-mac-gen', resource_id: nil, raven_id: rid_dog_3_weight %},
    dog_3_ideal_weight: {% render 'raven-mac-gen', resource_id: nil, raven_id: rid_dog_3_ideal_weight %},
    dog_3_activity_level: {% render 'raven-mac-gen', resource_id: nil, raven_id: rid_dog_3_activity_level %},
    dog_3_has_health_issue: {% render 'raven-mac-gen', resource_id: nil, raven_id: rid_dog_3_has_health_issue %},
    dog_3_prescription_diet: {% render 'raven-mac-gen', resource_id: nil, raven_id: rid_dog_3_prescription_diet %},
    dog_4_name: {% render 'raven-mac-gen', resource_id: nil, raven_id: rid_dog_4_name %},
    dog_4_sex: {% render 'raven-mac-gen', resource_id: nil, raven_id: rid_dog_4_sex %},
    dog_4_age_in_months: {% render 'raven-mac-gen', resource_id: nil, raven_id: rid_dog_4_age_in_months %},
    dog_4_breed: {% render 'raven-mac-gen', resource_id: nil, raven_id: rid_dog_4_breed %},
    dog_4_neutered: {% render 'raven-mac-gen', resource_id: nil, raven_id: rid_dog_4_neutered %},
    dog_4_weight_profile: {% render 'raven-mac-gen', resource_id: nil, raven_id: rid_dog_4_weight_profile %},
    dog_4_weight: {% render 'raven-mac-gen', resource_id: nil, raven_id: rid_dog_4_weight %},
    dog_4_ideal_weight: {% render 'raven-mac-gen', resource_id: nil, raven_id: rid_dog_4_ideal_weight %},
    dog_4_activity_level: {% render 'raven-mac-gen', resource_id: nil, raven_id: rid_dog_4_activity_level %},
    dog_4_has_health_issue: {% render 'raven-mac-gen', resource_id: nil, raven_id: rid_dog_4_has_health_issue %},
    dog_4_prescription_diet: {% render 'raven-mac-gen', resource_id: nil, raven_id: rid_dog_4_prescription_diet %},
    dog_5_name: {% render 'raven-mac-gen', resource_id: nil, raven_id: rid_dog_5_name %},
    dog_5_sex: {% render 'raven-mac-gen', resource_id: nil, raven_id: rid_dog_5_sex %},
    dog_5_age_in_months: {% render 'raven-mac-gen', resource_id: nil, raven_id: rid_dog_5_age_in_months %},
    dog_5_breed: {% render 'raven-mac-gen', resource_id: nil, raven_id: rid_dog_5_breed %},
    dog_5_neutered: {% render 'raven-mac-gen', resource_id: nil, raven_id: rid_dog_5_neutered %},
    dog_5_weight_profile: {% render 'raven-mac-gen', resource_id: nil, raven_id: rid_dog_5_weight_profile %},
    dog_5_weight: {% render 'raven-mac-gen', resource_id: nil, raven_id: rid_dog_5_weight %},
    dog_5_ideal_weight: {% render 'raven-mac-gen', resource_id: nil, raven_id: rid_dog_5_ideal_weight %},
    dog_5_activity_level: {% render 'raven-mac-gen', resource_id: nil, raven_id: rid_dog_5_activity_level %},
    dog_5_has_health_issue: {% render 'raven-mac-gen', resource_id: nil, raven_id: rid_dog_5_has_health_issue %},
    dog_5_prescription_diet: {% render 'raven-mac-gen', resource_id: nil, raven_id: rid_dog_5_prescription_diet %},
    dog_6_name: {% render 'raven-mac-gen', resource_id: nil, raven_id: rid_dog_6_name %},
    dog_6_sex: {% render 'raven-mac-gen', resource_id: nil, raven_id: rid_dog_6_sex %},
    dog_6_age_in_months: {% render 'raven-mac-gen', resource_id: nil, raven_id: rid_dog_6_age_in_months %},
    dog_6_breed: {% render 'raven-mac-gen', resource_id: nil, raven_id: rid_dog_6_breed %},
    dog_6_neutered: {% render 'raven-mac-gen', resource_id: nil, raven_id: rid_dog_6_neutered %},
    dog_6_weight_profile: {% render 'raven-mac-gen', resource_id: nil, raven_id: rid_dog_6_weight_profile %},
    dog_6_weight: {% render 'raven-mac-gen', resource_id: nil, raven_id: rid_dog_6_weight %},
    dog_6_ideal_weight: {% render 'raven-mac-gen', resource_id: nil, raven_id: rid_dog_6_ideal_weight %},
    dog_6_activity_level: {% render 'raven-mac-gen', resource_id: nil, raven_id: rid_dog_6_activity_level %},
    dog_6_has_health_issue: {% render 'raven-mac-gen', resource_id: nil, raven_id: rid_dog_6_has_health_issue %},
    dog_6_prescription_diet: {% render 'raven-mac-gen', resource_id: nil, raven_id: rid_dog_6_prescription_diet %},
    dog_7_name: {% render 'raven-mac-gen', resource_id: nil, raven_id: rid_dog_7_name %},
    dog_7_sex: {% render 'raven-mac-gen', resource_id: nil, raven_id: rid_dog_7_sex %},
    dog_7_age_in_months: {% render 'raven-mac-gen', resource_id: nil, raven_id: rid_dog_7_age_in_months %},
    dog_7_breed: {% render 'raven-mac-gen', resource_id: nil, raven_id: rid_dog_7_breed %},
    dog_7_neutered: {% render 'raven-mac-gen', resource_id: nil, raven_id: rid_dog_7_neutered %},
    dog_7_weight_profile: {% render 'raven-mac-gen', resource_id: nil, raven_id: rid_dog_7_weight_profile %},
    dog_7_weight: {% render 'raven-mac-gen', resource_id: nil, raven_id: rid_dog_7_weight %},
    dog_7_ideal_weight: {% render 'raven-mac-gen', resource_id: nil, raven_id: rid_dog_7_ideal_weight %},
    dog_7_activity_level: {% render 'raven-mac-gen', resource_id: nil, raven_id: rid_dog_7_activity_level %},
    dog_7_has_health_issue: {% render 'raven-mac-gen', resource_id: nil, raven_id: rid_dog_7_has_health_issue %},
    dog_7_prescription_diet: {% render 'raven-mac-gen', resource_id: nil, raven_id: rid_dog_7_prescription_diet %},
    dog_8_name: {% render 'raven-mac-gen', resource_id: nil, raven_id: rid_dog_8_name %},
    dog_8_sex: {% render 'raven-mac-gen', resource_id: nil, raven_id: rid_dog_8_sex %},
    dog_8_age_in_months: {% render 'raven-mac-gen', resource_id: nil, raven_id: rid_dog_8_age_in_months %},
    dog_8_breed: {% render 'raven-mac-gen', resource_id: nil, raven_id: rid_dog_8_breed %},
    dog_8_neutered: {% render 'raven-mac-gen', resource_id: nil, raven_id: rid_dog_8_neutered %},
    dog_8_weight_profile: {% render 'raven-mac-gen', resource_id: nil, raven_id: rid_dog_8_weight_profile %},
    dog_8_weight: {% render 'raven-mac-gen', resource_id: nil, raven_id: rid_dog_8_weight %},
    dog_8_ideal_weight: {% render 'raven-mac-gen', resource_id: nil, raven_id: rid_dog_8_ideal_weight %},
    dog_8_activity_level: {% render 'raven-mac-gen', resource_id: nil, raven_id: rid_dog_8_activity_level %},
    dog_8_has_health_issue: {% render 'raven-mac-gen', resource_id: nil, raven_id: rid_dog_8_has_health_issue %},
    dog_8_prescription_diet: {% render 'raven-mac-gen', resource_id: nil, raven_id: rid_dog_8_prescription_diet %},
    dog_9_name: {% render 'raven-mac-gen', resource_id: nil, raven_id: rid_dog_9_name %},
    dog_9_sex: {% render 'raven-mac-gen', resource_id: nil, raven_id: rid_dog_9_sex %},
    dog_9_age_in_months: {% render 'raven-mac-gen', resource_id: nil, raven_id: rid_dog_9_age_in_months %},
    dog_9_breed: {% render 'raven-mac-gen', resource_id: nil, raven_id: rid_dog_9_breed %},
    dog_9_neutered: {% render 'raven-mac-gen', resource_id: nil, raven_id: rid_dog_9_neutered %},
    dog_9_weight_profile: {% render 'raven-mac-gen', resource_id: nil, raven_id: rid_dog_9_weight_profile %},
    dog_9_weight: {% render 'raven-mac-gen', resource_id: nil, raven_id: rid_dog_9_weight %},
    dog_9_ideal_weight: {% render 'raven-mac-gen', resource_id: nil, raven_id: rid_dog_9_ideal_weight %},
    dog_9_activity_level: {% render 'raven-mac-gen', resource_id: nil, raven_id: rid_dog_9_activity_level %},
    dog_9_has_health_issue: {% render 'raven-mac-gen', resource_id: nil, raven_id: rid_dog_9_has_health_issue %},
    dog_9_prescription_diet: {% render 'raven-mac-gen', resource_id: nil, raven_id: rid_dog_9_prescription_diet %},
    dog_10_name: {% render 'raven-mac-gen', resource_id: nil, raven_id: rid_dog_10_name %},
    dog_10_sex: {% render 'raven-mac-gen', resource_id: nil, raven_id: rid_dog_10_sex %},
    dog_10_age_in_months: {% render 'raven-mac-gen', resource_id: nil, raven_id: rid_dog_10_age_in_months %},
    dog_10_breed: {% render 'raven-mac-gen', resource_id: nil, raven_id: rid_dog_10_breed %},
    dog_10_neutered: {% render 'raven-mac-gen', resource_id: nil, raven_id: rid_dog_10_neutered %},
    dog_10_weight_profile: {% render 'raven-mac-gen', resource_id: nil, raven_id: rid_dog_10_weight_profile %},
    dog_10_weight: {% render 'raven-mac-gen', resource_id: nil, raven_id: rid_dog_10_weight %},
    dog_10_ideal_weight: {% render 'raven-mac-gen', resource_id: nil, raven_id: rid_dog_10_ideal_weight %},
    dog_10_activity_level: {% render 'raven-mac-gen', resource_id: nil, raven_id: rid_dog_10_activity_level %},
    dog_10_has_health_issue: {% render 'raven-mac-gen', resource_id: nil, raven_id: rid_dog_10_has_health_issue %},
    dog_10_prescription_diet: {% render 'raven-mac-gen', resource_id: nil, raven_id: rid_dog_10_prescription_diet %},
    
    customer_veterinarian: {% render 'raven-mac-gen', resource_id: nil, raven_id: rid_customer_veterinarian %},
    customer_veterinarian_rec: {% render 'raven-mac-gen', resource_id: nil, raven_id: rid_customer_veterinarian_rec %},

    unlisted_vet_hospital_name: {% render 'raven-mac-gen', resource_id: nil, raven_id: unlisted_vet_hospital_name %},
    unlisted_vet_vet_name: {% render 'raven-mac-gen', resource_id: nil, raven_id: unlisted_vet_vet_name %},
    unlisted_vet_contact_details: {% render 'raven-mac-gen', resource_id: nil, raven_id: unlisted_vet_contact_details %},

  }
};

</script>

