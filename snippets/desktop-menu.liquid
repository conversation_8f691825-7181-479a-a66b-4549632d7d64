<desktop-navigation>
  <ul class="header__linklist list--unstyled {% unless bottom_navigation %}hidden-pocket hidden-lap{% endunless %}" role="list">
    {%- comment %}<locksmith:d27b>{% endcomment -%}
      {%- assign locksmith_ed1f_forloop__size = 0 %}{%- for link in menu.links -%}{% capture var %}{% render 'locksmith-variables', scope: 'subject', subject: link, subject_parent: menu, variable: 'transparent' %}{% endcapture %}{% if var == 'true' %}{% assign locksmith_ed1f_forloop__size = locksmith_ed1f_forloop__size | plus: 1 %}{% endif %}{% endfor %}{% assign locksmith_ed1f_forloop__index = nil -%}
    {%- comment %}</locksmith:d27b>{% endcomment -%}
    {%- for link in menu.links -%}
      {%- comment %}<locksmith:3b56>{% endcomment -%}
        {%- capture var %}{% render 'locksmith-variables', scope: 'subject', subject: link, subject_parent: menu, variable: 'transparent' %}{% endcapture %}{% if var == "true" %}{% if locksmith_ed1f_forloop__index == nil %}{% assign locksmith_ed1f_forloop__index = 1 %}{% assign locksmith_ed1f_forloop__index0 = 0 %}{% else %}{% assign locksmith_ed1f_forloop__index = locksmith_ed1f_forloop__index | plus: 1 %}{% assign locksmith_ed1f_forloop__index0 = locksmith_ed1f_forloop__index0 | plus: 1 %}{% endif %}{% if locksmith_ed1f_forloop__index == 1 %}{% assign locksmith_ed1f_forloop__first = true %}{% else %}{% assign locksmith_ed1f_forloop__first = false %}{% endif %}{% if locksmith_ed1f_forloop__index == locksmith_ed1f_forloop__size %}{% assign locksmith_ed1f_forloop__last = true %}{% else %}{% assign locksmith_ed1f_forloop__last = false %}{% endif %}{% assign locksmith_ed1f_forloop__rindex = locksmith_ed1f_forloop__size | minus: locksmith_ed1f_forloop__index | minus: 1 %}{% assign locksmith_ed1f_forloop__rindex0 = locksmith_ed1f_forloop__size | minus: locksmith_ed1f_forloop__index0 | minus: 1 %}{% else %}{% continue %}{% endif -%}
      {%- comment %}</locksmith:3b56>{% endcomment -%}
      {%- assign link_title_downcase = link.title | strip | downcase -%}
      {%- assign mega_menu_block = '' -%}
      {%- assign mega_menu_images = '' -%}

      {%- for block in section.blocks -%}
        {%- assign menu_item_downcase = block.settings.menu_item | strip | downcase -%}

        {%- if menu_item_downcase == link_title_downcase -%}
          {%- assign mega_menu_block = block -%}
          {%- break -%}
        {%- endif -%}
      {%- endfor -%}

      <li class="header__linklist-item {% if link.links.size > 0 or mega_menu_block != '' %}has-dropdown{% endif %}" data-item-title="{{ link.title | escape }}">
        <a class="header__linklist-link link--animated" href="{{ link.url }}" {% if link.links.size > 0 or mega_menu_block != '' %}aria-controls="desktop-menu-{{ forloop.index }}" aria-expanded="false"{% endif %}>
          {{- link.title -}}
        </a>

        {%- if mega_menu_block != '' -%}
          {%- assign images_count = 0 -%}

          {%- capture mega_menu_images -%}
            {%- for i in (1..6) -%}
              {%- capture image_setting -%}image_{{ i }}{%- endcapture -%}

              {%- if mega_menu_block.settings[image_setting] != blank -%}
                {%- assign images_count = images_count | plus: 1 -%}

                {%- capture image_heading_setting -%}image_{{ i }}_heading{%- endcapture -%}
                {%- capture image_text_setting -%}image_{{ i }}_text{%- endcapture -%}
                {%- capture image_link_setting -%}image_{{ i }}_link{%- endcapture -%}

                {%- capture image_push -%}
                  <div class="mega-menu__image-wrapper">
                    {%- assign menu_image = mega_menu_block.settings[image_setting] -%}
                    {{ menu_image | image_url: width: menu_image.width | image_tag: loading: 'lazy', sizes: '240px', sizes: '240,480,720', class: 'mega-menu__image' }}
                  </div>

                  {%- if mega_menu_block.settings[image_heading_setting] != '' -%}
                    <p class="mega-menu__heading heading heading--small">{{ mega_menu_block.settings[image_heading_setting] }}</p>
                  {%- endif -%}

                  {%- if mega_menu_block.settings[image_text_setting] != '' -%}
                    <span class="mega-menu__text">{{ mega_menu_block.settings[image_text_setting] }}</span>
                  {%- endif -%}
                {%- endcapture -%}

                {%- if mega_menu_block.settings[image_link_setting] != blank -%}
                  <a href="{{ mega_menu_block.settings[image_link_setting] }}" class="mega-menu__image-push image-zoom">
                    {{- image_push -}}
                  </a>
                {%- else -%}
                  <div class="mega-menu__image-push image-zoom">
                    {{- image_push -}}
                  </div>
                {%- endif -%}
              {%- endif -%}
            {%- endfor -%}
          {%- endcapture -%}

          {%- if link.links.size > 0 or mega_menu_images != blank -%}
            <div hidden id="desktop-menu-{{ forloop.index }}" class="mega-menu" {{ mega_menu_block.shopify_attributes }}>
              <div class="container">
                <div class="mega-menu__inner">
                  {%- if mega_menu_block.settings.images_position == 'left' and mega_menu_images != blank -%}
                    <div class="mega-menu__images-wrapper {% if images_count >= 3 %}mega-menu__images-wrapper--tight{% endif %}">
                      {{- mega_menu_images -}}
                    </div>
                  {%- endif -%}

                  {%- if link.links.size > 0 -%}
                    <div class="mega-menu__columns-wrapper">
                      {%- for sub_link in link.links -%}
                        {%- comment %}<locksmith:0bfc>{% endcomment -%}
                          {%- capture var %}{% render 'locksmith-variables', scope: 'subject', subject: sub_link, subject_parent: link, variable: 'transparent' %}{% endcapture %}{% if var == "true" %}{% else %}{% continue %}{% endif -%}
                        {%- comment %}</locksmith:0bfc>{% endcomment -%}
                        <div class="mega-menu__column">
                          {%- if sub_link.url == '#' -%}
                            <span class="mega-menu__title heading heading--small">{{ sub_link.title }}</span>
                          {%- else -%}
                            <a href="{{ sub_link.url }}" class="mega-menu__title heading heading--small">{{ sub_link.title }}</a>
                          {%- endif -%}

                          {%- if sub_link.links.size > 0 -%}
                            <ul class="linklist list--unstyled" role="list">
                              {%- for sub_sub_link in sub_link.links -%}
                                {%- comment %}<locksmith:7554>{% endcomment -%}
                                  {%- capture var %}{% render 'locksmith-variables', scope: 'subject', subject: sub_sub_link, subject_parent: sub_link, variable: 'transparent' %}{% endcapture %}{% if var == "true" %}{% else %}{% continue %}{% endif -%}
                                {%- comment %}</locksmith:7554>{% endcomment -%}
                                <li class="linklist__item">
                                  <a href="{{ sub_sub_link.url }}" class="link--faded">{{ sub_sub_link.title }}</a>
                                </li>
                              {%- endfor -%}
                            </ul>
                          {%- endif -%}
                        </div>
                      {%- endfor -%}
                    </div>
                  {%- endif -%}

                  {%- if mega_menu_block.settings.images_position == 'right' and mega_menu_images != blank -%}
                    <div class="mega-menu__images-wrapper {% if images_count >= 3 %}mega-menu__images-wrapper--tight{% endif %}">
                      {{- mega_menu_images -}}
                    </div>
                  {%- endif -%}
                </div>
              </div>
            </div>
          {%- endif -%}
        {%- elsif link.links.size > 0 -%}
          <ul hidden id="desktop-menu-{{ forloop.index }}" class="nav-dropdown {% if link.levels == 1 %}nav-dropdown--restrict{% endif %} list--unstyled" role="list">
            {%- comment %}<locksmith:b1b3>{% endcomment -%}
              {%- assign locksmith_f468_forloop__size = 0 %}{%- for sub_link in link.links -%}{% capture var %}{% render 'locksmith-variables', scope: 'subject', subject: sub_link, subject_parent: link, variable: 'transparent' %}{% endcapture %}{% if var == 'true' %}{% assign locksmith_f468_forloop__size = locksmith_f468_forloop__size | plus: 1 %}{% endif %}{% endfor %}{% assign locksmith_f468_forloop__index = nil -%}
            {%- comment %}</locksmith:b1b3>{% endcomment -%}
            {%- for sub_link in link.links -%}
              {%- comment %}<locksmith:358a>{% endcomment -%}
                {%- capture var %}{% render 'locksmith-variables', scope: 'subject', subject: sub_link, subject_parent: link, variable: 'transparent' %}{% endcapture %}{% if var == "true" %}{% if locksmith_f468_forloop__index == nil %}{% assign locksmith_f468_forloop__index = 1 %}{% assign locksmith_f468_forloop__index0 = 0 %}{% else %}{% assign locksmith_f468_forloop__index = locksmith_f468_forloop__index | plus: 1 %}{% assign locksmith_f468_forloop__index0 = locksmith_f468_forloop__index0 | plus: 1 %}{% endif %}{% if locksmith_f468_forloop__index == 1 %}{% assign locksmith_f468_forloop__first = true %}{% else %}{% assign locksmith_f468_forloop__first = false %}{% endif %}{% if locksmith_f468_forloop__index == locksmith_f468_forloop__size %}{% assign locksmith_f468_forloop__last = true %}{% else %}{% assign locksmith_f468_forloop__last = false %}{% endif %}{% assign locksmith_f468_forloop__rindex = locksmith_f468_forloop__size | minus: locksmith_f468_forloop__index | minus: 1 %}{% assign locksmith_f468_forloop__rindex0 = locksmith_f468_forloop__size | minus: locksmith_f468_forloop__index0 | minus: 1 %}{% else %}{% continue %}{% endif -%}
              {%- comment %}</locksmith:358a>{% endcomment -%}
              <li class="nav-dropdown__item {% if sub_link.links.size > 0 %}has-dropdown{% endif %}">
                <a class="nav-dropdown__link link--faded" href="{{ sub_link.url }}" {% if sub_link.links.size > 0 %}aria-controls="desktop-menu-{{ forloop.parentloop.index }}-{{ locksmith_f468_forloop__index }}" aria-expanded="false"{% endif %}>
                  {{- sub_link.title -}}

                  {%- if sub_link.links.size > 0 -%}
                    {% render 'icon' with 'dropdown-arrow-right', direction_aware: true %}
                  {%- endif -%}
                </a>

                {%- if sub_link.links.size > 0 -%}
                  <ul hidden id="desktop-menu-{{ forloop.parentloop.index }}-{{ locksmith_f468_forloop__index }}" class="nav-dropdown list--unstyled" role="list">
                    {%- for sub_sub_link in sub_link.links -%}
                      {%- comment %}<locksmith:315b>{% endcomment -%}
                        {%- capture var %}{% render 'locksmith-variables', scope: 'subject', subject: sub_sub_link, subject_parent: sub_link, variable: 'transparent' %}{% endcapture %}{% if var == "true" %}{% else %}{% continue %}{% endif -%}
                      {%- comment %}</locksmith:315b>{% endcomment -%}
                      <li class="nav-dropdown__item">
                        <a class="nav-dropdown__link link--faded" href="{{ sub_sub_link.url }}">{{ sub_sub_link.title }}</a>
                      </li>
                    {%- endfor -%}
                  </ul>
                {%- endif -%}
              </li>
            {%- endfor -%}
          </ul>
        {%- endif -%}
      </li>
    {%- endfor -%}
  </ul>
</desktop-navigation>