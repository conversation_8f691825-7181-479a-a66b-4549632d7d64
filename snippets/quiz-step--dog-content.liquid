{%- capture quiz_dog_diets -%}
Allergies and/or Skin Issues
Calcium Oxalate and/or Struvite Stones
Cancer Related
Grain Intolerance
Heart Issues
Joint Issues
Kidney Disease
Kidney Disease and Pancreatitis
Liver Issues
Pancreatitis
Stomach and/or GI Issues
Urate Stones
Not Listed
{%- endcapture -%}

  <quiz-step-dog-general data-quiz-substep data-dog="$DOGNUM" class="quiz-substep">

    <div class="quiz-step__body">

      <div class="quiz-step-form"> 

        <div class="quiz-step-title h1">Tell us about <span data-dog-name></span>!</div>

        {% if settings.quiz_dog_image_1 != blank %}
          <img class="quiz-step-icon" sizes="(max-width: 740px) calc(100vw - 96px), (max-width: 999px) calc(100vw - 160px), 600px" loading="lazy" {% render 'image-attributes', image: settings.quiz_dog_image_1, sizes: '600,700,800,1000,1200,1400' %}>
        {% endif %}

        <quiz-step-line class="quiz-step-line">

          <expanding-input class="expanding-input--select">
            <select class="expanding-input__input" name="dog-$DOGNUM-sex" id="dog-$DOGNUM-sex" data-dog-sex required>
              <option value="">Your Dog's Sex</option>
              <option data-display-value="She" value="Female">She</option>
              <option data-display-value="He" value="Male">He</option>
            </select>
            <span class="expanding-input__display quiz-step-line__input quiz-input" role="textbox" data-default="He/She"></span>
          </expanding-input>

          <span class="quiz-step-line__text">is a</span>
          
          <expanding-input>
            <span class="expanding-input__display quiz-step-line__input quiz-input" role="textbox" contenteditable data-type="number" data-default="?"></span>
            <input type="number" class="expanding-input__input" name="dog-$DOGNUM-age" id="dog-$DOGNUM-age" data-dog-age>
          </expanding-input>

          <expanding-input class="expanding-input--select">
            
            <select class="expanding-input__input" name="dog-$DOGNUM-age-type" id="dog-$DOGNUM-age-type" data-dog-age-type required>
              <option value="">Select</option>
              <option value="Year">Year</option>
              <option value="Month">Month</option> 
            </select>

            <span class="expanding-input__display quiz-step-line__input quiz-input" role="textbox" data-default="Year/Month"></span>
          </expanding-input>

          <span class="quiz-step-line__text">old</span>
        
          {%- assign dog_breed_config = settings.quiz_dog_breeds | newline_to_br | split: '<br />' -%}

          <expanding-input class="expanding-input--select">

            <select class="expanding-input__input" name="dog-$DOGNUM-breed" id="dog-$DOGNUM-breed" data-dog-breed required>
              <option value="">Select Breed</option>
              {%- for breed in dog_breed_config -%}
                <option value="{{ breed | strip }}">{{ breed | strip }}</option>
              {%- endfor -%}
            </select>
            <span class="expanding-input__display quiz-step-line__input quiz-input" role="textbox" data-default="Select Breed"></span>
          </expanding-input>
        
          <div class="quiz-step-line__hint text--subdued">Different breeds may have different nutrient needs.</div>

        </quiz-step-line>

        <quiz-step-line class="quiz-step-line quiz-step-line--hidden">

          <hr class="hr--clear hr--xsmall">
          <span class="quiz-step-line__text"><span data-dog-name></span></span>
          <expanding-input class="expanding-input--select">
            <select class="expanding-input__input" name="dog-$DOGNUM-neutered" id="dog-$DOGNUM-neutered" data-dog-neutered required>
              <option value="">Select</option>
              <option value="is">is</option>
              <option value="is not">is not</option>
            </select>
            <span class="expanding-input__display quiz-step-line__input quiz-input" role="textbox" data-default="is / is not"></span>
          </expanding-input>
          <span class="quiz-step-line__text"><span data-term-neutered>spayed</span>.</span>

          <div class="quiz-step-line__hint text--subdued">Spayed / neutered pups need fewer calories.</div>
        
        </quiz-step-line>

      </div>

    </div>

    <div class="quiz-step__footer">

      <div class="container">

        <div class="quiz-step-actions">

          <button type="button" is="loader-button" class="button button--highlight" data-quiz-button-next data-quiz-button-update-dog disabled="disabled">
            <span class="button__text">{{ 'quiz.general.continue' | t }}</span>
            <span class="button__icon">{%- render 'icon' with 'nav-arrow-right' -%}</span>
          </button>

        </div>
      
      </div>

    </div>
  
  </quiz-step-dog-general>

  <quiz-step-dog-weight-profile data-quiz-substep data-dog="1" class="quiz-substep">

    <div class="quiz-step__inner">

      <div class="quiz-step__body">

        <div class="quiz-step-form">

          <div class="quiz-step-title h2"><span data-dog-name></span> is</div>

          <quiz-tiles class="quiz-tiles">

            <div class="quiz-tiles__container">

              <quiz-tile class="quiz-tile" data-hint="{{ 'quiz.steps.weight_profile.level_1.hint' | t }}" data-value="{{ 'quiz.steps.weight_profile.level_1.title' | t }}" {{ block.shopify_attributes }}>

                {% if settings.quiz_dog_weight_profile_1 != blank %}
                  <span class="quiz-tile__icon">
                    <img class="quiz-tile__icon--default" sizes="(max-width: 740px) calc(100vw - 96px), (max-width: 999px) calc(100vw - 160px), 600px" loading="lazy" {% render 'image-attributes', image: settings.quiz_dog_weight_profile_1, sizes: '600,700,800,1000,1200,1400' %}>
                    {% if settings.quiz_dog_weight_profile_1_hover != blank %}
                      <img class="quiz-tile__icon--hover" sizes="(max-width: 740px) calc(100vw - 96px), (max-width: 999px) calc(100vw - 160px), 600px" loading="lazy" {% render 'image-attributes', image: settings.quiz_dog_weight_profile_1_hover, sizes: '600,700,800,1000,1200,1400' %}>
                    {% endif %}
                  </span>
                {% endif %}
                
                <span class="quiz-tile__text">
                  <span class="quiz-tile__text__title text--large">{{ 'quiz.steps.weight_profile.level_1.title' | t }}</span>
                  <span class="quiz-tile__text__hint">{{ 'quiz.steps.weight_profile.level_1.hint' | t }}</span>
                </span>

              </quiz-tile>

              <quiz-tile class="quiz-tile" data-hint="{{ 'quiz.steps.weight_profile.level_2.hint' | t }}" data-value="{{ 'quiz.steps.weight_profile.level_2.title' | t }}" {{ block.shopify_attributes }}>

                {% if settings.quiz_dog_weight_profile_2 != blank %}
                  <span class="quiz-tile__icon">
                    <img class="quiz-tile__icon--default" sizes="(max-width: 740px) calc(100vw - 96px), (max-width: 999px) calc(100vw - 160px), 600px" loading="lazy" {% render 'image-attributes', image: settings.quiz_dog_weight_profile_2, sizes: '600,700,800,1000,1200,1400' %}>
                    {% if settings.quiz_dog_weight_profile_2_hover != blank %}
                      <img class="quiz-tile__icon--hover" sizes="(max-width: 740px) calc(100vw - 96px), (max-width: 999px) calc(100vw - 160px), 600px" loading="lazy" {% render 'image-attributes', image: settings.quiz_dog_weight_profile_2_hover, sizes: '600,700,800,1000,1200,1400' %}>
                    {% endif %}
                  </span>
                {% endif %}
                
                <span class="quiz-tile__text">
                  <span class="quiz-tile__text__title text--large">{{ 'quiz.steps.weight_profile.level_2.title' | t }}</span>
                  <span class="quiz-tile__text__hint">{{ 'quiz.steps.weight_profile.level_2.hint' | t }}</span>
                </span>

              </quiz-tile>

              <quiz-tile class="quiz-tile" data-hint="{{ 'quiz.steps.weight_profile.level_3.hint' | t }}" data-value="{{ 'quiz.steps.weight_profile.level_3.title' | t }}" {{ block.shopify_attributes }}>

                {% if settings.quiz_dog_weight_profile_3 != blank %}
                  <span class="quiz-tile__icon">
                    <img class="quiz-tile__icon--default" sizes="(max-width: 740px) calc(100vw - 96px), (max-width: 999px) calc(100vw - 160px), 600px" loading="lazy" {% render 'image-attributes', image: settings.quiz_dog_weight_profile_3, sizes: '600,700,800,1000,1200,1400' %}>
                    {% if settings.quiz_dog_weight_profile_3_hover != blank %}
                      <img class="quiz-tile__icon--hover" sizes="(max-width: 740px) calc(100vw - 96px), (max-width: 999px) calc(100vw - 160px), 600px" loading="lazy" {% render 'image-attributes', image: settings.quiz_dog_weight_profile_3_hover, sizes: '600,700,800,1000,1200,1400' %}>
                    {% endif %}
                  </span>
                {% endif %}
                
                <span class="quiz-tile__text">
                  <span class="quiz-tile__text__title text--large">{{ 'quiz.steps.weight_profile.level_3.title' | t }}</span>
                  <span class="quiz-tile__text__hint">{{ 'quiz.steps.weight_profile.level_3.hint' | t }}</span>
                </span>

              </quiz-tile>

            </div>

            <div class="quiz-tiles__hint text--subdued hidden-pocket" data-weight-profile-tip data-default="Take a look at your dog and choose the weight profile that looks close.">Take a look at your dog and choose the weight profile that looks close.</div>

            <input id="dog-$DOGNUM-weight-profile" name="dog-$DOGNUM-weight-profile" class="quiz-tiles__input" type="hidden" />

          </quiz-tiles>

        </div>

        {% if section.settings.hint != blank %}
          <div class="quiz-step-hint">{{ section.settings.hint }}</div>
        {% endif %}
    
      </div>

      <div class="quiz-step__footer">

        <div class="container">

          <div class="quiz-step-actions">

            <button type="button" is="loader-button" class="button button--highlight" data-quiz-button-next disabled="disabled">
              <span class="button__text">{{ 'quiz.general.continue' | t }}</span>
              <span class="button__icon">{%- render 'icon' with 'nav-arrow-right' -%}</span>
            </button>

          </div>
        
        </div>

      </div>

    </div>

  </quiz-step-dog-weight-profile>

  <quiz-step-dog-weight-details data-quiz-substep data-dog="1" class="quiz-substep">

    <div class="quiz-step__inner">

      <div class="quiz-step__body">

        <div class="quiz-step-form">

          <quiz-step-line class="quiz-step-line">

            <span class="quiz-step-line__text"><span data-dog-name></span> weighs
              <expanding-input>
                <span class="expanding-input__display quiz-step-line__input quiz-input" role="textbox" contenteditable data-type="number" data-default="?"></span>
                <input type="number" class="expanding-input__input" name="dog-$DOGNUM-weight" id="dog-$DOGNUM-weight" required>
              </expanding-input>
              <span class="quiz-step-line__text">lbs</span>
            </span>

          </quiz-step-line>

          <quiz-step-line class="quiz-step-line quiz-step-line--hidden">

            <hr class="hr--clear hr--xsmall">
            <span class="quiz-step-line__text"><span data-term-sex-possessive>His</span> ideal weight is</span>
            <expanding-input>
              <span class="expanding-input__display quiz-step-line__input quiz-input" role="textbox" contenteditable data-type="number" data-default="?"></span>
              <input type="number" class="expanding-input__input" name="dog-$DOGNUM-ideal-weight" id="dog-$DOGNUM-ideal-weight" required>
            </expanding-input>
            <span class="quiz-step-line__text">lbs</span>

            <div class="quiz-step-line__hint">
              <button type="button" is="toggle-button" aria-controls="quiz-popup--weight" aria-expanded="false" class="link text--subdued">
                <span class="quiz-navigation-button__text">{{ 'quiz.steps.weight.popup_link' | t }}</span>
              </button>
            </div>
          
          </quiz-step-line>

          <div class="text--subdued"></div>

        </div>

      </div>

      <div class="quiz-step__footer">

        <div class="container">

          <div class="quiz-step-actions">

            <button type="button" is="loader-button" class="button button--highlight" data-quiz-button-next disabled="disabled">
              <span class="button__text">{{ 'quiz.general.continue' | t }}</span>
              <span class="button__icon">{%- render 'icon' with 'nav-arrow-right' -%}</span>
            </button>

          </div>
        
        </div>

      </div>

    </div>

  </quiz-step-dog-weight-details>

  <quiz-step-dog-activity data-quiz-substep data-dog="1" class="quiz-substep">

    <div class="quiz-step__inner">

      <div class="quiz-step__body">

        <div class="quiz-step-form">

          <div class="quiz-step-title h2"><span data-dog-name></span>'s activity level is</div>

          <quiz-tiles class="quiz-tiles">

            <div class="quiz-tiles__container">

              <quiz-tile class="quiz-tile" data-hint="{{ 'quiz.steps.activity.level_1.hint' | t }}" data-value="{{ 'quiz.steps.activity.level_1.title' | t }}" {{ block.shopify_attributes }}>

                {% if settings.quiz_dog_activity_1 != blank %}
                  <span class="quiz-tile__icon">
                    <img class="quiz-tile__icon--default" sizes="(max-width: 740px) calc(100vw - 96px), (max-width: 999px) calc(100vw - 160px), 600px" loading="lazy" {% render 'image-attributes', image: settings.quiz_dog_activity_1, sizes: '600,700,800,1000,1200,1400' %}>
                    {% if settings.quiz_dog_activity_1_hover != blank %}
                      <img class="quiz-tile__icon--hover" sizes="(max-width: 740px) calc(100vw - 96px), (max-width: 999px) calc(100vw - 160px), 600px" loading="lazy" {% render 'image-attributes', image: settings.quiz_dog_activity_1_hover, sizes: '600,700,800,1000,1200,1400' %}>
                    {% endif %}
                  </span>
                {% endif %}
                
                <span class="quiz-tile__text">
                  <span class="quiz-tile__text__title text--large">{{ 'quiz.steps.activity.level_1.title' | t }}</span>
                  <span class="quiz-tile__text__hint">{{ 'quiz.steps.activity.level_1.hint' | t }}</span>
                </span>

              </quiz-tile>

              <quiz-tile class="quiz-tile" data-hint="{{ 'quiz.steps.activity.level_2.hint' | t }}" data-value="{{ 'quiz.steps.activity.level_2.title' | t }}" {{ block.shopify_attributes }}>

                {% if settings.quiz_dog_activity_2 != blank %}
                  <span class="quiz-tile__icon">
                    <img class="quiz-tile__icon--default" sizes="(max-width: 740px) calc(100vw - 96px), (max-width: 999px) calc(100vw - 160px), 600px" loading="lazy" {% render 'image-attributes', image: settings.quiz_dog_activity_2, sizes: '600,700,800,1000,1200,1400' %}>
                    {% if settings.quiz_dog_activity_2_hover != blank %}
                      <img class="quiz-tile__icon--hover" sizes="(max-width: 740px) calc(100vw - 96px), (max-width: 999px) calc(100vw - 160px), 600px" loading="lazy" {% render 'image-attributes', image: settings.quiz_dog_activity_2_hover, sizes: '600,700,800,1000,1200,1400' %}>
                    {% endif %}
                  </span>
                {% endif %}
                
                <span class="quiz-tile__text">
                  <span class="quiz-tile__text__title text--large">{{ 'quiz.steps.activity.level_2.title' | t }}</span>
                  <span class="quiz-tile__text__hint">{{ 'quiz.steps.activity.level_2.hint' | t }}</span>
                </span>

              </quiz-tile>

              <quiz-tile class="quiz-tile" data-hint="{{ 'quiz.steps.activity.level_3.hint' | t }}" data-value="{{ 'quiz.steps.activity.level_3.title' | t }}" {{ block.shopify_attributes }}>

                {% if settings.quiz_dog_activity_3 != blank %}
                  <span class="quiz-tile__icon">
                    <img class="quiz-tile__icon--default" sizes="(max-width: 740px) calc(100vw - 96px), (max-width: 999px) calc(100vw - 160px), 600px" loading="lazy" {% render 'image-attributes', image: settings.quiz_dog_activity_3, sizes: '600,700,800,1000,1200,1400' %}>
                    {% if settings.quiz_dog_activity_3_hover != blank %}
                      <img class="quiz-tile__icon--hover" sizes="(max-width: 740px) calc(100vw - 96px), (max-width: 999px) calc(100vw - 160px), 600px" loading="lazy" {% render 'image-attributes', image: settings.quiz_dog_activity_3_hover, sizes: '600,700,800,1000,1200,1400' %}>
                    {% endif %}
                  </span>
                {% endif %}
                
                <span class="quiz-tile__text">
                  <span class="quiz-tile__text__title text--large">{{ 'quiz.steps.activity.level_3.title' | t }}</span>
                  <span class="quiz-tile__text__hint">{{ 'quiz.steps.activity.level_3.hint' | t }}</span>
                </span>

              </quiz-tile>

            </div>

            <div class="quiz-tiles__hint text--subdued hidden-pocket" data-weight-profile-tip data-default="How active is your dog?">How active is your dog?</div>

            <input id="dog-$DOGNUM-activity-level" name="dog-$DOGNUM-activity-level" class="quiz-tiles__input" type="hidden" />

          </quiz-tiles>

        </div>

        {% if section.settings.hint != blank %}
          <div class="quiz-step-hint">{{ section.settings.hint }}</div>
        {% endif %}
    
      </div>

      <div class="quiz-step__footer">

        <div class="container">

          <div class="quiz-step-actions">

            <button type="button" is="loader-button" class="button button--highlight" data-quiz-button-next disabled="disabled">
              <span class="button__text">{{ 'quiz.general.continue' | t }}</span>
              <span class="button__icon">{%- render 'icon' with 'nav-arrow-right' -%}</span>
            </button>

          </div>
        
        </div>

      </div>

    </div>

  </quiz-step-dog-activity>
  
  <quiz-step-dog-health data-quiz-substep data-dog="1" class="quiz-substep">

    <div class="quiz-step__inner">

      <div class="quiz-step__body">

        <div class="quiz-step-form">

          <form action="#" method="post" id="step-form">

            <quiz-step-line class="quiz-step-line">

              <span class="quiz-step-line__text"><span data-dog-name></span></span>
              <expanding-input class="expanding-input--select">
                <select class="expanding-input__input" name="dog-$DOGNUM-has-health-issue" id="dog-$DOGNUM-has-health-issue" required show-nextline-if="true">
                  <option value="">Select</option>
                  <option data-display-value="has" value="true">has</option>
                  <option data-display-value="does not have" value="false">does not have</option>
                </select>
                <span class="expanding-input__display quiz-step-line__input quiz-input" role="textbox" data-default="has / does not have"></span>
              </expanding-input>
              <span class="quiz-step-line__text">a major health issue</span>
            
            </quiz-step-line>

            <quiz-step-line class="quiz-step-line quiz-step-line--hidden" optional-if-hidden>

              <span class="quiz-step-line__text">and</span>
              <expanding-input class="expanding-input--select">
                <select class="expanding-input__input" name="dog-$DOGNUM-needs-prescription" id="dog-$DOGNUM-needs-prescription" required show-nextline-if="true">
                  <option value="">Select</option>
                  <option data-display-value="does" value="true">does</option>
                  <option data-display-value="does not" value="false" next-line-optional>does not</option>
                </select>
                <span class="expanding-input__display quiz-step-line__input quiz-input" role="textbox" data-default="does / does not"></span>
              </expanding-input>
              <span class="quiz-step-line__text">require a prescription diet</span>
            
            </quiz-step-line>

            <quiz-step-line class="quiz-step-line quiz-step-line--hidden" optional-if-hidden>

              <hr class="hr--clear hr--xsmall">
              <span class="quiz-step-line__text"><span data-dog-sex-pronoun>He</span> is prescribed a</span>
              <expanding-input class="expanding-input--select" >

                {%- assign dog_diet_config = quiz_dog_diets | newline_to_br | split: '<br />' -%}

                <select class="expanding-input__input" name="dog-$DOGNUM-prescription-diet" id="dog-$DOGNUM-prescription-diet" required>
                  <option value="">Select Diet</option>
                  {% for diet in dog_diet_config %}
                    <option value="{{ diet | strip }}">{{ diet | strip }}</option>
                  {% endfor %}
                </select>
                
                <span class="expanding-input__display quiz-step-line__input quiz-input" role="textbox" data-default="Select Diet"></span>
              </expanding-input>
              <span class="quiz-step-line__text"> diet</span>
            
            </quiz-step-line>

          </form>

        </div>

        <div class="quiz-step-hint">
          <div class="quiz-step-hint__in-progress">
            Thanks for telling us about <span data-dog-name></span>. Now let's create a profile for your other pups!
          </div>
          <div class="quiz-step-hint__final">
            Thanks for telling us about all your pups!
          </div>
        </div>
    
      </div>

      <div class="quiz-step__footer">

        <div class="container">

          <div class="quiz-step-actions">

            <button type="button" is="loader-button" class="button button--highlight" data-quiz-button-next data-quiz-button-dog-complete disabled="disabled">
              <span class="button__text">{{ 'quiz.general.continue' | t }}</span>
              <span class="button__icon">{%- render 'icon' with 'nav-arrow-right' -%}</span>
            </button>

          </div>
        
        </div>

      </div>

    </div>

  </quiz-step-dog-health>