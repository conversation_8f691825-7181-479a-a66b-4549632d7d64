{%- comment %}
  Hey there! This asset is managed by <PERSON><PERSON> (uselocksmith.com).

  Do not modify this file. Any changes will be reset the next time that
  <PERSON><PERSON> interacts with this theme.

  Last updated: Wed, 23 Oct 2024 20:17:53 -0400 (EDT)
{% endcomment -%}

{%assign _d='rgba(0,0,0,0)'%}{%assign _5="settings"%}{%assign _l="option_type"%}{%assign _x="text_background"%}{%assign _9="text_color"%}{%assign _t="button_background"%}{%assign _p="button_text_color"%}{%assign _0h=cart%}{%assign _01=image%}{%assign _0d=customer%}{%assign _09=theme%}{%capture _%}{%assign _05=_h%}{%capture locksmith_client%}<script data-locksmith>!function(){var require=undefined,reqwest=function(){function succeed(e){var t=protocolRe.exec(e.url);return t=t&&t[1]||context.location.protocol,httpsRe.test(t)?twoHundo.test(e.request.status):!!e.request.response}function handleReadyState(e,t,n){return function(){return e._aborted?n(e.request):e._timedOut?n(e.request,"Request is aborted: timeout"):void(e.request&&4==e.request[readyState]&&(e.request.onreadystatechange=noop,succeed(e)?t(e.request):n(e.request)))}}function setHeaders(e,t){var n,s=t.headers||{};s.Accept=s.Accept||defaultHeaders.accept[t.type]||defaultHeaders.accept["*"];var r="undefined"!=typeof FormData&&t.data instanceof FormData;for(n in!t.crossOrigin&&!s[requestedWith]&&(s[requestedWith]=defaultHeaders.requestedWith),!s[contentType]&&!r&&(s[contentType]=t.contentType||defaultHeaders.contentType),s)s.hasOwnProperty(n)&&"setRequestHeader"in e&&e.setRequestHeader(n,s[n])}function setCredentials(e,t){"undefined"!=typeof t.withCredentials&&"undefined"!=typeof e.withCredentials&&(e.withCredentials=!!t.withCredentials)}function generalCallback(e){lastValue=e}function urlappend(e,t){return e+(/[?]/.test(e)?"&":"?")+t}function handleJsonp(e,t,n,s){var r=uniqid++,a=e.jsonpCallback||"callback",o=e.jsonpCallbackName||reqwest.getcallbackPrefix(r),i=new RegExp("((^|[?]|&)"+a+")=([^&]+)"),l=s.match(i),c=doc.createElement("script"),u=0,d=-1!==navigator.userAgent.indexOf("MSIE 10.0");return l?"?"===l[3]?s=s.replace(i,"$1="+o):o=l[3]:s=urlappend(s,a+"="+o),context[o]=generalCallback,c.type="text/javascript",c.src=s,c.async=!0,"undefined"!=typeof c.onreadystatechange&&!d&&(c.htmlFor=c.id="_reqwest_"+r),c.onload=c.onreadystatechange=function(){if(c[readyState]&&"complete"!==c[readyState]&&"loaded"!==c[readyState]||u)return!1;c.onload=c.onreadystatechange=null,c.onclick&&c.onclick(),t(lastValue),lastValue=undefined,head.removeChild(c),u=1},head.appendChild(c),{abort:function(){c.onload=c.onreadystatechange=null,n({},"Request is aborted: timeout",{}),lastValue=undefined,head.removeChild(c),u=1}}}function getRequest(e,t){var n,s=this.o,r=(s.method||"GET").toUpperCase(),a="string"==typeof s?s:s.url,o=!1!==s.processData&&s.data&&"string"!=typeof s.data?reqwest.toQueryString(s.data):s.data||null,i=!1;return("jsonp"==s.type||"GET"==r)&&o&&(a=urlappend(a,o),o=null),"jsonp"==s.type?handleJsonp(s,e,t,a):((n=s.xhr&&s.xhr(s)||xhr(s)).open(r,a,!1!==s.async),setHeaders(n,s),setCredentials(n,s),context[xDomainRequest]&&n instanceof context[xDomainRequest]?(n.onload=e,n.onerror=t,n.onprogress=function(){},i=!0):n.onreadystatechange=handleReadyState(this,e,t),s.before&&s.before(n),i?setTimeout(function(){n.send(o)},200):n.send(o),n)}function Reqwest(e,t){this.o=e,this.fn=t,init.apply(this,arguments)}function setType(e){return null===e?undefined:e.match("json")?"json":e.match("javascript")?"js":e.match("text")?"html":e.match("xml")?"xml":void 0}function init(o,fn){function complete(e){for(o.timeout&&clearTimeout(self.timeout),self.timeout=null;0<self._completeHandlers.length;)self._completeHandlers.shift()(e)}function success(resp){var type=o.type||resp&&setType(resp.getResponseHeader("Content-Type"));resp="jsonp"!==type?self.request:resp;var filteredResponse=globalSetupOptions.dataFilter(resp.responseText,type),r=filteredResponse;try{resp.responseText=r}catch(e){}if(r)switch(type){case"json":try{resp=context.JSON?context.JSON.parse(r):eval("("+r+")")}catch(err){return error(resp,"Could not parse JSON in response",err)}break;case"js":resp=eval(r);break;case"html":resp=r;break;case"xml":resp=resp.responseXML&&resp.responseXML.parseError&&resp.responseXML.parseError.errorCode&&resp.responseXML.parseError.reason?null:resp.responseXML}for(self._responseArgs.resp=resp,self._fulfilled=!0,fn(resp),self._successHandler(resp);0<self._fulfillmentHandlers.length;)resp=self._fulfillmentHandlers.shift()(resp);complete(resp)}function timedOut(){self._timedOut=!0,self.request.abort()}function error(e,t,n){for(e=self.request,self._responseArgs.resp=e,self._responseArgs.msg=t,self._responseArgs.t=n,self._erred=!0;0<self._errorHandlers.length;)self._errorHandlers.shift()(e,t,n);complete(e)}this.url="string"==typeof o?o:o.url,this.timeout=null,this._fulfilled=!1,this._successHandler=function(){},this._fulfillmentHandlers=[],this._errorHandlers=[],this._completeHandlers=[],this._erred=!1,this._responseArgs={};var self=this;fn=fn||function(){},o.timeout&&(this.timeout=setTimeout(function(){timedOut()},o.timeout)),o.success&&(this._successHandler=function(){o.success.apply(o,arguments)}),o.error&&this._errorHandlers.push(function(){o.error.apply(o,arguments)}),o.complete&&this._completeHandlers.push(function(){o.complete.apply(o,arguments)}),this.request=getRequest.call(this,success,error)}function reqwest(e,t){return new Reqwest(e,t)}function normalize(e){return e?e.replace(/\r?\n/g,"\r\n"):""}function serial(e,t){var n,s,r,a,o=e.name,i=e.tagName.toLowerCase(),l=function(e){e&&!e.disabled&&t(o,normalize(e.attributes.value&&e.attributes.value.specified?e.value:e.text))};if(!e.disabled&&o)switch(i){case"input":/reset|button|image|file/i.test(e.type)||(n=/checkbox/i.test(e.type),s=/radio/i.test(e.type),r=e.value,(!n&&!s||e.checked)&&t(o,normalize(n&&""===r?"on":r)));break;case"textarea":t(o,normalize(e.value));break;case"select":if("select-one"===e.type.toLowerCase())l(0<=e.selectedIndex?e.options[e.selectedIndex]:null);else for(a=0;e.length&&a<e.length;a++)e.options[a].selected&&l(e.options[a])}}function eachFormElement(){var e,t,a=this,n=function(e,t){var n,s,r;for(n=0;n<t.length;n++)for(r=e[byTag](t[n]),s=0;s<r.length;s++)serial(r[s],a)};for(t=0;t<arguments.length;t++)e=arguments[t],/input|select|textarea/i.test(e.tagName)&&serial(e,a),n(e,["input","select","textarea"])}function serializeQueryString(){return reqwest.toQueryString(reqwest.serializeArray.apply(null,arguments))}function serializeHash(){var n={};return eachFormElement.apply(function(e,t){e in n?(n[e]&&!isArray(n[e])&&(n[e]=[n[e]]),n[e].push(t)):n[e]=t},arguments),n}function buildParams(e,t,n,s){var r,a,o,i=/\[\]$/;if(isArray(t))for(a=0;t&&a<t.length;a++)o=t[a],n||i.test(e)?s(e,o):buildParams(e+"["+("object"==typeof o?a:"")+"]",o,n,s);else if(t&&"[object Object]"===t.toString())for(r in t)buildParams(e+"["+r+"]",t[r],n,s);else s(e,t)}var context=this,XHR2;if("window"in context)var doc=document,byTag="getElementsByTagName",head=doc[byTag]("head")[0];else try{XHR2=require("xhr2")}catch(ex){throw new Error("Peer dependency `xhr2` required! Please npm install xhr2")}var httpsRe=/^http/,protocolRe=/(^\w+):\/\//,twoHundo=/^(20\d|1223)$/,readyState="readyState",contentType="Content-Type",requestedWith="X-Requested-With",uniqid=0,callbackPrefix="reqwest_"+ +new Date,lastValue,xmlHttpRequest="XMLHttpRequest",xDomainRequest="XDomainRequest",noop=function(){},isArray="function"==typeof Array.isArray?Array.isArray:function(e){return e instanceof Array},defaultHeaders={contentType:"application/x-www-form-urlencoded",requestedWith:xmlHttpRequest,accept:{"*":"text/javascript, text/html, application/xml, text/xml, */*",xml:"application/xml, text/xml",html:"text/html",text:"text/plain",json:"application/json, text/javascript",js:"application/javascript, text/javascript"}},xhr=function(e){if(!0!==e.crossOrigin)return context[xmlHttpRequest]?new XMLHttpRequest:XHR2?new XHR2:new ActiveXObject("Microsoft.XMLHTTP");var t=context[xmlHttpRequest]?new XMLHttpRequest:null;if(t&&"withCredentials"in t)return t;if(context[xDomainRequest])return new XDomainRequest;throw new Error("Browser does not support cross-origin requests")},globalSetupOptions={dataFilter:function(e){return e}};return Reqwest.prototype={abort:function(){this._aborted=!0,this.request.abort()},retry:function(){init.call(this,this.o,this.fn)},then:function(e,t){return e=e||function(){},t=t||function(){},this._fulfilled?this._responseArgs.resp=e(this._responseArgs.resp):this._erred?t(this._responseArgs.resp,this._responseArgs.msg,this._responseArgs.t):(this._fulfillmentHandlers.push(e),this._errorHandlers.push(t)),this},always:function(e){return this._fulfilled||this._erred?e(this._responseArgs.resp):this._completeHandlers.push(e),this},fail:function(e){return this._erred?e(this._responseArgs.resp,this._responseArgs.msg,this._responseArgs.t):this._errorHandlers.push(e),this},"catch":function(e){return this.fail(e)}},reqwest.serializeArray=function(){var n=[];return eachFormElement.apply(function(e,t){n.push({name:e,value:t})},arguments),n},reqwest.serialize=function(){if(0===arguments.length)return"";var e,t=Array.prototype.slice.call(arguments,0);return(e=t.pop())&&e.nodeType&&t.push(e)&&(e=null),e&&(e=e.type),("map"==e?serializeHash:"array"==e?reqwest.serializeArray:serializeQueryString).apply(null,t)},reqwest.toQueryString=function(e,t){var n,s,r=t||!1,a=[],o=encodeURIComponent,i=function(e,t){t="function"==typeof t?t():null==t?"":t,a[a.length]=o(e)+"="+o(t)};if(isArray(e))for(s=0;e&&s<e.length;s++)i(e[s].name,e[s].value);else for(n in e)e.hasOwnProperty(n)&&buildParams(n,e[n],r,i);return a.join("&").replace(/%20/g,"+")},reqwest.getcallbackPrefix=function(){return callbackPrefix},reqwest.compat=function(e,t){return e&&(e.type&&(e.method=e.type)&&delete e.type,e.dataType&&(e.type=e.dataType),e.jsonpCallback&&(e.jsonpCallbackName=e.jsonpCallback)&&delete e.jsonpCallback,e.jsonp&&(e.jsonpCallback=e.jsonp)),new Reqwest(e,t)},reqwest.ajaxSetup=function(e){for(var t in e=e||{})globalSetupOptions[t]=e[t]},reqwest}();
/*!
  * Reqwest! A general purpose XHR connection manager
  * license MIT (c) Dustin Diaz 2015
  * https://github.com/ded/reqwest
  */!function(){var o=window.Locksmith={},e=document.querySelector('script[type="application/vnd.locksmith+json"]'),n=e&&e.innerHTML;if(o.state={},o.util={},o.loading=!1,n)try{o.state=JSON.parse(n)}catch(u){}if(document.addEventListener&&document.querySelector){var s,r,a,t=[76,79,67,75,83,77,73,84,72,49,49],i=function(){r=t.slice(0)},l="style",c=function(e){e&&27!==e.keyCode&&"click"!==e.type||(document.removeEventListener("keydown",c),document.removeEventListener("click",c),s&&document.body.removeChild(s),s=null)};i(),document.addEventListener("keyup",function(e){if(e.keyCode===r[0]){if(clearTimeout(a),r.shift(),0<r.length)return void(a=setTimeout(i,1e3));i(),c(),(s=document.createElement("div"))[l].width="50%",s[l].maxWidth="1000px",s[l].height="85%",s[l].border="1px rgba(0, 0, 0, 0.2) solid",s[l].background="rgba(255, 255, 255, 0.99)",s[l].borderRadius="4px",s[l].position="fixed",s[l].top="50%",s[l].left="50%",s[l].transform="translateY(-50%) translateX(-50%)",s[l].boxShadow="0 2px 5px rgba(0, 0, 0, 0.3), 0 0 100vh 100vw rgba(0, 0, 0, 0.5)",s[l].zIndex="2147483645";var t=document.createElement("textarea");t.value=JSON.stringify(JSON.parse(n),null,2),t[l].border="none",t[l].display="block",t[l].boxSizing="border-box",t[l].width="100%",t[l].height="100%",t[l].background="transparent",t[l].padding="22px",t[l].fontFamily="monospace",t[l].fontSize="14px",t[l].color="#333",t[l].resize="none",t[l].outline="none",t.readOnly=!0,s.appendChild(t),document.body.appendChild(s),t.addEventListener("click",function(e){e.stopImmediatePropagation()}),t.select(),document.addEventListener("keydown",c),document.addEventListener("click",c)}})}o.isEmbedded=-1!==window.location.search.indexOf("_ab=0&_fd=0&_sc=1"),o.path=o.state.path||window.location.pathname,o.basePath=o.state.locale_root_url.concat("/apps/locksmith").replace(/^\/\//,"/"),o.reloading=!1,o.util.console=window.console||{log:function(){},error:function(){}},o.util.makeUrl=function(e,t){var n,s=o.basePath+e,r=[],a=o.cache();for(n in a)r.push(n+"="+encodeURIComponent(a[n]));for(n in t)r.push(n+"="+encodeURIComponent(t[n]));return o.state.customer_id&&(r.push("customer_id="+encodeURIComponent(o.state.customer_id)),r.push("customer_id_signature="+encodeURIComponent(o.state.customer_id_signature))),s+=(-1===s.indexOf("?")?"?":"&")+r.join("&")},o._initializeCallbacks=[],o.on=function(e,t){if("initialize"!==e)throw'Locksmith.on() currently only supports the "initialize" event';o._initializeCallbacks.push(t)},o.initializeSession=function(e){if(!o.isEmbedded){var t=!1,n=!0,s=!0;(e=e||{}).silent&&(s=n=!(t=!0)),o.ping({silent:t,spinner:n,reload:s,callback:function(){o._initializeCallbacks.forEach(function(e){e()})}})}},o.cache=function(e){var t={};try{var n=function r(e){return(document.cookie.match("(^|; )"+e+"=([^;]*)")||0)[2]};t=JSON.parse(decodeURIComponent(n("locksmith-params")||"{}"))}catch(u){}if(e){for(var s in e)t[s]=e[s];document.cookie="locksmith-params=; expires=Thu, 01 Jan 1970 00:00:00 GMT; path=/",document.cookie="locksmith-params="+encodeURIComponent(JSON.stringify(t))+"; path=/"}return t},o.cache.cart=o.state.cart,o.cache.cartLastSaved=null,o.params=o.cache(),o.util.reload=function(){o.reloading=!0;try{window.location.href=window.location.href.replace(/#.*/,"")}catch(u){o.util.console.error("Preferred reload method failed",u),window.location.reload()}},o.cache.saveCart=function(e){if(!o.cache.cart||o.cache.cart===o.cache.cartLastSaved)return e?e():null;var t=o.cache.cartLastSaved;o.cache.cartLastSaved=o.cache.cart,reqwest({url:"/cart/update.json",method:"post",type:"json",data:{attributes:{locksmith:o.cache.cart}},complete:e,error:function(e){if(o.cache.cartLastSaved=t,!o.reloading)throw e}})},o.util.spinnerHTML='<style>body{background:#FFF}@keyframes spin{from{transform:rotate(0deg)}to{transform:rotate(360deg)}}#loading{display:flex;width:100%;height:50vh;color:#777;align-items:center;justify-content:center}#loading .spinner{display:block;animation:spin 600ms linear infinite;position:relative;width:50px;height:50px}#loading .spinner-ring{stroke:currentColor;stroke-dasharray:100%;stroke-width:2px;stroke-linecap:round;fill:none}</style><div id="loading"><div class="spinner"><svg width="100%" height="100%"><svg preserveAspectRatio="xMinYMin"><circle class="spinner-ring" cx="50%" cy="50%" r="45%"></circle></svg></svg></div></div>',o.util.clobberBody=function(e){document.body.innerHTML=e},o.util.clobberDocument=function(e){e.responseText&&(e=e.responseText),document.documentElement&&document.removeChild(document.documentElement);var t=document.open("text/html","replace");t.writeln(e),t.close(),setTimeout(function(){var e=t.querySelector("[autofocus]");e&&e.focus()},100)},o.util.serializeForm=function(e){if(e&&"FORM"===e.nodeName){var t,n,s={};for(t=e.elements.length-1;0<=t;t-=1)if(""!==e.elements[t].name)switch(e.elements[t].nodeName){case"INPUT":switch(e.elements[t].type){default:case"text":case"hidden":case"password":case"button":case"reset":case"submit":s[e.elements[t].name]=e.elements[t].value;break;case"checkbox":case"radio":e.elements[t].checked&&(s[e.elements[t].name]=e.elements[t].value);break;case"file":}break;case"TEXTAREA":s[e.elements[t].name]=e.elements[t].value;break;case"SELECT":switch(e.elements[t].type){case"select-one":s[e.elements[t].name]=e.elements[t].value;break;case"select-multiple":for(n=e.elements[t].options.length-1;0<=n;n-=1)e.elements[t].options[n].selected&&(s[e.elements[t].name]=e.elements[t].options[n].value)}break;case"BUTTON":switch(e.elements[t].type){case"reset":case"submit":case"button":s[e.elements[t].name]=e.elements[t].value}}return s}},o.util.on=function(e,a,o,t){t=t||document;var i="locksmith-"+e+a,n=function(e){var t=e.target,n=e.target.parentElement,s=t.className.baseVal||t.className||"",r=n.className.baseVal||n.className||"";("string"==typeof s&&-1!==s.split(/\s+/).indexOf(a)||"string"==typeof r&&-1!==r.split(/\s+/).indexOf(a))&&!e[i]&&(e[i]=!0,o(e))};t.attachEvent?t.attachEvent(e,n):t.addEventListener(e,n,!1)},o.util.enableActions=function(e){o.util.on("click","locksmith-action",function(e){e.preventDefault();var t=e.target;t.dataset.confirmWith&&!confirm(t.dataset.confirmWith)||(t.disabled=!0,t.innerText=t.dataset.disableWith,o.post("/action",t.dataset.locksmithParams,{spinner:!1,type:"text",success:function(e){(e=JSON.parse(e.responseText)).message&&alert(e.message),o.util.reload()}}))},e)},o.util.inject=function(e,t){var n=["data","locksmith","append"];if(-1!==t.indexOf(n.join("-"))){var s=document.createElement("div");s.innerHTML=t,e.appendChild(s)}else e.innerHTML=t;var r,a,o=e.querySelectorAll("script");for(a=0;a<o.length;++a){r=o[a];var i=document.createElement("script");if(r.type&&(i.type=r.type),r.src)i.src=r.src;else{var l=document.createTextNode(r.innerHTML);i.appendChild(l)}e.appendChild(i)}var c=e.querySelector("[autofocus]");c&&c.focus()},o.post=function(e,t,n){!1!==(n=n||{}).spinner&&o.util.clobberBody(o.util.spinnerHTML);var s={};n.container===document?(s.layout=1,n.success=function(e){document.getElementById(n.container);o.util.clobberDocument(e)}):n.container&&(s.layout=0,n.success=function(e){var t=document.getElementById(n.container);o.util.inject(t,e),t.id===t.firstChild.id&&t.parentElement.replaceChild(t.firstChild,t)}),n.form_type&&(t.form_type=n.form_type),o.loading=!0;var r=o.util.makeUrl(e,s);reqwest({url:r,method:"post",type:n.type||"html",data:t,complete:function(){o.loading=!1},error:function(e){if(!o.reloading)if("dashboard.weglot.com"!==window.location.host){if(!n.silent)throw alert("Something went wrong! Please refresh and try again."),e;console.error(e)}else console.error(e)},success:n.success||o.util.clobberDocument})},o.postResource=function(e,t){e.path=o.path,e.search=window.location.search,e.state=o.state,e.passcode&&(e.passcode=e.passcode.trim()),e.email&&(e.email=e.email.trim()),e.state.cart=o.cache.cart,e.locksmith_json=o.jsonTag,e.locksmith_json_signature=o.jsonTagSignature,o.post("/resource",e,t)},o.ping=function(e){if(!o.isEmbedded){e=e||{};var t=function(){e.reload?o.util.reload():"function"==typeof e.callback&&e.callback()};o.post("/ping",{path:o.path,search:window.location.search,state:o.state},{spinner:!!e.spinner,silent:"undefined"==typeof e.silent||e.silent,type:"text",success:function(e){(e=JSON.parse(e.responseText)).messages&&0<e.messages.length&&o.showMessages(e.messages),e.cart&&o.cache.cart!==e.cart?(o.cache.cart=e.cart,o.cache.saveCart(function(){t(),e.cart&&e.cart.match(/^.+:/)&&o.util.reload()})):t()}})}},o.timeoutMonitor=function(){var e=o.cache.cart;o.ping({callback:function(){e!==o.cache.cart||setTimeout(function(){o.timeoutMonitor()},6e4)}})},o.showMessages=function(e){var t=document.createElement("div");t.style.position="fixed",t.style.left=0,t.style.right=0,t.style.bottom="-50px",t.style.opacity=0,t.style.background="#191919",t.style.color="#ddd",t.style.transition="bottom 0.2s, opacity 0.2s",t.style.zIndex=999999,t.innerHTML="        <style>          .locksmith-ab .locksmith-b { display: none; }          .locksmith-ab.toggled .locksmith-b { display: flex; }          .locksmith-ab.toggled .locksmith-a { display: none; }          .locksmith-flex { display: flex; flex-wrap: wrap; justify-content: space-between; align-items: center; padding: 10px 20px; }          .locksmith-message + .locksmith-message { border-top: 1px #555 solid; }          .locksmith-message a { color: inherit; font-weight: bold; }          .locksmith-message a:hover { color: inherit; opacity: 0.8; }          a.locksmith-ab-toggle { font-weight: inherit; text-decoration: underline; }          .locksmith-text { flex-grow: 1; }          .locksmith-cta { flex-grow: 0; text-align: right; }          .locksmith-cta button { transform: scale(0.8); transform-origin: left; }          .locksmith-cta > * { display: block; }          .locksmith-cta > * + * { margin-top: 10px; }          .locksmith-message a.locksmith-close { flex-grow: 0; text-decoration: none; margin-left: 15px; font-size: 30px; font-family: monospace; display: block; padding: 2px 10px; }                    @media screen and (max-width: 600px) {            .locksmith-wide-only { display: none !important; }            .locksmith-flex { padding: 0 15px; }            .locksmith-flex > * { margin-top: 5px; margin-bottom: 5px; }            .locksmith-cta { text-align: left; }          }                    @media screen and (min-width: 601px) {            .locksmith-narrow-only { display: none !important; }          }        </style>      "+e.map(function(e){return'<div class="locksmith-message">'+e+"</div>"}).join(""),document.body.appendChild(t),document.body.style.position="relative",document.body.parentElement.style.paddingBottom=t.offsetHeight+"px",setTimeout(function(){t.style.bottom=0,t.style.opacity=1},50),o.util.on("click","locksmith-ab-toggle",function(e){e.preventDefault();for(var t=e.target.parentElement;-1===t.className.split(" ").indexOf("locksmith-ab");)t=t.parentElement;-1!==t.className.split(" ").indexOf("toggled")?t.className=t.className.replace("toggled",""):t.className=t.className+" toggled"}),o.util.enableActions(t)}}()}();</script>
      <script data-locksmith>Locksmith.cache.cart={{_0h.attributes.locksmith|json}}</script>{%endcapture%}{%capture locksmith_customer_register_form%}{%capture _h%}<main class="quiz quiz--account">

  <quiz-steps class="quiz-steps">

    <div class="quiz-steps__inner">

      <section class="quiz-step quiz-step--form quiz-step--active">

        <div class="split-page">

          <div class="split-page__left">

            <div class="split-page__content-wrapper">
              
              <div class="split-page__header">
                
              </div>

              <div class="split-page__content">{%form 'create_customer', name: 'create', class: 'form', id: 'register-customer'%}<input type="hidden" name="customer[email]" name="customer[email]" required="required" id="real-form-email">
                  <input type="hidden" name="customer[password]" name="customer[password]" required="required" id="real-form-password">
                  <input type="hidden" name="customer[first_name]" id="real-form-firstname">
                  <input type="hidden" name="customer[last_name]" id="real-form-lastname">

                  <input type="hidden" name="customer[tags]" id="real-form-kyc">
                  <input type="hidden" name="customer[name]" id="real-form-note" data-hidden-note-input>{%if form.errors%}<div class="banner banner--error form__banner" id="login-form-error">
                      <span class="banner__ribbon">{%render 'icon' with 'form-error'%}</span>
                      <div class="banner__content">{{form.errors|default_errors}}</div>
                    </div>{%endif%}{%endform%}<form id="customer-register-form" method="post" action="#">

                  <split-page-step class="quiz-page-content page-content page-content--small split-page-step--visible">

                    <revealing-form class="revealing-form">
                    
                      <revealing-form-input class="input revealing-form-input revealing-form-input--visible">
                        <input type="text" placeholder="{{'quiz.register.full_name_placeholder'|t}}" id="customer[full_name]" class="input__field" name="customer[full_name]" required="required" autocomplete="email" {%if form.errors contains 'email'%}aria-invalid="true" aria-describedby="register-form-error"{%endif%} data-bound-split-name-input data-bound-first-name-input="real-form-firstname" data-bound-last-name-input="real-form-lastname">
                        <label for="customer[email]" class="input__label">{{'quiz.register.full_name'|t}}</label>
                      </revealing-form-input>
                      
                      <revealing-form-input class="input revealing-form-input">
                        <input type="email" placeholder="{{'customer.register.email'|t}}" id="customer[email]" class="input__field" name="customer[email]" required="required" autocomplete="email" {%if form.errors contains 'email'%}aria-invalid="true" aria-describedby="register-form-error"{%endif%} data-bound-hidden-input="real-form-email">
                        <label for="customer[email]" class="input__label">{{'customer.register.email'|t}}</label>
                      </revealing-form-input>

                      <revealing-form-input class="input revealing-form-input">
                        <input type="password" placeholder="{{'customer.register.password_placeholder'|t}}" id="customer[password]" class="input__field" name="customer[password]" required="required" minlength="5" autocomplete="new-password" {%if form.errors contains 'password'%}aria-invalid="true" aria-describedby="register-form-error"{%endif%} data-bound-hidden-input="real-form-password">
                        <label for="customer[password]" class="input__label">{{'customer.register.password'|t}}</label>
                      </revealing-form-input>

                      <revealing-form-actions class="form__actions revealing-form__actions">{%if section[_5].show_customer_info_step%}<button type="button" class="form__submit button button--primary" data-split-page-next>
                            <span class="button__text">{{'quiz.general.continue'|t}}</span>
                            <span class="button__icon">{%render 'icon' with 'nav-arrow-right'%}</span>
                          </button>{%else%}<button type="submit" class="form__submit button button--primary" form="register-customer">
                            <span class="button__text">{{'quiz.general.continue'|t}}</span>
                            <span class="button__icon">{%render 'icon' with 'nav-arrow-right'%}</span>
                          </button>{%endif%}</revealing-form-actions>

                    </revealing-form>

                  </split-page-step>{%if section[_5].show_customer_info_step%}<split-page-step class="quiz-page-content page-content page-content--small">

                      <div class="split-page__step-navigation">
                        <button class="quiz-navigation-button" type="button" data-split-page-prev>
                          <span class="quiz-navigation-button__icon">{%render 'icon' with 'chevron-back'%}</span>
                          <span class="quiz-navigation-button__text">Back</span>
                        </button>
                      </div>

                      <div class="input">
                        <p class="input__field">{{'quiz.inline-account.content.how_did_you_hear'|t}}</p>
                        <label class="input__label">{{'quiz.general.required'|t}}</label>
                      </div>{%for block in section.blocks%}{%if block.type == "kyc_option"%}<div class="kyc-option-radio input input--radio">
                            <div class="checkbox-container">
                              <input
                                type="radio"
                                class="checkbox"
                                name="customer[tags]"
                                id="customer[tags][{{forloop.index}}]"
                                value="Referred By: {{block.settings.title}}"
                                data-bound-hidden-input="real-form-kyc"
{%if section[_5].kyc_required == true%}required{%endif%}{%if block[_5][_l] != ""%}data-sub-option-container="{{block.id}}-options"{%else%}data-sub-option-container{%endif%}>
                              <label for="customer[tags][{{forloop.index}}]" class="">{{block.settings.title}}</label>
                            </div>
                          </div>{%if block[_5][_l] != ""%}{%if block[_5][_l] == "select"%}{%if block[_5].options != blank%}{%assign options_array=block[_5].options|newline_to_br|split:'<br />'%}<div class="input input--select input--sub-option" id="{{block.id}}-options" style="display: none">{%capture option_name%}{%if block[_5].option_name != blank%}{{block.settings.option_name}}{%else%}
                                      Option {{forloop.index}} Details
{%endif%}{%endcapture%}<select name="customer[note][{{option_name}}]" id="customer[note][{{option_name}}]" data-sub-option-input>
                                    <option value="" data-null-value>{{block.settings.placeholder|default:"Choose your Option"}}</option>{%for option in options_array%}<option value="{{option}}">{{option}}</option>{%endfor%}</select>
                                  <label for="customer[note][{{option_name}}]" class="visually-hidden">{{block.settings.title}}</label>

                                </div>{%endif%}{%elsif block[_5][_l] == "vets"%}{%if settings.quiz_veterinarians != blank%}{%assign options_array=settings.quiz_veterinarians|newline_to_br|split:'<br />'%}<div class="input input--select input--sub-option" id="{{block.id}}-options" style="display: none">{%capture option_name%}{%if block[_5].option_name != blank%}{{block.settings.option_name}}{%else%}
                                      Option {{forloop.index}} Details
{%endif%}{%endcapture%}<styled-select searchable observe> 
                                    <div>

                                      <select 
                                        id="customer[note][{{option_name}}]" 
                                        name="customer[note][{{option_name}}]" 
                                        data-sub-option-input 
                                        data-sub-option-input-reveal-value="Not Listed" 
                                        data-sub-option-input-reveal-input="{{block.id}}-other-input-container" 
                                        data-bound-hidden-input="real-form-kyc">

                                        <option value="" data-null-value>{{block.settings.placeholder|default:"Choose your Option"}}</option>{%for option in options_array%}<option value="{{option}}">{{option}}</option>{%endfor%}</select>
                                    </div>
                                  </styled-select>

                                  <label for="customer[note][{{option_name}}]" class="visually-hidden">{{block.settings.title}}</label>

                                  <div other-input id="{{block.id}}-other-input-container" class="hidden">
                                    <input type="text" name="customer[note][{{option_name}}]" placeholder="{{block.settings.placeholder|default:"Choose your Option"}}" data-sub-option-input id="{{block.id}}-other-input" />
                                    <label for="customer[note][{{option_name}}]" class="visually-hidden">{{block.settings.title}}</label>
                                  </div>

                                </div>{%endif%}{%elsif block[_5][_l] == "text"%}<div class="input input--text input--sub-option" id="{{block.id}}-options" style="display: none">{%capture option_name%}{%if block[_5].option_name != blank%}{{block.settings.option_name}}{%else%}
                                    Option {{forloop.index}} Details
{%endif%}{%endcapture%}<input type="text" name="customer[note][{{option_name}}]" id="customer[note][{{option_name}}]" placeholder="{{block.settings.placeholder|default:"Choose your Option"}}" data-sub-option-input />
                                <label for="customer[note][{{option_name}}]" class="visually-hidden">{{block.settings.title}}</label>

                              </div>{%endif%}{%endif%}{%endif%}{%endfor%}<div class="form__actions">

                        <button type="submit" class="form__submit button button--primary">
                          <span class="button__text">{{'quiz.general.create_account'|t}}</span>
                          <span class="button__icon">{%render 'icon' with 'nav-arrow-right'%}</span>
                        </button>{%comment%}<button type="button" class="button button--primary" data-split-page-prev>
                          <span class="button__icon">{%render 'icon' with 'nav-arrow-left'%}</span>
                          <span class="button__text">Back</span>
                        </button>{%endcomment%}<button type="submit" class="button button--link">
                          <span class="button__text">{{'quiz.general.skip'|t}}</span>
                          <span class="button__icon">{%render 'icon' with 'nav-arrow-right'%}</span>
                        </button>

                      </div>

                    </split-page-step>{%endif%}</form>

              </div>

              <div class="split-page__footer">{%if section[_5].footer_text != blank%}<div class="quiz-terms text--xsmall">{{section.settings.footer_text}}</div>{%endif%}</div>
              
            </div>
            
          </div>
          
          <div class="split-page__right hidden-pocket">{%if section[_5].page_banner != blank%}<img class="split-page__image" sizes="(max-width: 740px) calc(100vw - 96px), (max-width: 999px) calc(100vw - 160px), 600px" loading="lazy" {%render 'image-attributes', _shopify_image: section.settings.page_banner, sizes: '600,700,800,1000,1200,1400'%}>{%endif%}</div>

        </div>

      </section>
    
    </div>

  </quiz-steps>

</main>

<style>

  #shopify-section-{{section.id}} {
{%if section[_5][_x] == _d%}{%assign section_block_background=settings.background%}{%else%}{%assign section_block_background=section[_5][_x]%}{%endif%}{%if section[_5][_9] == _d%}{%assign heading_color=settings.heading_color%}{%assign text_color=settings[_9]%}{%else%}{%assign heading_color=section[_5][_9]%}{%assign text_color=section[_5][_9]%}{%endif%}{%if section[_5][_t] == _d%}{%assign button_background=settings.primary_button_background%}{%else%}{%assign button_background=section[_5][_t]%}{%endif%}{%if section[_5][_p] == _d%}{%assign button_text_color=settings.primary_button_text_color%}{%else%}{%assign button_text_color=section[_5][_p]%}{%endif%}
  
    --heading-color: {{heading_color.red}}, {{heading_color.green}}, {{heading_color.blue}};
    --text-color: {{text_color.red}}, {{text_color.green}}, {{text_color.blue}};
    --primary-button-background: {{button_background.red}}, {{button_background.green}}, {{button_background.blue}};
    --primary-button-text-color: {{button_text_color.red}}, {{button_text_color.green}}, {{button_text_color.blue}};
  
    --prev-next-button-background: {{settings.background.red}}, {{settings.background.green}}, {{settings.background.blue}};
    --prev-next-button-color: {{settings.text_color.red}}, {{settings.text_color.green}}, {{settings.text_color.blue}};
  
    --section-block-background: {{section_block_background.red}}, {{section_block_background.green}}, {{section_block_background.blue}};
    
  }

</style>

<script src="{{'quiz.js'|asset_url}}"></script>
<script src="{{'quiz-account.js'|asset_url}}"></script>{%endcapture%}{{_h|replace:'</form>', '<input type="hidden" name="return_to" value=""></form>'}}

      <script data-locksmith>
        
        
        window.document.title = "";
        var redirectUrl = window.location.href.replace('/account/register', '/');
        var inputNodes = document.querySelectorAll('input[name=return_to]');
        for (var i = 0; i < inputNodes.length; i++) {
          inputNodes[i].value = redirectUrl;
        }
      </script>{%endcapture%}{%capture locksmith_customer_login_form%}{%capture _h%}{%capture status%}{%form 'recover_customer_password'%}{%if form.posted_successfully?%}{%assign is_recover_posted_successfully=true%}{%else%}{%assign is_recover_posted_successfully=false%}{%endif%}{%endform%}{%endcapture%}<section class="quiz quiz--account-login">

  <div class="split-page">

    <div class="split-page__left">

      <div class="split-page__content-wrapper" id="login-form-container" style="display: flex">
        
        <div class="split-page__header">

        </div>

        <div class="split-page__content">

          <div class="page-header">
            <div class="page-header__text-wrapper text-container">
              <h1 class="heading h2">{{'customer.login.title'|t}}</h1>
              <p>{{'customer.login.instructions'|t}}</p>
            </div>
          </div>
          
          <div class="page-content page-content--small">
            <div class="account__block-list">{%for block in section.blocks%}<div class="account__block-item" {{block.shopify_attributes}}>{%case block.type%}{%when '@app'%}{%render block%}{%when 'liquid'%}{{block.settings.liquid}}{%when 'login'%}{%form 'customer_login', name: 'login', class: 'form'%}{%if form.errors%}<div class="banner banner--error form__banner" id="login-form-error">
                            <span class="banner__ribbon">{%render 'icon' with 'form-error'%}</span>
                            <p class="banner__content">{{form.errors.messages['form']}}</p>
                          </div>{%endif%}<div class="input">
                          <input type="email" id="customer[email]" autocomplete="email" class="input__field" name="customer[email]" placeholder="{{'customer.login.email'|t}}" required="required" {%if form.errors%}aria-invalid="true" aria-describedby="login-form-error"{%endif%}>
                          <label for="customer[email]" class="input__label">{{'customer.login.email'|t}}</label>
                        </div>
          
                        <div class="input">
                          <input type="password" id="customer[password]" class="input__field" name="customer[password]" placeholder="{{'customer.login.password'|t}}" required="required" autocomplete="current-password" {%if form.errors%}aria-invalid="true" aria-describedby="login-form-error"{%endif%}>
                          <label for="customer[password]" class="input__label">{{'customer.login.password'|t}}</label>
          
                          <button type="button" class="input__field-link link text--xsmall text--subdued" data-action="switch-login-form">{{'customer.login.forgot_password'|t}}</button>
                        </div>

                        <div class="form__actions">

                          <button type="submit" is="loader-button" class="form__submit button button--primary">
                            <span class="button__text">{{'customer.login.submit'|t}}</span>
                            <span class="button__icon">{%render 'icon' with 'nav-arrow-right'%}</span>
                          </button>
          
                        </div>{%endform%}{%endcase%}</div>{%endfor%}</div>
          </div>

        </div>

        <div class="split-page__footer">
          
          <span class="form__secondary-action text--subdued">
{{'customer.login.new_customer'|t}}
            <a href="{{routes.account_register_url}}" class="link">{{'customer.login.create_account'|t}}</a>
          </span>

        </div>
        
      </div>

      <div class="split-page__content-wrapper" id="recover-form-container" style="display: none">{%form 'recover_customer_password', name: 'recover', class: 'form'%}<div class="split-page__header">

          </div>

          <div class="split-page__content">

            <div class="page-header">
              <div class="page-header__text-wrapper text-container">
                <h1 class="heading h2">{{'customer.recover_password.title'|t}}</h1>{%unless is_recover_posted_successfully%}<p>{{'customer.recover_password.instructions'|t}}</p>{%endunless%}</div>
            </div>
          
            <div class="page-content page-content--small">{%if form.errors%}<div class="banner banner--error form__banner" id="recovery-form-error">
                  <span class="banner__ribbon">{%render 'icon' with 'form-error'%}</span>
                  <p class="banner__content">{{form.errors.messages['form']}}</p>
                </div>{%endif%}{%if form.posted_successfully?%}<div class="banner banner--success form__banner">
                  <span class="banner__ribbon">{%render 'icon' with 'form-success'%}</span>
                  <p class="banner__content">{{'customer.recover_password.success'|t}}</p>
                </div>{%else%}<div class="input">
                  <input type="email" id="customer[recover_email]" class="input__field" name="email" placeholder="{{'customer.recover_password.email'|t}}" required="required" {%if form.errors%}aria-invalid="true" aria-describedby="recovery-form-error"{%endif%}>
                  <label for="customer[recover_email]" class="input__label">{{'customer.recover_password.email'|t}}</label>
                </div>

                <div class="form__actions">
                  <button type="submit" is="loader-button" class="form__submit button button--primary">
                    <span class="button__text">{{'customer.recover_password.submit'|t}}</span>
                  </button>
                </div>{%endif%}</div>

          </div>

          <div class="split-page__footer">

            <span class="form__secondary-action text--subdued">
{{'customer.recover_password.remember_password'|t}}
              <button type="button" class="link" data-action="switch-login-form">{{'customer.recover_password.back_to_login'|t}}</button>
            </span>

          </div>{%endform%}</div>

    </div>
    
    <div class="split-page__right hidden-pocket">{%if section[_5].page_banner != blank%}<img class="split-page__image" sizes="(max-width: 740px) calc(100vw - 96px), (max-width: 999px) calc(100vw - 160px), 600px" loading="lazy" {%render 'image-attributes', _shopify_image: section.settings.page_banner, sizes: '600,700,800,1000,1200,1400'%}>{%endif%}</div>

  </div>

</section>

<script>
  // The script for this is very minimal so we just embed it here
  window.addEventListener('DOMContentLoaded', () => {
    const loginFormElement = document.getElementById('login-form-container'),
      recoverFormElement = document.getElementById('recover-form-container');

      console.log(loginFormElement);

    const switchForms = () => {

      console.log(loginFormElement.style.display);
      console.log(recoverFormElement.style.display);

      loginFormElement.style.display = (window.getComputedStyle(loginFormElement).display) === 'flex' ? 'none' : 'flex';
      recoverFormElement.style.display = (window.getComputedStyle(recoverFormElement).display) === 'flex' ? 'none' : 'flex';
    }
{%if is_recover_posted_successfully%}
      switchForms();
{%else%}
      if (window.location.hash === '#recover') {
        switchForms();
      }
{%endif%}

    Array.from(document.querySelectorAll('[data-action="switch-login-form"]')).forEach((button) => {
      button.addEventListener('click', () => switchForms());
    });
  });
</script>

<style>

  #shopify-section-{{section.id}} {
{%if section[_5][_x] == _d%}{%assign section_block_background=settings.background%}{%else%}{%assign section_block_background=section[_5][_x]%}{%endif%}{%if section[_5][_9] == _d%}{%assign heading_color=settings.heading_color%}{%assign text_color=settings[_9]%}{%else%}{%assign heading_color=section[_5][_9]%}{%assign text_color=section[_5][_9]%}{%endif%}{%if section[_5][_t] == _d%}{%assign button_background=settings.primary_button_background%}{%else%}{%assign button_background=section[_5][_t]%}{%endif%}{%if section[_5][_p] == _d%}{%assign button_text_color=settings.primary_button_text_color%}{%else%}{%assign button_text_color=section[_5][_p]%}{%endif%}
  
    --heading-color: {{heading_color.red}}, {{heading_color.green}}, {{heading_color.blue}};
    --text-color: {{text_color.red}}, {{text_color.green}}, {{text_color.blue}};
    --primary-button-background: {{button_background.red}}, {{button_background.green}}, {{button_background.blue}};
    --primary-button-text-color: {{button_text_color.red}}, {{button_text_color.green}}, {{button_text_color.blue}};
  
    --prev-next-button-background: {{settings.background.red}}, {{settings.background.green}}, {{settings.background.blue}};
    --prev-next-button-color: {{settings.text_color.red}}, {{settings.text_color.green}}, {{settings.text_color.blue}};
  
    --section-block-background: {{section_block_background.red}}, {{section_block_background.green}}, {{section_block_background.blue}};
    
  }

</style>{%endcapture%}{{_h|replace:'</form>', '<input type="hidden" name="return_url" value=""></form>'}}

      <script data-locksmith>
        
        
        window.document.title = "";
        var redirectUrl = window.location.href.replace('/account/login', '/');
        var inputNodes = document.querySelectorAll('input[name=return_url]');
        for (var i = 0; i < inputNodes.length; i++) {
          inputNodes[i].value = redirectUrl;
        }

        document.querySelectorAll('a[data-locksmith][href*="/customer_identity/sso_hint"]').forEach(function(node) {
          node.search = `return_to=${encodeURIComponent(window.location.href)}`;
        });
      </script>{%endcapture%}{%assign customer_login_form=locksmith_customer_login_form%}{%assign customer_register_form=locksmith_customer_register_form%}{%unless _lock_guest_content%}{%capture locksmith_guest_content%}{%if _lock_guest_content != blank%}{{_lock_guest_content}}{%else%}<div class="page-width">
  <p>This content is protected. Please log in with your customer account to continue.</p>

  <div>{{locksmith_customer_login_form}}</div>
</div>{%endif%}{%endcapture%}{%endunless%}{%if locksmith_access_denied_content == blank%}{%capture locksmith_access_denied_content%}{%if _0d%}{%if _lock_access_denied_content != blank%}{{_lock_access_denied_content}}{%else%}<div class="page-width">
  <p>This content is protected, but it doesn’t look like you have access. If you feel this is a mistake, please contact the store owner.</p>
</div>{%endif%}{%if locksmith_manual_lock%}<p><a href="#" onclick="window.parent.__locksmith_hide_frame(); return false;">&lsaquo; Back</a></p>{%endif%}{%elsif locksmith_requires_customer%}{{locksmith_guest_content}}{%else%}{%if _lock_access_denied_content != blank%}{{_lock_access_denied_content}}{%else%}<div class="page-width">
  <p>This content is protected, but it doesn’t look like you have access. If you feel this is a mistake, please contact the store owner.</p>
</div>{%endif%}{%endif%}{%if _09.name contains "Debut"%}<style>
            /* fix for Debut+Gempages, per Gempages technical support */
            html.gemapp.video {
              display: block;
            }
          </style>{%endif%}{%endcapture%}{%endif%}{%capture locksmith_passcode_form%}<p id="locksmith-passcode-form-content"><strong>…</strong></p>
      <script>Locksmith.postResource({}, {spinner: false, container: 'locksmith-passcode-form-content', form_type: 'passcode'});</script>{%endcapture%}{%capture locksmith_email_form%}<p id="locksmith-newsletter-form-content"><strong>…</strong></p>
      <script>Locksmith.postResource({}, {spinner: false, container: 'locksmith-newsletter-form-content', form_type: 'mailing_list'});</script>{%endcapture%}{%capture locksmith_confirmation_form%}<p id="locksmith-confirmation-form-content"><strong>…</strong></p>
      <script>Locksmith.postResource({}, {spinner: false, container: 'locksmith-confirmation-form-content', form_type: 'confirmation'});</script>{%endcapture%}{%assign _h=_05%}{%endcapture%}