{% if product_description == blank %}
  {% assign product_description = product.description | split: "<h5>Ingredients</h5>" | first | default: '$PRODUCT_DESCRIPTION' %}
{% endif %}

{% if product_id == blank %}
  {% assign product_id = product.id | default: "$PRODUCT_ID" %}
{% endif %}

{% if product_image != blank %}
  {% assign product_image = product.featured_image | image_url %}
{% else %}
  {% assign product_image = '$PRODUCT_IMAGE_URL' %}
{% endif %}

{% if product_title == blank %}
  {% assign product_title = product.title | default: '$PRODUCT_TITLE' %}
{% endif %}

{% if product_url == blank %}
  {% assign product_url = product.url | default: '$PRODUCT_URL' %}
{% endif %}

<modal-content id="quiz-popup--recipe-{{ product_id }}" class="modal recipe-modal modal--quiz">

  <div class="modal__overlay"></div>

  <div class="modal__content">

    <button type="button" class="modal__close-button tap-area" data-action="close" title="{{ 'general.accessibility.close' | t | escape }}">
      {%- render 'icon' with 'close' -%}
    </button>

    <div class="newsletter-modal">

      <img class="newsletter-modal__image" loading="lazy" src="{{ product_image }}" alt="{{ product_title }} image">

      <div class="newsletter-modal__content newsletter-modal__content--extra text-container text--center">

        <h3 class="recipe-modal__title">{{ product_title }}</h3>
        
        <div class="recipe-modal__description rte">
          {{ product_description }}
        </div>
        
        <div class="recipe-modal__actions">
          <a href="{{ product_url }}" target="_blank" class="button button--text">
            <span class="button__text">{{ 'quiz.results.see_ingredients' | t }}</span>
            <span class="button__icon">{% render 'icon' with 'close' %}</span>
          </a>
        </div>
        
      </div>

    </div>
  </div>
  
</modal-content>