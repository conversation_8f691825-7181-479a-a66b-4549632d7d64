{%- assign link_classes = 'link-bar__link link--animated text--large' -%}
{%- assign current_link_class = 'account-link-current' -%}

<div class="link-bar hidden-phone">
  <div class="container">
    <div class="link-bar__wrapper">
      <ul class="link-bar__linklist list--unstyled" role="list">

        {%- assign current_link = request.path | strip -%}

        <li class="link-bar__link-item">
          {%- assign link_url = routes.account_url -%}
          <a href="{{ routes.account_url }}" class="{{ link_classes }} {% if link_url == current_link %}{{ current_link_class }}{% endif %}">{{ 'customer.orders.title' | t }}</a>
        </li>

        <li class="link-bar__link-item">
          {%- assign link_url = routes.account_url | strip -%}
          <a is="scroll-link" href="{{ request.path }}#my-vet" class="{{ link_classes }}">My Vet</a>
        </li>

        <li class="link-bar__link-item">
          {%- assign link_url = routes.account_url | strip -%}
          <a is="scroll-link" href="{{ request.path }}#my-dogs" class="{{ link_classes }}">My Dogs</a>
        </li>

        <li class="link-bar__link-item">
          {%- assign link_url = routes.account_url | strip -%}
          <a is="scroll-link" href="{{ routes.account_url }}#discounts" class="{{ link_classes }}">Discounts</a>
        </li>

        <li class="link-bar__link-item">
          {%- assign link_url = routes.account_addresses_url | strip -%}
          <a href="{{ routes.account_addresses_url }}" class="{{ link_classes }} {% if link_url == current_link %}{{ current_link_class }}{% endif %}">{{ 'customer.addresses.title' | t }}</a>
        </li>

        <li class="link-bar__link-item">
          {%- assign link_url = routes.root_url | append: "tools/bundle-subscriptions" | strip -%}
          <a href="{{ link_url }}" class="{{ link_classes }} {% if link_url == current_link %}{{ current_link_class }}{% endif %}">{{ 'customer.subscriptions.manage' | t }}</a>
        </li> 

        <li class="link-bar__link-item">
          <a href="{{ routes.account_logout_url }}" class="{{ link_classes }} text--subdued" data-no-instant>{{ 'customer.logout.title' | t }}</a>
        </li>

      </ul>
    </div>
  </div>
</div>