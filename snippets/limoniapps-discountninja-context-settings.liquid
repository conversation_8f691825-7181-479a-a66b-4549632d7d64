{"Cart": {"QuantitySpinnerMode": "disable"}, "DrawerCart": {}, "Checkout": {"LoadingCheckoutMessage1": "<div style=\"display: inline-flex;justify-content: center;align-items: center;\"><div class=\"limoniapps-discountninja-spinner\"></div><span style=\"font-size: 14px\"> Loading checkout...</span></div>", "LoadingCheckoutMessage2": "<div style=\"display: inline-flex;justify-content: center;align-items: center;\"><span style=\"font-size: 14px\">⚠️ Technical failure.<br/><br/>Failed to apply the discount</br>to the checkout.<br/>Return to the cart and check out to try again.</span></div>", "LoadingCheckoutMessage3": "<div style=\"display: inline-flex;justify-content: center;align-items: center;\"><div class=\"limoniapps-discountninja-spinner\"></div><span style=\"font-size: 14px\"> Redirecting to secure checkout...</span></div>", "CheckoutMode": "1,1F,N,NF", "DiscountCumulationMode": "highestwins", "DiscountCumulationModeCartLevel": "sum", "SubscriptionProductsMessage": "<span style=\"color: rgb(255, 0, 0); font-size: 14px;\">Discounts cannot be applied at checkout when subscription products are in the cart</span>", "HideAdditionalCheckoutButtons": true, "RedirectBuyNowButtons": true, "BlockCheckoutButton": true, "OverlayBackgroundColor": "rgba(0,0,0,0.7)", "PopupBackgroundColor": "rgba(255,255,255,1)"}, "Currency": {"BaseCurrency": "USD", "BaseCurrencySymbol": "$", "RoundPrices": false, "RoundToCents": 95}, "General": {"ShowPoweredBy": false, "ExitIntentMode": "default", "StoreShareableLinks": true, "StorePromotionCodes": true, "PercentageCalculationMode": "line", "ShareableLinksExpiration": 1, "PromotionCodesExpiration": 1}, "GWPSettings": {"PageRefreshText": "Updating your cart...", "FreeGiftAddedText": "A free gift has been added to the cart!", "AllowAddingManuallyRemovedGifts": false}, "PriceUpdate": {"PriceFormat": {"Other": {"Format": "\\{\\{DISCOUNTED_PRICE\\}\\} \\{\\{ORIGINAL_PRICE,STRIKETHROUGH\\}\\}", "CustomFormat": false, "Alignment": "left"}, "OtherMultiTier": {"Format": "As low as&nbsp;\\{\\{DISCOUNTED_PRICE_HIGHEST_TIER\\}\\}", "CustomFormat": true, "Alignment": "left"}, "Product": {"Format": "\\{\\{DISCOUNTED_PRICE\\}\\} \\{\\{ORIGINAL_PRICE,STRIKETHROUGH\\}\\}", "CustomFormat": false, "Alignment": "left"}, "CartProduct": {"Format": "\\{\\{DISCOUNTED_PRICE\\}\\} \\{\\{ORIGINAL_PRICE,STRIKETHROUGH\\}\\}", "CustomFormat": false}, "CartLine": {"Format": "\\{\\{DISCOUNTED_PRICE\\}\\} \\{\\{ORIGINAL_PRICE,STRIKETHROUGH\\}\\}", "CustomFormat": false}, "CartSubtotal": {"Format": "\\{\\{DISCOUNTED_PRICE\\}\\} \\{\\{ORIGINAL_PRICE,STRIKETHROUGH\\}\\}", "CustomFormat": false}, "DrawerCartProduct": {"Format": "\\{\\{DISCOUNTED_PRICE\\}\\}", "CustomFormat": false}, "DrawerCartLine": {"Format": "\\{\\{DISCOUNTED_PRICE\\}\\}", "CustomFormat": false}, "DrawerCartSubtotal": {"Format": "\\{\\{DISCOUNTED_PRICE\\}\\} \\{\\{ORIGINAL_PRICE,STRIKETHROUGH\\}\\}", "CustomFormat": false}}, "Enable": {"Home": true, "Product": true, "Collection": true, "Search": true, "Blog": true, "Custom": true, "Cart": true, "DrawerCart": true}, "Mode": {"ProductPrice": "custom", "ProductPriceMobile": "custom", "ProductBanner": "appblock", "CollectionProductPrice": "custom", "CollectionProductPriceMobile": "custom", "CollectionProductBadge": "custom", "CollectionProductCard": "custom", "CollectionProductHandle": "custom", "FeaturedProductPrice": "custom", "FeaturedProductBanner": "custom", "FeaturedProductCard": "custom", "FeaturedProductHandle": "custom", "CartRoot": "custom", "CartProductPrice": "custom", "CartProductComment": "custom", "CartLinePrice": "custom", "CartQuantity": "custom", "CartSubtotal": "custom", "CartSubtotalPrice": "custom", "CartPromotionSummary": "appblock", "CartBanner": "custom", "DrawerCartProductPrice": "custom", "DrawerCartProductComment": "custom", "DrawerCartLinePrice": "custom", "DrawerCartQuantity": "custom", "DrawerCartSubtotal": "custom", "DrawerCartSubtotalPrice": "custom", "CartItemSelector": "custom", "CartItemKeySelector": "custom", "CartItemKeyAttribute": "custom", "DrawerCartItemSelector": "custom", "DrawerCartItemKeySelector": "custom", "DrawerCartItemKeyAttribute": "custom", "DrawerCartRoot": "custom", "DrawerCartPromotionSummary": "custom", "ReloadPageAfterCartAdjustmentsOnCart": true, "ReloadPageAfterCartAdjustments": true, "CallbackAfterReloadPageForCartAdjustments": "custom"}, "HideZeroCents": true}, "StickyBar": {"AnimationExit": "animated fadeOutUpBig", "AnimationOnGoal": "animated rubberBand", "AnimationOnReminder": "animated shake", "ReminderDuration": 40000, "Cycle": {"DurationMilliseconds": 6000, "Mode": "cycle", "AnimationOnCyle": "animated fadeOut fadeIn"}}, "Notification": {"Delay": 10000, "HideMode": "minimize", "AnimationEnter": "animated fadeInUpBig", "AnimationEnterMobile": "animated fadeInUpBig", "AnimationExit": "animated fadeOutDownBig", "AnimationOnReminder": "animated rubberBand", "OfferCounterAnimate": false, "OfferCounter": true, "MinimizeButton": true, "ReminderDuration": 40000, "MinimizeAfterFirstView": true}, "CountdownClock": {"FormattingTemplateIn_Style1": "{d}[d] [0,1], {\\d}{h}[h] [1,1], {\\h}{m}[m] [2,1] [4,0] {\\m}[s] [3,1]", "FormattingTemplateIn_Style2": "{d}[d] [0,1], {\\d}{h}[h] [1,1], {\\h}{m}[m] [2,1] [4,0] {\\m}[s] [3,1]", "FormattingTemplateIn_Style3": "{d}[d] [0,1], {\\d}{h}[h] [1,1], {\\h}{m}[m] [2,1] [4,0] {\\m}[s] [3,1]", "FormattingTemplateIn_Style4": "[d]d [h]h [m]m [s]s", "FormattingTemplateIn_Style5": "[d]d [h]h [m]m [s]s", "FormattingTemplateOnDate": "dddd, MMMM Do YYYY", "FormattingTemplateOnTime": "h:mmA", "RestartFrequency": "oncepersession"}, "Badge": {"HideThirdPartyBadges": true}, "Label": {"UseSoldOutLabelProductPage": true, "UseSoldOutLabelCollectionPage": true, "UseFromLabelCollectionPage": true}, "ProductPageBanner": {"Mode": "best"}, "PromotionSummary": {"ShowComments": false, "HideSubtotal": true, "HideSubtotalDrawerCart": true, "NewFooter": true, "FooterSubtotal": {"Text": "<span style=\"font-size: 14px;\">\\{\\{SUBTOTAL\\}\\}</span>"}, "FooterSubtotalTextColor": "#333333", "FooterSubtotalLabel": {"Text": "<span style=\"font-size: 14px;\">Subtotal</span>"}, "FooterSubtotalLabelTextColor": "#333333", "FooterTotalDiscount": {"Text": "<span style=\"font-size: 14px;\">-\\{\\{TOTAL_DISCOUNT\\}\\}</span>"}, "FooterTotalDiscountTextColor": "#333333", "FooterTotalDiscountLabel": {"Text": "<span style=\"font-size: 14px;\">Discount (\\{\\{DISCOUNT_PERCENTAGE_TOTAL\\}\\})</span>"}, "FooterTotalDiscountLabelTextColor": "#333333", "FooterTotal": {"Text": "<span style=\"font-size: 16px;\"><b>\\{\\{TOTAL\\}\\}</b></span>"}, "FooterTotalTextColor": "#333333", "FooterTotalLabel": {"Text": "<span style=\"font-size: 16px;\"><b>Total</b></span>"}, "FooterTotalLabelTextColor": "#333333", "FooterDiscountedTotal": {"Text": "<span style=\"font-size: 16px;\"><b>\\{\\{DISCOUNTED_TOTAL\\}\\}</b></span>"}, "FooterDiscountedTotalTextColor": "#333333", "FooterDiscountedTotalLabel": {"Text": "<span style=\"font-size: 16px;\"><b>Total</b></span>"}, "FooterDiscountedTotalLabelTextColor": "#333333", "ShowBody": true, "SubtotalRowMode": "discount_order_level", "DiscountRowMode": "discount_order_level"}, "StructuredData": {"Microdata": true, "JsonLD": true}}