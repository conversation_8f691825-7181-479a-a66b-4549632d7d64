{% comment %} {%- assign dog_name = 'Ripley' -%} {% endcomment %}
{%- assign dog_name = "<PERSON>" -%}
{%- assign box_price = 0 -%}

{% if dog_name != blank %}
  {% assign dog_name_last_character = dog_name | slice: -1 %}
  {% if dog_name_last_character == "s" %}
    {% assign dog_name_possessive = dog_name | append: "'" %}
  {% else %}
    {% assign dog_name_possessive = dog_name | append: "'s" %}
  {% endif %}
{% endif %}

{%- assign color_label_list = 'general.label.color' | t | replace: ', ', ',' | downcase | split: ',' -%}
{%- assign color_white_label = 'general.label.white' | t | downcase -%}
{%- assign product_form_id = 'product-form-' | append: section.id | append: '-' | append: product.id -%}
{%- assign variant_picker_block = section.blocks | where: 'type', 'variant_picker' | first -%}
{%- assign buy_buttons_block = section.blocks | where: 'type', 'buy_buttons' | first -%}

  <style>
    @media screen and (min-width: 1000px) {
      :root {
        --anchor-offset: 140px; /* When the sticky form is activate, every scroll must be offset by an extra value */
      }
    }
  </style>

<quiz-sticky-form form-id="{{ product_form_id }}" class="product-sticky-form quiz-sticky-form">
  
  <div class="container">
    <div class="product-sticky-form__inner">
      <div class="product-sticky-form__content-wrapper">

        <div class="product-sticky-form__info">
          <div class="product-sticky-form__bottom-info">

            {% assign dog_name_last_character = dog_name | slice: -1 %}
            {% if dog_name_last_character == "s" %}
              {% assign dog_name = dog_name | append: "'" %}
            {% else %}
              {% assign dog_name = dog_name | append: "'s" %}
            {% endif %}

            <span class="quiz-sticky-form__title h4"><span data-dog-name-possessive>{{ dog_name_possessive | default: "$DOGNAME_POSSESSIVE" }}</span> Starter Box</span>
            
            <span class="product-sticky-form__price h5">
              {%- if settings.currency_code_enabled -%}
                {{- product.selected_or_first_available_variant.price | money_with_currency -}}
              {%- else -%}
                {{- product.selected_or_first_available_variant.price | money -}}
              {%- endif -%}
            </span>

            <div class="product-sticky-form__unit-price">

              <div class="unit-price-measurement">
              
                <span class="heading heading--small text--subdued unit-price-measurement__price" data-box-price>{{ box_price | money }}</span>
                <span class="heading heading--small text--subdued unit-price-measurement__separator">/</span>

                <span class="heading heading--small text--subdued unit-price-measurement__reference-value">{{ settings.quiz_starter_boxes_days }} Days</span>

                <span class="unit-price-measurement__link">
                  <a class="smooth-scroll-anchor" href="#quiz-feeding-calculator">See our feeding plan for <span data-dog-name>Ripley</span></a>
                </span>

              </div>
            </div>

          </div>
        </div>
      </div>

      {%- comment -%}This form is a simplified representation of the main form, and therefore uses its own code path{%- endcomment -%}
      <div class="product-sticky-form__form">

        <div form-id="{{ product_form_id }}" class="product-sticky-form__payment-container">

          <button is="loader-button" form="{{ product_form_id }}" type="submit" class="button-next button button--tertiary button--hollow" data-quiz-button-next>
            Next Dog
          </button>

          {% comment %} <button is="toggle-button" aria-controls="quiz-popup--upsells" aria-expanded="false" class="button-checkout button button--highlight" data-quiz-button-checkout disabled> {% endcomment %}
          <button is="loader-button" class="button-checkout button button--highlight" data-quiz-button-checkout disabled>
            Checkout
          </button>

        </div>
      </div>
    </div>
  </div>

</quiz-sticky-form>