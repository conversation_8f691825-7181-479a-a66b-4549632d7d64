{% comment %}
  modal_id - Required - Modal ID, used for opening of collapsibles.
  modal_title - Required - Modal title. Shown at top of modal.
  modal_content - Required - Modal content.
  modal_show_close - Shows close button at bottom of modal.
{% endcomment %}

{% if modal_id and modal_title and modal_content %}

  <modal-content section="{{ section.id }}" id="{{ modal_id }}" class="modal modal--{{ modal_title | handle }}">

    <div class="modal__overlay"></div>

    <div class="modal__content">

      <button type="button" class="modal__close-button tap-area" data-action="close" title="{{ 'general.accessibility.close' | t | escape }}">
        {%- render 'icon' with 'close' -%}
      </button>

      <div class="modal__header">
        <div class="modal__title">
          <span class="heading heading--small">{{ modal_title }}</span>
        </div>
      </div>

      <div class="quiz-modal-content">

        {% comment %} Modal Content {% endcomment %}

        <div class="newsletter-modal__content text-container text--center">
          {{ modal_content }}
        </div>

        {% comment %} Modal Footer {% endcomment %}

        {%- capture modal_footer -%}
          {%- if modal_show_close -%}
            <button is="toggle-button" class="link" data-action="close">Close</button>
          {%- endif -%}
        {%- endcapture -%}

        {%- if modal_footer != blank -%}
          <div class="quiz-modal-footer">
            {{ modal_footer }}
          </div>
        {%- endif -%}

      </div>
    </div>
  </modal-content>
  
{% endif %}