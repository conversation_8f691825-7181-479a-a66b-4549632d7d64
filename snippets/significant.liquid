{% comment %}
 HTML comments included to check original value on front-end
{% endcomment %}

{%- if places and value -%}

  {%- assign value = value -%}
  {%- assign value_rounded = value | round -%}
  {%- assign value_rounded_sigfig = value | round: places -%}

  {%- capture display -%}
    {%- if value == value_rounded -%}
      <!-- {{ value }} - rounded -->
      {{ value | round }}
    {%- else -%}
      <!-- {{ value }} -->
      {{ value_rounded_sigfig }}
    {%- endif -%}
  {%- endcapture -%}

  {%- if display -%}
    {{- display -}}
  {%- endif -%}
  
{%- endif -%}