{%- capture modal_title -%}
  {%- if title -%}
    {{ title }}
  {%- elsif section.settings.vet_notlisted_title != blank -%}
    {{ section.settings.vet_notlisted_title }}
  {%- else -%}
    {{ 'vet_partners.not_listed.modal_title' | t }}
  {%- endif -%}
{%- endcapture -%}

{%- capture modal_description -%}
  {%- if description -%}
    {{ description }}
  {%- elsif section.settings.vet_notlisted_content != blank -%}
    {{ section.settings.vet_notlisted_content }}
  {%- else -%}
    {{ 'vet_partners.not_listed.modal_description' | t }}
  {%- endif -%}
{%- endcapture -%}

<modal-content section="{{ section.id }}" id="{{ section.id }}--vet-notlisted" class="modal modal--vet-not-listed">

  <div class="modal__overlay"></div>

  <div class="modal__content">
    
    {%- comment -%}
    <button type="button" class="modal__close-button tap-area" data-action="close" title="{{ 'general.accessibility.close' | t | escape }}">
      {%- render 'icon' with 'close' -%}
    </button>
    {%- endcomment -%}

    <div class="newsletter-modal">

      <div class="newsletter-modal__content text-container text--center">

        {%- if modal_title != blank -%}
          <h2 class="heading h5">{{ modal_title }}</h2>
        {%- endif -%}

        {%- if modal_description != blank -%}
          <p>{{- modal_description -}}</p>
        {%- endif -%}

        <form action="#" id="{{ section.id }}--vet-not-listed-form" data-unlisted-vet-form>

          <div class="input">
            <input type="text" id="{{ section.id }}--vet-notlisted--dog-name" name="{{ section.id }}--vet-notlisted--dog-name" class="input__field is-filled unlisted-dog-name" required>
            <label for="{{ section.id }}--vet-notlisted--dog-name" class="input__label">Your Dog's Name *</label>
          </div>

          <div class="input">
            <input type="text" id="{{ section.id }}--vet-notlisted--hospital-name" name="{{ section.id }}--vet-notlisted--hospital-name" class="input__field is-filled unlisted-hospital-name" required>
            <label for="{{ section.id }}--vet-notlisted--hospital-name" class="input__label">Hospital Name *</label>
          </div>

          <div class="input">
            <input type="text" id="{{ section.id }}--vet-notlisted--vet-name" name="{{ section.id }}--vet-notlisted--vet-name" class="input__field is-filled unlisted-vet-name">
            <label for="{{ section.id }}--vet-notlisted--vet-name" class="input__label">Vet Name</label>
          </div>

          <div class="input">
            <input type="text" id="{{ section.id }}--vet-notlisted--contact" name="{{ section.id }}--vet-notlisted--contact" class="input__field is-filled unlisted-contact-details" required>
            <label for="{{ section.id }}--vet-notlisted--contact" class="input__label">Contact Details *</label>
            <span class="text--xxsmall text--subdued">Phone Number / E-mail Address / Website</span>
          </div>

          <div class="input">
            <button type="submit" class="button button--highlight button--full">Submit Vet Info</button>
          </div>

        </form>

      </div>
    </div>
  </div>

</modal-content>