{% capture back_button_label %}
  {%- if back_button_label -%}
    {{- back_button_label -}}
  {%- else -%}
    {{- 'quiz.general.back' | t -}}
  {%- endif -%}
{% endcapture %}

<div class="quiz-navigation">

  <div class="quiz-navigation__inner">

    <div class="quiz-navigation__actions-left">
    
      <button class="quiz-navigation-button" data-quiz-button-back {% if back_button_hidden == true %}{% endif %}>
        <span class="quiz-navigation-button__icon">{%- render 'icon' with 'chevron-back' -%}</span>
        <span class="quiz-navigation-button__text">{{ back_button_label }}</span>
      </button>

    </div>
    
    <div class="quiz-navigation__progress">

      <div class="quiz-navigation__logo">
        {% comment %} {% render 'quiz-logo' %} {% endcomment %}
        <a href="/">
          <img src="https://cdn.shopify.com/s/files/1/1683/1605/files/logo--reversed.png?v=1674594897" alt="Logo">
        </a>
      </div>

      <quiz-progress-bar class="quiz-progress-bar" data-quiz-progress data-starting-progress-segment="{{ starting_progress | default: '0' }}" style="--quiz-progress: 0; ">

        <span class="quiz-progress-bar__progress-track">
          <span class="quiz-progress-bar__progress"></span>
        </span>
        
        <span class="quiz-progress-bar__dot">
          {% render 'quiz-logo' %}
        </span>
        
        <span class="quiz-progress-bar__sections">

          <span class="quiz-progress-bar__label quiz-progress-bar__label-start text--large">Dogs</span>

          <span class="quiz-progress-bar__section quiz-progress-bar__section--1">
          </span>

          <span class="quiz-progress-bar__label quiz-progress-bar__label-middle text--large">Recipes</span>
          
          <span class="quiz-progress-bar__section quiz-progress-bar__section--2">
            <span class="quiz-progress-bar__section__divider"></span>
          </span>

          <span class="quiz-progress-bar__label quiz-progress-bar__label-end text--large">Cart</span>

        </span>
        
      </quiz-progress-bar>

    </div>

    <div class="quiz-navigation__actions-right">
      
      {% unless customer %}
        <button is="toggle-button" aria-controls="quiz-popup--login" aria-expanded="false" class="quiz-navigation-button">
          <span class="quiz-navigation-button__text">Login</span>
        </button>
      {% endunless %}

    </div>

  </div>

</div>