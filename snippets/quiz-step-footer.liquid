<div class="quiz-step__footer">

  <div class="container">

    {% if section.settings.hint != blank %}
      <div class="quiz-step-hint">{{ section.settings.hint }}</div>
    {% endif %}

    <div class="quiz-step-actions">

      <button type="button" is="loader-button" class="button button--highlight" form="step-form" data-quiz-button-next data-quiz-button-populate-dogs disabled="disabled">
        <span class="button__text">{{ 'quiz.general.continue' | t }}</span>
        <span class="button__icon">{%- render 'icon' with 'nav-arrow-right' -%}</span>
      </button>

    </div>
  
  </div>

</div>