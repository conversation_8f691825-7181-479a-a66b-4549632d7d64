{%- assign current_link = request.path | strip -%}

<div class="mobile-toolbar hidden-tablet-and-up">
  <button class="mobile-toolbar__item" is="toggle-button" aria-expanded="false" aria-controls="account-links-popover">{{- 'customer.orders.title' | t -}} {%- render 'icon' with 'chevron' -%}</button>
</div>

<popover-content id="account-links-popover" class="popover">
  <span class="popover__overlay"></span>

  <header class="popover__header">
    <span class="popover__title heading h6">{{- 'customer.general.title' | t -}}</span>

    <button type="button" class="popover__close-button tap-area tap-area--large" data-action="close" title="{{ 'general.accessibility.close' | t | escape }}">
      {%- render 'icon' with 'close' -%}
    </button>
  </header>

  <div class="popover__content">
    <div class="popover__choice-list">
      
      {%- assign link_url = routes.account_url -%}
      <a href="{{ link_url }}" class="popover__choice-item">
        <span class="popover__choice-label" {% if link_url == current_link %}aria-current="true"{% endif %}>{{ 'customer.orders.title' | t }}</span>
      </a>

      {%- assign link_url = routes.account_url | append: '#my-vet' -%}
      <a is="scroll-link" data-action="close" href="{{ link_url }}" class="popover__choice-item">
        <span class="popover__choice-label" {% if link_url == current_link %}aria-current="true"{% endif %}>My Vet</span>
      </a>
      
      {%- assign link_url = routes.account_url | append: '#my-dogs' -%}
      <a is="scroll-link" data-action="close" href="{{ link_url }}" class="popover__choice-item">
        <span class="popover__choice-label" {% if link_url == current_link %}aria-current="true"{% endif %}>My Dogs</span>
      </a>

      {%- assign link_url = routes.account_url | append: '#discounts' -%}
      <a is="scroll-link" data-action="close" href="{{ link_url }}" class="popover__choice-item">
        <span class="popover__choice-label" {% if link_url == current_link %}aria-current="true"{% endif %}>My Discounts</span>
      </a>

      {%- assign link_url = routes.account_addresses_url -%}
      <a href="{{ link_url }}" class="popover__choice-item">
        <span class="popover__choice-label" {% if link_url == current_link %}aria-current="true"{% endif %}>{{ 'customer.addresses.title' | t }}</span>
      </a>

      {%- assign link_url = routes.root_url | append: "tools/bundle-subscriptions" | strip -%}
      <a href="{{ link_url }}" class="popover__choice-item">
        <span class="popover__choice-label" {% if link_url == current_link %}aria-current="true"{% endif %}>{{ 'customer.subscriptions.manage' | t }}</span>
      </a>

      {%- assign link_url = routes.account_logout_url -%}
      <a href="{{ link_url }}" class="popover__choice-item text--subdued" data-no-instant>
        <span class="popover__choice-label" {% if link_url == current_link %}aria-current="true"{% endif %}>{{ 'customer.logout.title' | t }}</span>
      </a>
    </div>
  </div>
</popover-content>