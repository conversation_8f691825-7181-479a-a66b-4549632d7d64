<script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.13.3/dist/cdn.min.js"></script>
<!--
  1) create ravens in FieldsRaven admin
 -->
{% liquid
  assign rid_dog_1_name = 'hgugp1Y'
  assign rid_dog_1_ideal_weight = 'cm5bUVQ'
  assign rid_dog_1_age_in_months = 'm_20V9o'
  assign rid_dog_1_activity_level = '8gti5i4'
  assign rid_dog_1_neutered = 'ezucqNc'
  assign rid_dog_1_prescription_diet = 'jJxBp94'

  assign rid_dog_2_name = 'nVeCcOc'
  assign rid_dog_2_ideal_weight = 'k8t-45E'
  assign rid_dog_2_age_in_months = 'C1njY2c'
  assign rid_dog_2_activity_level = 'uFD5ZXo'
  assign rid_dog_2_neutered = 'T1geHvM'
  assign rid_dog_2_prescription_diet = 'abvLMu8'
%}
<!-- quiz -->
<div x-data="{
  
  dog_1_name: {%- render 'raven-mac-gen', resource_id: nil, raven_id: rid_dog_1_name -%},
  dog_1_ideal_weight: {%- render 'raven-mac-gen', resource_id: nil, raven_id: rid_dog_1_ideal_weight -%},
  dog_1_age_in_months: {%- render 'raven-mac-gen', resource_id: nil, raven_id: rid_dog_1_age_in_months -%},
  dog_1_activity_level: {%- render 'raven-mac-gen', resource_id: nil, raven_id: rid_dog_1_activity_level -%},
  dog_1_neutered: {%- render 'raven-mac-gen', resource_id: nil, raven_id: rid_dog_1_neutered -%},
  dog_1_prescription_diet: {%- render 'raven-mac-gen', resource_id: nil, raven_id: rid_dog_1_prescription_diet -%},
  dog_2_name: {%- render 'raven-mac-gen', resource_id: nil, raven_id: rid_dog_2_name -%},
  dog_2_ideal_weight: {%- render 'raven-mac-gen', resource_id: nil, raven_id: rid_dog_2_ideal_weight -%},
  dog_2_age_in_months: {%- render 'raven-mac-gen', resource_id: nil, raven_id: rid_dog_2_age_in_months -%},
  dog_2_activity_level: {%- render 'raven-mac-gen', resource_id: nil, raven_id: rid_dog_2_activity_level -%},
  dog_2_neutered: {%- render 'raven-mac-gen', resource_id: nil, raven_id: rid_dog_2_neutered -%},
  dog_2_prescription_diet: {%- render 'raven-mac-gen', resource_id: nil, raven_id: rid_dog_2_prescription_diet -%},

  submitFlockWithEmail() {

    const customer_email = `karim-test-${Math.floor(Math.random() * 1000)}@example.com`;
    //const customer_email = '{{ customer.email }}';

    const requestParams = {
      flock: [
        Object.assign({}, this.dog_1_name, {value: 'Rex'}),
        Object.assign({}, this.dog_1_ideal_weight, {value: 15.5}),
        Object.assign({}, this.dog_1_age_in_months, {value: 12}),
        Object.assign({}, this.dog_1_activity_level, {value: 'Active'}),
        Object.assign({}, this.dog_1_neutered, {value: 'Yes'}),
        Object.assign({}, this.dog_1_prescription_diet, {value: 'N/A'}),
        Object.assign({}, this.dog_2_name, {value: 'Rex 2'}),
        Object.assign({}, this.dog_2_ideal_weight, {value: 22.2}),
        Object.assign({}, this.dog_2_age_in_months, {value: 2}),
        Object.assign({}, this.dog_2_activity_level, {value: 'Active'}),
        Object.assign({}, this.dog_2_neutered, {value: 'Yes'}),
        Object.assign({}, this.dog_2_prescription_diet, {value: 'N/A'}),
      ],
      customer_email: customer_email
    }

    console.log('🚀🚀🚀🚀 requestParams: ', requestParams);

    const start = Date.now();

    const response = fetch('/apps/raven/create_multiple_metafields', {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(requestParams)
    })

    response
      .then(res => res.json())
      .then(resJson => {
        console.log('resJson: ', resJson)
        const end = Date.now();
        console.log(`⏰⏰ Execution time: ${end - start} ms`);
      })
      
  },
  sendRaven(params) {
    
    const response = fetch('/apps/raven/create_metafield', {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(params)
    })

    response
      .then(res => res.json())
      .then(resJson => console.log('resJson: ', resJson));

  },
  submitCustomerEmailRaven() {

    const customer_email = '{{ customer.email }}';
    const requestParams = { raven: Object.assign({}, this.dog_1_name, {value: 'Rex Solo 2'}, {customer_email: customer_email}) };
    
    console.log('💥👷👉 customer_email: ', customer_email);
    console.log('💥👷👉 requestParams: ', requestParams);
    
    this.sendRaven(requestParams)
    
  }
}">
  <button type="button" @click="submitFlockWithEmail()">
    Send a flock
  </button>
  <br>
  <button type="button" @click="submitCustomerEmailRaven()">
    Test individual field
  </button>
</div>
