{% render 'quiz-js-variables' %}

<style>

  :root {
    --heading-color: 255 255 255;
  }

</style>

<main class="quiz quiz--flow">

  <quiz-steps class="quiz-steps">

    {% render 'quiz-navigation' %}

    <quiz-sub-navigation class="quiz-sub-navigation quiz-sub-navigation--hidden">
      
      <div class="quiz-step__tabs">

        <div class="container">

          <quiz-dog-navigation class="quiz-step__tabs-inner">

            {% comment %}
              Dog names are dynamically loaded here.
            {% endcomment%} 
            
          </quiz-dog-navigation>
        
        </div>

      </div>

    </quiz-sub-navigation>

    <div class="quiz-steps__inner">

      <section class="quiz-step quiz-step--home quiz-step--active">
        {% section 'quiz-step--home' %}
      </section>
      
      <section class="quiz-step quiz-step--dogs-number">
        {% section 'quiz-step--dogs-number' %}
      </section>

      <section class="quiz-step quiz-step--dogs-substeps">
        
        {% comment %}
          Dog substeps are dynamically loaded here.
        {% endcomment%}

      </section>

      {% if customer %}
        
      {% else %}

        <section class="quiz-step quiz-step--request-email">
          {% section 'quiz-step--email' %}
        </section>

      {% endif %}

    </div>
    
  </quiz-steps>

</main>

{% section 'quiz-popup--login' %}
{% section 'quiz-popup--register' %}

{% section 'quiz-popup--weight' %}

{% comment %} <script src="{{ 'quiz.js' | asset_url }}"></script> {% endcomment %}