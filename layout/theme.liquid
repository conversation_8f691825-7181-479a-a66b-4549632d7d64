{%- comment %}<locksmith:c18e>{% endcomment -%}
  {%- include 'locksmith' -%}
{%- comment %}</locksmith:c18e>{% endcomment -%}

{% comment %} 
 ------------------------------------------------------------------------------------------------------------------------
 ===== Wynwood State Variables ===== 
 ------------------------------------------------------------------------------------------------------------------------
{% endcomment %}

{%- assign customer_vet = blank -%}

{%- comment -%}
------------------------------------------------------------------------------------------------------------------------
NOTE TO DEVELOPERS: welcome to Focal theme! We hope that you will enjoy editing this theme as much as we did for
  developing it. We have put a lot of work to make this theme as developer friendly as possible by offering you
  hooks to integrate into critical parts of the theme. You will find the complete technical documentation (including
  all events, dependencies...) in the "custom.js" file, located in the Assets folder.
------------------------------------------------------------------------------------------------------------------------
{%- endcomment -%}

<!doctype html>

{%- assign direction = 'ltr' -%}
{%- case request.locale.iso_code -%}
  {%- when 'ar' or 'arc' or 'dv' or 'fa' or 'ha' or 'he' or 'kwh' or 'ks' or 'ku' or 'ps' or 'ur' or 'yi' -%}
    {%- assign direction = 'rtl' -%}
{%- endcase -%}

{% if template contains "quiz" %}
  {% assign quiz_class = "on-quiz" %}
{% endif %}

<html class="no-js" lang="{{ request.locale.iso_code }}" dir="{{ direction }}">
  <head>
    
    {{ locksmith_initializations }}
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, height=device-height, minimum-scale=1.0, maximum-scale=1.0">
    <meta name="theme-color" content="{{ settings.header_background }}">

    <title>{% if page_title == blank %}{{ shop.name }}{% else %}{{ page_title }}{% if current_page != 1 %} &ndash; {{ 'general.meta.page' | t: page: current_page }}{% endif %}{% endif %}</title>

    {%- if page_description -%}
      {%- comment %}<locksmith:b522>{% endcomment -%}
        {%- capture var %}{% render 'locksmith-variables', variable: 'transparent', hiding_scope: 'resource' %}{% endcapture %}{% if var == 'true' %}<meta name="description" content="{{ page_description | escape }}">{% endif -%}
        {%- comment %}original: <meta name="description" content="{{ page_description | escape }}">{%- endcomment %}
      {%- comment %}</locksmith:b522>{% endcomment -%}
    {%- endif -%}
    <link rel="stylesheet" href="https://use.typekit.net/hfc7bsp.css">
    <!-- Google Tag Manager -->
    <script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
    new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
    j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
    'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
    })(window,document,'script','dataLayer','GTM-MFNXTCS');</script>
    <!-- End Google Tag Manager -->

    <link rel="canonical" href="{{ canonical_url }}">

    {%- if settings.favicon -%}
      <link rel="shortcut icon" href="{{ settings.favicon | img_url: '96x96' }}" type="image/png">
    {%- endif -%}

    {%- comment -%}Few prefech to increase performance on commonly used third-parties{%- endcomment -%}
    <link rel="preconnect" href="https://cdn.shopify.com">
    <link rel="dns-prefetch" href="https://productreviews.shopifycdn.com">
    <link rel="dns-prefetch" href="https://www.google-analytics.com">

    {%- unless settings.heading_font.system? and settings.text_font.system? -%}
      <link rel="preconnect" href="https://fonts.shopifycdn.com" crossorigin>
    {%- endunless -%}

    {%- comment -%}Preload important resources{%- endcomment -%}
    <link rel="preload" as="style" href="{{ 'theme.css' | asset_url }}">
    <link rel="preload" as="script" href="{{ 'vendor.js' | asset_url }}">
    <link rel="preload" as="script" href="{{ 'theme.js' | asset_url }}">
    <link rel="preload" as="script" href="{{ 'custom.js' | asset_url }}">

    {%- comment -%}
      IMPLEMENTATION NOTE: for improving performance, Focal tries to preload images on known pages (such as blog page,
      product or collection pages). In order to keep a main JS file size minimal, the theme also lazyloads non-essential
      libraries (such as PhotoSwipe or Flickity) and only loads them on demand.
    {%- endcomment -%}
    {%- if request.page_type == 'product' -%}
      {%- assign selected_media = product.selected_variant.featured_media | default: product.featured_media -%}

      <link rel="preload" as="fetch" href="{{ product.url }}.js" crossorigin>
      <link rel="preload" as="image" imagesizes="(max-width: 999px) calc(100vw - 48px), 640px" imagesrcset="{% render 'image-attributes', image: selected_media, sizes: '400,500,600,700,800,900,1000,1100,1200,1300,1400,1500,1600,1700,1800', sizes_only: true %}">

      {%- if product.media.size > 1 -%}
        <link rel="preload" as="script" href="{{ 'flickity.js' | asset_url }}">
      {%- endif -%}
    {%- elsif request.page_type == 'collection' and collection.image -%}
      <link rel="preload" as="image" media="(max-width: 740px)" imagesizes="100vw" imagesrcset="{% render 'image-attributes', image: collection.image, sizes: '400,500,600,700,800,900,1000', height_constraint: 600, crop: 'center', sizes_only: true %}">
      <link rel="preload" as="image" media="(min-width: 741px)" imagesizes="100vw" imagesrcset="{% render 'image-attributes', image: collection.image, sizes: '600,700,800,1000,1200,1400,1600,1800,2000,2200,2400,2600', sizes_only: true %}">
    {%- endif -%}

    {% render 'social-meta-tags' %}
    {% render 'microdata-schema' %}
    
    {% render 'css-variables', direction: direction %}
    {% render 'css-variables--focal', direction: direction %}

    {% render 'js-variables', direction: direction %}
    {% render 'wynwood-variables', direction: direction %}
    {% render 'raven-js-variables' %}
    {% render 'quiz-js-variables', direction: direction %}

    <link rel="stylesheet" href="{{ 'theme.css' | asset_url }}">
    <link rel="stylesheet" href="{{ 'custom.min.css' | asset_url }}">
    
    <link rel="stylesheet" href="{{ 'quiz.min.css' | asset_url }}">

    <script src="{{ 'vendor.js' | asset_url }}" defer></script>
    <script src="{{ 'theme.js' | asset_url }}" defer></script>
    <script src="{{ 'custom.js' | asset_url }}" defer></script>

    {% comment %} ====================== QUIZ ====================== {% endcomment %}

    {% if template contains "quiz" %}

      <link rel="preload" as="script" href="{{ 'quiz.js' | asset_url }}">
      <script src="{{ 'quiz.js' | asset_url }}" defer></script> 

    {% endif %}

    {% comment %} ====================== END QUIZ ====================== {% endcomment %}

    {{ content_for_header }}
  
    {% render 'limoniapps-discountninja-header' %}{%- if product -%}{%- assign discountninjaproduct = product -%}{%- endif -%}{%- if collection -%}{%- assign discountninjacollection = collection -%}{%- endif -%}
    
</head>

  {%- capture feature_classes -%}
    {% if settings.show_image_zoom %}
      features--image-zoom
    {% endif %}
  {%- endcapture -%}

  {%- capture page_classes -%}
    template-{{ template.name | handle }} template-{{ template | handle }} {% if customer %}logged-in{% else %}logged-out{% endif %}
  {%- endcapture -%}

  <body class="no-focus-outline {{ feature_classes }} {{ page_classes }} {{ quiz_class }}" data-instant-allow-query-string>

    <!-- Google Tag Manager (noscript) -->
<noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-MFNXTCS"
height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
<!-- End Google Tag Manager (noscript) -->
    
    {%- comment -%}Common SVG definitions that we are re-using in several places{%- endcomment -%}
    <svg class="visually-hidden">
      <linearGradient id="rating-star-gradient-half">
        <stop offset="50%" stop-color="rgb(var(--product-star-rating))" />
        <stop offset="50%" stop-color="rgb(var(--product-star-rating))" stop-opacity="0.4" />
      </linearGradient>
    </svg>

    <a href="#main" class="visually-hidden skip-to-content">{{ 'general.accessibility.skip_to_content' | t }}</a>
    <loading-bar class="loading-bar"></loading-bar>

    {%- if request.page_type != 'gift_card' and request.page_type != 'password' -%}
      {%- sections 'header-group' -%}
      {%- sections 'overlay-group' -%}

      {%- unless settings.cart_type == 'page' or request.page_type == 'cart' -%}
        {%- section 'mini-cart' -%}
      {%- endunless -%}
    {%- endif -%}

    {%- if request.page_type == "product" -%}
      {%- unless customer and customer.metafields.custom.veterinarian != blank -%}
        {%- if product.tags contains settings.prescription_food_tag -%}
          {%- section 'vet-sticky-bar' -%}
        {%- endif -%}
      {%- endunless -%}
    {%- elsif template contains "quiz" -%}
      {%- unless customer and customer.metafields.custom.veterinarian != blank -%}
        {%- section 'vet-sticky-bar' -%}
      {%- endunless -%}
    {%- endif -%}

    <div id="main" role="main" class="anchor">
      {%- comment -%}
      IMPLEMENTATION NOTE: on collection and search page, the theme was designed to show on mobile the filter bar and/or sort
        by at the very top of the page. However, due to the fact that sections are isolated, I had to move the code
        here. Then, the collection section, based on the selected settings, will eventually hide those elements in CSS
      {%- endcomment -%}
      {%- if request.page_type == 'collection' or request.page_type == 'search' -%}
        {%- assign active_filters_count = 0 -%}

        {%- if request.page_type == 'collection' -%}
          {%- assign filters = collection.filters -%}
        {%- else -%}
          {%- assign filters = search.filters -%}
        {%- endif -%}

        {%- for filter in filters -%}
          {%- if filter.type == 'list' -%}
            {%- assign active_filters_count = active_filters_count | plus: filter.active_values.size -%}
          {%- elsif filter.type == 'price_range' and filter.min_value.value or filter.max_value.value -%}
            {%- assign active_filters_count = active_filters_count | plus: 1 -%}
          {%- endif -%}
        {%- endfor -%}

        {%- assign is_toolbar_collapsed = false -%}

        {%- if request.page_type == 'search' and search.results.first.object_type != 'product' and search.results_count > 0 -%}
          {%- assign is_toolbar_collapsed = true -%}
        {%- endif -%}

        <div id="mobile-facet-toolbar" class="mobile-toolbar {% if is_toolbar_collapsed %}is-collapsed{% endif %} hidden-lap-and-up">
          <button is="toggle-button" class="mobile-toolbar__item mobile-toolbar__item--filters {% if active_filters_count > 0 %}has-filters{% endif %}" aria-expanded="false" aria-controls="facet-filters" style="display: none">
            {%- render 'icon' with 'filters' -%}
            <span class="mobile-toolbar__item-label">{{- 'collection.general.filters' | t -}}</span>
          </button>

          <button is="toggle-button" class="mobile-toolbar__item mobile-toolbar__item--sort" aria-expanded="false" aria-controls="sort-by-popover" style="display: none">
            <span class="mobile-toolbar__item-label">{{- 'collection.general.sort_by' | t -}}</span>
            {%- render 'icon' with 'chevron' -%}
          </button>
        </div>
      {%- endif -%}

      {{ content_for_layout }}
    </div>

    {%- if request.page_type != 'gift_card' and request.page_type != 'password' -%}
      {%- sections 'footer-group' -%}
    {%- endif -%}
    
    {% include 'limoniapps-discountninja-body' %}
    {% include 'smile-initializer' %}

  </body>
</html>