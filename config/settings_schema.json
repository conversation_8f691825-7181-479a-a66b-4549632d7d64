[{"name": "theme_info", "theme_name": "Focal", "theme_author": "Maestrooo", "theme_version": "11.2.1", "theme_documentation_url": "https://support.maestrooo.com/", "theme_support_url": "https://support.maestrooo.com/article/203-contact-us"}, {"name": "🩺 Vet Partners", "settings": [{"type": "header", "content": "Widget Content"}, {"type": "text", "id": "veterinarians_widget_title", "label": "Title", "default": "Who's Your Vet?"}, {"type": "richtext", "id": "veterinarians_widget_description", "label": "Text", "default": "<p>For our Fresh Rx® diets, we work closely with our Veterinary Partners to ensure the best care for your pup. Please select your veterinarian's office from the dropdown to checkout with a Fresh Rx diet.</p>"}, {"type": "richtext", "id": "veterinarians_widget_message", "label": "Message", "default": "<p>You have a Fresh Rx® diet in your cart. Please select your veterinarian to check out.</p>", "info": "<p>Shown when RX product is in cart</p>."}, {"type": "checkbox", "id": "veterinarians_metaobject", "label": "Use Veterinarian List", "info": "For all vet partners features, use the [metaobject list](https://admin.shopify.com/store/dishes-for-dogs-development/content/metaobjects/entries/veterinarian) in the admin instead of this list.", "default": true}, {"type": "checkbox", "id": "veterinarians_metaobject_use_states", "label": "Sort veterinarians by state", "info": "Organizes vets by state in dropdowns.", "default": true}, {"type": "textarea", "id": "quiz_veterinarians", "label": "LEGACY: Veterinarians", "info": "This list has been replaced by the metaobject list in the setting above."}, {"type": "text", "id": "prescription_diet_tag", "label": "Prescription Diet Tag", "default": "Condition Specific", "info": "Tag that indicates a prescription diet. Used to show vet partner widget in cart."}, {"type": "header", "content": "Cart Items"}, {"type": "paragraph", "content": "The cart can behave in a special way when there are prescription food items in the cart. When there are prescription food items in the cart, the cart will show a widget to select the customer's vet, and disable the checkout button if it is not selected. Prescription food items are indicated in the cart with an icon."}, {"type": "text", "id": "prescription_food_tag", "label": "Prescription Food Tag", "default": "Condition Specific"}, {"type": "text", "id": "prescription_food_tag_label", "label": "Prescription Food Tag Text", "default": "Prescription", "info": "If different from the tag on the product, this text shows instead of the tag wherever the tag text shows. (e.g. the cart)"}, {"type": "text", "id": "prescription_food_tag_tooltip", "label": "Tag Tooltip", "default": "This food item is a prescription item. Please select your vet to checkout."}]}, {"name": "❄️ Frozen Food", "settings": [{"type": "header", "content": "Frozen Food"}, {"type": "checkbox", "id": "frozen_food_enable", "label": "Enable", "default": true}, {"type": "text", "id": "frozen_food_shipping_threshold", "label": "Free shipping minimum amount", "info": "Enter amount using number only. If using multiple currencies, separate each currency code with its amount (eg.: USD:50,EUR:60,JPY:9000)", "default": "50"}, {"type": "header", "content": "Cart Items"}, {"type": "paragraph", "content": "The cart can behave in a special way when there are frozen food items in the cart. When there are frozen food items in the cart, the cart will have a minimum order value. Frozen food items are indicated in the cart with an icon."}, {"type": "text", "id": "frozen_food_tag", "label": "Frozen Food Tag", "default": "Frozen"}, {"type": "text", "id": "frozen_food_tag_label", "label": "Frozen Food Tag Text", "default": "Frozen", "info": "If different from the tag on the product, this text shows instead of the tag wherever the tag text shows. (e.g. the cart)"}, {"type": "text", "id": "frozen_food_tag_tooltip", "label": "Tag Tooltip", "default": "This food item is frozen. A special minimum order value applies. Please check our shipping info for more details."}]}, {"name": "📦 Starter Box Settings", "settings": [{"type": "paragraph", "content": "Resources"}, {"type": "header", "content": "Starter Boxes"}, {"type": "range", "id": "quiz_starter_boxes_days", "step": 7, "min": 7, "max": 28, "label": "Days in Starter Box", "info": "How many days' worth of food is in a starter box.", "default": 14}, {"type": "header", "content": "Subscriptions"}, {"type": "paragraph", "content": "IMPORTANT NOTE: Subscription products must always have subscription periods in multiples of 2. (e.g. 2 weeks, 4 weeks, 6 weeks, etc.)"}, {"type": "paragraph", "content": "If you're experiencing any issues, please make sure Awtom<PERSON> does not have periods that aren't multiples of 2. (e.g. 3 weeks, 5 weeks)"}, {"type": "textarea", "id": "quiz_subscription_weights", "label": "Quiz Subscription Length and Dog Weights", "placeholder": "0:4\n10:2", "info": "Format: \"[Dog Weight]:[Subscription Length]\""}, {"type": "paragraph", "content": "Examples:"}, {"type": "paragraph", "content": "0:4 -> 0-10lbs dogs and over get a 4 week subscription."}, {"type": "paragraph", "content": "10:2 -> 10lbs+ dogs and over get a 2 week subscription."}]}, {"name": "❓ Quiz Settings", "settings": [{"type": "header", "content": "Resources"}, {"type": "header", "content": "Data"}, {"type": "textarea", "id": "quiz_dog_breeds", "label": "Dog Breeds", "placeholder": "Golden Retriever\nLabrador\nFrench Bulldog", "info": "These breeds will be shown in the quiz at the 'Tell us about [Dog Name]' step. Each breed must be in its own line."}, {"type": "text", "id": "quiz_free_shipping_discount_code", "label": "Discount Code", "info": "This discount code is automatically applied to all orders with a Starter Box in the cart."}, {"type": "header", "content": "Pages"}, {"type": "url", "id": "quiz_bookmark_quiz_account", "label": "Quiz Account Creation"}, {"type": "url", "id": "quiz_bookmark_quiz", "label": "Quiz Page"}, {"type": "url", "id": "quiz_bookmark_results", "label": "Results Page"}, {"type": "url", "id": "quiz_bookmark_post_data", "label": "Post Data"}, {"type": "header", "content": "Images"}, {"type": "paragraph", "content": "Dogs"}, {"type": "image_picker", "id": "quiz_dog_image_1", "label": "Image"}, {"type": "paragraph", "content": "Weight Profiles"}, {"type": "image_picker", "id": "quiz_dog_weight_profile_1", "label": "Weight 1"}, {"type": "image_picker", "id": "quiz_dog_weight_profile_1_hover", "label": "Weight 1 Hover"}, {"type": "image_picker", "id": "quiz_dog_weight_profile_2", "label": "Weight 2"}, {"type": "image_picker", "id": "quiz_dog_weight_profile_2_hover", "label": "Weight 2 Hover"}, {"type": "image_picker", "id": "quiz_dog_weight_profile_3", "label": "Weight 3"}, {"type": "image_picker", "id": "quiz_dog_weight_profile_3_hover", "label": "Weight 3 Hover"}, {"type": "paragraph", "content": "Activity Levels"}, {"type": "image_picker", "id": "quiz_dog_activity_1", "label": "Activity 1"}, {"type": "image_picker", "id": "quiz_dog_activity_1_hover", "label": "Activity 1 Hover"}, {"type": "image_picker", "id": "quiz_dog_activity_2", "label": "Activity 2"}, {"type": "image_picker", "id": "quiz_dog_activity_2_hover", "label": "Activity 2 Hover"}, {"type": "image_picker", "id": "quiz_dog_activity_3", "label": "Activity 3"}, {"type": "image_picker", "id": "quiz_dog_activity_3_hover", "label": "Activity 3 Hover"}, {"type": "paragraph", "content": "Loading Overlay"}, {"type": "image_picker", "id": "quiz_overlay_loading_gif", "label": "Loading Animation"}, {"type": "header", "content": "<PERSON><PERSON>"}, {"type": "image_picker", "id": "quiz_cart_background", "label": "<PERSON>t <PERSON> Background"}]}, {"name": "❓ Quiz Collections", "settings": [{"type": "header", "content": "Healthy Puppy Underweight (HPU)"}, {"type": "collection", "id": "quiz_collection_hpu", "label": "Quiz Results Collection"}, {"type": "richtext", "id": "quiz_collection_hpu_message", "label": "Message", "default": "<p>We recommend starting with our HPU recipe (but you can mix it up if you like).</p>"}, {"type": "header", "content": "Healthy Puppy Ideal (HPR)"}, {"type": "collection", "id": "quiz_collection_hpr", "label": "Quiz Results Collection"}, {"type": "richtext", "id": "quiz_collection_hpr_message", "label": "Message", "default": "<p>We recommend starting with our HPR recipe (but you can mix it up if you like).</p>"}, {"type": "header", "content": "Healthy Puppy Overweight (HPO)"}, {"type": "collection", "id": "quiz_collection_hpo", "label": "Quiz Results Collection"}, {"type": "richtext", "id": "quiz_collection_hpo_message", "label": "Message", "default": "<p>We recommend starting with our HPO recipe (but you can mix it up if you like).</p>"}, {"type": "header", "content": "Healthy Adult Underweight (HAU)"}, {"type": "collection", "id": "quiz_collection_hau", "label": "Quiz Results Collection"}, {"type": "richtext", "id": "quiz_collection_hau_message", "label": "Message", "default": "<p>We recommend starting with our HAU recipe (but you can mix it up if you like).</p>"}, {"type": "header", "content": "Healthy Adult Ideal (HAR)"}, {"type": "collection", "id": "quiz_collection_har", "label": "Quiz Results Collection"}, {"type": "richtext", "id": "quiz_collection_har_message", "label": "Message", "default": "<p>We recommend starting with our HAR recipe (but you can mix it up if you like).</p>"}, {"type": "header", "content": "Healthy Adult Overweight (HAO)"}, {"type": "collection", "id": "quiz_collection_hao", "label": "Quiz Results Collection"}, {"type": "richtext", "id": "quiz_collection_hao_message", "label": "Message", "default": "<p>We recommend starting with our HAO recipe (but you can mix it up if you like).</p>"}, {"type": "header", "content": "Healthy Senior Underweight (HSU)"}, {"type": "collection", "id": "quiz_collection_hsu", "label": "Quiz Results Collection"}, {"type": "richtext", "id": "quiz_collection_hsu_message", "label": "Message", "default": "<p>We recommend starting with our HSU recipe (but you can mix it up if you like).</p>"}, {"type": "header", "content": "Healthy Senior Ideal (HSR)"}, {"type": "collection", "id": "quiz_collection_hsr", "label": "Quiz Results Collection"}, {"type": "richtext", "id": "quiz_collection_hsr_message", "label": "Message", "default": "<p>We recommend starting with our HSR recipe (but you can mix it up if you like).</p>"}, {"type": "header", "content": "Healthy Senior Overweight (HSO)"}, {"type": "collection", "id": "quiz_collection_hso", "label": "Quiz Results Collection"}, {"type": "richtext", "id": "quiz_collection_hso_message", "label": "Message", "default": "<p>We recommend starting with our HSO recipe (but you can mix it up if you like).</p>"}, {"type": "header", "content": "Allergies"}, {"type": "collection", "id": "quiz_collection_allergies", "label": "Quiz Results Collection"}, {"type": "richtext", "id": "quiz_collection_allergies_message", "label": "Message", "default": "<p>We recommend starting with our ALLERGIES recipe (but you can mix it up if you like).</p>"}, {"type": "header", "content": "Heart Issues"}, {"type": "collection", "id": "quiz_collection_heart", "label": "Quiz Results Collection"}, {"type": "richtext", "id": "quiz_collection_heart_message", "label": "Message", "default": "<p>We recommend starting with our HEART recipe (but you can mix it up if you like).</p>"}, {"type": "header", "content": "Liver Issues"}, {"type": "collection", "id": "quiz_collection_liver", "label": "Quiz Results Collection"}, {"type": "richtext", "id": "quiz_collection_liver_message", "label": "Message", "default": "<p>We recommend starting with our LIVER recipe (but you can mix it up if you like).</p>"}, {"type": "header", "content": "Kidney Issues"}, {"type": "collection", "id": "quiz_collection_kidney", "label": "Quiz Results Collection"}, {"type": "richtext", "id": "quiz_collection_kidney_message", "label": "Message", "default": "<p>We recommend starting with our KIDNEY recipe (but you can mix it up if you like).</p>"}, {"type": "header", "content": "Pancreatitis"}, {"type": "collection", "id": "quiz_collection_pancreatitis", "label": "Quiz Results Collection"}, {"type": "richtext", "id": "quiz_collection_pancreatitis_message", "label": "Message", "default": "<p>We recommend starting with our PANCREATITIS recipe (but you can mix it up if you like).</p>"}, {"type": "header", "content": "Cancer"}, {"type": "collection", "id": "quiz_collection_cancer", "label": "Quiz Results Collection"}, {"type": "richtext", "id": "quiz_collection_cancer_message", "label": "Message", "default": "<p>We recommend starting with our CANCER recipe (but you can mix it up if you like).</p>"}, {"type": "header", "content": "Calcium Oxolate / Struvite Stones"}, {"type": "collection", "id": "quiz_collection_calcium_oxolate", "label": "Quiz Results Collection"}, {"type": "richtext", "id": "quiz_collection_calcium_oxolate_message", "label": "Message", "default": "<p>We recommend starting with our CALCIUM OXOLATE recipe (but you can mix it up if you like).</p>"}, {"type": "header", "content": "Calcium Oxolate / Urate Stones"}, {"type": "collection", "id": "quiz_collection_urate_stones", "label": "Quiz Results Collection"}, {"type": "richtext", "id": "quiz_collection_urate_stones_message", "label": "Message", "default": "<p>We recommend starting with our CALCIUM OXOLATE / URATE STONES recipe (but you can mix it up if you like).</p>"}, {"type": "header", "content": "Kidney and Pancreatitis"}, {"type": "collection", "id": "quiz_collection_kidney_pancreatitis", "label": "Quiz Results Collection"}, {"type": "richtext", "id": "quiz_collection_kidney_pancreatitis_message", "label": "Message", "default": "<p>We recommend starting with our PANCREATITIS recipe (but you can mix it up if you like).</p>"}, {"type": "header", "content": "GI Issues"}, {"type": "collection", "id": "quiz_collection_gi", "label": "Quiz Results Collection"}, {"type": "richtext", "id": "quiz_collection_gi_message", "label": "Message", "default": "<p>We recommend starting with our GI ISSUES recipe (but you can mix it up if you like).</p>"}, {"type": "header", "content": "Joint Issues"}, {"type": "collection", "id": "quiz_collection_joint", "label": "Quiz Results Collection"}, {"type": "richtext", "id": "quiz_collection_joint_message", "label": "Message", "default": "<p>We recommend starting with our JOING ISSUES recipe (but you can mix it up if you like).</p>"}, {"type": "header", "content": "Grain Intolerance"}, {"type": "collection", "id": "quiz_collection_grain_intolerance", "label": "Quiz Results Collection"}, {"type": "richtext", "id": "quiz_collection_grain_intolerance_message", "label": "Message", "default": "<p>We recommend starting with our GRAIN INTOLERANCE recipe (but you can mix it up if you like).</p>"}]}, {"name": "⚡️ Quiz - Styles", "settings": [{"type": "header", "content": "Body text"}, {"type": "font_picker", "id": "quiz_heading_font", "label": "Heading Font", "info": "Selecting different fonts can affect the speed of your online store. Learn more about [system fonts.](https://help.shopify.com/en/manual/online-store/os/store-speed/improving-speed#fonts)", "default": "helvetica_n4"}, {"type": "font_picker", "id": "quiz_text_font", "label": "Body Font", "info": "Selecting different fonts can affect the speed of your online store. Learn more about [system fonts.](https://help.shopify.com/en/manual/online-store/os/store-speed/improving-speed#fonts)", "default": "helvetica_n4"}, {"type": "header", "content": "Brand"}, {"type": "color", "id": "color__brand__1", "label": "Brand Colour - Main Yellow", "default": "#FFD91F"}, {"type": "color", "id": "color__brand__2", "label": "Brand Colour - Main Black", "default": "#2E2E2E"}, {"type": "color", "id": "color__brand__3", "label": "Brand Colour - Dark Gray", "default": "#5C5C5C"}, {"type": "color", "id": "color__brand__4", "label": "Brand Colour - Medium Gray", "default": "#9A9A9A"}, {"type": "color", "id": "color__brand__5", "label": "Brand Colour - Light Gray", "default": "#E6E6E6"}, {"type": "color", "id": "color__brand__6", "label": "Brand Colour - White", "default": "#ffffff"}, {"type": "color", "id": "color__brand__7", "label": "Brand Colour - Off Yellow", "default": "#F5E8A9"}, {"type": "header", "content": "Colors"}, {"type": "paragraph", "content": "<PERSON><PERSON><PERSON>"}, {"type": "color", "id": "color__default", "label": "<PERSON><PERSON><PERSON>", "default": "#2E2E2E"}, {"type": "color", "id": "color__default__light", "label": "Default - Light", "default": "#474747"}, {"type": "color", "id": "color__default__dark", "label": "Default - Dark", "default": "#121212"}, {"type": "color", "id": "background_color__default", "label": "<PERSON><PERSON><PERSON>", "default": "#F7F7F7"}, {"type": "paragraph", "content": "Highlight"}, {"type": "color", "id": "color__highlight", "label": "Primary", "default": "#FFD91F"}, {"type": "color", "id": "color__highlight__light", "label": "Primary - Tint Light", "default": "#FFD91F"}, {"type": "color", "id": "color__highlight__dark", "label": "Primary - <PERSON><PERSON>", "default": "#FFD91F"}, {"type": "color", "id": "background_color__highlight", "label": "Highlight", "default": "#FEF5EE"}, {"type": "paragraph", "content": "Primary"}, {"type": "color", "id": "color__primary", "label": "Primary", "default": "#2E2E2E"}, {"type": "color", "id": "color__primary__light", "label": "Primary - Tint Light", "default": "#2E2E2E"}, {"type": "color", "id": "color__primary__dark", "label": "Primary - <PERSON><PERSON>", "default": "#2E2E2E"}, {"type": "color", "id": "background_color__primary", "label": "Primary", "default": "#FEF5EE"}, {"type": "paragraph", "content": "Secondary"}, {"type": "color", "id": "color__secondary", "label": "Secondary", "default": "#5C5C5C"}, {"type": "color", "id": "color__secondary__light", "label": "Secondary - Tint Light", "default": "#5C5C5C"}, {"type": "color", "id": "color__secondary__dark", "label": "Secondary - Tint Dark", "default": "#5C5C5C"}, {"type": "color", "id": "background_color__secondary", "label": "Secondary", "default": "#F5F7F8"}, {"type": "paragraph", "content": "Tertiary"}, {"type": "color", "id": "color__tertiary", "label": "Tertiary", "default": "#9A9A9A"}, {"type": "color", "id": "color__tertiary__light", "label": "Tertiary - Tint Light", "default": "#9A9A9A"}, {"type": "color", "id": "color__tertiary__dark", "label": "Tertiary - Tint Dark", "default": "#9A9A9A"}, {"type": "color", "id": "background_color__tertiary", "label": "Tertiary", "default": "#9A9A9A"}, {"type": "paragraph", "content": "Success"}, {"type": "color", "id": "color__success", "label": "Success", "default": "#2E9E7B"}, {"type": "color", "id": "color__success__light", "label": "Success - Tint Light", "default": "#55D7AE"}, {"type": "color", "id": "color__success__dark", "label": "Success - <PERSON><PERSON>", "default": "#2A8468"}, {"type": "color", "id": "background_color__success", "label": "Success", "default": "#D8F3EB"}, {"type": "paragraph", "content": "Warning"}, {"type": "color", "id": "color__warning", "label": "Warning", "default": "#FF8717"}, {"type": "color", "id": "color__warning__light", "label": "Warning - Tint Light", "default": "#FFB269"}, {"type": "color", "id": "color__warning__dark", "label": "Warning - <PERSON><PERSON>", "default": "#DF7716"}, {"type": "color", "id": "background_color__warning", "label": "Warning", "default": "#FFE7D1"}, {"type": "paragraph", "content": "Danger"}, {"type": "color", "id": "color__danger", "label": "Danger", "default": "#DE2A2A"}, {"type": "color", "id": "color__danger__light", "label": "Danger - Tint Light", "default": "#FF3A3A"}, {"type": "color", "id": "color__danger__dark", "label": "Danger - <PERSON><PERSON>", "default": "#D00606"}, {"type": "color", "id": "background_color__danger", "label": "Danger", "default": "#FFEBEB"}, {"type": "paragraph", "content": "Info"}, {"type": "color", "id": "color__info", "label": "Info", "default": "#4CA1ED"}, {"type": "color", "id": "color__info__light", "label": "Info - Tint Light", "default": "#63B5FF"}, {"type": "color", "id": "color__info__dark", "label": "Info - Tint Dark", "default": "#3E8FD8"}, {"type": "color", "id": "background_color__info", "label": "Info", "default": "#D9EDFF"}, {"type": "paragraph", "content": "Content Colors"}, {"type": "color", "id": "background_color_body", "label": "Body", "default": "#FFFFFF"}, {"type": "color", "id": "background_color_content_1", "label": "Content 1", "default": "#FFFFFF"}, {"type": "color", "id": "background_color_content_2", "label": "Content 2", "default": "#F7F7F7"}, {"type": "color", "id": "background_color_content_3", "label": "Content 3", "default": "#F7F7F7"}, {"type": "color", "id": "background_color_content_reversed_1", "label": "Reversed - Content 1", "default": "#232323"}, {"type": "color", "id": "background_color_content_reversed_2", "label": "Reversed - Content 2", "default": "#999999"}, {"type": "color", "id": "background_color_content_reversed_3", "label": "Reversed - Content 3", "default": "#232323"}, {"type": "color_background", "id": "overlay_color", "label": "Overlay Background"}, {"type": "header", "content": "Content Colors"}, {"type": "color", "id": "color_text", "label": "Body Text", "default": "#5C5C5C"}, {"type": "color", "id": "color_text_dark", "label": "Body Text (Strong)", "default": "#2e2e2e"}, {"type": "color", "id": "color_body_light", "label": "Body Text (Light)", "default": "#9A9A9A"}, {"type": "color", "id": "color_body_reversed", "label": "Body Text - Reversed", "default": "#DDDDDD"}, {"type": "color", "id": "color_body_reversed_strong", "label": "Body Text - <PERSON><PERSON><PERSON> (Strong)", "default": "#FFFFFF"}, {"type": "color", "id": "color_link", "label": "Links", "default": "#8F917D"}, {"type": "color", "id": "color_heading_1", "label": "Heading 1", "default": "#AF8663"}, {"type": "color", "id": "color_heading_2", "label": "Heading 2", "default": "#333333"}, {"type": "color", "id": "color_heading_3", "label": "Heading 3", "default": "#999999"}, {"type": "header", "content": "Layout Colors"}, {"type": "color", "id": "color_line", "label": "Lines", "default": "#E6E6E6"}, {"type": "color", "id": "color_line_light", "label": "Lines - Light", "default": "#f2f2f2"}, {"type": "color", "id": "color_line_dark", "label": "Lines - Dark", "default": "#C8C8C8"}, {"type": "header", "content": "Product Colors"}, {"type": "color", "id": "color_prices", "label": "Prices", "default": "#232323"}, {"type": "color", "id": "color_prices_sale", "label": "Prices - Sale", "default": "#232323"}, {"type": "color", "id": "color_prices_compare", "label": "Prices - Compare At", "default": "#CCCCCC"}, {"type": "header", "content": "Product Colors"}, {"type": "color", "id": "color_product_title", "label": "Title", "default": "#333333"}, {"type": "header", "content": "Other Colors"}, {"type": "color", "id": "color_reviews", "label": "Reviews", "default": "#FFE381"}]}, {"name": "Appearance", "settings": [{"type": "paragraph", "content": "Adapt the theme to your brand style by changing rounded elements, spacing and icons style."}, {"type": "range", "id": "icon_stroke_width", "min": 1, "max": 2, "step": 0.1, "unit": "px", "label": "Icon thickness", "default": 2}, {"type": "range", "id": "button_border_radius", "min": 0, "max": 30, "step": 2, "unit": "px", "label": "Button/input border radius", "default": 0}, {"type": "select", "id": "block_border_radius", "label": "Block border radius", "options": [{"value": "none", "label": "None"}, {"value": "small", "label": "Small"}, {"value": "medium", "label": "Medium"}, {"value": "large", "label": "Large"}], "default": "none"}, {"type": "select", "id": "vertical_spacing", "label": "Vertical spacing", "info": "Impact the vertical spacing separating each section.", "options": [{"value": "small", "label": "Small"}, {"value": "medium", "label": "Medium"}, {"value": "large", "label": "Large"}], "default": "large"}]}, {"name": "Colors", "settings": [{"type": "header", "content": "General"}, {"type": "color", "id": "heading_color", "label": "Heading", "default": "#1c1b1b"}, {"type": "color", "id": "text_color", "label": "Body text", "default": "#677279"}, {"type": "color", "id": "background", "label": "Background", "default": "#ffffff"}, {"type": "color", "id": "secondary_background", "label": "Secondary background", "info": "Use a color close to the main background.", "default": "#f7f8fd"}, {"type": "color", "id": "success_color", "label": "Success", "default": "#168342"}, {"type": "color", "id": "error_color", "label": "Error", "default": "#e00000"}, {"type": "header", "content": "Header"}, {"type": "color", "id": "header_background", "label": "Background", "default": "#1e316a"}, {"type": "color", "id": "header_text_color", "label": "Text", "default": "#ffffff"}, {"type": "header", "content": "Footer"}, {"type": "color", "id": "footer_background", "label": "Background", "default": "#f3f5f6"}, {"type": "color", "id": "footer_text_color", "label": "Text", "default": "#677279"}, {"type": "header", "content": "Primary button"}, {"type": "color", "id": "primary_button_background", "label": "Background", "default": "#00badb"}, {"type": "color", "id": "primary_button_text_color", "label": "Text", "default": "#ffffff"}, {"type": "header", "content": "Secondary button"}, {"type": "color", "id": "secondary_button_background", "label": "Background", "default": "#1e2d7d"}, {"type": "color", "id": "secondary_button_text_color", "label": "Text", "default": "#ffffff"}, {"type": "header", "content": "Product"}, {"type": "color", "id": "product_rating_color", "label": "Star rating", "default": "#f6a429"}, {"type": "color", "id": "product_on_sale_accent", "label": "On sale accent", "default": "#e00000"}, {"type": "color", "id": "product_sold_out_accent", "label": "Sold out accent", "default": "#6f719b"}, {"type": "color", "id": "product_custom_label_background", "label": "Custom label", "info": "[Learn more](https://support.maestrooo.com/article/75-collection-displaying-custom-label)", "default": "#1e316a"}, {"type": "color", "id": "product_custom_label_2_background", "label": "Custom label 2", "info": "[Learn more](https://support.maestrooo.com/article/75-collection-displaying-custom-label)", "default": "#1e316a"}, {"type": "color", "id": "product_in_stock_text_color", "label": "In stock accent", "default": "#168342"}, {"type": "color", "id": "product_low_stock_text_color", "label": "Low stock accent", "default": "#e00000"}]}, {"name": "Typography", "settings": [{"type": "header", "content": "Headings"}, {"type": "font_picker", "id": "heading_font", "label": "Font", "info": "Selecting different fonts can affect the speed of your online store. Learn more about [system fonts.](https://help.shopify.com/en/manual/online-store/os/store-speed/improving-speed#fonts)", "default": "helvetica_n4"}, {"type": "select", "id": "heading_font_size", "label": "Heading size", "options": [{"value": "small", "label": "Small"}, {"value": "medium", "label": "Medium"}, {"value": "large", "label": "Large"}], "default": "medium"}, {"type": "select", "id": "heading_text_transform", "label": "Heading style", "options": [{"value": "normal", "label": "Normal"}, {"value": "lowercase", "label": "Lowercase"}, {"value": "uppercase", "label": "Uppercase"}], "default": "uppercase"}, {"type": "header", "content": "Body text"}, {"type": "font_picker", "id": "text_font", "label": "Font", "info": "Selecting different fonts can affect the speed of your online store. Learn more about [system fonts.](https://help.shopify.com/en/manual/online-store/os/store-speed/improving-speed#fonts)", "default": "helvetica_n4"}, {"type": "range", "id": "text_font_size", "label": "Base size", "min": 12, "max": 20, "unit": "px", "default": 15}]}, {"name": "Currency format", "settings": [{"type": "header", "content": "Currency codes"}, {"type": "paragraph", "content": "Cart and checkout prices always show currency codes. Example: $1.00 USD."}, {"type": "checkbox", "id": "currency_code_enabled", "label": "Show currency codes", "default": false}]}, {"name": "Animation", "settings": [{"type": "paragraph", "content": "Users who configured their preferences to minimize non-essential motion will automatically experience reduced animations."}, {"type": "checkbox", "id": "show_image_zoom", "label": "Show image zoom", "info": "Affect various images that are slowly zoomed on hover", "default": true}, {"type": "checkbox", "id": "stagger_products_apparition", "label": "Reveal products one by one", "info": "Affect any product grid listing", "default": true}, {"type": "checkbox", "id": "stagger_blog_posts_apparition", "label": "Reveal blog posts one by one", "info": "Affect any blog post listing", "default": true}, {"type": "checkbox", "id": "reveal_product_media", "label": "Reveal product media", "info": "Affect product pages and featured product section", "default": true}]}, {"name": "Color swatch", "settings": [{"type": "checkbox", "id": "round_color_swatches", "label": "Round color swatches", "default": false}, {"type": "textarea", "id": "color_swatch_config", "label": "Configuration (deprecated)", "placeholder": "Red:#ff0000\nGreen:#00ff00\nBlue:#0000ff", "info": "Native color swatch should be used instead. [Learn more](https://help.shopify.com/en/manual/online-store/themes/theme-structure/theme-settings#options-with-swatches). Configuration based color swatches is deprecated and will be removed in the future. If you still need to use the configuration based system, you can [learn more](https://support.maestrooo.com/article/80-product-uploading-custom-color-for-color-swatch) about the exact naming convention."}]}, {"name": "Products grid", "settings": [{"type": "checkbox", "id": "show_vendor", "label": "Show vendor", "default": false}, {"type": "checkbox", "id": "show_secondary_image", "label": "Show secondary image on hover", "default": false}, {"type": "checkbox", "id": "product_add_to_cart", "label": "Enable quick add to cart", "default": true}, {"type": "checkbox", "id": "show_product_rating", "label": "Show product rating", "info": "To display a rating, add a product rating app. [Learn more](https://help.shopify.com/en/manual/products/product-reviews/installation)", "default": false}, {"type": "checkbox", "id": "show_discount", "label": "Show discount label", "default": true}, {"type": "select", "id": "discount_mode", "label": "Show discount as...", "options": [{"value": "percentage", "label": "Percentage"}, {"value": "saving", "label": "Saving"}], "default": "saving"}, {"type": "select", "id": "product_color_display", "label": "Color display mode", "options": [{"value": "hide", "label": "<PERSON>de"}, {"value": "count", "label": "Count"}, {"value": "swatch", "label": "Swatch"}], "default": "count"}, {"type": "select", "id": "product_image_size", "label": "Product image size", "options": [{"value": "natural", "label": "Natural"}, {"value": "short", "label": "Short (4:3)"}, {"value": "square", "label": "Square (1:1)"}, {"value": "tall", "label": "Tall (2:3)"}], "default": "natural"}]}, {"name": "<PERSON><PERSON>", "settings": [{"type": "select", "id": "cart_type", "label": "Cart type", "options": [{"value": "drawer", "label": "Drawer"}, {"value": "message", "label": "Message"}, {"value": "page", "label": "Page"}], "default": "message"}, {"type": "select", "id": "cart_icon", "label": "Cart icon", "options": [{"value": "shopping_bag", "label": "Shopping bag"}, {"value": "shopping_cart", "label": "Shopping cart"}, {"value": "tote_bag", "label": "Tote bag"}], "default": "shopping_bag"}, {"type": "checkbox", "id": "cart_show_free_shipping_threshold", "label": "Show free shipping minimum amount", "info": "Make sure that you have properly configured your [shipping rates](/admin/settings/shipping).", "default": false}, {"type": "text", "id": "cart_free_shipping_threshold", "label": "Free shipping minimum amount", "info": "Enter amount using number only. If using multiple currencies, separate each currency code with its amount (eg.: USD:50,EUR:60,JPY:9000)", "default": "50"}]}, {"name": "Social media", "settings": [{"type": "header", "content": "Accounts"}, {"type": "text", "id": "social_facebook", "label": "Facebook"}, {"type": "text", "id": "social_twitter", "label": "X (formerly Twitter)"}, {"type": "text", "id": "social_threads", "label": "Threads"}, {"type": "text", "id": "social_pinterest", "label": "Pinterest"}, {"type": "text", "id": "social_instagram", "label": "Instagram"}, {"type": "text", "id": "social_vimeo", "label": "Vimeo"}, {"type": "text", "id": "social_tumblr", "label": "Tumblr"}, {"type": "text", "id": "social_youtube", "label": "YouTube"}, {"type": "text", "id": "social_tiktok", "label": "TikTok"}, {"type": "text", "id": "social_linkedin", "label": "LinkedIn"}, {"type": "text", "id": "social_snapchat", "label": "Snapchat"}, {"type": "text", "id": "social_fancy", "label": "Fancy"}, {"type": "text", "id": "social_wechat", "label": "WeChat"}, {"type": "text", "id": "social_reddit", "label": "Reddit"}, {"type": "text", "id": "social_line", "label": "LINE"}, {"type": "text", "id": "social_spotify", "label": "Spotify"}]}, {"name": "Favicon", "settings": [{"type": "image_picker", "id": "favicon", "label": "Image", "info": "96 x 96px .png recommended"}]}]