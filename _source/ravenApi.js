const RavenAPI = {

  storage: {

    raven_api_secret: '{{ shop.metafields.fields_raven.api_secret }}'
    
  },

  functions: {

    ravenField(raven_id, resource_id) {

      return {
        raven_id: '{{ raven_id }}',
        resource_id: '{{ resource_id }}',
        raven_mac: '{{ raven_mac }}'
      }

    }

  },

  api: {

    postFlockWithEmail(email, flock) {

      console.log('postFlockWithEmail()');
      console.log('email - ' + email);
      console.log('flock - ' + flock);

      if (typeof email != "string") {
        throw new Error('Parameter Error: email required as type String: postFlockWithEmail(email, flock)');
      }

      if (typeof flock != "object") {
        throw new Error('Parameter Error: flock required as type Object: postFlockWithEmail(email, flock)');
      }

      const requestParams = {
        flock: flock,
        customer_email: email
      }

      console.log(requestParams);

      const response = fetch('/apps/raven/create_multiple_metafields', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(requestParams)
      });

      const start = Date.now();

      response
        .then(res => res.json())
        .then(resJson => {

          const end = Date.now();

          console.log('resJson: ', resJson)
          console.log(`⏰⏰ Execution time: ${end - start} ms`);

        });

    },

    postRaven(params) {

      const response = fetch('/apps/raven/create_metafield', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(params)
      })

      response
        .then(res => res.json())
        .then(resJson => console.log('resJson: ', resJson));

    }

  }

}