.button {

  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5em;
  transition: color 0.25s;
  text-align:center;

  line-height: 1.2;
  // min-height: var(--button-height);
  min-height: unset;

  padding: 0.5em 1.5em;

  font-weight: var(--text-font-bold-weight);

  font-size: var(---font-size-button--mobile);
  @include respond-to($medium-up) {
    font-size: var(---font-size-button--desktop);
  }

  &[disabled] {
    --button-background: 154, 154, 154;
    cursor: not-allowed;
    opacity: 0.5;
    background-position: 100% -100%, 100% 100% !important;
  }

  &:not(.button--link) {
    min-width: 200px;
  }

  .loader-button__text {
    gap: 16px;
  }

  &:not(.button--text) {
    padding-left: 50px;
    padding-right: 50px;
    @include respond-to($small-down) {
      padding: 0 30px;
    }
  }

  &.button--highlight {
    --button-background: var(---color--highlight--rgb);
    --button-text-color: 0, 0, 0;
  }

  &.button--tertiary {
    --button-background: var(---color--tertiary--rgb);
    --button-text-color: 0, 0, 0;
  }

  &.button--tab {
    
    min-width: unset;
    padding-left: 0 !important;
    padding-right: 0 !important;

    color: RGB(var(---color--brand-4--rgb));

    cursor: pointer;

    &[disabled] {
      background: none;
      pointer-events: none;
      cursor: not-allowed;
    }

    &:not([disabled]) {

      cursor: pointer;

      &:hover,
      &:focus {
        color: RGB(var(---color--tertiary--rgb));
      }
    
      &.active {
        color: RGB(var(---color--brand-1--rgb));
        cursor: default;
        pointer-events: none;
      }

    }

  }

  &.button--text {

    display: inline-flex;
    align-items: center;
    gap: 10px;

    min-width: 0;
    padding-left: 0;
    padding-right: 0;

    font-size: var(---font-size-body-large--desktop);
    font-weight: var(---font-weight-body--bold);

    transition: 0.25s color;

    &:focus,
    &:hover {
      color: var(---color-text--light);
    }

    .button__icon {

      svg {
        width: 10px;
        height: 10px;
        transform: rotate(45deg);
      }
    }

  }

  &.button--hollow {
    
    background: transparent;
    color: RGB(var(--button-background));

    box-shadow: 0 0 0 1px RGB(var(--button-background));

    transition: 0.25s color, 0.25s box-shadow, 0.25s background;

    &:hover {
      background: RGB(var(--button-background));
      color: RGB(var(--button-text-color));
    }

  }

  &.button--stealth {
    
    background: RGB(var(--section-block-background));
    color: RGB(var(--text-color));

    transition: 0.25s color, 0.25s box-shadow, 0.25s background;

    &:hover {

      background: RGB(var(--button-background));
      color: RGB(var(--button-text-color));

    }

  }

  &.button--tiny {

    // height: 45px;

    padding: 0.25em 0.5em;

    line-height: 24px;
    padding-left: 24px;
    padding-right: 24px;
    min-width: 0;

    font-size: var(---font-size-button--mobile);
    
    @include respond-to($medium-down) {
      min-width: unset;
    }

    @include respond-to($medium-up) {
      height: 36px;
      line-height: 36px;
      font-size: var(---font-size-button--mobile);
    }
  }

  &.button--large {

    font-size: var(---font-size-button-large--mobile);

    // min-height: var(--button-height--large);
    // line-height: 56px;
    min-height: unset;
    padding: 0.5em 1.5em;
    
    @include respond-to($medium-down) {
      min-width: unset;
    }

    @include respond-to($medium-up) {
      height: 64px;
    }

    font-size: var(---font-size-button-large--mobile);
    @include respond-to($medium-up) {
      font-size: var(---font-size-button-large--desktop);
    }

  }
  
}

