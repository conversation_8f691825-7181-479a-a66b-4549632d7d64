body.on-quiz {

  .shopify-section--announcement-bar,
  store-header {
    display: none !important;
  }
}

.quiz {

  display: flex;
  flex-direction: column;
  justify-content: center;

  --quiz-navigation-height: 104px;

  @include respond-to($small-down) {
    --quiz-navigation-height: 85px;
  }



  /* ----- Basic Styling ----- */

  &.quiz--flow {
    background: var(---background-color--content-reversed-1);
    --text-color: var(---color-text--reversed--rgb);
  }

  &.quiz--account {

    // .quiz-navigation__center,
    .quiz-modal-footer {
      display: none;
    }

    .quiz-navigation__center {
      visibility: hidden;
      pointer-events: none;
    }
    
    .quiz-steps__inner {
      .quiz-step {
        justify-content: center;
        position: absolute;
      }
    }

  }

  &.quiz--has-navigation {
    min-height: calc(100vh - var(--header-height) - var(--quiz-navigation-height));
  }

  quiz-steps.quiz-steps {
    display: block;
  }

  .quiz-steps__inner {

    position: relative;
    overflow: hidden;
    // min-height: calc(100vh - var(--quiz-navigation-height));
    // min-height: calc(100vh - var(--quiz-navigation-height) - var(--quiz-sub-navigation-height));
    min-height: calc(100vh - var(--quiz-navigation-height) - 60px);
    // min-height: calc(100vh - var(--header-height)); 
    // min-height: calc(100vh - var(--header-height) - var(--quiz-navigation-height));

    @include respond-to($medium-down) {
      .quiz-step {
        position: static;
        // justify-content: flex-start;
      }
    }

  }

  /* ----- Sub Navigation ----- */

  .quiz-sub-navigation {
    display: block;
    position: sticky;
    top: calc(var(--header-height) + var(--quiz-navigation-height) - 10px);

    z-index: 3;
    width: 100%;
    background: linear-gradient(0deg, rgba(var(---background-color--content-reversed-1--rgb), 0) 0%, rgba(var(---background-color--content-reversed-1--rgb), 1) 90%);

    pointer-events: none;
    transition: transform 0.25s;

    @include respond-to($small-down) {
      padding-bottom: 40px;
    }

    @include respond-to($small-up) {
      padding-top: 10px;
    }

    &.quiz-sub-navigation--hidden {
      transform: translateY(-100%);
    }

  }

  /* ----- Steps ----- */

  .quiz-step {

    display: none;
    flex-direction: column;
    justify-content: center;

    visibility: hidden;

    position: absolute;
    width: 100%;
    height: 100%;
    left: 0;
    right: 0;

    @include respond-to($small-down) {
      overflow-y: scroll;
      justify-content: center;
    }

    @include respond-to($wide-up) {
      // justify-content: flex-start;
    }

  }

  .quiz--account {
    
  }

  .quiz-step--home {
    
    overflow: hidden; // Handle the horizontal scrollbars from the decorations. (Makes the sticky footer unworkable on this step)

    .quiz-step__footer {
      background: none;
    }

  }

  .quiz-step--active {
    display: flex;
    visibility: visible;
  }

  .quiz-step--animating {
    display: flex;
    // position: absolute;
  }

  .quiz-step--leaving {
    @include respond-to($medium-down) {
      display: flex;
    }
  }

  .quiz-step__inner {

    // min-height: calc(100vh - var(--header-height));
    // min-height: calc(100vh - var(--header-height) - var(--quiz-navigation-height));

    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    position: relative;
    z-index: 1;

    .h1 {
      line-height: 1;
    }

    @include respond-to($medium-down) {

      // height: unset;
      // max-height: unset;
      // min-height: calc(100vh - var(--header-height));

    }

  }

  .quiz-step__header {

    position: absolute;
    top: 0;

    max-width: var(--container-max-width);
    width: 100%;
    margin: 0 auto auto auto;

  }


  .quiz-step__body {

    text-align: center;
    padding: 20px var(--container-gutter);

    >.quiz-step-actions {
      flex-direction: column;
      align-items: center;
    }

    @include respond-to($wide-up) {
      padding-top: 5px;
      padding-bottom: 0px;
    }

  }

  .quiz-step__body--forms {
    width: 100%;

    .quiz-step-description {
      max-width: 800px;
      margin: 20px auto 40px;;
    }

    +.quiz-step__footer .quiz-step-actions__inner {
      max-width: 500px;
      margin: 20px auto;
    }
  }

  .quiz-step__body--narrow {

    max-width: 600px;

  }

  .quiz-step__footer {

    position: sticky;
    bottom: 0;

    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 10px;

    width: 100%;
    padding: 20px var(--container-gutter);

    // padding-top: var(--container-gutter);
    // padding-bottom: var(--container-gutter);
    // padding-left: var(--container-gutter);
    // padding-right: var(--container-gutter);

    margin-top: auto;

    text-align: center;

    // background: linear-gradient(180deg, rgba(var(---background-color--content-reversed-1--rgb), 0) 0%, rgba(var(---background-color--content-reversed-1--rgb), 1) 50%);
    background: linear-gradient(180deg, rgba(var(---background-color--content-reversed-1--rgb), 0) 0%, rgba(var(---background-color--content-reversed-1--rgb), 1) 20%);

    @include respond-to($medium-down) {

      .button {
        width: 100%;
      }

      .quiz-step-actions {
        order: 1;

      }

      .quiz-step-actions-account {
        order: 0;
      }

    }

  }

  .quiz-customer-forms {

    display: flex;
    justify-content: center;
    gap: 30px;

    @include respond-to($medium-down) {
      flex-direction: column;
    }

  }

  .quiz-customer-form {

    --block-border-radius: 20px;
    --section-block-background: var(---background-color--content-reversed-3--rgb);

    background: var(---background-color--content-reversed-3);
    border-radius: var(--block-border-radius);

    display: flex;
    flex-direction: column;
    justify-content: stretch;
    max-width: 490px;
    width: 100%;

    padding: 30px;
    
    form {
      height: 100%;
      display: flex;
      flex-direction: column;
    }

    .input {
      
      margin-top: 20px;
      margin-bottom: 20px;

      .input__field {
        padding: 0.25em 0.5em;
      }

      &:last-child {
        margin-top: 0;
        margin-bottom: 0;
        padding-top: 0;
      }

      &:first-child {
        // margin-top: 0;
      }
    }

    .input:last-child {
      // margin-top: auto;
      // padding-top: 20px;
    }

    @media (max-width: 1000px) {
      margin: auto;
    }

    .input__field {
      font-size: 20px !important;
      padding: 0.5em 1em;
    }

  }

  .quiz-customer-form__step {
    padding: 1em 0;

    .input--radio {
      label {
        font-size: var(---font-size-body-large--desktop);
      }
    }

    .kyc-option-radio {
      margin: 0.5em 0 !important;
    }

    .input__field {
      padding-left: 0  !important;
    }
  }

  /* ----- Tabs ----- */

  .quiz-step__tabs {

    &:before,
    &:after {

      display: inline-block;
      position: absolute;
      top: 0;
      z-index: 3;

      content: '';
      width: 50px;
      height: 100%;

      pointer-events: none;

    }

    &:before {
      left: 0;
      background: linear-gradient(270deg, rgba(var(---background-color--content-reversed-1--rgb), 0), rgba(var(---background-color--content-reversed-1--rgb), 1) 100%);
    }

    &:after {
      right: 0;
      background: linear-gradient(90deg, rgba(var(---background-color--content-reversed-1--rgb), 0), rgba(var(---background-color--content-reversed-1--rgb), 1) 100%);
    }


  }

  .quiz-step__tabs-inner {

    display: flex;
    gap: 20px;

    scroll-snap-type: x mandatory;
    scroll-snap-align: center;

    overflow: scroll;

    display: flex;
    align-items: flex-start;
    justify-content: center;
    padding: 10px 50px;
    gap: 30px;

    width: 100%;

    &::-webkit-scrollbar {
      display: none;
    }

    @include respond-to($medium-down) {
      padding: 0;
    }

    .quiz-step-tab {
      display: flex;
    }

  }

  .quiz-step-tab {

    display: block;
    scroll-snap-align: center;

    white-space: nowrap;

    pointer-events: all;

    @include respond-to($medium-down) {
      padding: 0 20px;
    }

  }

  .quiz-step-logo {
    max-width: 200px;
    margin-top: 50px;
    margin-bottom: 30px;

    @include respond-to($medium-up) {
      margin-top: 100px;
      margin-bottom: 50px;
    }

  }

  .quiz-step-icon {

    width: 150px;
    height: 150px;

    margin-block-end: 20px;

    @media (max-width: 700px) {
    // @include respond-to($small-down) {
      .quiz-step-icon {
        width: 120px;
        height: 120px;
        margin-bottom: -20px;
      }
    }

  }




  /* ----- Step Content Elements ----- */

  .quiz-step-title {

    margin-bottom: 0.5em;
    margin-top: 0;

    strong, span {
      color: RGB(var(---color--brand-1--rgb));
    }

  }

  .quiz-step-description {

    margin-top: 30px;
    margin-bottom: 30px;

  }

  .quiz-step-line {

    display: block;
    margin: 10px 0;
    line-height: 1.2;

    font-size: var(---font-size-h4--mobile);

    @include respond-to($small-up) {
      font-size: var(---font-size-h4--desktop);
    }

    opacity: 1;

    transition: transform 0.25s ease-in-out,
    opacity 0.25s ease-in-out;

    select {
      font-size: 1rem !important;
    }

    &.quiz-step-line--hidden {
      opacity: 0;
      transform: translateY(-10%);
      pointer-events: none;
    }

    >* {
      display: inline-block;
    }

    .quiz-step-line__hint {
      display: block;
    }

  }

  .quiz-step-line__hint {
    margin-top: 1em;
    font-size: var(---font-size-body--desktop);
  }

  .quiz-step-hint {

    margin: 30px auto;

    @include respond-to($small-down) {
      margin: 0 auto;
      max-width: 200px;
    }

  }

  .quiz-step-actions {

    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 1em;

    width: 100%;
    padding-top: 20px;
    padding-bottom: 20px;

    @include respond-to($small-up) {
      padding-top: 30px;
    }

    @include respond-to($wide-up) {
      padding-top: 5px;
    }

  }

  .quiz-step-actions-account {
    margin: 10px auto;
  }

  .quiz-step-form {
    padding-bottom: var(--container-gutter);
  }

  /* ----- Inputs ----- */

  .quiz-input {

    display: inline-block;
    background-color: transparent;
    border: none;

    min-width: 10px;
    border-bottom: 2px solid var(---color--secondary);
    margin: 10px 0;

    font-weight: var(--text-font-bold-weight);

    color: var(---color--highlight);
    transition: 0.25s border-color;

    &::placeholder {
      font-weight: var(--text-font-weight);
      opacity: 1;
    }

    &:focus,
    &:hover {
      outline: none;
      border-color: var(---color--highlight);
    }

  }

  select.quiz-input {
    padding-right: 50px;
    background-image: url("data:image/svg+xml,%3Csvg width='12' height='6' viewBox='0 0 12 6' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M1 0.5L6 5.5L11 0.5' stroke='%23E6E6E6' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E%0A");
    background-size: 15px;
    background-repeat: no-repeat;
    background-position: calc(100% - 10px);
  }

  quiz-step-dog-health {

    .quiz-step-hint__final {
      display: none;
    }

    .quiz-step-hint__in-progress {
      display: block;
    }

    &:last-of-type {
      .quiz-step-hint__final {
        display: block;
      }

      .quiz-step-hint__in-progress {
        display: none;
      }
    }

  }


  /* ----- Decoration ----- */

  .quiz-decoration {

    content: "";
    position: absolute;
    z-index: 0;

    pointer-events: none;

  }

  .quiz-decoration--1 {

    width: 850px;
    height: 100%;
    left: 0;
    top: 0;
    bottom: 0;
    z-index: 0;

    transform: scale(1.1) translate(0%);

    svg {
      transform: translateX(-40%);
    }

  }

  .quiz-decoration--2 {

    width: 850px;
    height: 100%;
    right: 0;
    top: 0;
    bottom: 0;
    z-index: 0;

    transform: scale(1.1) translate(0%);

    svg {
      transform: translateX(30%) rotate(160deg);
    }

  }

  @include respond-to($small-down) {

    .quiz-decoration--1 {

      width: 100%;
      height: 400px;
      left: 0;
      top: auto;
      bottom: 0;
      z-index: 0;

      transform-origin: 50% 50%;

      transform: scale(1.2) translateX(0%) translateY(30%) rotate(230deg);

      svg {
        transform: translateX(0);
      }

    }

    .quiz-decoration--2 {
      display: none;
    }
  }

}