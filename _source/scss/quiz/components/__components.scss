.quiz {

  .price {
    font-weight: 500;
    letter-spacing: 0;
  }

  /* ----- Quiz Elements ----- */

  .quiz-page-header {
    text-align: center;
    margin-bottom: 20px;
  }

  .quiz-page-footer {
    text-align: center;
    margin-bottom: 20px;
    margin-top: 20px;
  }

  .quiz-terms {
    margin-top: 20px;
    max-width: 350px;
    margin: auto;
    text-align: center;
  }

  .quiz-sticky-form {
    --background: var(---background-color--content-reversed-1--rgb);
    --border-color: var(---color-text--rgb);
    border-bottom: 0;
  }

  /* ----- Radio Buttons ----- */

  .input--sub-option {
    margin: 0;
    padding-left: 30px;
    text-align: left;
    margin: 0 !important;
    
    .nice-select, select, input {
        font-size: var(---font-size-body--desktop) !important;
    }

    input,
    textarea,
    select, .select {
      padding: 0.5em 3em 0.5em 1.4em;
      display: block;
      font-size: 18px;
      background-color: rgba(var(--section-block-background), 0.8);
      margin-bottom: 1em;
      border-radius: 8px;
      border: 1px solid rgba(var(--text-color), 0.25);

      &:hover {
        background-color: rgba(var(--section-block-background), 1);
        border: 1px solid rgba(var(--text-color), 0.5);
      }

      &:focus {
        border: 1px solid rgba(var(--text-color), 0.5);
      }
      
    }

    .select,
    select {

      background-image: var(---icon--chevron);
      background-position: right 1em top 50%;
      background-repeat: no-repeat;
      background-size: 14px;

    }

  }

  /* ----- Radio Buttons ----- */

  .input--radio {

    margin: 0;
    margin-bottom: 0.5em;

    label {

      font-size: var(---font-size-label-radio--mobile);

      @media (min-width: 1000px) {
        font-size: var(---font-size-label-radio--desktop);
      }

    }

    input[type="radio"] {

      background: #fff;
      position: relative;
      top: -1px;

      &:after {

        content: '';

        display: inline-block;
        position: absolute;
        left: 0;
        top: 0;
        right: 0;
        bottom: 0;
        margin: auto;

        color: #000;
        width: 10px;
        height: 10px;
        border-radius: 100%;
        background: currentColor;

        transform: scale(0);
        opacity: 0.5;

        transition: transform .25s, opacity .25s;

      }

      &:checked,
      &:hover {
        &:after {
          transform: scale(1);
        }
      }

      &:checked {
        &:after {
          opacity: 1;
        }

        +label {
          font-weight: 700;
        }
      }
    }

    .checkbox-container {
      align-items: center;
    }

    .checkbox {
      width: 16px;
      height: 16px;
      border-radius: 100%;

      @media (min-width: 1000px) {
        width: 18px;
        height: 18px;
      }
    }


  }

  /* ----- HRs ----- */

  // hr, .hr {

  //   width: 100%;
  //   margin: 2em auto;
  //   border-width: 0.5px;
  //   border-bottom: 0;
  //   border-style: solid;
  //   border-color: var(---color-line);

  //   &.hr--light {
  //     border-color: var(---color-line--light);
  //   }

  //   &.hr--dark {
  //     border-color: var(---color-line--dark);
  //   }

  //   &.hr--clear {
  //     border-color: transparent;
  //   }

  //   &.hr--small {
  //     margin: 1em 0;
  //   }

  //   &.hr--xsmall {
  //     margin: 0.5em 0;
  //   }

  //   &.hr--narrow {
  //     max-width: 70px;
  //   }

  // }

  .banner {

    border-radius: 20px;
    font-weight: var(---font-weight-body--bold);

    .banner__content {
      a {
        text-decoration: underline;
      }
    }

    &.banner--success {
      color: var(---color-text--strong);
      background-color: var(---color--highlight);
    }
    
    &.banner--error {
      color: var(---color-text--reversed-strong);
      background-color: var(---color--danger);
    }

  }


}
