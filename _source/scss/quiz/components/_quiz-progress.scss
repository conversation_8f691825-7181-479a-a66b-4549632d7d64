.quiz--flow {
  position: relative;
  color: RGB(var(--text-color));
}

.quiz--account {
  .quiz-navigation__inner {
    align-items: center;
  }
}

.quiz-navigation {

  position: sticky;
  z-index: 4;
  top: 0;
  top: var(--header-height);
  // top: var(--header-height-quiz);
  left: 0;
  right: 0;
  width: 100%;

  background: var(---background-color--content-reversed-1);
  box-shadow: 0px 4px 12px 4px rgba(0, 0, 0, 0.25);

  color: RGB(var(---color-text--reversed--rgb));

  transition: transform 0.25s;

}

.quiz-navigation--hidden {
  // transform: translateY(-100%);
}

.quiz-navigation__inner {
  
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  
  max-width: var(--container-max-width);
  padding-left: var(--container-gutter);
  padding-right: var(--container-gutter);
  margin: auto;
  
  @include respond-to($small-down) {
    align-items: center;
  }
  
}

.quiz-navigation__actions-left,
.quiz-navigation__actions-right {
  flex: 1 0 30%;
  @include respond-to($small-down) {
    top: 20px;
  }
}

.quiz-navigation__center {
  align-items: center;
}

.quiz-navigation-button {
  
  display: flex;
  align-items: center;
  gap: 0.5em;
  padding: 1em 0;
  
  font-weight: var(---font-weight-body--bold);

  @include respond-to($small-down) {
    padding: 0;
  }
  
  &:focus,
  &:hover {
      opacity: 0.7;
  }

  &.quiz-navigation-button--hidden {
    display: none;
  }
  
}

.quiz-navigation__actions-left {
  display: flex;
  justify-content: flex-start;
  text-align: left;
  @include respond-to($small-down) {
      position: absolute;
      left: var(--container-gutter);
  }
}

.quiz-navigation__actions-right {
  display: flex;
  justify-content: flex-end;
  text-align: right;
  @include respond-to($small-down) {
      position: absolute;
      right: var(--container-gutter);
  }
}

.quiz-navigation__logo {
  
  padding: 5px 0;

  @include respond-to($small-down) {
    padding-top: 20px;
  }

  img {
    
    max-width: 120px;
    margin-bottom: 10px;

    @include respond-to($small-down) {
      max-width: 100px;
    }

  }
  
  @include respond-to($small-down) {
    
    padding-top: 20px;

    position: absolute;
    top: 0;

    a {
      &:hover {
        opacity: 0.75;
      }
    }
    
    img,
    svg {
      width: 100px;
    }

  }

}

.quiz-navigation__progress {
  
  display: flex;
  flex-direction: column;
  align-items: center;

  padding: 30px 0 40px;
  max-width: 630px;
  width: 100%;

  @include respond-to($small-up) {
    padding-top: 15px;
  }

  @include respond-to($small-down) {
    padding-top: 60px;
    padding-bottom: 10px;
  }
}

.quiz-navigation__actions-left,
.quiz-navigation__actions-right {
  top: 14px;
}

/* ----- Progress Bar ----- */

.quiz-progress-bar {
  position: relative;
  max-width: 630px;
  width: 100%;
}

.quiz-progress-bar__label {

  position: absolute;
  max-width: 100px;
  text-align: center;
  padding-top: 12px;

  @include respond-to($small-down) {
    display: none;
  }

  @include respond-to($small-up) {
    padding-top: 24px;
  }

}

.quiz-progress-bar__label-start {
  text-align: left;
  left: 0;

  @include respond-to($small-up) {
    transform: translateX(-50%);
  }
}

.quiz-progress-bar__label-middle {
  left: 0;
  right: 0;
  margin: auto;
}

.quiz-progress-bar__label-end {
  text-align: right;
  right: 0;

  @include respond-to($small-up) {
    transform: translateX(50%);
  }
}

.quiz-progress-bar__sections {
  
  display: flex;
  justify-content: stretch;
  
  height: 12px;
  
  transform: translateY(-12px);

  @include respond-to($small-down) {
    height: 8px;
    transform: translateY(-8px);
  }
  
}

.quiz-progress-bar__section {
  
  width: 100%;
  text-align: center;
  position: relative;
  
}

.quiz-progress-bar__section__label {
  position: absolute;
  left: 0;
  right: 0;
  bottom: -5px;
  transform: translateY(100%);
  @include respond-to($small-down) {
    display: none;
  }
  @include respond-to($small-up) {
    padding-top: 8px;
  }
}

.quiz-progress-bar__dot {
  
  position: absolute;
  top: 0;
  left: calc(100% * var(--quiz-progress));
  z-index: 1;

  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;

  transform: translate(-50%, -25%);
  
  vertical-align: top;
  
  transition: 0.25s left;

  background: var(---color--highlight);
  border-radius: 100%;

  svg {
    width: 24px;
    opacity: 0.75;

    * {
      fill: #000;
    }
  }

  @include respond-to($small-down) {
    width: 18px;
    height: 18px;
    border-radius: 100%;
    background: var(---color--brand-1);
    svg {
        display: none;
    }
  }

  
  
  
}

.quiz-progress-bar__progress-track {
  
  position: relative;
  display: block;
  height: 12px;
  
  box-shadow: 0 0 0 1px var(---color--brand-3) inset;
  
  border-radius: 24px;
  overflow: hidden;

  @include respond-to($small-down) {
    height: 8px;
  }
  
}

.quiz-progress-bar__section__divider {
  display: block;
  width: 1px;
  height: 12px;
  background: var(---color--brand-3);
  transform: rotate(15deg);

  @include respond-to($small-down) {
    height: 8px;
  }

}

.quiz-progress-bar__progress {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  background: var(---color--brand-1);
  transform: scaleX(var(--quiz-progress));
  transform-origin: left;
  transition: transform 0.25s;

  @include respond-to($small-down) {
    padding: 10px 0;
  }

}