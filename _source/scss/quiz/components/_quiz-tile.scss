/* ----- Tiles ----- */

.quiz-tiles {

  .quiz-tiles__container {
    
    display: flex;
    margin: var(--container-gutter) 0;
    gap: 30px;
    flex-wrap: wrap;
    justify-content: center;

    @include respond-to($medium-down) {
      gap: 15px;
    }

  }

}

.quiz-tiles__input {
  @include visually-hidden();
}

.quiz-tile {

  display: flex;
  flex-direction: column;
  align-items: center;

  padding: 20px;
  border: 2px solid var(---color--brand-3);

  border-radius: 8px;
  background-color: var(---color--brand-2);
  transition: 0.25s border-color;

  cursor: pointer;

  &:hover {
    background: var(---color--primary--dark);
  }

  &:focus {
    border-color: var(---color--highlight);
  }

  &.quiz-tile--selected,
  &:focus {
    
    border-color: var(---color--highlight);

    .quiz-tile__icon--default:not(:only-child) {
      opacity: 0;
    }

    .quiz-tile__icon--hover {
      opacity: 1;
    }

  }

  .quiz-tile__icon {
    position: relative;
  }

  .quiz-tile__icon--hover {
    position: absolute;
    left: 0;
    top: 0;
    opacity: 0;
  }

  .quiz-tile__text__hint {
    display: block;
  }

  .quiz-tile__text__title {
    display: block;
  }

  @include respond-to($small-up) {

    .quiz-tile__text__hint {
      display: none;
    }

  }

  @include respond-to($small-down) {

    flex-direction: row;
    gap: 20px;
    width: 100%;
    padding: 10px;

    .quiz-tile__text {
      text-align: left;
    }

    .quiz-tile__icon {

      flex: 1 0 150px;
      max-height: 100px;
      max-width: 100px;
      width: 100%;
      height: 100%;

      img {
        max-height: 100px;
        max-width: 100px;
        width: 100%;
        height: 100%;
      }
    }

  }
}

.quiz-tile__icon {
  img {
    height: 150px;
    width: 150px;
  }
}