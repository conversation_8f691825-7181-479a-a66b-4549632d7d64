body.template-product {
  
  .product-form {
    gap: 20px;
  }

  .product-meta__title {

    font-family: var(---font-family-body);
    font-weight: var(---font-weight-body--bold);

    color: var(---color-products-title);

    font-size: var(---font-size-product-title--major--mobile);
    @media(min-width: 1000px) {
        font-size: var(---font-size-product-title--major--desktop);
    }

  }

  .product-meta__vendor-link {
    display: inline-flex;
    align-items: center;
    gap: 1em;
  }

  .product-meta__vendor-logo {
    max-height: 60px;
    width: auto;
  }

  .product-meta__share-button-item {
    color: var(---color-text);
  }

  .product-tabs__trust-title {
    color: var(---color-text);
  }

  /* ----- Product Gallery ----- */

  .product__zoom-button {
    color: var(---color--secondary);
    border-color: RGBA(var(---color--secondary--rgb), 0.25);
    background-color: var(---background-color--content-1);
    transition: 0.25s color, 0.25s background-color;

    &:focus,
    &:hover {
      color: var(---color-text--reversed);
      background-color: var(---color--secondary);
    }
  }

  /* ----- Trust Buttons ----- */

  .trust-buttons {

    display: flex;
    flex-direction: column;
    gap: 12px;

  }

  .trust-button {

    --button-color--1: var(---color--secondary);
    --button-color--1--hover: var(---background-color--default);

    display: flex;
    align-items: center;
    gap: 10px;

    &:focus,
    &:focus-within,
    &:hover {
      .trust-button__icon {
        svg {
          color: var(--button-color--1);
        }
      }

      .trust-button__text {
        color: var(--button-color--1);
      }

      .trust-button__icon-right {
        transform: translateX(0);
      }
    }

  }

  .trust-button__icon {

    border: 1px solid var(---color-line);
    background: var(--button-color--1--hover);
    border-radius: 100%;
    padding: 4px;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;

    transition: 0.25s background-color;

    svg {
      
      width: 16px;
      height: 16px;
      
      color: var(---color--primary);

      * {
        transition: 0.25s color;
      }
    }
  }

  .trust-button__icon-right {
    margin-left: auto;
    color: var(---color--primary);
    transition: transform 0.25s;
    transform: translateX(-50%);

    svg {
      width: 10px;
      height: 10px;
    }
  }

  .trust-button__text {
    
    font-size: var(---font-size-body--mobile);
    font-weight: var(---font-weight-body--bolder);
    transition: 0.25s color;

    @include respond-to($small-up) {
      font-size: var(---font-size-body--desktop);
    }

  }

  /* ----- Payment Plan Notice ----- */

  .payment-plan-notice {
    display: flex;
    align-items: center;
    gap: 0.5em;
    @include respond-to($small-down) {
      flex-direction: column;
    }
  }

  .payment-plan-notice__image-container {
    max-width: 100px;
  }

  .payment-plan-notice__text {
    font-size: var(---font-size-body-xs--mobile);
    font-style: italic;
    color: var(---color-text--light);

    strong {
      color: var(---color-text--dark);
    }

    @media(min-width: 1000px) {
      font-size: var(---font-size-body-xs--desktop);
    }
  }
  
  /* ----- Inventory ----- */

  .inventory {

    display: inline-flex;
    align-items: center;
    color: RGB(var(---color-text));

    font-size: var(---font-size-body-small--mobile);
    @include respond-to($medium-up) {
      font-size: var(---font-size-body-small--desktop);
    }

    &:before {
      width: 16px;
      height: 16px;
      content: '';
      display: inline-block;
      margin-right: 0.5em;
      background-color: currentColor;
      border-radius: 100%;
      color: RGB(var(--product-in-stock-text-color));
    }

    &.inventory--low {
      // color: RGB(var(--product-low-stock-text-color));
      &:before {
        color: RGB(var(--product-low-stock-text-color));
      }
    }

    &.inventory--sold-out {
      // color: RGB(var(--product-no-stock-text-color));
      &:before {
        color: RGB(var(--product-no-stock-text-color));
      }
    }

  }

}
