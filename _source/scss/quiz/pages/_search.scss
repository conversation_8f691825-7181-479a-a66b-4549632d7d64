body.template-search {

  .mobile-toolbar {
    &.is-collapsed {
      margin-top: 0;
    }
  }

  .search-header {
    background: var(---background-color--tertiary);

    .tabs-nav,
    .page-header__text-wrapper {
      margin-bottom: 0;
    }

    .tabs-nav {
      .tabs-nav__item {
        --heading-color: var(---color--tertiary--rgb);

      }

      .tabs-nav__position {
        color: RGB(var(---color--tertiary--rgb));
      }
    }
  }

  .search-body {
    
    padding: var(--vertical-breather) 0;

    .product-facet {
      margin-top: 0;
    }

    &.search-body--has-results {
      background: var(---background-color--content-1);
    }
    &.search-body--no-results {
      background: var(---background-color--tertiary);
    }

  }

  .main-search__submit {
    top: 50%;
    transform: translateY(-100%);
  }

  .main-search__input {
    background: var(---input-background);
    border-radius: var(---border-radius--inputs);
  }

  .main-search__submit {}

}