// Breakpoint Helpers

/*================ Build Base Grid Classes ================*/

@include responsive-display-helper();
@include responsive-text-align-helper();

/*================ Build Responsive Grid Classes ================*/

@each $breakpoint in $breakpoint-has-widths {

  @include respond-to($breakpoint) {

    @include responsive-display-helper('#{$breakpoint}--');
    @include responsive-text-align-helper('#{$breakpoint}--');

    .br--#{$breakpoint} {
      display: block;
    }

  }

}

.clearfix {
  @include clearfix();
}

.fallback-text,
.visually-hidden {
  @include visually-hidden();
}

.hidden {
  display: none;
}

.uppercase,
.text-transform--uppercase {
  text-transform: uppercase !important;
}

.text-transform--none {
  text-transform: none !important;
}

.strikethrough {
  text-decoration: line-through;
}

// Colors

@each $color in $semiotics {
  .color--#{$color} {
    color: var(---color--#{$color}) !important;
  }
}

@each $color in $semiotics {
  .background-color--#{$color} {
    background: var(---color--#{$color});
  }
}


.justify-content-center {
  justify-content: center !important;
}

.object-position--top {
  object-position: top !important;
}

.object-position--bottom {
  object-position: bottom !important;
}

.object-position--center {
  object-position: center !important;
}

.object-position--left {
  object-position: left !important;
}

.object-position--right {
  object-position: right !important;
}

// Text

.text-align--center {
  text-align: center !important;
}

.text-align--left {
  text-align: left !important;
}

.text-align--right {
  text-align: right !important;
}

.text-align--center--mobile {
  @include respond-to($small-down) {
    text-align: center !important;
  }
}

.text-align--left--mobile {
  @include respond-to($small-down) {
    text-align: left !important;
  }
}

.text-align--right--mobile {
  @include respond-to($small-down) {
    text-align: right !important;
  }
}

// Layout

.no-margin {
  margin: 0 !important;
}

.no-margin--top {
  margin-top: 0 !important;
}

.no-margin--right {
  margin-right: 0 !important;
}

.no-margin--left {
  margin-left: 0 !important;
}

.no-margin--bottom {
  margin-bottom: 0 !important;
}

.no-padding {
  padding: 0 !important;
}

.no-padding--top {
  padding-top: 0 !important;
}

.no-padding--right {
  padding-right: 0 !important;
}

.no-padding--left {
  padding-left: 0 !important;
}

.no-padding--bottom {
  padding-bottom: 0 !important;
}

.padding-left--10 {
  padding-left: 10px !important;
}

.padding-left--20 {
  padding-left: 20px !important;
}

.padding-left--30 {
  padding-left: 30px !important;
}

.padding-left--40 {
  padding-left: 40px !important;
}

.padding-left--50 {
  padding-left: 50px !important;
}

.padding-right--10 {
  padding-right: 10px !important;
}

.padding-right--20 {
  padding-right: 20px !important;
}

.padding-right--30 {
  padding-right: 30px !important;
}

.padding-right--40 {
  padding-right: 40px !important;
}

.padding-right--50 {
  padding-right: 50px !important;
}

.padding-top--10 {
  padding-top: 10px !important;
}

.padding-top--20 {
  padding-top: 20px !important;
}

.padding-top--30 {
  padding-top: 30px !important;
}

.padding-top--40 {
  padding-top: 40px !important;
}

.padding-top--50 {
  padding-top: 50px !important;
}

.padding-bottom--10 {
  padding-bottom: 10px !important;
}

.padding-bottom--20 {
  padding-bottom: 20px !important;
}

.padding-bottom--30 {
  padding-bottom: 30px !important;
}

.padding-bottom--40 {
  padding-bottom: 40px !important;
}

.padding-bottom--50 {
  padding-bottom: 50px !important;
}

body.logged-in {
  .logged-in--hidden {
    display: none !important;
  }
}

body.logged-out {
  .logged-out--hidden {
    display: none !important;
  }
}

.fraction {
  margin-left: 0.25em;
  font-size: 0.75em;
  letter-spacing: -0.1em;
}