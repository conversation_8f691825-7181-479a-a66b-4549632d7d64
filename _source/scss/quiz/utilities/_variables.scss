$site-width: 1400px;

$container-width: 1200px;

$container-narrow-width: 800px;
$container-extra-narrow-width: 600px;

$container-gutter--desktop: 30px;
$container-gutter--mobile: 30px;

$megamenu-width: 1240px;

$section-spacer--desktop: 50px;
$section-spacer--mobile: 25px;

/*  ------------------------------
    Grid Variables
    ------------------------------ */

/**
 * Define your breakpoints. The first value is the prefix that shall be used for
 * your classes (e.g. `.small--one-half`), the second value is the media query
 * that the breakpoint fires at.
 */

$grid-small: 640px;
$grid-medium: 1008px;
$grid-large: 1140px;

$small: 'small';
$small-down: 'small-down';
$small-up: 'small-up';
$medium: 'medium';
$medium-down: 'medium-down';
$medium-up: 'medium-up';
$large: 'large';
$large-down: 'large-down';
$large-up: 'large-up';

// The `$breakpoints` list is used to build our media queries.
// You can use these in the media-query mixin.
$breakpoints: (
  $small-down '(max-width: #{$grid-small})',
  $small '(min-width: #{$grid-small + 1}) and (max-width: #{$grid-medium - 1})',
  $small-up '(min-width: #{$grid-small + 1})',
  $medium-down '(max-width: #{$grid-medium})',
  $medium '(min-width: #{$grid-medium + 1}) and (max-width: #{$grid-large - 1})',
  $medium-up '(min-width: #{$grid-medium + 1})',
  $large-down '(max-width: #{$grid-large})',
  $large '(min-width: #{$grid-large + 1})',
  $large-up '(min-width: #{$grid-large + 1})'
);


/**
 * Define which namespaced breakpoints you would like to generate for each of
 * widths, push and pull. This is handy if you only need pull on, say, desk, or
 * you only need a new width breakpoint at mobile sizes. It allows you to only
 * compile as much CSS as you need. All are turned on by default, but you can
 * add and remove breakpoints at will.
 *
 * Push and pull shall only be used if `$push` and/or `$pull` and `$responsive`
 * have been set to ‘true’.
 */
$breakpoint-has-widths: (
  'small',
  'small-up',
  'small-down',
  'medium',
  'medium-up',
  'medium-down',
  'large',
  'large-up',
  'large-down'
) !default;

$breakpoint-has-push:   (
  'small',
  'small-up',
  'small-down',
  'medium',
  'medium-up',
  'medium-down',
  'large',
  'large-up',
  'large-down'
) !default;

$breakpoint-has-pull:   (
  'small',
  'small-up',
  'small-down',
  'medium',
  'medium-up',
  'medium-down',
  'large',
  'large-up',
  'large-down'
) !default;



$color-default: 'default';
$color-primary: 'primary';
$color-secondary: 'secondary';
$color-tertiary: 'tertiary';
$color-success: 'success';
$color-warning: 'warning';
$color-danger: 'danger';
$color-info: 'info';
$color-link: 'link';

$semiotics: (
  $color-default,
  $color-primary,
  $color-secondary,
  $color-tertiary,
  $color-success,
  $color-warning,
  $color-danger,
  $color-info,
  $color-link
);



$headings: (
  h1,
  h2,
  h3,
  h4,
  h5,
  h6
)
