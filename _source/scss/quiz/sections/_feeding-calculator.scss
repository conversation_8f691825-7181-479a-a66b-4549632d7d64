.quiz {

  .section__header {

    .heading {
      span {
        color: #fff;
      }

    }

    .text--large {
      span {
        color: var(---color--highlight);
      }
    }

  }

  .feeding-calculator {

    --block-border-radius: 20px;

    --text-color: #2E2E2E;
    --heading-color: #2E2E2E;

    display: block;
    color: #2E2E2E;

    max-width: 800px;
    margin: auto;
    padding: 40px;
    background: #fff;

    border-radius: var(--block-border-radius);

    .text--subdued {
      color: RGB(var(---color-text--light--rgb));
    }

  }

  .feeding-calculator__inner {

  }


  .feeding-calculator__header {
    display: flex;
    width: 100%;
    justify-content: center;
    align-items: center;
    gap: 2em;

    @media(max-width: 800px) {
      flex-direction: column;
      gap: 1.5em;
    }
  }

  .feeding-calculator__body {

    hr {
      width: 400px;
      margin: 2em auto;
    }

  }

  .feeding-calculator__footer {
    margin-top: 30px;
    text-align: center;
  }

  /* ----- Days ----- */

  .feeding-calculator-days {
    display: flex;
    gap: 20px;
    justify-content: center;
    flex-wrap: wrap;
    
    margin-top: 40px;
  }

  .feeding-calculator-day {

    flex: 1 0 20%;

    padding: 20px 10px;
    border: 1px solid var(---color-line--light);

    text-align: center;

    border-radius: var(--block-border-radius);

    p {
      margin: 0;
    }

    @media (max-width: 800px) {
      min-width: 40%;
    }

    @media (max-width: 400px) {
      min-width: 100%;
    }

  }

  .feeding-calculator-day__image {
    img {
      max-width: 70px;
      margin: 20px 0;
    }
  }

  .feeding-calculator-day__amounts {
    margin-top: 0.5em;
    > div {
      line-height: 1.4;
    }
  }
  
  /* ----- Per Day Labels ----- */

  .feeding-calculator-perday {
    display: flex;
    gap: 10px;

    align-items: center;

    @media(max-width: 800px) {
      flex-direction: column;
    }

  }

  .feeding-calculator-perday__label {
    margin: 0;
  }

  .feeding-calculator-perday__quantity {

    display: flex;
    align-items: center;
    justify-content: center;

    padding: 0 0.5em;
    height: 50px;

    font-size: 1.4em;
    letter-spacing: -0.1em;
    font-weight: var(---font-weight-body--bold);

    /* Light Gray */
    border: 1px solid #E6E6E6;
    border-radius: 8px;
  }
}