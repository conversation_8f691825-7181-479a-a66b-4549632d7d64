.quiz {

  .quiz-results-product__footer {
    min-height: 50px;
  }

  /* ----- Products ----- */

  quiz-results-products {

    .gallery__list-wrapper {
      padding: var(--vertical-breather) 0;
    }
    
    .gallery__list {
      @include respond-to($large-up) {
        justify-content: center;;
      }
    }

    &.quiz-results-products--disabled {

      quiz-results-product {

        &:not(.quiz-results-product--selected) {
          opacity: 0.5;
          pointer-events: none;
        }

      }

    }

  }


  /* ----- Product ----- */

  .quiz-results-product {

    --quiz-results-product-color: var(---color--highlight);
    --section-block-background: var(---background-color--content-1--rgb);
    --text-color: RGB(var(---color-text--dark--rgb));

    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    width: 350px;

    background: RGB(var(--section-block-background));
    color: var(--text-color);
    box-shadow: 0 0 5px rgba(0, 0, 0, .15);

    border-radius: 8px;

    transform: scale3d(1);

    transition:
      transform 0.25s ease-in-out,
      box-shadow 0.25s ease-in-out,
      opacity 0.25s ease-in-out;

    .quiz-ribbon--recommended {
      display: none;
    }

    .quiz-results-product-button--soldout {
      display: none;
    }

    .quiz-results-product-button--add-to-cart {
      display: flex;
    }

    &.quiz-results-product--recommended {
    
      transform: scale(1.05);
  
      &:hover {
        transform: scale(1.075) !important;
      }

      .quiz-ribbon--recommended,
      .quiz-results-tag--recommended {
        display: inline-flex;
      }

    }

    &.quiz-results-product--soldout {
    
      .quiz-results-tag--soldout {
        display: inline-flex;
      }

      .quiz-results-tag__inner {
        display: inline-block;
      }

      .quiz-results-product-button--soldout {
        display: flex;
      }

      .quiz-results-product-button--add-to-cart {
        display: none;
      }

    }

    &:hover {
      transform: scale(1.025);
      box-shadow: 0 10px 25px rgba(0, 0, 0, .1);
      opacity: 1 !important;
    }

    &:not(:first-child) {
      margin-left: 30px;
    }

    &:before {

      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      margin: auto;
      display: inline-block;
      content: '';
      width: 140px;
      height: 140px;
      background: var(--quiz-results-product-color);
      z-index: 0;
      outline: 3px solid var(--quiz-results-product-color);
      border-radius: 100%;
      transform: translateY(-15%);
      
    }

    &:not(.quiz-results-product--selected) {
      .quiz-results-product__checkbox-button {
        &:hover {
          .quiz-results-product__checkbox-input {
            background-color: RGBA(var(---color--brand-2--rgb), 0.25);

            &:before {
              opacity: 1;
            }
          }
        }
      }
    }

    .gallery__figure {
      padding: 30px;
    }

    .price--highlight {
      color: var(---color-price);
    }

    .quiz-results-product__checkbox-label {
      font-weight: var(---font-weight-body--bold);
    }

    @include respond-to($small-down) {
      max-width: 80vw;
    }

    // Tags

    .quiz-results-product__tags {
      margin: 1em 0;
    }

    .quiz-results-tag {
      
      padding: 0.5em 1.25em;
      border-radius: 100px;
      display: none; // hidden by default
      font-weight: bold;

      &.quiz-results-tag--recommended {
        background: RGB(var(---color--highlight--rgb));
      }

      &.quiz-results-tag--soldout {
        background: RGB(var(---color--danger--rgb));
        color: RGB(var(---color-text--reversed--rgb));
      }

    }

    .quiz-results-tag__inner {
      display: inline-block;
    }

  }

  .quiz-results-product--selected {

    .quiz-results-product__checkbox-input {
      background-color: RGB(var(---color--brand-2--rgb));

      &:before {
        opacity: 1;
      }
    }

    ~.quiz-results-product:not(.quiz-results-product--selected) {
      // opacity: 0.5;
    }

  }

  /* ----- Inner ----- */

  .quiz-results-product__inner {

    position: relative;
    z-index: 1;

  }

  .quiz-results-product__header {

    position: relative;
    padding-bottom: 80px;
    background: var(--quiz-results-product-color);

    border-top-left-radius: 8px;
    border-top-right-radius: 8px;

  }

  .quiz-results-product__footer {
    border-top: 1px solid var(---color-line--light);
  }

  .quiz-results-product__image {

    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    z-index: 1;
    margin: auto;

    transform: translateY(-15%);

    width: 140px;
    height: 140px;
    border: 10px solid transparent;
    border-radius: 100%;
    object-fit: cover;

  }

  /* ----- Checkbox ----- */

  .quiz-results-product__checkbox-button {

    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;

    min-height: 50px;
    width: 100%;

  }

  .quiz-results-product__checkbox {

    display: flex;
    align-items: center;
    gap: 0.5em;

  }

  .quiz-results-product__checkbox-input {

    display: flex;

    width: 24px;
    height: 24px;
    border-radius: 4px;
    background-color: RGB(var(---color-line--light--rgb));

    transition: 0.25s background-color ease-in-out;

    &:before {

      content: '';
      display: block;
      flex: 1 0 auto;
      background-image: url("data:image/svg+xml,%3Csvg width='16' height='13' viewBox='0 0 16 13' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M15 1L5.375 12L1 7' stroke='white' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E%0A");
      background-repeat: no-repeat;
      background-position: center;
      opacity: 0;
      transition: 0.25s opacity ease-in-out;

    }

  }

    /* ----- View Details ----- */

  .quiz-results-product__view-details {

    display: flex;
    justify-content: center;
    text-align: center;
    margin-top: 12px;

  }

  .quiz-results-product__details {

    position: relative;
    margin: 0;
    padding: 50px 20px 20px;

    text-align: center;
    background: var(---background-color--content-1);

  }

  .quiz-ribbon {

    --ribbon-color: var(--section-block-background);

    display: inline-flex;
    justify-content: center;
    align-items: center;

    position: absolute;
    width: 36px;
    height: 55px;

    font-size: 24px;
    font-weight: 700;

    background: RGB(var(--ribbon-color));

    &.quiz-ribbon--number {
      --ribbon-color: var(--section-block-background);
      left: 30px;
    }
  
    &.quiz-ribbon--recommended {
      --ribbon-color: var(---color--highlight--rgb);
      right: 30px;
    }

    &:after {
      bottom: 0;
      left: 50%;
      border: solid transparent;
      content: "";
      height: 0;
      width: 0;
      position: absolute;
      pointer-events: none;
      border-color: rgba(136, 183, 213, 0);
      border-bottom-color: var(--quiz-results-product-header-color);
      border-width: 16px;
      margin-left: -16px;

      transform: scaleY(0.70);
      transform-origin: bottom;
    }

    > span {
      margin-top: -5px;
      text-align: center;
      letter-spacing: -.15em;
      width: 100%;
    }

    .quiz-ribbon__bottom {

      svg {
        position: absolute;
        width: 100%;
        bottom: 2px;
        left: 0;
        right: 0;
        transform: translateY(100%);

        * {
          fill: RGB(var(--ribbon-color));
        }
      }

    }

  }


  /* ----- Sticky Form ----- */

    .quiz-sticky-form {
  
      top: unset;
      bottom: 0;
      padding: 20px 0;
  
      background: RGB(var(--background));
      border-top: RBB(var(--border-color));
  
      @include respond-to($medium-up) {
        padding: 30px 0;
      }

      .quiz-sticky-form__title {
        
        display: inline-block;
        margin-bottom: 0.25em;
        margin-top: 0;

        span, strong {
          color: RGB(var(---color--highlight--rgb));
        }

      }
  
      .unit-price-measurement {
        color: #9a9a9a;
      }

      .unit-price-measurement {
        line-height: 1;
        vertical-align: baseline;

        a {
          color: var(---color--highlight);
          text-decoration: underline;
        }

        .unit-price-measurement__link {

          display: flex;
          flex-direction: column;
          justify-content: center;
          
          margin-left: 0.5em;

        }
      }
  
      .product-sticky-form__content-wrapper {
        @media (max-width: 1000px) {
          margin-bottom: 20px;
        }
      }
  
      .product-sticky-form__payment-container {
        display: flex;
        gap: 15px;
        @include respond-to($small-down) {
          flex-direction: column;
        }
      }
  
      .button-checkout {
        @include respond-to($small-down) {
          width: 100%;
        }
      }
  
    }


    /* ----- Quiz Results - Dogs ----- */
    
    
    /* ----- Quiz Results - Dog ----- */

    quiz-results-dog {

      display: none;

      &.quiz-results-dog--active {
        display: block;
      }

      .quiz-results-dog__header {
        text-align: center;
        padding: var(--vertical-breather-tight) 0;
      }

      .quiz-results-dog__title {
        span {
          color: RGB(var(---color--highlight--rgb));
        }
      }

      .quiz-results-dog__content {
        margin: var(--container-gutter) 0;
      }

    }
  

}