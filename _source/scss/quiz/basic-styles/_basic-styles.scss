/*  ==============================
    1. Root Styles
    ============================== */

  .quiz {
    
    background: RGB(var(--section-block-background));

    font-weight: var(---font-weight-body);
    font-style: var(---font-style-body);
    letter-spacing: var(---letter-spacing--body);

    font-size: var(---font-size-body--mobile);
    line-height: var(---line-height-body--mobile);

    @include respond-to($medium-up) {
      font-size: var(---font-size-body--desktop);
      line-height: var(---line-height-body--desktop);
    }

    scroll-behavior: smooth;

    ::selection {
      color: var(---color--highlight);
      background: RGBA(var(---color--highlight--rgb), 0.2); 
    }

  }