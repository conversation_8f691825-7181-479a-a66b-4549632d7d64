.quiz {

  /*  ------------------------------
    1. Inputs
    ------------------------------ */

  // 1.1. General

  textarea,
  select,
  input[type="text"],
  input[type="number"],
  input[type="email"],
  input[type="tel"],
  input[type="password"],
  // input[type="search"],
  input[type="date"] {

    &[disabled] {
      cursor: not-allowed;
    }

  }

  .input {

    margin-bottom: 0px;
    margin-top: 30px;

    .input__field {

      margin-bottom: 0;
      padding-left: 15px;
      padding-right: 15px;

      @include input-style--1();

      &[required="required"] {
        +.input__label {
          &:after {
            content: "*";
          }
        }
      }

    }

    .input__label {

      left: 0;
      transform: scale(.733) translateY(calc(-32px - 0.5em));

      font-weight: var(---font-weight-body);
      color: RGB(var(--text-color));

      font-size: var(---font-size-body--mobile);

      @include respond-to($small-up) {
        font-size: var(---font-size-body--desktop);
      }

    }

    .button {
      margin: 0;
    }

  }


  // 1.2. Selects

  select {
    
    appearance: none;
    background: transparent;
    background-image: var(---icon--chevron-down);
    background-position: right 1em top 50%;
    background-repeat: no-repeat;
    background-size: 14px;

    @include input-style--1();

  }

  option,
  optgroup {
    font-size: 1rem;
  }

  .select-wrapper {

    .select {

      color: var(---color-text--dark);
      border: 1px solid var(---color-line);
      border-radius: 0;

    }

  }

  .form__actions {

    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;

    // margin-top: var(--vertical-breather);
  
    text-align: center;
  
    .button {
      display: inline-flex;
    }
  
  }


  /*  ------------------------------
      2. Labels
      ------------------------------ */

  label {
    @include label-structure();
  }

  .label-style {
    @include label-style();
  }


  /*  ------------------------------
      3. Fieldsets
      ------------------------------ */

  fieldset {

    display: block;
    appearance: none;
    border: none;
    margin: 0;
    padding: 0;

  }


}