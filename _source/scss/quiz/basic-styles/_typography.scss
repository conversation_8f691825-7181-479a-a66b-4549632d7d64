
h1, .h1,
h2, .h2,
h3, .h3,
h4, .h4
// h5, .h5,
// h6, .h6,
// .heading--small 
{
  line-height: var(---line-height-heading--mobile);
  letter-spacing: var(---letter-spacing-heading--mobile);
}


  h1, .h1,
  h2, .h2,
  h3, .h3,
  h4, .h4,
  // h5, .h5,
  // h6, .h6 
  {
    
    font-family: var(--heading-font-family);
    font-weight: var(--heading-font-weight);
    font-style: var(--heading-font-style);
    color: rgb(var(--heading-color));
    text-transform: var(--heading-text-transform);
    display: block;

    letter-spacing: var(---letter-spacing-heading--mobile);
    font-weight: var(---font-weight-heading);
  }


  .heading--large, h1, .h1,
  .rte .heading--large,
  .rte h1,
  .rte .h1 {

    @include heading-style--1();

    font-size: var(---font-size-h0--mobile);
    @include respond-to($small-up){
      font-size: var(---font-size-h0--desktop);
    }

  }

  h1, .h1,
  .rte h1, .rte .h1 {

    @include heading-style--1();

    font-size: var(---font-size-h1--mobile);
    @include respond-to($small-up){
      font-size: var(---font-size-h1--desktop);
    }

  }

  h2, .h2,
  .rte h2, .rte .h2 {

    @include heading-style--1();

    font-size: var(---font-size-h2--mobile);
    @include respond-to($small-up){
      font-size: var(---font-size-h2--desktop);
    }

  }

  h3, .h3,
  .rte h3, .rte .h3 {

    @include heading-style--1();

    font-size: var(---font-size-h3--mobile);
    @include respond-to($small-up){
      font-size: var(---font-size-h3--desktop);
    }

  }

  h4, .h4,
  .rte h4, .rte .h4 {

    @include heading-style--1();

    font-size: var(---font-size-h4--mobile);
    @include respond-to($small-up){
      font-size: var(---font-size-h4--desktop);
    }

  }

  h5, .h5,
  .rte h5, .rte .h5 {

    @include heading-style--2();

    font-size: var(---font-size-h5--mobile);
    @include respond-to($small-up){
      font-size: var(---font-size-h5--desktop);
    }

  }

  h6, .h6,
  .rte h6, .rte .h6 {

    @include heading-style--2();

    font-size: var(---font-size-h6--mobile);
    @include respond-to($small-up){
      font-size: var(---font-size-h6--desktop);
    }

  }


  .subheading {
    color: RGB(var(--subheading-color));
  }

  .heading.heading--regular {

    letter-spacing: var(---letter-spacing-subheading--mobile);
    text-transform: uppercase;
    font-style: var(---font-style-heading);
    font-weight: var(---font-weight-heading);
  
    font-family: var(---font-family-heading);
    font-size: var(---font-size-subheading-large--mobile);
    
    @include respond-to($small-up) {
      font-size: var(---font-size-subheading-large--desktop);
    }


  }

  .product-sticky-form__title,
  .heading.heading--small {

    letter-spacing: var(---letter-spacing-subheading--mobile);
    text-transform: uppercase;
    font-style: var(---font-style-heading);
    font-weight: var(---font-weight-heading);

    font-family: var(---font-family-heading);
    font-size: var(---font-size-subheading--mobile);
    
    + p,
    + .h1,
    + h1,
    + .h2,
    + h2,
    + .h3,
    + h3,
    + .h4,
    + h4 {
        margin-top: 12px;
    }

    + hr {
      margin-top: 0;
    }

    @include respond-to($small-up) {
      font-size: var(---font-size-subheading--desktop);
    }

  }

  .heading.heading--xsmall {

    letter-spacing: var(---letter-spacing-subheading--mobile);
    text-transform: uppercase;
    font-style: var(---font-style-heading);
    font-weight: var(---font-weight-heading);

    font-family: var(---font-family-heading);
    font-size: var(---font-size-subheading-small--mobile);

    @include respond-to($small-up) {
      font-size: var(---font-size-subheading-small--desktop);
    }

  }


  // Text Content

  body {
    line-height: var(---line-height-body--mobile);
  }

  .text--small {

    margin-top: 0;

    font-size: var(---font-size-body--mobile);
    @include respond-to($small-up){
      font-size: var(---font-size-body--desktop);
    }

  }

  .text--xxsmall,
  .tiny {
    font-size: var(---font-size-body-xs--mobile);
    @include respond-to($small-up){
      font-size: var(---font-size-body-xs--desktop);
    }
  }

  .text--xsmall,
  .minor {
    font-size: var(---font-size-body-small--mobile);
    @include respond-to($small-up){
      font-size: var(---font-size-body-small--desktop);
    }
  }

  .text--large,
  .major {
    font-size: var(---font-size-body-large--mobile);
    @include respond-to($small-up){
      font-size: var(---font-size-body-large--desktop);
    }
  }

  .p--mobile {

    @include respond-to($small-down){

      font-size: var(---font-size-body--mobile);
      font-family: var(---font-family-body);
      font-weight: var(---font-weight-body);

    }

  }

  .text--xxsmall,
  .tiny {
    font-size: var(---font-size-body-xs--mobile);
    @include respond-to($small-up){
      font-size: var(---font-size-body-xs--desktop);
    }
    p {
      font-size: var(---font-size-body-xs--mobile);
      @include respond-to($small-up){
        font-size: var(---font-size-body-xs--desktop);
      }
    }
  }

  .text--xxsmall--mobile,
  .p-tiny--mobile {

    @include respond-to($small-down){
      font-family: var(---font-family-body);
      font-weight: var(---font-weight-body);
      font-size: var(---font-size-body-xs--mobile);
    }

  }

  .text--xsmall,
  .minor {
    font-size: var(---font-size-body-small--mobile);
    @include respond-to($small-up){
      font-size: var(---font-size-body-small--desktop);
    }
    p:not(.heading) {
      font-size: var(---font-size-body-small--mobile);
      @include respond-to($small-up){
        font-size: var(---font-size-body-small--desktop);
      }
    }
  }

  .text--small--mobile,
  .p-minor--mobile {

    @include respond-to($small-down){
      font-size: var(---font-size-body-small--mobile);
      font-family: var(---font-family-body);
      font-weight: var(---font-weight-body);
    }

  }

  .text--large,
  .major {
    font-size: var(---font-size-body-large--mobile);
    @include respond-to($small-up){
      font-size: var(---font-size-body-large--desktop);
    }
    p:not(.heading) {
      font-size: var(---font-size-body-large--mobile);
      @include respond-to($small-up){
        font-size: var(---font-size-body-large--desktop);
      }
    }
  }

  .text--large--mobile,
  .p-major--mobile {

    @include respond-to($small-down){
      font-size: var(---font-size-body-large--mobile);
      font-family: var(---font-family-body);
      font-weight: var(---font-weight-body);
    }

  }

  strong, .strong {

    // --color: var(---color-text-dark);

    font-weight: var(---font-weight-body--bold);

  }

  .link.link--strong {
    font-weight: var(---font-weight-body--bold);
    text-decoration: none;
  }

  .blockquote, blockquote {

    /*
    font-size: var(---font-size-h2--mobile);
    @include respond-to($small-up){
      font-size: var(---font-size-h2--desktop);
    }
    */

    font-size: var(---font-size-h3--mobile);
    @include respond-to($small-up){
      font-size: var(---font-size-h3--desktop);
    }

  }


  /* Product Titles */

  .product-item {

    .product-item-meta__title {

      font-family: var(---font-family-heading);
      font-weight: var(---font-weight-body--bold);
      line-height: var(---line-height-heading--mobile);
      letter-spacing: var(---letter-spacing-subheading--mobile);

      font-size: var(---font-size-body-large--mobile);
      @include respond-to($small-up){
        font-size: var(---font-size-body-large--desktop);
      }
  
    }

  }

  .product-meta__title {

    font-size: var(---font-size-h1--mobile);
    @include respond-to($small-up){
      font-size: var(---font-size-h1--desktop);
    }

  }

  /* Header */

  .header__linklist {
    // font-family: var(---font-family-heading);
    font-weight: var(---font-weight-body--bold);
  }





  .quiz {

    h1, .h1,
    h2, .h2,
    h3, .h3,
    h4, .h4,
    // h5, .h5,
    // h6, .h6 
    {

      margin-top: 36px;
      margin-bottom: 12px;

    }

  }


  .jdgm-carousel-title {
    @extend .h2;
  }