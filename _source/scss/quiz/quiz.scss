// @import 'https://use.typekit.net/bdo8yri.css';

/* 1. Variables */

// @import "./utilities/_variables.scss";
@import "./utilities/_variables--focal.scss";

/* 2. Mixins */

@import "./utilities/_mixins.scss";

/* 3. Fonts  */

@import "./fonts/_fonts.scss";

/* 4. Basic Styles */
// @import "./utilities/_normalize.scss"; // Prestige already contains Normalize

@import
  // "./basic-styles/_theme-overrides.scss",
  "./basic-styles/_basic-styles.scss",
  "./basic-styles/_form-elements.scss",
  "./basic-styles/_images.scss"
;

/* 5. Layout */

@import
  "./layout/_body-modifiers.scss"
  ;

/* 6. Sections */

@import

  // Global Section Styling
    "./sections/_feeding-calculator.scss",

  // Override Theme Sections
    // "./sections/_mini-cart.scss",

  // Custom Sections
  // "./sections/_custom-section.scss",
    "./sections/_quiz-results.scss",
  
  // Bespoke Sections
  // "./sections/_bespok-section.scss",

  // Misc. Section Styling
  "./sections/__sections.scss"

  ;

/* 7. Page-Specific Styles */

@import
  "./pages/__pages.scss"
  ;

/* 8. Components */

@import
  
  "./components/_theme-sticky-form.scss",

  "./components/_drawers.scss",
  // "./components/_buttons.scss",
  // "./components/_modal.scss",
  "./components/_quiz-progress.scss",
  "./components/_quiz-step.scss",
  
  // "./components/_revealing-forms.scss",
  // "./components/_split-page-step.scss",
  
  // "./components/_expanding-input.scss",

  "./components/_quiz-tile.scss",
  "./components/_quiz-substep.scss",
  "./components/_quiz-loading-overlay.scss",
  
  // "./components/_box-line-item.scss",

  "./components/__components.scss"

  ;

// @import

  // Global Elements

  // Collection Elements

  // Product Elements

  // Cart Elements

  // Misc. Elements
// "./sections/_quiz-results.scss"

  // ;

/* 9. Apps  */

// @import
  // "./apps/__apps.scss"
  // ;

/* 10. Utility Classes */

// @import 
  // "./utilities/_classes.scss"
  // ;

/* 11. Third-Party Styles */

@import 
  // "./third-party/_nice-select.scss",
  "./third-party/__thirdparty.scss"
  ;

/* 12. Animations */

@import 
  "./utilities/_animations.scss"
  ;