/* 1. Variables */

// @import "./utilities/_variables.scss";
@import "./utilities/_variables--focal.scss";

/* 2. Mixins */

@import "./utilities/_mixins.scss";

/* 3. Fonts  */

// @import "./fonts/_fonts.scss";

/* 4. Basic Styles */
// @import "./utilities/_normalize.scss"; // Prestige already contains Normalize

@import
  "./basic-styles/_theme-overrides.scss",
  // "./basic-styles/_basic-styles.scss",
  "./basic-styles/_typography.scss",
  "./basic-styles/_icons.scss",
  "./basic-styles/__basic-styles.scss"
;

/* 5. Layout */

@import
  "./layout/_split-page.scss",
  "./layout/_body-modifiers.scss"
  ;

/* 6. Sections */

@import
 
  // Global Section Styling
    // "./sections/_section.scss",
    "./sections/_theme-customers-account.scss",
    "./sections/_mini-cart.scss",

  // Override Theme Sections
    // "./sections/_theme-section.scss",

  // Custom Sections
  "./sections/_custom-dog-info.scss",
  "./sections/_custom-main-product.scss",
  "./sections/_feeding-calculator-standalone.scss", 
  // "./sections/_custom-section.scss",
  
  // Bespoke Sections
  // "./sections/_collection-seo-description.scss",
  // "./sections/_bespoke-section.scss",

  // Misc. Section Styling
  "./sections/__sections.scss";

/* 7. Page-Specific Styles */

@import
  // "./pages/_home.scss",
  // "./pages/_collection.scss",
  // "./pages/_product.scss",
  // "./pages/_cart.scss",
  // "./pages/_blog.scss",
  // "./pages/_article.scss",
  // "./pages/_page.scss",
  // "./pages/_page-about.scss",
  // "./pages/_page-rewards.scss",
  // "./pages/_search.scss",
  "./pages/__pages.scss"
  ; 

/* 8. Components */

@import

  // Global Elements
  "./components/_buttons.scss",
  "./components/_modal.scss",
  "./components/_form-elements.scss",
  "./components/_revealing-forms.scss",
  "./components/_expanding-input.scss",
  "./components/_box-line-item.scss",
  "./components/_tile-radios.scss",
  "./components/_tables.scss",

  "./components/_shipping-bar.scss",
  
  "./components/_vet-sticky-bar.scss",
  "./components/_cart-vet-partner.scss",

  // Collection Elements
    // "./components/_component.scss",

  // Product Elements
    // "./components/_component.scss",

  // Cart Elements
    // "./components/_component.scss",

  // Misc. Elements
  "./components/_split-page-step.scss",
  "./components/_banners.scss",
  "./components/_weight-range.scss",

  "./components/__components.scss"

  ;

/* 9. Apps  */ 

@import
  "./apps/_awtomic.scss",
  "./apps/_judgeme.scss",
  "./apps/_section-store.scss",
  "./apps/__apps.scss" 
  ;

/* 10. Utility Classes */

@import 
  "./utilities/_classes.scss"
  ;

/* 11. Third-Party Styles */

@import 
  "./third-party/_nice-select.scss",
  "./third-party/__thirdparty.scss"
  ;

/* 12. Animations */

@import 
  "./utilities/_animations.scss"
  ;
 