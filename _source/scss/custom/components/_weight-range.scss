.weight-range {

  --track-height: 14px;
  --thumb-size: 40px;
  --thumb-icon-size: 80px;

  display: block;
  margin-top: var(--thumb-icon-size);

  position: relative;

}

.weight-range__inner {

  position: relative; //

  line-height: 0;
  height: var(--track-height);
  border-radius: 100px;
  background: RGB(var(---color--brand-2--rgb));
  outline: 2px solid RGB(var(--text-color));

}

.weight-range__thumb {

  pointer-events: none;

  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;

  position: absolute;
  left: var(--range-position);

  transform:
    translateX(calc(-1 * var(--thumb-icon-size) / 2)) translateY(50%);


  // transform: translateX(calc(-1 * var(--thumb-icon-size) / 2 + calc(var(--offset) / 2)));

  bottom: 0;
  bottom: 50%;
  //  bottom: calc(var());
  z-index: 1;

  //   display: none;

}



.weight-range__range {
  height: var(--track-height);
}

@mixin thumb-styling() {

  height: var(--thumb-icon-size);
  width: var(--thumb-icon-size);

  transform:
    translateY(calc(calc(-1 * var(--thumb-size) / 2) - calc(-1 * var(--track-height) / 2)))
    translateX(calc(100 * var(--range-offset)));

  opacity: 0;

}

.weight-range__range::range-thumb { @include thumb-styling; };
.weight-range__range::slider-thumb { @include thumb-styling; };
.weight-range__range::-moz-range-thumb { @include thumb-styling; };
.weight-range__range::-webkit-slider-thumb { @include thumb-styling; };

.weight-range__range::range-track { opacity: 0; }
.weight-range__range::slider-track { opacity: 0; }
.weight-range__range::-moz-range-track { opacity: 0; }
.weight-range__range::-webkit-slider-runnable-track { opacity: 0; }

.weight-range__track {

  position: absolute;
  left: 0;
  top: 0;
  height: var(--track-height);
  width: var(--range-position);

  background: RGB(var(---color--brand-1--rgb));
  border-radius: 100px;

  pointer-events: none;

}

.weight-range__thumb-icon {

  height: var(--thumb-icon-size);
  width: var(--thumb-icon-size);

  background-image: url(https://cdn.shopify.com/s/files/1/1683/1605/files/pup-thumb.png?v=1719171809);
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;

  //  transform: translateX(-50%);

}

.weight-range__thumb-value {

  display: inline-flex;
  // display: none;
  padding: 0em 0.5em;
  border-radius: 8px;

  background-color: RGB(var(---color--brand-1--rgb));
  outline: 2px solid RGB(var(--text-color));

  position: absolute;
  top: 0px;
  transform: translateY(-100%);

}

.weight-range__labels {

  display: flex;
  justify-content: space-between;
  margin-top: 0.25em;
  pointer-events: none;
  user-select: none;

}