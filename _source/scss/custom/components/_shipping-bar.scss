.shipping-bar {

  --background--unmet: RGBA(235, 87, 87, .3);
  --progress-background: #D9D9D9;
  --loading-bar-background: 255, 255, 255;

  position: relative;
  display: block;
  margin-top: 15px !important;

  .shipping-bar__progress {

    background: var(--progress-background);

    &:before {

      content: '';
      display: block;
      position: absolute;
      width: 2px;
      height: 100%;
      background: currentColor;
      left: calc(var(--frozen-threshold) * 100%);
      z-index: 10;

    }
  }

  &.shipping-bar--frozen-food--unmet {
    background: var(--background--unmet) !important;
    .shipping-bar__progress {
      &:after {
        background: var(---color--danger) !important;
      }
    }
  }

}

.shipping-bar__icon {

  --icon-size: 44px;

  position: absolute;
  left: 0;
  top: 0;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: var(--icon-size);
  height: var(--icon-size);

  vertical-align: top;

  background: #fff;

  border-radius: 100%;
  box-shadow: 0 0 5px rgba(0, 0, 0, .1);

  transform: translate(-50%, -30%);

}

.shipping-bar__text {
  line-height: 1.4;
  margin-bottom: 0.5em;
  display: inline-block;
}