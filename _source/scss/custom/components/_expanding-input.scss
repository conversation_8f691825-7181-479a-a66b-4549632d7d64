expanding-input {

  position: relative;

  select {
    
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    height: 100%;
    width: 100%;
    margin: 0 !important;
    opacity: 0;
    
    &:focus,
    &:hover {
      + .expanding-input__display {
        outline: none;
        border-color: var(---color--highlight);
      }
    }

  }

}

.expanding-input__display {

  padding-left: 0.5em;
  padding-right: 0.5em;

  cursor: text;

  &:after {
    transition: color 0.25s;
  }

  &:empty {
    color: var(---color--secondary);

    &:after {
      content: attr(data-default);
      font-weight: 300;
    }
  }

  &:focus {
    color: var(---color--highlight);
  }

}

.expanding-input--select {

  cursor: pointer;

  .expanding-input__display {

    padding-right: 50px;
    background-image: url("data:image/svg+xml,%3Csvg width='12' height='6' viewBox='0 0 12 6' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M1 0.5L6 5.5L11 0.5' stroke='%23E6E6E6' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E%0A");
    background-size: 15px;
    background-repeat: no-repeat;
    background-position: calc(100% - 10px);

    pointer-events: none;

  }

}

.expanding-input__input {
  &:not(select) {
    @include visually-hidden();
  }
}

select.expanding-input__input {

  // Windows Select Styling
  option {
    background-color: #e6e6e6 !important;
    text-align: left !important;
    font-size: var(---font-size-body-large--desktop) !important;
    color: var(---color-text) !important;
  }

}