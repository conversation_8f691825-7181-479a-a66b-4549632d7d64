.box-line-item {

  --box-line-item-padding: 20px;

  .product-item-tag--frozen {
    background: RGB(var(--root-background));
  }

}

.box-line-item__inner {

  margin-bottom: 20px;
  padding: var(--box-line-item-padding) 0;

  background: rgba(var(---color--brand-5--rgb), 0.25);
  border-radius: 8px;

}

.box-line-item__body {

  display: flex;
  gap: var(--box-line-item-padding);

  padding: 0 var(--box-line-item-padding);

}

.box-line-item__image {
  max-width: 92px;
  width: 100%;
  object-fit: cover;
}

.line-item__image-inner {
  position: relative;
}

.box-line-item__contents {
  width: 100%;
}

.box-line-item__footer {
  display: flex;
  justify-content: space-between;

  border-top: 1px solid rgba(var(---color--brand-5--rgb), 1);
  padding: 0 var(--box-line-item-padding);
  padding-top: 10px;
  margin-top: 15px;
}