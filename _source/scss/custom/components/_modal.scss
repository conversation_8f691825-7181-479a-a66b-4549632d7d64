  .modal {

    --background: var(---background-color--content-1--rgb);

    .modal__close-button {
      top: 26px;
      right: 26px;
    }

    .modal__header {
      text-align: center;
      padding-top: 24px;
    }

    .modal__title {
      font-size: var(---font-size-button-large--desktop);
    }

    .modal__content {

      border-radius: 8px;

    }

    .form__actions {
      margin-top: 2em;

      .button {
        @include respond-to($small-down) {
          width: 100%;
          justify-content: center;
        }
      }

    }

  }

  .modal--login {

    .quiz-modal-footer {

      text-align: center;
      padding-bottom: var(--vertical-breather);

      font-size: var(---font-size-body--desktop);

    }

  }

  .modal--register {

    .modal__content {
      overflow: visible;
    }

    .quiz-modal__image {
      width: 185px;
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      margin: auto;

      transform: translateY(-60%);
    }

    .newsletter-modal__content {
      padding-top: 90px !important;
    }

    .quiz-modal-footer {
      margin-top: 20px !important;
    }

    .button--link {
      transform: translateY(calc(var(--vertical-breather)));
    }

  }

  .recipe-modal {
    .newsletter-modal {
      flex-direction: column;
    }

    .newsletter-modal__content {
      padding: var(--container-gutter);
      text-align: left;
    }

    .modal__close-button {
      color: var(---color-text--reversed);
      transition: transform 0.25s;

      &:hover {
        transform: rotate(90deg);
      }
    }
  }

  .modal--upsells {

    /* ----- Quiz Results Product ----- */

    .quiz-results-product {
      width: 300px;
    }

    .quiz-results-product__header {
      img {
        margin: auto;
      }
    }

    .quiz-results-product__footer {
      display: flex;
      justify-content: center;
      padding: 0 20px;
    }

    /* ----- Layout ----- */

    .quiz-modal-content__header {
      padding: 0 40px;
      margin: auto;
      max-width: 450px;
    }

    .newsletter-modal__content {
      max-width: unset;
      padding-top: var(--container-gutter);
      padding-bottom: var(--container-gutter);
      background: var(---background-color--content-2);
    }

    .quiz-results-product__footer-price {
      display: flex;
      margin-right: auto;

      .price {
        font-size: 16px;
      }
    }

    .price-list {
      display: flex;
      align-items: center;
    }

    .gallery {

      @include respond-to($medium-down) {
        margin-left: calc(var(--container-gutter) * -1);
        margin-right: calc(var(--container-gutter) * -1);
      }

    }

  }