.tile-radio-input {
  // display: none;

  &:checked {
    +.tile-radio {
      background: var(---color--brand-7);
      border-color: var(---color--brand-7);
      outline: 2px solid var(---color--brand-2);
    }
  }
}

.tile-radios {
  display: flex;
  justify-content: center;
  gap: 15px;
  // gap: var(--container-gutter);
  flex-wrap: wrap;

}

.tile-radio {

  display: flex;
  align-items: center;
  gap: 0.75em;

  width: 100%;

  cursor: pointer;

  text-align: center;
  line-height: 1.1;

  padding: 10px;
  border: 1px solid var(---color-line--light);

  // border-radius: var(---input-border-radius);
  border-radius: 8px;
  box-shadow: 0 0px 0px rgba(0, 0, 0, .1);

  user-select: none;

  transition: transform 0.25s, box-shadow 0.25s;


  &:hover {
    transform: scale(1.05);
    box-shadow: 0 2px 10px rgba(0,0,0,.1);
  }

  // @media (min-width: 1000px) {
  @include respond-to($small-up) {
    text-align: center;
    width: auto;
    flex-direction: column;
    padding: 20px;
  }

}

.tile-radio__icon {
  pointer-events: none;
  width: 66px;
  height: 66px;
}

.tile-radio__content {
  display: flex;
  flex-direction: column;
  gap: 0.25em;
  text-align: left;

  // @media (min-width: 1000px) {
  @include respond-to($small-up) {
    text-align: center;
  }
}

.tile-radio__title {
  line-height: 1.2;
}

.tile-radio__description {
  line-height: 1.2;
}