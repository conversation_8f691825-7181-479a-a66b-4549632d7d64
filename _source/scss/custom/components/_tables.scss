.table {

  &.table--auto {
    table-layout: auto;
  }

  &.table--1 {

    border-radius: 4px;
    overflow: hidden;

    tr {
      &:hover {
        td {
          background: var(---color--brand-1);
        }
      }

    }

    td {
      background: var(---color--brand-7);
    }

    td,
    th {

      padding: 0.75em 0.25em;
      text-align: center;
      vertical-align: middle;
      line-height: 1.2;

      &:first-child {
        padding-left: 0.75em;
        text-align: left;
        font-weight: var(--text-font-bold-weight);
      }

      &:last-child {
        padding-right: 0.75em;
      }

    }

    thead {
      th {
        font-family: var(--heading-font-family);
        font-weight: var(--body-font-weight);
        font-size: 0.9em;
        letter-spacing: 0.05em;
        background: var(---color--brand-2);
        color: var(---color-text--reversed);
      }
    }

  }
}