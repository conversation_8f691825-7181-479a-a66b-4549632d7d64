.vet-sticky-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 9;

  box-shadow: var(---shadow--modal);

  transform: translateY(100%);
  transition: transform 500ms;

  // animation: vet-sticky-bar--tease 2s;

  &[open] {
    display: block;
    transform: translateY(0);
  }

  &:hover {
    &:not([open]) {
      transform: translateY(var(--tease-tug-1));
    }
  }

  .cart-vet-text {
    @include respond-to($small-down) {
      // display: flex;
      // justify-content: center;
      display: block;
      width: 100%;
    }
  }

  .cart-vet-partner {
    display: flex;
  }

  // Selector Form

  .vet-partner-selector-wrapper {
    @include respond-to($medium-up) {
      flex: 1 0 250px;
      max-width: 250px;
    }
  }
  
  .cart-vet-partner__vet {
    justify-content: flex-end;
    padding: 0;
    border: 0;
  }

  .cart-vet-partner__selector-form {

    @include respond-to($medium-up) {
      display: flex;
      justify-content: end;
    }

  }

  .cart-vet-partner__selector-button {
    flex: 0 2 max-content;
  }

  // Vet Text
  
  .cart-vet-partner__vet-text {
    padding: 15px 30px;
    font-size: var(---font-size-h6--desktop);
    background: var(---background-color--secondary);
    border-radius: 10px;

    @include respond-to($small-down) {
      display: block;
      width: 100%;
    }
  }

  .cart-vet-partner__product-notice {
    display: none;
  }

}

.vet-sticky-bar__tag {

  --tab-background: var(---color--highlight--rgb);
  --tab-color: var(---color--default--rgb);

  --edge-width: 30px;
  --edge-corner-radius: 8px;

  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  transform: translateY(-100%);

  display: flex;
  align-items: center;
  justify-content: center;

  width: max-content;
  padding: 0.5em;
  margin: auto;

  font-weight: var(--bold-font-weight);

  text-align: center;
  background: RGB(var(--tab-background));
  color: RGB(var(--tab-color));

  transition: background 250ms;

  &:hover {
    // --tab-color: var(---color--highlight--dark--rgb);
    --tab-background: var(---color--highlight--dark--rgb);
  }

  &:before,
  &:after {

    content: '';
    display: block;

    position: absolute;
    top: 0;
    z-index: -1;

    background: RGB(var(--tab-background));
    transition: background 250ms;
    width: var(--edge-width);
    height: 100%;

  }

  &:before {
    left: -10px;
    transform: skew(-10deg);
    border-top-left-radius: var(--edge-corner-radius);
  }

  &:after {
    right: -10px;
    transform: skew(10deg);
    border-top-right-radius: var(--edge-corner-radius);
  }

}


.vet-sticky-bar__wrapper {

  padding: 20px;

  background-color: RGB(var(--section-background));

  .container {
    @include respond-to($small-down) {
      padding: 0;
    }
  }

}

.cart-vet-partner__vet {

  @include respond-to($small-down) {
    padding-top: 10px;
    border-top: 1px solid var(---color-line--light);
  }

}

.vet-sticky-bar__inner {
  display: flex;
  flex-direction: column;

  @include respond-to($medium-up) {
    flex-direction: row;
    gap: 20px;
  }
}

.vet-sticky-bar__info {
  display: flex;
  gap: 10px;
}

.vet-sticky-bar__actions {
  display: flex;
  justify-content: flex-end;
}

.vet-sticky-bar__text {
  .subheading {
    margin: 0;
  }
}

@keyframes vet-sticky-bar--tease {

  0% {
    transform: translateY(var(--tease-start));
  }

  15% {
    transform: translateY(var(--tease-tug-1));
  }

  30% {
    transform: translateY(var(--tease-start));
  }

  60% {
    transform: translateY(var(--tease-start));
  }

  80% {
    transform: translateY(var(--tease-tug-2));
  }

  100% {
    transform: translateY(var(--tease-start));
  }

}

:root {
  @include respond-to($small-down) {
    --tease-start: 100%;
    --tease-tug-1: 95%;
    --tease-tug-2: 85%;
  }

  @include respond-to($small-up) {
    --tease-start: 100%;
    --tease-tug-1: 90%;
    --tease-tug-2: 70%;
  }
}