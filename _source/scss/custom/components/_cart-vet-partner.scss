/*  --------------------------------------------------
    Cart
    -------------------------------------------------- */

.cart-vet-partner {

  --vertical-spacing: 12px;
  --spacing: 12px;

  display: flex;
  flex-direction: column;
  gap: var(--vertical-spacing);

  transition: opacity 0.25s;

  &[loading] {
    pointer-events: none;
    opacity: 0.25;
  }

  .unlisted-vet-container {
    background: var(---background-color--secondary);
    border-radius: 4px;
    padding: .5em 1em;
    text-align: center;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
  }

}

.modal--vet-not-listed {
  .modal__overlay {
    pointer-events: none;
  }
}

.cart-vet-partner__inner {

  display: flex;
  flex-direction: column;
  gap: 12px;

}

.cart-vet-partner__selector-form {
  display: grid;
  grid-auto-flow: row;
  gap: var(--spacing);
  // grid-auto-flow: column;

  @include respond-to($medium-down) {
    grid-auto-flow: row;
  }

  select {
    @include respond-to($medium-up) {
      height: 100%;
    }
  }
}

.cart-vet-partner__vet {

  display: flex;
  align-items: center;
  justify-content: center;

  text-align: center;

}

.cart-vet-partner__product-notice {

  text-align: center;

}

.cart-vet-partner__notice {
  display: flex;
  padding: 16px;
  margin-top: var(--spacing);
  gap: var(--spacing);
  border: 2px solid var(---color--default);
  border-radius: var(--block-border-radius);
}

.cart-vet-partner__notice-text {
  .subheading {
    margin-bottom: 0.25em;
  }
}

.cart-vet-text {
  line-height: 1;
}



/*  --------------------------------------------------
    Account Page
    -------------------------------------------------- */

.account-vet-partner {

  max-width: 800px;
  padding: 0 0;
  margin: 0 auto;

  .account-vet-partner__inner {
    margin-top: 20px;
    padding: 20px;
    background: var(---background-color--secondary);
    border-radius: var(--block-border-radius);

    @include respond-to($medium-up) {
      padding: 30px;
    }
  }

  // Overrides

  .cart-vet-partner__selector {
    padding-bottom: 20px;
    margin-bottom: 20px;
    border-bottom: 1px solid var(---color-line--light);
  }

  .cart-vet-partner__vet {
    padding: 0;
    border: 0;
  }

  .cart-vet-partner__vet-text {
    font-size: var(--base-font-size);
    padding: 15px 30px;
    border-radius: 10px;
  }

  .cart-vet-partner__notice {
    background: var(---background-color--content-1);
  }

  .cart-vet-partner__product-notice {
    display: none;
  }

  .cart-vet-partner__selector-form {
    .select {
      background: var(---background-color--content-1);
    }
  }

  .cart-vet-notice {
    display: block !important;
    height: auto;
    visibility: visible;

    button {
      display: none;
    }
  }

  .cart-vet-partner__notice {
    padding: 20px;

    @media (min-width: 1000px) {
      padding: 30px;
    }
  }

  .cart-vet-text {
    display: flex;
    justify-content: center;
  }

  .cart-vet-partner {
    display: flex;
    gap: 0;
  }

}