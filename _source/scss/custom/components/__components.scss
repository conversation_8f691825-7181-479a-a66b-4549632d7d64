.popover,
.mobile-toolbar {
  background: var(---background-color--content-1) !important;
}

.nav-dropdown {
  z-index: 1;
  background: RGB(var(--background));
}

.button {
  line-height: 1.2;
}

.drawer {
}

.button-wrapper {

  
  &.button-wrapper--vertical {
    display: flex;
    align-items: center;
    gap: 0.5em;
    flex-direction: column;
  }

}


hr, .hr {

  width: 100%;
  margin: 2em auto;
  border-width: 0.5px;
  border-bottom: 0;
  border-style: solid;
  border-color: var(---color-line);

  &.hr--light {
    border-color: var(---color-line--light);
  }

  &.hr--dark {
    border-color: var(---color-line--dark);
  }

  &.hr--clear {
    border-color: transparent;
  }

  &.hr--small {
    margin: 1em 0;
  }

  &.hr--xsmall {
    margin: 0.5em 0;
  }

  &.hr--narrow {
    max-width: 70px;
    margin-left: auto !important;
    margin-right: auto !important;
  }

}


[data-tooltip] {
  // position: relative;
}

[data-tooltip]:before {
  font-size: var(---font-size-body-xs) !important;
}

.account-link-current {
  color: var(---color--highlight);
}