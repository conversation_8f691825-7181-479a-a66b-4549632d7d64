.account-dog-info {

    --text-font-weight: 300;

    display: flex;
    flex-direction: column;
    gap: 30px;
    padding: 24px;

    border-radius: 18px;
    overflow: hidden;

    font-weight: var(--text-font-weight);

    color: RGB(var(--text-color));

    strong {
        font-weight: 700;
    }

    h1,
    h2,
    h3,
    h4,
    h5,
    h6,
    .h1,
    .h2,
    .h3,
    .h4,
    .h5,
    .h6 {
        color: RGB(var(--heading-color));
    }

    .account-dog-info__body {}

    .account-dog-info {
        display: flex;
    }

    .button-wrapper {

        display: flex;
        flex-direction: column;
        gap: 0.5em;

        @include respond-to($medium-up) {
            display: flex;
            flex-direction: row;
        }
    }

    .button {
        line-height: 1.2 !important;
    }

    .button-wrapper--center {
        justify-content: center;
    }

    @include respond-to($medium-up) {
        padding: 48px;
    }

}