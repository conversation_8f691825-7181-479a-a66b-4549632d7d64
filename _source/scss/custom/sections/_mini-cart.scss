.mini-cart {

  --root-background: var(---background-color--content-1--rgb);
  --section-block-background: var(---background-color--content-1--rgb);
  --background: var(---background-color--content-1--rgb);

  width: 100vw;

  /* ----- Loading Overlay ----- */

  &:after {
  
    pointer-events: none;

    content: "";
    display: block;
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 1;

    transition: 0.25s background-color;

    background: RGBA(255, 255, 255, 0);

  }

  &.cart-drawer--loading {

    &:after {
      background: RGBA(var(---background-color--content-1--rgb), 0.75);
      pointer-events: auto;
    }

  }

  /* ----- Drawer Header ----- */

  .drawer__header {

    border-bottom: 0;
    max-height: none;
    height: auto;

  }

  .drawer__title {
    text-transform: none;
    margin-bottom: 0;
  }

  .drawer__close-button {
    bottom: 0;
    top: 0;
  }

  free-shipping-bar {
    
    padding: 20px 30px;
    margin: 0 0 20px 0;
    background: RGB(var(---background-color--content-2--rgb));

    border-radius: var(---border-radius--general);

    .text--small {
      margin-bottom: 0;
    }

  }

  .mini-cart__drawer-footer {
    --root-border-color: var(---color-line--light--rgb);
    padding: 20px var(--container-gutter);
  }

  // Buttons


  // Product Item Meta

  .product-item-meta__title {
    line-height: 1.2;
    font-size: var(---font-size-body--desktop);
  }

  .product-item-meta__property {
    font-weight: var(---font-weight-body);
  }

  .product-item-meta__price-and-remove {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .line-item__quantity {
      margin-top: 0;
    }
  }

  /* ----- Line Items ----- */

  .line-item {
    
    .line-item__content-wrapper {
      margin-top: 0;
      margin-bottom: 35px;
    }

    .line-item__info {
      width: 100%;
    }

    .line-item__image {
      border-radius: 6px;
    }

    .line-item__image-wrapper {
      margin-right: 10px;
      @include respond-to($medium-up) {
        margin-right: 20px;
      }
    }

    .line-item__remove-button {
      font-weight: 400;
    }

    // Price Display

    .product-item-meta__property-list {
      margin: 0;
    }

    .product-item-meta__price-list-container {
      margin: 0;
    }

    // Quantity Selector

    .quantity-selector {
      --quantity-selector-height: 32px;
      overflow: hidden;
    }

    .quantity-selector__input {
      font-size: var(---font-size-body-small--desktop);
      font-weight: var(---font-weight-body);
      background: RGB(var(---background-color--content-1--rgb));
    }

    .quantity-selector__button {
      background: RGB(var(---background-color--content-1--rgb));
    }

    .line-item__remove-button {
      font-size: var(---font-size-body-small--desktop);
      font-weight: var(---font-weight-body);
    }

  }


  .mini-cart__drawer-prefooter {

    padding: 10px var(--container-gutter);
    text-align: center;
    font-weight: var(---font-weight-body);
    position: relative;

    &:after {
      content: '';
      display: block;

      position: absolute;
      height: 30px;
      width: 100%;
      top: 0;
      transform: translateY(calc(-100%));

      background: linear-gradient(to bottom, rgba(255, 255, 255, 0), rgba(255, 255, 255, 1));
    }

  }

  
  /* ----- Cart Subscriptions Box ----- */

  .cart-subscriptions {

    display: block;
    margin-bottom: 12px;
    
    border-radius: 8px;
    background: var(---background-color--content-2);

    .cart-subscriptions-form__actions {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 1em;
    }

    .cart-subscriptions__form {
      padding: 12px;
      border-top: 1px solid rgba(var(---color--brand-6--rgb), 0.5);

      @include respond-to($medium-up) {
        padding: 24px;
      }
    }

    .subscriptions-input {

      margin: 1em 0;
      gap: 10px;

      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;

      @include respond-to($medium-up) {
        flex-direction: row;
      }

      label {
        font-weight: 700;
        font-size: var(---font-size-body--small);
      }

      select {
        padding: 0.25em 2.5em 0.25em 0.75em;
        border-radius: 8px;
      }
    }

  }

  /* ----- Tags ----- */

  .product-item-tags {
    display: flex;
    flex-wrap: wrap;
    margin: 0.5em 0;
    gap: 10px;
  }
  
  .product-item-tag {
  
    display: inline-flex;
    align-items: center;
    padding: 0.35em 0.5em;
    gap: 0.25em;
  
    background: var(---background-color--secondary);
    border-radius: 4px;
  
    // pointer-events: none;
    user-select: none;

    svg {
      * {
        fill: currentColor;
        outline: currentColor;
      }
    }
  
  }
  
  .product-item-tag--prescription {
    background: RGB(var(---background-color--danger--rgb));
    color: RGB(var(---color--danger--rgb));
  }
  
  .product-item-tag--subscription {
    background-color: var(---color--highlight);
    // color: RGB(var(---color--danger--rgb));
  }
  
  .product-item-tag__icon {
    display: flex;
    align-items: center;
    line-height: 1;
  
    svg {
      width: 20px;
      height: 20px;
    }
  }
  
  .product-item-tag__text {
    line-height: 1;
  }


  /* ----- Shipping Details ----- */

  .shipping-details {

    --padding-horizontal: 30px;
    --padding-vertical: 20px;

  }

  .shipping-details__inner {

    margin-top: calc(1 * var(--padding-vertical));

    margin-left: calc(-1 * var(--padding-horizontal));
    margin-right: calc(-1 * var(--padding-horizontal));

    padding-top: var(--padding-vertical);
    padding-left: var(--padding-horizontal);
    padding-right: var(--padding-horizontal);

    border-top: 1px solid RGBA(0, 0, 0, .2);

  }

  .shipping-details__footer {

    margin-top: calc(1 * var(--padding-vertical));

    padding-top: calc(0.5 * var(--padding-vertical));
    padding-left: var(--padding-horizontal);
    padding-right: var(--padding-horizontal);

    border-top: 1px solid RGBA(0, 0, 0, .2);

    line-height: 1.2;

  }

  .shipping-details__header {
    display: flex;
    justify-content: space-between;
  }

  .shipping-details__heading {
    padding: 0;
    margin: 0;
  }

  .shipping-details__table {

    width: 100%;

    text-align: left;
    font-size: var(---font-size-body-small--desktop);

    th,
    td {
      font-size: 0.9em !important;
    }

    tr {

      th,
      td {
        text-align: center;
        padding: 0.1em 0;

        &:first-child {
          text-align: left;
        }

        &:last-child {
          text-align: right;
        }
      }
    }
  }

  .shipping-details__message {

    background-color: var(---background-color--content-1);
    padding: 0.75em;
    margin: 1em 0;
    border-radius: var(--block-border-radius);

    line-height: 1.4;

    p {
      font-size: 0.9em !important;
    }

  }

  .cart-vet-partner__selector-form {
    border-top: 1px solid var(---color-line--light);
    padding-top: 20px;
    margin-top: 20px;
  }

}