.section {

  &.section--use-padding {
    margin: 0;
    padding: var(--vertical-breather) 0;
  }

  &.section--half-padding {
    --vertical-breather: calc(var(--vertical-breather) / 2);
  }

  &.section--double-spacing {
    --vertical-breather: var(--vertical-breather-double);
  }

  &.section--no-padding {
    margin: 0;
    padding: 0;
  }

  .container {

    // margin-top: 0;
    // margin-bottom: 0;
    // padding-top: var(--vertical-breather);
    // padding-bottom: var(--vertical-breather);

  }

  .container--no-padding {
    padding: 0;
  }

  &.section--no-spacing {

    // margin-top: 0;
    // margin-bottom: 0;
    // padding-top: 0;
    // padding-bottom: 0;

  }

  &.section--no-spacing--top {

    // margin-top: 0;
    // padding-top: 0;

  }

  .subheading {
    + .heading {}
  }

  .section__header {
    &.section__header--wide {

    }
  }

  .section__footer {
    &.section__footer-left {

    }
  }

}

.container--smaller {
  max-width: 700px;
}