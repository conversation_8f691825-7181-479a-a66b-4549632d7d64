[class*="template-customers"] {

  // --background: var(---background-color--content-1);;

  /* ----- Link Bar ----- */

  .link-bar {

    --background: var(---background-color--content-reversed-1);
    --text-color: var(---color-text--reversed);

    background: var(--background);
    color: var(--text-color);

    .link-bar__link-item {

      transition: 0.25s color;
      &:hover {
        color: var(---color--highlight);
      }

      .text--subdued {
        transition: 0.25s color;
        &:hover {
          color: var(---color--danger) !important;
        }
      }
    }

    .text--underlined {

      --text-color: var(---color--highlight);

      color: var(--text-color);
      
      text-decoration: none;
      cursor: default;
      pointer-events: none;

      &:after {
        content: none;
      }

    }

  }


  /* ----- Page Header ----- */

  .page-header {

    .heading {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 0.25em;
    }

    .bubble-count {
      background-color: var(---color--highlight);
      color: var(---color-text);
      font-weight: 700;
      letter-spacing: -0.075em !important;
      height: 36px;
      width: 36px;
      font-size: 0.5em;
    }
  }


  /* ----- Link Bar ----- */

}



.account {
  
  background-color: var(---background-color--content-1);

  .account__orders-table {
  
    font-weight: 400;
    font-size: var(---font-fize-body);

    thead {
      th {
        padding: 0.5rem 0;
      }
    }

    td {
      padding: 0.5rem 0;
    }

    .reorder-button {
      letter-spacing: 0;
      min-width: 0;
      padding: 0em 1em;
      line-height: 2.4em;

      background-color: var(---color--highlight);

    }

  }

  .account__order-item-actions {
    display: flex;
    flex-direction: column;
    gap: 0.5em;
    @media (min-width: 1000px) {
      flex-direction: row;
      gap: 20px;
    }
  }

}

