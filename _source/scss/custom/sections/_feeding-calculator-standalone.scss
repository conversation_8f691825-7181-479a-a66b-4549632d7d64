.shopify-section--feeding-calculator {

  @include respond-to($small-up) {

    margin-top: 150px;

  }

}

.fieldset {

  --form-input-gap: 24px;

  margin: var(--container-gutter) 0;

  &:last-child {
    margin-bottom: 0;
  }
  
}

.feeding-calculator {

  position: relative;

}

.feeding-calculator__icon {

  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  margin: auto;

  transform: translateY(-65%);

  display: none; // Hidden on Mobile

  @include respond-to($small-up) {

    display: block;
    
    width: 200px;
    height: 200px;
    
  }

  @include respond-to($large-up) {
    
    width: 250px;
    height: 250px;
    
  }

}


// Table

.calculator-results-table {

  tr {
    &.selected {
      td {
        background: var(---color--brand-1);
      }
  
      .label {
        visibility: visible;
      }
    }
  }

  tr.results-row {
    cursor: pointer;
  }

  th {
    span {
      display: block;
      font-size: 0.75em;
    }
  }

  td, th {

    .label {
      margin: 0 0.5em;
    }

  }

  .label {
    visibility: hidden;
    padding: 0.4em 0.8em;
  }

}


.results-row {

  button.link {
    display: flex;
    align-items: center;
  }

  .results-row__details {
    display: flex;
    align-items: flex-start;
    justify-content: flex-start;
    gap: 0.5em;
  }

  .results-row__external-link {
    padding: 0.05em 0.25em;
    border: 1px solid RGBA(var(---color-line--rgb), 0.25);
    border-radius: 5px;
    background-color: RGBA(var(---color-line--rgb), 0);
    transition: 0.25s background-color;
    &:hover, &:focus {
      background-color: RGBA(var(---color-line--rgb), 0.1);
    }
  }
}






/* ========== Nutrition ========== */

.feeding-calculator-nutrition {

  --product-image-size: 120px;
  --product-image-border-size: 36px;

  --primary-button-background: var(--product-color);

  position: relative;
  padding-top: 30px;

}

.feeding-calculator-nutrition__header {

  position: relative;

  display: flex;
  justify-content: center;

  height: 60px;

  background: RGB(var(--product-color));
  border-top-left-radius: var(--block-border-radius);
  border-top-right-radius: var(--block-border-radius);

  &:after {

    content: '';

    display: block;
    padding: 15px;
    margin: auto;

    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1;

    --offset: calc(-1 * (var(--product-image-size) - var(--product-image-border-size) * 2));

    // transform: translateY(-45px);
    transform: translateY(var(--offset));

    background: RGB(var(--product-color));
    border-radius: 120px;
    width: calc(var(--product-image-size) + 30px);
    height: calc(var(--product-image-size) + 30px);

  }

}

.feeding-calculator-nutrition__header-image {

  position: absolute;
  top: 0;
  transform: translateY(-30px);
  z-index: 2;

}

.feeding-calculator-nutrition__content {

  padding-top: calc(var(--vertical-breather) / 2);
  position: relative;
  z-index: 1;

  background: RGB(var(--section-background, var(--background)));
}

.spaced-content--tight {
  gap: 20px;
}


/* ----- Ratings ----- */

.nutrition-ratings {

  display: flex;
  align-items: center;
  justify-content: center;
  flex-wrap: wrap;
  gap: 10px;
  @include respond-to($medium-up) {
    gap: 20px;
  }
  
}

/* ----- Nutrition Summary ----- */

.nutrition-summary {
  display: flex;
  flex-direction: column;
  gap: 20px;
  @include respond-to($small-up) {
    flex-direction: row;
    .nutrition-summary-item {
      width: 100%;
    }
  }
}

.nutrition-summary-item {}

.nutrition-summary-item__title {
  margin-bottom: 0.5em;
}


/* ========== Analysis Table ========== */


.nutritional-analysis {

  --row-spacing: 0.35em;
  
  text-align: left;
  border: 5px solid RGB(var(--heading-color));
  padding: 20px;
  
  font-size: var(---font-size-body-small--mobile);
  @include respond-to($small-up) {
    font-size: var(---font-size-body-small--desktop);
  }

}

.nutritional-analysis__footer {

  margin-top: 20px;

  font-size: var(---font-size-body-xs--mobile);
  @include respond-to($small-up) {
    font-size: var(---font-size-body-xs--desktop);
  }
}


/* ----- Analysis Category ----- */

.analysis-category {
  display: block;
}

.analysis-category__header {
  display: flex;
  gap: 10px;
  width: 100%;
  padding: var(--row-spacing) 0;
  border-bottom: 1px solid var(---color-line--light);

  &[aria-expanded="true"] {
    .analysis-category__button {
      &:before {
        content: "-";
      }
    }
  }


}


/* ----- Analysis Header ----- */

.analysis-header {
  
  display: block;
  padding: var(--row-spacing) 0;
  margin-top: 1em;
  margin-bottom: 5px;
  border-bottom: 1px solid var(---color-line);

  &:first-child {
    margin-top: 0;
  }
  
}

.analysis-header__title {
  font-size: 20px;
  text-transform: uppercase;
  font-weight: var(---font-weight-body--bold);
}


/* ----- Analysis Category ----- */

.analysis-category__content {}

.analysis-category__title {
  font-weight: var(---font-weight-body--bold);
  // font-weight: bold;
}

.analysis-category__button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;

  font-weight: bold;
  border-radius: 4px;
  background: RGBA(var(--text-color), 0.05);

  &:focus,
  &:hover {
    background: RGBA(var(--text-color), 0.1);
  }

  &:before {
    content: "+";
  }

}

.analysis-category__content {
  display: none;
  width: 100%;
}

.analysis-category__header[aria-expanded="true"]+.analysis-category__content {
  display: table !important;
}

.analysis-category {
  .analysis-row {
    >*:first-child {
      @media(min-width: 480px) {
        padding-left: 30px;
      }
    }
  }
}


/* ----- Analysis Table ----- */

.analysis-table {
  display: table;
  width: 100%;
}

.analysis-table-row {

  display: table-row;
  gap: 5px;

  width: 100%;

  line-height: 1.4;
  font-size: 0.95em;

  > * {

    width: 70px;
    text-align: center;

    @media (min-width: 480px) {
      width: 100px;
    }

    &:first-child {
      text-align: left;
      width: auto;
      margin-right: auto;
    }

    &:last-child {
      text-align: right;
    }
  }
}


.analysis-row {
  width: 100%;
  display: table-row;

  >* {
    display: table-cell;
    padding-left: 5px;
    padding-right: 5px;
    padding: var(--row-spacing) 0;
    border-bottom: 1px solid var(---color-line--light);

    &:last-child {
      text-align: right;
      padding-right: 0;
    }
  }

}

/* ========== Classes ========== */

.product-color {
  color: RGB(var(--product-color));
}

.spaced-content {
  text-align: center;
  display: grid;
  grid-auto-flow: row;
  gap: 32px;

  >* {
    margin: 0;
  }
}

.spaced-content--tight {
  gap: 20px;
}