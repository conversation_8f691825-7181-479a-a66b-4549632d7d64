.nice-select:active,
.nice-select.open,
.nice-select:focus {
  border-color: rgba(var(--text-color), 0.5);
}

.nice-select:after {
  border-bottom: 2px solid #999;
  border-right: 2px solid #999;
  content: "";
  display: block;
  height: 10px;
  width: 10px;
  margin-top: -6px;
  pointer-events: none;
  position: absolute;
  right: 17px;
  top: 50%;
  transform-origin: center;
  transform: rotate(45deg);
  transition: all .15s ease-in-out;
}

.nice-select.open:after {
  transform: rotate(-135deg)
}

.nice-select.open .nice-select-dropdown {
  opacity: 1;
  pointer-events: auto;
  transform: scale(1) translateY(0)
}

.nice-select.disabled {
  border-color: #ededed;
  color: rgba(var(--text-color), 0.5);
  pointer-events: none
}

.nice-select.disabled:after {
  border-color: rgba(var(--text-color), 0.5);
}

.nice-select.wide {
  width: 100%
}

.nice-select.wide .nice-select-dropdown {
  left: 0 !important;
  right: 0 !important
}

.nice-select.right {
  float: right
}

.nice-select.right .nice-select-dropdown {
  left: auto;
  right: 0
}

.nice-select .nice-select-dropdown {
  width: 100%;
  margin-top: 4px;
  background-color: rgba(var(--section-background));
  border-radius: 5px;
  box-shadow: 0 0 0 1px rgba(68, 68, 68, .11);
  pointer-events: none;
  position: absolute;
  top: 100%;
  left: 0;
  transform-origin: 50% 0;
  transform: scale(0.75) translateY(19px);
  transition: all .2s cubic-bezier(0.5, 0, 0, 1.25), opacity .15s ease-out;
  z-index: 9;
  opacity: 0
}

.nice-select .list {
  border-radius: 5px;
  box-sizing: border-box;
  overflow: hidden;
  padding: 0;
  max-height: 210px;
  overflow-y: auto;

  li:first-child {
    display: none !important;  
  }

}

.nice-select .option {
  cursor: pointer;
  font-weight: 400;
  list-style: none;
  outline: none;
  padding-left: 18px;
  padding-right: 29px;
  text-align: left;
  transition: all .2s
}

.nice-select .option.selected {
  font-weight: bold
}

.nice-select .option.disabled {
  background-color: rgba(0, 0, 0, 0);
  color: #999;
  cursor: default
}

.nice-select .optgroup {
  font-weight: bold
}

.no-csspointerevents .nice-select .nice-select-dropdown {
  display: none
}

.no-csspointerevents .nice-select.open .nice-select-dropdown {
  display: block
}

.nice-select .list::-webkit-scrollbar {
  // width: 0
}

.nice-select .has-multiple {
  white-space: inherit;
  height: auto;
  padding: 7px 12px;
  min-height: 36px;
  line-height: 22px
}

.nice-select .has-multiple span.current {
  border: 1px solid rgba(var(--text-color), 0.5);
  background: rgba(var(--section-background), 0.8);
  padding: 0 10px;
  border-radius: 3px;
  display: inline-block;
  line-height: 24px;
  font-size: 14px;
  margin-bottom: 3px;
  margin-right: 3px
}

.nice-select .has-multiple .multiple-options {
  display: block;
  line-height: 24px;
  padding: 0
}

.nice-select .nice-select-search-box {
  box-sizing: border-box;
  width: 100%;
  padding: 5px;
  pointer-events: none;
  border-radius: 5px 5px 0 0
}

.nice-select .nice-select-search {
  box-sizing: border-box;
  background-color: rgba(var(--section-background), 0.8);
  border: 1px solid rgba(var(--text-color), 0.5);
  border-radius: 3px;
  color: rgba(var(--text-color), 1);
  display: inline-block;
  vertical-align: middle;
  padding: 7px 12px;
  margin: 0 10px 0 0;
  width: 100%;
  min-height: 36px;
  line-height: 22px;
  height: auto;
  outline: 0 !important;
}


/* ------ Custom ------ */


styled-select {
  select {
    @include visually-hidden;
  }

  >div {
    >select {
      display: none !important;
    }
  }

  div.nice-select {
    float: none;
  }
}

.nice-select {

  --section-background: 255, 255, 255;

  display: flex;
  justify-content: flex-start;

  height: 100%;
  width: 100%;
  padding: .5em 2em .5em 1em;

  border: 1px solid rgba(var(--text-color), 0.25);
  background-color: rgba(var(--section-background), 0.8);
  border-radius: var(--button-border-radius);

  text-align: left;

  &:hover {
    background-color: rgba(var(--section-background), 1);
    border: 1px solid rgba(var(--text-color), 0.5);
  }

  &:focus {
    border: 1px solid rgba(var(--text-color), 0.5);
  }

  &.nice-select--position-top {
    .nice-select-dropdown {
      bottom: calc(100% + 10px);
      top: unset;
    }
  }

  .current {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .list {
    border-radius: 0;
  }

  .option {
    padding: 0.5em 1em 0.5em 1em;
    line-height: 1.2;
    color: rgba(var(--text-color), .7);

    &[data-value="Not Listed"] {
      color: rgba(var(--text-color), 1);
      font-style: italic;
      font-weight: var(--text-font-bold-weight) !important;
      // margin: 5px;
      // background: rgba(var(--text-color), .1);
    }
  }

  .nice-select-dropdown {
    border-radius: var(--block-border-radius);
  }

  .nice-select-search {}


  .nice-select-search-box {
    input {
      border-radius: 6px;
      border: 1px solid #e6e6e6;
      padding: .7em calc(1em - 5px) .7em calc(1em - 5px);
    }
  }

}

.cart-vet-partner-select {
  // For state option group headings
  .nice-select-dropdown {
    .option.null.disabled {
      text-transform: uppercase;
      color: RGB(var(--text-color));
      font-weight: 700 !important;

      &:hover {
        background: transparent !important;

        &:after {
          display: none !important;
        }
      }
    }
  }
}


.nice-select {

  --section-background: var();

  cursor: pointer;
  position: relative;
  
  .nice-select-dropdown {
    cursor: default;
    box-shadow: 0 0 10px rgba(0, 0, 0, .25);

    .list {
      margin-top: 0;

      .option {
        position: relative;

        &.null {
          font-weight: normal;
        }

        &:after {
          content: "";
          display: block;
          position: absolute;
          left: 0;
          top: 0;
          width: 100%;
          height: 100%;
          background: rgba(0, 0, 0, 0.1);
          opacity: 0;
          z-index: -1;
        }

        &:hover {
          &:after {
            opacity: 1;
          }
        }
      }
    }
  }
}



