.product-form__buy-buttons {

  .awt-style-1 {

    .bundleapp-wrapper {
  
      margin: 0 !important;
  
      .bundleapp-plan-selector-group {
  
        border-radius: 12px;
        border: 0;
  
        // outline: 2px solid var(---color--primary);
        border: 2px solid RGB(var(--border-color));
  
        transition: 0.25s border, 0.25s background;
  
        >label {
          font-weight: 800;
          color: var(--text-color);
        }
  
        &.bundleapp-plan-selector-group--selected {
          background: var(---color--highlight);
        }
      }
  
      .bundleapp-plan-selector-description {
  
        line-height: 1.2;
  
        span {
          background: var(---background-color--content-1);
          font-weight: 400;
          padding: 20px;
          border: 0;
        }
      }
  
      .bundleapp-plan-selector-plan {
        margin: 0;
        padding: 0.5em;
  
        label {
          font-size: 0.9em;
        }
      }
  
      .bundleapp-plan-selector-select {
        padding: 0.4em 0.8em !important;
        margin: 0 !important;
        border-radius: 12px;
        border: 2px solid RGB(var(--border-color)) !important;
      }
  
    }
    
  }


}