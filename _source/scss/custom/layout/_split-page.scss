.split-page {
  
  @include respond-to($medium-up) {
    min-height: 80vh;
    height: 100%;
    display: grid;
    grid-template-columns: 3fr 4fr;
  }

  background: RGB(var(--section-block-background));

  .page-header__text-wrapper {
    margin-top: 0;
    margin-bottom: 38px;
  }

}

.split-page__header {
  
  padding-top: calc(var(--vertical-breather) * 2);
  
}

.split-page__footer {
  
  padding-bottom: calc(var(--vertical-breather) * 2);
  justify-self: flex-end;
  
  .form__secondary-action {
    margin: 0;
    button,
    a {
      font-weight: bold;
      text-decoration: none;
    }
  }

}

.split-page__left {

}

.split-page__right {

}

.split-page__image {
  height: 100%;
  object-fit: cover;
}

.split-page__content {
  padding: 0 var(--container-gutter);
}

.split-page__content-wrapper {

  position: relative;
  
  height: 100%;
  margin: auto;
  
  display: flex;
  flex-direction: column;
  justify-content: space-between;

  @include respond-to($medium-up) {
    max-height: calc(100vh - var(--header-height));
  }

}