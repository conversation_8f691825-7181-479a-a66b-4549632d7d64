var __defProp = Object.defineProperty;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __publicField = (obj, key, value) => {
  __defNormalProp(obj, typeof key !== "symbol" ? key + "" : key, value);
  return value;
};

// node_modules/ftdomdelegate/main.js
function Delegate(root) {
  this.listenerMap = [{}, {}];
  if (root) {
    this.root(root);
  }
  this.handle = Delegate.prototype.handle.bind(this);
  this._removedListeners = [];
}
Delegate.prototype.root = function (root) {
  const listenerMap = this.listenerMap;
  let eventType;
  if (this.rootElement) {
    for (eventType in listenerMap[1]) {
      if (listenerMap[1].hasOwnProperty(eventType)) {
        this.rootElement.removeEventListener(eventType, this.handle, true);
      }
    }
    for (eventType in listenerMap[0]) {
      if (listenerMap[0].hasOwnProperty(eventType)) {
        this.rootElement.removeEventListener(eventType, this.handle, false);
      }
    }
  }
  if (!root || !root.addEventListener) {
    if (this.rootElement) {
      delete this.rootElement;
    }
    return this;
  }
  this.rootElement = root;
  for (eventType in listenerMap[1]) {
    if (listenerMap[1].hasOwnProperty(eventType)) {
      this.rootElement.addEventListener(eventType, this.handle, true);
    }
  }
  for (eventType in listenerMap[0]) {
    if (listenerMap[0].hasOwnProperty(eventType)) {
      this.rootElement.addEventListener(eventType, this.handle, false);
    }
  }
  return this;
};
Delegate.prototype.captureForType = function (eventType) {
  return ["blur", "error", "focus", "load", "resize", "scroll"].indexOf(eventType) !== -1;
};
Delegate.prototype.on = function (eventType, selector, handler, useCapture) {
  let root;
  let listenerMap;
  let matcher;
  let matcherParam;
  if (!eventType) {
    throw new TypeError("Invalid event type: " + eventType);
  }
  if (typeof selector === "function") {
    useCapture = handler;
    handler = selector;
    selector = null;
  }
  if (useCapture === void 0) {
    useCapture = this.captureForType(eventType);
  }
  if (typeof handler !== "function") {
    throw new TypeError("Handler must be a type of Function");
  }
  root = this.rootElement;
  listenerMap = this.listenerMap[useCapture ? 1 : 0];
  if (!listenerMap[eventType]) {
    if (root) {
      root.addEventListener(eventType, this.handle, useCapture);
    }
    listenerMap[eventType] = [];
  }
  if (!selector) {
    matcherParam = null;
    matcher = matchesRoot.bind(this);
  } else if (/^[a-z]+$/i.test(selector)) {
    matcherParam = selector;
    matcher = matchesTag;
  } else if (/^#[a-z0-9\-_]+$/i.test(selector)) {
    matcherParam = selector.slice(1);
    matcher = matchesId;
  } else {
    matcherParam = selector;
    matcher = Element.prototype.matches;
  }
  listenerMap[eventType].push({
    selector,
    handler,
    matcher,
    matcherParam
  });
  return this;
};
Delegate.prototype.off = function (eventType, selector, handler, useCapture) {
  let i;
  let listener;
  let listenerMap;
  let listenerList;
  let singleEventType;
  if (typeof selector === "function") {
    useCapture = handler;
    handler = selector;
    selector = null;
  }
  if (useCapture === void 0) {
    this.off(eventType, selector, handler, true);
    this.off(eventType, selector, handler, false);
    return this;
  }
  listenerMap = this.listenerMap[useCapture ? 1 : 0];
  if (!eventType) {
    for (singleEventType in listenerMap) {
      if (listenerMap.hasOwnProperty(singleEventType)) {
        this.off(singleEventType, selector, handler);
      }
    }
    return this;
  }
  listenerList = listenerMap[eventType];
  if (!listenerList || !listenerList.length) {
    return this;
  }
  for (i = listenerList.length - 1; i >= 0; i--) {
    listener = listenerList[i];
    if ((!selector || selector === listener.selector) && (!handler || handler === listener.handler)) {
      this._removedListeners.push(listener);
      listenerList.splice(i, 1);
    }
  }
  if (!listenerList.length) {
    delete listenerMap[eventType];
    if (this.rootElement) {
      this.rootElement.removeEventListener(eventType, this.handle, useCapture);
    }
  }
  return this;
};
Delegate.prototype.handle = function (event) {
  let i;
  let l;
  const type = event.type;
  let root;
  let phase;
  let listener;
  let returned;
  let listenerList = [];
  let target;
  const eventIgnore = "ftLabsDelegateIgnore";
  if (event[eventIgnore] === true) {
    return;
  }
  target = event.target;
  if (target.nodeType === 3) {
    target = target.parentNode;
  }
  if (target.correspondingUseElement) {
    target = target.correspondingUseElement;
  }
  root = this.rootElement;
  phase = event.eventPhase || (event.target !== event.currentTarget ? 3 : 2);
  switch (phase) {
    case 1:
      listenerList = this.listenerMap[1][type];
      break;
    case 2:
      if (this.listenerMap[0] && this.listenerMap[0][type]) {
        listenerList = listenerList.concat(this.listenerMap[0][type]);
      }
      if (this.listenerMap[1] && this.listenerMap[1][type]) {
        listenerList = listenerList.concat(this.listenerMap[1][type]);
      }
      break;
    case 3:
      listenerList = this.listenerMap[0][type];
      break;
  }
  let toFire = [];
  l = listenerList.length;
  while (target && l) {
    for (i = 0; i < l; i++) {
      listener = listenerList[i];
      if (!listener) {
        break;
      }
      if (target.tagName && ["button", "input", "select", "textarea"].indexOf(target.tagName.toLowerCase()) > -1 && target.hasAttribute("disabled")) {
        toFire = [];
      } else if (listener.matcher.call(target, listener.matcherParam, target)) {
        toFire.push([event, target, listener]);
      }
    }
    if (target === root) {
      break;
    }
    l = listenerList.length;
    target = target.parentElement || target.parentNode;
    if (target instanceof HTMLDocument) {
      break;
    }
  }
  let ret;
  for (i = 0; i < toFire.length; i++) {
    if (this._removedListeners.indexOf(toFire[i][2]) > -1) {
      continue;
    }
    returned = this.fire.apply(this, toFire[i]);
    if (returned === false) {
      toFire[i][0][eventIgnore] = true;
      toFire[i][0].preventDefault();
      ret = false;
      break;
    }
  }
  return ret;
};
Delegate.prototype.fire = function (event, target, listener) {
  return listener.handler.call(target, event, target);
};
function matchesTag(tagName, element) {
  return tagName.toLowerCase() === element.tagName.toLowerCase();
}
function matchesRoot(selector, element) {
  if (this.rootElement === window) {
    return element === document || element === document.documentElement || element === window;
  }
  return this.rootElement === element;
}
function matchesId(id, element) {
  return id === element.id;
}
Delegate.prototype.destroy = function () {
  this.off();
  this.root();
};
var main_default = Delegate;

// js/components/input-binding-manager.js
var InputBindingManager = class {
  constructor() {
    this.delegateElement = new main_default(document.body);
    this.delegateElement.on("change", "[data-bind-value]", this._onValueChanged.bind(this));
  }
  _onValueChanged(event, target) {
    const boundElement = document.getElementById(target.getAttribute("data-bind-value"));
    if (boundElement) {
      if (target.tagName === "SELECT") {
        target = target.options[target.selectedIndex];
      }
      boundElement.innerHTML = target.hasAttribute("title") ? target.getAttribute("title") : target.value;
    }
  }
};

// js/helper/event.js
function triggerEvent(element, name, data = {}) {
  element.dispatchEvent(new CustomEvent(name, {
    bubbles: true,
    detail: data
  }));
}
function triggerNonBubblingEvent(element, name, data = {}) {
  element.dispatchEvent(new CustomEvent(name, {
    bubbles: false,
    detail: data
  }));
}

// js/helper/currency.js
function formatMoney(cents, format = "") {
  if (typeof cents === "string") {
    cents = cents.replace(".", "");
  }
  const placeholderRegex = /\{\{\s*(\w+)\s*\}\}/, formatString = format || window.themeVariables.settings.moneyFormat;
  function defaultTo(value2, defaultValue) {
    return value2 == null || value2 !== value2 ? defaultValue : value2;
  }
  function formatWithDelimiters(number, precision, thousands, decimal) {
    precision = defaultTo(precision, 2);
    thousands = defaultTo(thousands, ",");
    decimal = defaultTo(decimal, ".");
    if (isNaN(number) || number == null) {
      return 0;
    }
    number = (number / 100).toFixed(precision);
    let parts = number.split("."), dollarsAmount = parts[0].replace(/(\d)(?=(\d\d\d)+(?!\d))/g, "$1" + thousands), centsAmount = parts[1] ? decimal + parts[1] : "";
    return dollarsAmount + centsAmount;
  }
  let value = "";
  switch (formatString.match(placeholderRegex)[1]) {
    case "amount":
      value = formatWithDelimiters(cents, 2);
      break;
    case "amount_no_decimals":
      value = formatWithDelimiters(cents, 0);
      break;
    case "amount_with_space_separator":
      value = formatWithDelimiters(cents, 2, " ", ".");
      break;
    case "amount_with_comma_separator":
      value = formatWithDelimiters(cents, 2, ".", ",");
      break;
    case "amount_with_apostrophe_separator":
      value = formatWithDelimiters(cents, 2, "'", ".");
      break;
    case "amount_no_decimals_with_comma_separator":
      value = formatWithDelimiters(cents, 0, ".", ",");
      break;
    case "amount_no_decimals_with_space_separator":
      value = formatWithDelimiters(cents, 0, " ");
      break;
    case "amount_no_decimals_with_apostrophe_separator":
      value = formatWithDelimiters(cents, 0, "'");
      break;
  }
  if (formatString.indexOf("with_comma_separator") !== -1) {
    return formatString.replace(placeholderRegex, value);
  } else {
    return formatString.replace(placeholderRegex, value);
  }
}

// js/custom-element/custom-html-element.js
var CustomHTMLElement = class extends HTMLElement {
  constructor() {
    super();
    this._hasSectionReloaded = false;
    if (Shopify.designMode) {
      this.rootDelegate.on("shopify:section:select", (event) => {
        const parentSection = this.closest(".shopify-section");
        if (event.target === parentSection && event.detail.load) {
          this._hasSectionReloaded = true;
        }
      });
    }
  }
  get rootDelegate() {
    return this._rootDelegate = this._rootDelegate || new main_default(document.documentElement);
  }
  get delegate() {
    return this._delegate = this._delegate || new main_default(this);
  }
  showLoadingBar() {
    triggerEvent(document.documentElement, "theme:loading:start");
  }
  hideLoadingBar() {
    triggerEvent(document.documentElement, "theme:loading:end");
  }
  untilVisible(intersectionObserverOptions = { rootMargin: "30px 0px", threshold: 0 }) {
    const onBecameVisible = () => {
      this.classList.add("became-visible");
      this.style.opacity = "1";
    };
    return new Promise((resolve) => {
      if (window.IntersectionObserver) {
        this.intersectionObserver = new IntersectionObserver((event) => {
          if (event[0].isIntersecting) {
            this.intersectionObserver.disconnect();
            requestAnimationFrame(() => {
              resolve();
              onBecameVisible();
            });
          }
        }, intersectionObserverOptions);
        this.intersectionObserver.observe(this);
      } else {
        resolve();
        onBecameVisible();
      }
    });
  }
  disconnectedCallback() {
    var _a;
    this.delegate.destroy();
    this.rootDelegate.destroy();
    (_a = this.intersectionObserver) == null ? void 0 : _a.disconnect();
    delete this._delegate;
    delete this._rootDelegate;
  }
};

// node_modules/tabbable/dist/index.esm.js
var candidateSelectors = ["input", "select", "textarea", "a[href]", "button", "[tabindex]", "audio[controls]", "video[controls]", '[contenteditable]:not([contenteditable="false"])', "details>summary:first-of-type", "details"];
var candidateSelector = /* @__PURE__ */ candidateSelectors.join(",");
var matches = typeof Element === "undefined" ? function () {
} : Element.prototype.matches || Element.prototype.msMatchesSelector || Element.prototype.webkitMatchesSelector;
var getCandidates = function getCandidates2(el, includeContainer, filter) {
  var candidates = Array.prototype.slice.apply(el.querySelectorAll(candidateSelector));
  if (includeContainer && matches.call(el, candidateSelector)) {
    candidates.unshift(el);
  }
  candidates = candidates.filter(filter);
  return candidates;
};
var isContentEditable = function isContentEditable2(node) {
  return node.contentEditable === "true";
};
var getTabindex = function getTabindex2(node) {
  var tabindexAttr = parseInt(node.getAttribute("tabindex"), 10);
  if (!isNaN(tabindexAttr)) {
    return tabindexAttr;
  }
  if (isContentEditable(node)) {
    return 0;
  }
  if ((node.nodeName === "AUDIO" || node.nodeName === "VIDEO" || node.nodeName === "DETAILS") && node.getAttribute("tabindex") === null) {
    return 0;
  }
  return node.tabIndex;
};
var sortOrderedTabbables = function sortOrderedTabbables2(a, b) {
  return a.tabIndex === b.tabIndex ? a.documentOrder - b.documentOrder : a.tabIndex - b.tabIndex;
};
var isInput = function isInput2(node) {
  return node.tagName === "INPUT";
};
var isHiddenInput = function isHiddenInput2(node) {
  return isInput(node) && node.type === "hidden";
};
var isDetailsWithSummary = function isDetailsWithSummary2(node) {
  var r = node.tagName === "DETAILS" && Array.prototype.slice.apply(node.children).some(function (child) {
    return child.tagName === "SUMMARY";
  });
  return r;
};
var getCheckedRadio = function getCheckedRadio2(nodes, form) {
  for (var i = 0; i < nodes.length; i++) {
    if (nodes[i].checked && nodes[i].form === form) {
      return nodes[i];
    }
  }
};
var isTabbableRadio = function isTabbableRadio2(node) {
  if (!node.name) {
    return true;
  }
  var radioScope = node.form || node.ownerDocument;
  var queryRadios = function queryRadios2(name) {
    return radioScope.querySelectorAll('input[type="radio"][name="' + name + '"]');
  };
  var radioSet;
  if (typeof window !== "undefined" && typeof window.CSS !== "undefined" && typeof window.CSS.escape === "function") {
    radioSet = queryRadios(window.CSS.escape(node.name));
  } else {
    try {
      radioSet = queryRadios(node.name);
    } catch (err) {
      console.error("Looks like you have a radio button with a name attribute containing invalid CSS selector characters and need the CSS.escape polyfill: %s", err.message);
      return false;
    }
  }
  var checked = getCheckedRadio(radioSet, node.form);
  return !checked || checked === node;
};
var isRadio = function isRadio2(node) {
  return isInput(node) && node.type === "radio";
};
var isNonTabbableRadio = function isNonTabbableRadio2(node) {
  return isRadio(node) && !isTabbableRadio(node);
};
var isHidden = function isHidden2(node, displayCheck) {
  if (getComputedStyle(node).visibility === "hidden") {
    return true;
  }
  var isDirectSummary = matches.call(node, "details>summary:first-of-type");
  var nodeUnderDetails = isDirectSummary ? node.parentElement : node;
  if (matches.call(nodeUnderDetails, "details:not([open]) *")) {
    return true;
  }
  if (!displayCheck || displayCheck === "full") {
    while (node) {
      if (getComputedStyle(node).display === "none") {
        return true;
      }
      node = node.parentElement;
    }
  } else if (displayCheck === "non-zero-area") {
    var _node$getBoundingClie = node.getBoundingClientRect(), width = _node$getBoundingClie.width, height = _node$getBoundingClie.height;
    return width === 0 && height === 0;
  }
  return false;
};
var isDisabledFromFieldset = function isDisabledFromFieldset2(node) {
  if (isInput(node) || node.tagName === "SELECT" || node.tagName === "TEXTAREA" || node.tagName === "BUTTON") {
    var parentNode = node.parentElement;
    while (parentNode) {
      if (parentNode.tagName === "FIELDSET" && parentNode.disabled) {
        for (var i = 0; i < parentNode.children.length; i++) {
          var child = parentNode.children.item(i);
          if (child.tagName === "LEGEND") {
            if (child.contains(node)) {
              return false;
            }
            return true;
          }
        }
        return true;
      }
      parentNode = parentNode.parentElement;
    }
  }
  return false;
};
var isNodeMatchingSelectorFocusable = function isNodeMatchingSelectorFocusable2(options, node) {
  if (node.disabled || isHiddenInput(node) || isHidden(node, options.displayCheck) || isDetailsWithSummary(node) || isDisabledFromFieldset(node)) {
    return false;
  }
  return true;
};
var isNodeMatchingSelectorTabbable = function isNodeMatchingSelectorTabbable2(options, node) {
  if (!isNodeMatchingSelectorFocusable(options, node) || isNonTabbableRadio(node) || getTabindex(node) < 0) {
    return false;
  }
  return true;
};
var tabbable = function tabbable2(el, options) {
  options = options || {};
  var regularTabbables = [];
  var orderedTabbables = [];
  var candidates = getCandidates(el, options.includeContainer, isNodeMatchingSelectorTabbable.bind(null, options));
  candidates.forEach(function (candidate, i) {
    var candidateTabindex = getTabindex(candidate);
    if (candidateTabindex === 0) {
      regularTabbables.push(candidate);
    } else {
      orderedTabbables.push({
        documentOrder: i,
        tabIndex: candidateTabindex,
        node: candidate
      });
    }
  });
  var tabbableNodes = orderedTabbables.sort(sortOrderedTabbables).map(function (a) {
    return a.node;
  }).concat(regularTabbables);
  return tabbableNodes;
};
var focusableCandidateSelector = /* @__PURE__ */ candidateSelectors.concat("iframe").join(",");
var isFocusable = function isFocusable2(node, options) {
  options = options || {};
  if (!node) {
    throw new Error("No node provided");
  }
  if (matches.call(node, focusableCandidateSelector) === false) {
    return false;
  }
  return isNodeMatchingSelectorFocusable(options, node);
};

// node_modules/focus-trap/dist/focus-trap.esm.js
function ownKeys(object, enumerableOnly) {
  var keys = Object.keys(object);
  if (Object.getOwnPropertySymbols) {
    var symbols = Object.getOwnPropertySymbols(object);
    if (enumerableOnly) {
      symbols = symbols.filter(function (sym) {
        return Object.getOwnPropertyDescriptor(object, sym).enumerable;
      });
    }
    keys.push.apply(keys, symbols);
  }
  return keys;
}
function _objectSpread2(target) {
  for (var i = 1; i < arguments.length; i++) {
    var source = arguments[i] != null ? arguments[i] : {};
    if (i % 2) {
      ownKeys(Object(source), true).forEach(function (key) {
        _defineProperty(target, key, source[key]);
      });
    } else if (Object.getOwnPropertyDescriptors) {
      Object.defineProperties(target, Object.getOwnPropertyDescriptors(source));
    } else {
      ownKeys(Object(source)).forEach(function (key) {
        Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));
      });
    }
  }
  return target;
}
function _defineProperty(obj, key, value) {
  if (key in obj) {
    Object.defineProperty(obj, key, {
      value,
      enumerable: true,
      configurable: true,
      writable: true
    });
  } else {
    obj[key] = value;
  }
  return obj;
}
var activeFocusTraps = function () {
  var trapQueue = [];
  return {
    activateTrap: function activateTrap(trap) {
      if (trapQueue.length > 0) {
        var activeTrap = trapQueue[trapQueue.length - 1];
        if (activeTrap !== trap) {
          activeTrap.pause();
        }
      }
      var trapIndex = trapQueue.indexOf(trap);
      if (trapIndex === -1) {
        trapQueue.push(trap);
      } else {
        trapQueue.splice(trapIndex, 1);
        trapQueue.push(trap);
      }
    },
    deactivateTrap: function deactivateTrap(trap) {
      var trapIndex = trapQueue.indexOf(trap);
      if (trapIndex !== -1) {
        trapQueue.splice(trapIndex, 1);
      }
      if (trapQueue.length > 0) {
        trapQueue[trapQueue.length - 1].unpause();
      }
    }
  };
}();
var isSelectableInput = function isSelectableInput2(node) {
  return node.tagName && node.tagName.toLowerCase() === "input" && typeof node.select === "function";
};
var isEscapeEvent = function isEscapeEvent2(e) {
  return e.key === "Escape" || e.key === "Esc" || e.keyCode === 27;
};
var isTabEvent = function isTabEvent2(e) {
  return e.key === "Tab" || e.keyCode === 9;
};
var delay = function delay2(fn) {
  return setTimeout(fn, 0);
};
var findIndex = function findIndex2(arr, fn) {
  var idx = -1;
  arr.every(function (value, i) {
    if (fn(value)) {
      idx = i;
      return false;
    }
    return true;
  });
  return idx;
};
var valueOrHandler = function valueOrHandler2(value) {
  for (var _len = arguments.length, params = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {
    params[_key - 1] = arguments[_key];
  }
  return typeof value === "function" ? value.apply(void 0, params) : value;
};
var getActualTarget = function getActualTarget2(event) {
  return event.target.shadowRoot && typeof event.composedPath === "function" ? event.composedPath()[0] : event.target;
};
var createFocusTrap = function createFocusTrap2(elements, userOptions) {
  var doc = (userOptions === null || userOptions === void 0 ? void 0 : userOptions.document) || document;
  var config = _objectSpread2({
    returnFocusOnDeactivate: true,
    escapeDeactivates: true,
    delayInitialFocus: true
  }, userOptions);
  var state = {
    containers: [],
    tabbableGroups: [],
    nodeFocusedBeforeActivation: null,
    mostRecentlyFocusedNode: null,
    active: false,
    paused: false,
    delayInitialFocusTimer: void 0
  };
  var trap;
  var getOption = function getOption2(configOverrideOptions, optionName, configOptionName) {
    return configOverrideOptions && configOverrideOptions[optionName] !== void 0 ? configOverrideOptions[optionName] : config[configOptionName || optionName];
  };
  var containersContain = function containersContain2(element) {
    return !!(element && state.containers.some(function (container) {
      return container.contains(element);
    }));
  };
  var getNodeForOption = function getNodeForOption2(optionName) {
    var optionValue = config[optionName];
    if (typeof optionValue === "function") {
      for (var _len2 = arguments.length, params = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {
        params[_key2 - 1] = arguments[_key2];
      }
      optionValue = optionValue.apply(void 0, params);
    }
    if (!optionValue) {
      if (optionValue === void 0 || optionValue === false) {
        return optionValue;
      }
      throw new Error("`".concat(optionName, "` was specified but was not a node, or did not return a node"));
    }
    var node = optionValue;
    if (typeof optionValue === "string") {
      node = doc.querySelector(optionValue);
      if (!node) {
        throw new Error("`".concat(optionName, "` as selector refers to no known node"));
      }
    }
    return node;
  };
  var getInitialFocusNode = function getInitialFocusNode2() {
    var node = getNodeForOption("initialFocus");
    if (node === false) {
      return false;
    }
    if (node === void 0) {
      if (containersContain(doc.activeElement)) {
        node = doc.activeElement;
      } else {
        var firstTabbableGroup = state.tabbableGroups[0];
        var firstTabbableNode = firstTabbableGroup && firstTabbableGroup.firstTabbableNode;
        node = firstTabbableNode || getNodeForOption("fallbackFocus");
      }
    }
    if (!node) {
      throw new Error("Your focus-trap needs to have at least one focusable element");
    }
    return node;
  };
  var updateTabbableNodes = function updateTabbableNodes2() {
    state.tabbableGroups = state.containers.map(function (container) {
      var tabbableNodes = tabbable(container);
      if (tabbableNodes.length > 0) {
        return {
          container,
          firstTabbableNode: tabbableNodes[0],
          lastTabbableNode: tabbableNodes[tabbableNodes.length - 1]
        };
      }
      return void 0;
    }).filter(function (group) {
      return !!group;
    });
    if (state.tabbableGroups.length <= 0 && !getNodeForOption("fallbackFocus")) {
      throw new Error("Your focus-trap must have at least one container with at least one tabbable node in it at all times");
    }
  };
  var tryFocus = function tryFocus2(node) {
    if (node === false) {
      return;
    }
    if (node === doc.activeElement) {
      return;
    }
    if (!node || !node.focus) {
      tryFocus2(getInitialFocusNode());
      return;
    }
    node.focus({
      preventScroll: !!config.preventScroll
    });
    state.mostRecentlyFocusedNode = node;
    if (isSelectableInput(node)) {
      node.select();
    }
  };
  var getReturnFocusNode = function getReturnFocusNode2(previousActiveElement) {
    var node = getNodeForOption("setReturnFocus", previousActiveElement);
    return node ? node : node === false ? false : previousActiveElement;
  };
  var checkPointerDown = function checkPointerDown2(e) {
    var target = getActualTarget(e);
    if (containersContain(target)) {
      return;
    }
    if (valueOrHandler(config.clickOutsideDeactivates, e)) {
      trap.deactivate({
        returnFocus: config.returnFocusOnDeactivate && !isFocusable(target)
      });
      return;
    }
    if (valueOrHandler(config.allowOutsideClick, e)) {
      return;
    }
    e.preventDefault();
  };
  var checkFocusIn = function checkFocusIn2(e) {
    var target = getActualTarget(e);
    var targetContained = containersContain(target);
    if (targetContained || target instanceof Document) {
      if (targetContained) {
        state.mostRecentlyFocusedNode = target;
      }
    } else {
      e.stopImmediatePropagation();
      tryFocus(state.mostRecentlyFocusedNode || getInitialFocusNode());
    }
  };
  var checkTab = function checkTab2(e) {
    var target = getActualTarget(e);
    updateTabbableNodes();
    var destinationNode = null;
    if (state.tabbableGroups.length > 0) {
      var containerIndex = findIndex(state.tabbableGroups, function (_ref) {
        var container = _ref.container;
        return container.contains(target);
      });
      if (containerIndex < 0) {
        if (e.shiftKey) {
          destinationNode = state.tabbableGroups[state.tabbableGroups.length - 1].lastTabbableNode;
        } else {
          destinationNode = state.tabbableGroups[0].firstTabbableNode;
        }
      } else if (e.shiftKey) {
        var startOfGroupIndex = findIndex(state.tabbableGroups, function (_ref2) {
          var firstTabbableNode = _ref2.firstTabbableNode;
          return target === firstTabbableNode;
        });
        if (startOfGroupIndex < 0 && state.tabbableGroups[containerIndex].container === target) {
          startOfGroupIndex = containerIndex;
        }
        if (startOfGroupIndex >= 0) {
          var destinationGroupIndex = startOfGroupIndex === 0 ? state.tabbableGroups.length - 1 : startOfGroupIndex - 1;
          var destinationGroup = state.tabbableGroups[destinationGroupIndex];
          destinationNode = destinationGroup.lastTabbableNode;
        }
      } else {
        var lastOfGroupIndex = findIndex(state.tabbableGroups, function (_ref3) {
          var lastTabbableNode = _ref3.lastTabbableNode;
          return target === lastTabbableNode;
        });
        if (lastOfGroupIndex < 0 && state.tabbableGroups[containerIndex].container === target) {
          lastOfGroupIndex = containerIndex;
        }
        if (lastOfGroupIndex >= 0) {
          var _destinationGroupIndex = lastOfGroupIndex === state.tabbableGroups.length - 1 ? 0 : lastOfGroupIndex + 1;
          var _destinationGroup = state.tabbableGroups[_destinationGroupIndex];
          destinationNode = _destinationGroup.firstTabbableNode;
        }
      }
    } else {
      destinationNode = getNodeForOption("fallbackFocus");
    }
    if (destinationNode) {
      e.preventDefault();
      tryFocus(destinationNode);
    }
  };
  var checkKey = function checkKey2(e) {
    if (isEscapeEvent(e) && valueOrHandler(config.escapeDeactivates, e) !== false) {
      e.preventDefault();
      trap.deactivate();
      return;
    }
    if (isTabEvent(e)) {
      checkTab(e);
      return;
    }
  };
  var checkClick = function checkClick2(e) {
    if (valueOrHandler(config.clickOutsideDeactivates, e)) {
      return;
    }
    var target = getActualTarget(e);
    if (containersContain(target)) {
      return;
    }
    if (valueOrHandler(config.allowOutsideClick, e)) {
      return;
    }
    e.preventDefault();
    e.stopImmediatePropagation();
  };
  var addListeners = function addListeners2() {
    if (!state.active) {
      return;
    }
    activeFocusTraps.activateTrap(trap);
    state.delayInitialFocusTimer = config.delayInitialFocus ? delay(function () {
      tryFocus(getInitialFocusNode());
    }) : tryFocus(getInitialFocusNode());
    doc.addEventListener("focusin", checkFocusIn, true);
    doc.addEventListener("mousedown", checkPointerDown, {
      capture: true,
      passive: false
    });
    doc.addEventListener("touchstart", checkPointerDown, {
      capture: true,
      passive: false
    });
    doc.addEventListener("click", checkClick, {
      capture: true,
      passive: false
    });
    doc.addEventListener("keydown", checkKey, {
      capture: true,
      passive: false
    });
    return trap;
  };
  var removeListeners = function removeListeners2() {
    if (!state.active) {
      return;
    }
    doc.removeEventListener("focusin", checkFocusIn, true);
    doc.removeEventListener("mousedown", checkPointerDown, true);
    doc.removeEventListener("touchstart", checkPointerDown, true);
    doc.removeEventListener("click", checkClick, true);
    doc.removeEventListener("keydown", checkKey, true);
    return trap;
  };
  trap = {
    activate: function activate(activateOptions) {
      if (state.active) {
        return this;
      }
      var onActivate = getOption(activateOptions, "onActivate");
      var onPostActivate = getOption(activateOptions, "onPostActivate");
      var checkCanFocusTrap = getOption(activateOptions, "checkCanFocusTrap");
      if (!checkCanFocusTrap) {
        updateTabbableNodes();
      }
      state.active = true;
      state.paused = false;
      state.nodeFocusedBeforeActivation = doc.activeElement;
      if (onActivate) {
        onActivate();
      }
      var finishActivation = function finishActivation2() {
        if (checkCanFocusTrap) {
          updateTabbableNodes();
        }
        addListeners();
        if (onPostActivate) {
          onPostActivate();
        }
      };
      if (checkCanFocusTrap) {
        checkCanFocusTrap(state.containers.concat()).then(finishActivation, finishActivation);
        return this;
      }
      finishActivation();
      return this;
    },
    deactivate: function deactivate(deactivateOptions) {
      if (!state.active) {
        return this;
      }
      clearTimeout(state.delayInitialFocusTimer);
      state.delayInitialFocusTimer = void 0;
      removeListeners();
      state.active = false;
      state.paused = false;
      activeFocusTraps.deactivateTrap(trap);
      var onDeactivate = getOption(deactivateOptions, "onDeactivate");
      var onPostDeactivate = getOption(deactivateOptions, "onPostDeactivate");
      var checkCanReturnFocus = getOption(deactivateOptions, "checkCanReturnFocus");
      if (onDeactivate) {
        onDeactivate();
      }
      var returnFocus = getOption(deactivateOptions, "returnFocus", "returnFocusOnDeactivate");
      var finishDeactivation = function finishDeactivation2() {
        delay(function () {
          if (returnFocus) {
            tryFocus(getReturnFocusNode(state.nodeFocusedBeforeActivation));
          }
          if (onPostDeactivate) {
            onPostDeactivate();
          }
        });
      };
      if (returnFocus && checkCanReturnFocus) {
        checkCanReturnFocus(getReturnFocusNode(state.nodeFocusedBeforeActivation)).then(finishDeactivation, finishDeactivation);
        return this;
      }
      finishDeactivation();
      return this;
    },
    pause: function pause() {
      if (state.paused || !state.active) {
        return this;
      }
      state.paused = true;
      removeListeners();
      return this;
    },
    unpause: function unpause() {
      if (!state.paused || !state.active) {
        return this;
      }
      state.paused = false;
      updateTabbableNodes();
      addListeners();
      return this;
    },
    updateContainerElements: function updateContainerElements(containerElements) {
      var elementsAsArray = [].concat(containerElements).filter(Boolean);
      state.containers = elementsAsArray.map(function (element) {
        return typeof element === "string" ? doc.querySelector(element) : element;
      });
      if (state.active) {
        updateTabbableNodes();
      }
      return this;
    }
  };
  trap.updateContainerElements(elements);
  return trap;
};

// js/helper/animation.js
var CustomAnimation = class {
  constructor(effect) {
    this._effect = effect;
    this._playState = "idle";
    this._finished = Promise.resolve();
  }
  get finished() {
    return this._finished;
  }
  get animationEffects() {
    return this._effect instanceof CustomKeyframeEffect ? [this._effect] : this._effect.animationEffects;
  }
  cancel() {
    this.animationEffects.forEach((animationEffect) => animationEffect.cancel());
  }
  finish() {
    this.animationEffects.forEach((animationEffect) => animationEffect.finish());
  }
  play() {
    this._playState = "running";
    this._effect.play();
    this._finished = this._effect.finished;
    this._finished.then(() => {
      this._playState = "finished";
    }, (rejection) => {
      this._playState = "idle";
    });
  }
};
var CustomKeyframeEffect = class {
  constructor(target, keyframes, options = {}) {
    if (!target) {
      return;
    }
    if ("Animation" in window) {
      this._animation = new Animation(new KeyframeEffect(target, keyframes, options));
    } else {
      options["fill"] = "forwards";
      this._animation = target.animate(keyframes, options);
      this._animation.pause();
    }
    this._animation.addEventListener("finish", () => {
      target.style.opacity = keyframes.hasOwnProperty("opacity") ? keyframes["opacity"][keyframes["opacity"].length - 1] : null;
      target.style.visibility = keyframes.hasOwnProperty("visibility") ? keyframes["visibility"][keyframes["visibility"].length - 1] : null;
    });
  }
  get finished() {
    if (!this._animation) {
      return Promise.resolve();
    }
    return this._animation.finished ? this._animation.finished : new Promise((resolve) => this._animation.onfinish = resolve);
  }
  play() {
    if (this._animation) {
      this._animation.startTime = null;
      this._animation.play();
    }
  }
  cancel() {
    if (this._animation) {
      this._animation.cancel();
    }
  }
  finish() {
    if (this._animation) {
      this._animation.finish();
    }
  }
};
var GroupEffect = class {
  constructor(childrenEffects) {
    this._childrenEffects = childrenEffects;
    this._finished = Promise.resolve();
  }
  get finished() {
    return this._finished;
  }
  get animationEffects() {
    return this._childrenEffects.flatMap((effect) => {
      return effect instanceof CustomKeyframeEffect ? effect : effect.animationEffects;
    });
  }
};
var ParallelEffect = class extends GroupEffect {
  play() {
    const promises = [];
    for (const effect of this._childrenEffects) {
      effect.play();
      promises.push(effect.finished);
    }
    this._finished = Promise.all(promises);
  }
};
var SequenceEffect = class extends GroupEffect {
  play() {
    this._finished = new Promise(async (resolve, reject) => {
      try {
        for (const effect of this._childrenEffects) {
          effect.play();
          await effect.finished;
        }
        resolve();
      } catch (exception) {
        reject();
      }
    });
  }
};

var RevealingForm = class extends CustomHTMLElement {

  constructor() {
    super();
  }

  connectedCallback() {

    this.inputs = Array.from(this.querySelectorAll("revealing-form-input"));
    this.actions = this.querySelector("revealing-form-actions");
    this.submit = this.actions.querySelector('button');

    this._setupBehaviour();

    this.updateSubmit();

  }

  updateSubmit() {
    if (this.valid == true) {
      this.submit.disabled = false;
    }
    else {
      this.submit.disabled = true;
    }
  }

  get valid() {

    let valid = false;

    for (let i = 0; i < this.inputs.length; i++) {
      const input = this.inputs[i];
      if (input.valid == false) {
        return false;
      }
    }

    return true;

  }

  _setupBehaviour() {

  }

}

var Templates = {

  quizResultsProduct(options) {

    if (!options || !options.product) {
      return;
    }

    let html = window.quizVariables.results.results_product;

    const index = options.index;

    const product = options.product;
    const product_id = product.id.replace("gid://shopify/Product/", "")
    const product_title = product.title;
    const product_description = (!product.shortDescription) ? "" : product.shortDescription.value;
    const product_color = (!product.productColor) ? "var(---color--highlight)" : product.productColor.value;
    const product_url = product.url;
    const product_image_url = (!product.quizResultImage) ? product.images.edges[0].node.url : product.quizResultImage.reference.image.originalSrc;

    const variant = product.variants.edges[0].node;
    const variant_first_id = variant.id.replace('gid://shopify/ProductVariant/', "");
    const variant_first_price = (!variant.price) ? "" : Number(variant.price.amount) * 100;
    const variant_first_compare_price = (!variant.compareAtPrice) ? "" : Number(variant.compareAtPrice.amount) * 100;
    const variant_first_calories = (!variant.calories.value) ? "" : Number(variant.calories.value);

    const variant_last = product.variants.edges[product.variants.edges.length - 1].node;
    const variant_last_id = variant_last.id.replace('gid://shopify/ProductVariant/', "");
    // const variant_last_price = (!variant.price) ? "" : Number(variant.price.amount) * 100;
    // const variant_last_compare_price = (!variant.compareAtPrice) ? "" : Number(variant.compareAtPrice.amount) * 100;
    // const variant_last_calories = (!variant.calories.value) ? "" : Number(variant.calories.value);

    html = html.replaceAll("$INDEX", index);

    html = html.replaceAll("$PRODUCT_ID", product_id);
    html = html.replaceAll("$PRODUCT_SHORT_DESCRIPTION", product_description);
    html = html.replaceAll("$PRODUCT_TITLE", product_title);
    html = html.replaceAll("$PRODUCT_COLOR", product_color);
    html = html.replaceAll("$PRODUCT_URL", product_url);
    html = html.replaceAll("$PRODUCT_IMAGE_URL", product_image_url);

    html = html.replaceAll("$VARIANT_ID_FIRST", variant_first_id);
    html = html.replaceAll("$VARIANT_PRICE", variant_first_price);
    html = html.replaceAll("$VARIANT_PRICE_COMPARE", variant_first_compare_price);
    html = html.replaceAll("$VARIANT_METAFIELD_CALORIES", variant_first_calories);

    html = html.replaceAll("$VARIANT_ID_LAST", variant_last_id);

    return html;

  },

  quizResultsModal(options) {

    if (!options || !options.product) {
      return;
    }

    let html = window.quizVariables.results.results_product_modal;

    const product = options.product;
    const product_id = product.id.replace("gid://shopify/Product/", "")
    const product_title = product.title;
    const product_color = (!product.productColor) ? "" : product.productColor.value;
    const product_description = product.descriptionHtml.indexOf('<h5>Ingredients</h5>') ? product.descriptionHtml.split('<h5>Ingredients</h5>')[0] : product.descriptionHtml;
    const product_url = product.onlineStoreUrl;
    const product_image_url = product.images.edges[0].node.url;

    const variant = product.variants.edges[0].node;
    const variant_id = variant.id.replace('gid://shopify/ProductVariant/', "");
    const variant_price = (!variant.price) ? "" : Number(variant.price.amount) * 100;
    const variant_compare_price = (!variant.compareAtPrice) ? "" : Number(variant.compareAtPrice.amount) * 100;

    const variant_image = variant.id.replace('gid://shopify/ProductVariant/', "");

    html = html.replaceAll("$PRODUCT_ID", product_id);
    html = html.replaceAll("$PRODUCT_DESCRIPTION", product_description);
    html = html.replaceAll("$PRODUCT_TITLE", product_title);
    html = html.replaceAll("$PRODUCT_COLOR", product_color);
    html = html.replaceAll("$PRODUCT_URL", product_url);
    html = html.replaceAll("$PRODUCT_IMAGE_URL", product_image_url);

    html = html.replaceAll("$VARIANT_ID", variant_id);
    html = html.replaceAll("$VARIANT_PRICE", variant_price);
    html = html.replaceAll("$VARIANT_PRICE_COMPARE", variant_compare_price);

    return html;

  },

  quizStepTab: function (options) {

    if (!options) {
      return;
    }

    let name = !options.name ? 'Dog Name' : options.name;
    let attributes = !options.attributes ? '' : options.attributes;
    let classes = !options.classes ? '' : options.classes;

    let html =
      `<quiz-step-tab class="quiz-step-tab button button--tab ${classes}" data-name="${options.name}" ${attributes}>
          ${options.name}
        </quiz-step-tab>`;

    return html;

  },

  quizStepLineText: function (options) {

    if (!options) {
      return;
    }

    return `<span class="quiz-step-line__text">${options.text}</span>`

  },

  expandingInput: function (options) {

    if (!options) {
      return;
    }

    let html = "";

    let id = "expanding-input";
    let name = "expanding-input";
    let placeholder = "Expanding Input";

    if (options) {

      if (options.id) {
        id = options.id;
      }

      if (options.name) {
        name = options.name;
      }

      if (options.placeholder) {
        placeholder = options.placeholder;
      }

    }

    html = `
      <expanding-input>
        <span class="expanding-input__display quiz-step-line__input quiz-input" role="textbox" contenteditable data-default="${placeholder}"></span>
        <input type="hidden" class="expanding-input__input" name="${name}" id="${id}">
      </expanding-input>
      `

    return html;

  }

}

var RevealingFormInput = class extends CustomHTMLElement {

  constructor() {
    super();
  }

  connectedCallback() {

    this.revealingForm = this.closest("revealing-form");
    this._setupBehaviour();

  }
  _setupBehaviour() {

    this.input.addEventListener("input", this.inputOnInput.bind(this));

  }
  inputOnInput(event) {

    this.revealingForm.updateSubmit();

    if (this.input.value == "") {
      if (this.prevInput) {
        if (this.prevInput.input.value == "") {
          this.hide();
          this.prevInput.input.focus();
          return;
        }
      }
    }

    if (this.nextInput) {
      if (this.nextInput.valid == true) {
        return;
      }
    }

    if (this.valid == true && this.isLastInput == false) {
      if (this.nextInput) {
        if (this.nextInput.visible == false) {
          if (this.nextInput.animating == false) {
            this.nextInput.show();
          }
        }
      }
    }
    else {
      if (this.nextInput) {
        if (this.nextInputsFull == false) {
          this.nextInput.hide();
        }
      }
    }

  }
  async hide() {

    this.classList.add("revealing-form-input--animating");

    const animation = new CustomAnimation(new CustomKeyframeEffect(this,
      {
        visibility: ["visible", "hidden"],
        transform: ["translateX(0)", "translateX(-20%)"],
        opacity: ["1", "0"]
      },
      {
        duration: 300,
        easing: "cubic-bezier(0.23, 1, 0.32, 1)"
      }
    ));

    animation.play();

    await animation.finished;

    this.classList.remove("revealing-form-input--visible");
    this.classList.remove("revealing-form-input--animating");

  }
  async show() {

    this.classList.add("revealing-form-input--animating");

    const animation = new CustomAnimation(new CustomKeyframeEffect(this,
      {
        visibility: ["hidden", "visible"],
        transform: ["translateX(-20%)", "translateX(0)"],
        opacity: ["0", "1"]
      },
      {
        duration: 300,
        easing: "cubic-bezier(0.23, 1, 0.32, 1)"
      }
    ));

    animation.play();

    await animation.finished;

    this.classList.add("revealing-form-input--visible");
    this.classList.remove("revealing-form-input--animating");

  }
  get valid() {
    if (this.input.value != "") {
      return true;
    }
    return false;
  }
  get visible() {
    return this.classList.contains("revealing-form-input--visible");
  }
  get animating() {
    return this.classList.contains("revealing-form-input--animating");
  }
  get input() {
    return this.querySelector("input");
  }
  get isLastInput() {
    if (typeof this.nextInput == "undefined") {
      return true;
    }
    else {
      return false;
    }
  }
  get nextInputsFull() {
    let sibling = this.nextElementSibling;
    while (sibling) {
      if (sibling.matches("revealing-form-input")) {
        if (sibling.input.value != "") {
          return true;
        }
      };
      sibling = sibling.nextElementSibling
    }
    return false;
  }
  get nextInput() {
    let sibling = this.nextElementSibling;
    while (sibling) {
      if (sibling.matches("revealing-form-input")) return sibling;
      sibling = sibling.nextElementSibling
    }
  }
  get prevInput() {
    let sibling = this.previousElementSibling;
    while (sibling) {
      if (sibling.matches("revealing-form-input")) return sibling;
      sibling = sibling.previousElementSibling
    }
  }
}

var SplitPageStep = class extends CustomHTMLElement {

  constructor() {
    super();
  }

  connectedCallback() {

    this.nextButtons = this.querySelectorAll("[data-split-page-next]");
    this.prevButtons = this.querySelectorAll("[data-split-page-prev]");

    this.nextButtons.forEach((button) => {
      button.addEventListener("click", this.gotoNextStep.bind(this));
    });

    this.prevButtons.forEach((button) => {
      button.addEventListener("click", this.gotoPrevStep.bind(this));
    });

  }

  async gotoNextStep() {

    if (!this.nextStep) {
      return;
    }

    this.classList.add("split-page-step--animating");
    this.nextStep.classList.add("split-page-step--animating");

    triggerEvent(this.nextStep, "quiz:split-page:next-step:start");

    const animation = new CustomAnimation(new CustomKeyframeEffect(this,
      {
        visibility: ["visible", "hidden"],
        transform: ["translateY(0%)", "translateY(-20%)"],
        opacity: ["1", "0"]
      },
      {
        duration: 300,
        easing: "cubic-bezier(0.23, 1, 0.32, 1)"
      }
    ));

    animation.play();

    await animation.finished;
    this.classList.remove("split-page-step--visible");
    this.classList.remove("split-page-step--animating");

    const animation2 = new CustomAnimation(new CustomKeyframeEffect(this.nextStep,
      {
        visibility: ["hidden", "visible"],
        transform: ["translateY(20%)", "translateY(0%)"],
        opacity: ["0", "1"]
      },
      {
        duration: 300,
        easing: "cubic-bezier(0.23, 1, 0.32, 1)"
      }
    ));

    animation2.play();

    triggerEvent(this.nextStep, "quiz:split-page:next-step:arrive");

    this.nextStep.classList.add("split-page-step--visible");
    this.nextStep.classList.remove("split-page-step--animating");

  }

  async gotoPrevStep() {

    if (!this.prevStep) {
      return;
    }

    triggerEvent(this.prevStep, "quiz:split-page:prev-step:start");

    this.classList.add("split-page-step--animating");
    this.prevStep.classList.add("split-page-step--animating");

    const animation = new CustomAnimation(new CustomKeyframeEffect(this,
      {
        visibility: ["visible", "hidden"],
        transform: ["translateY(0%)", "translateY(-20%)"],
        opacity: ["1", "0"]
      },
      {
        duration: 300,
        easing: "cubic-bezier(0.23, 1, 0.32, 1)"
      }
    ));

    animation.play();

    await animation.finished;
    this.classList.remove("split-page-step--visible");
    this.classList.remove("split-page-step--animating");

    const animation2 = new CustomAnimation(new CustomKeyframeEffect(this.prevStep,
      {
        visibility: ["hidden", "visible"],
        transform: ["translateY(20%)", "translateY(0%)"],
        opacity: ["0", "1"]
      },
      {
        duration: 300,
        easing: "cubic-bezier(0.23, 1, 0.32, 1)"
      }
    ));

    animation2.play();

    this.prevStep.classList.add("split-page-step--visible");
    this.prevStep.classList.remove("split-page-step--animating");

  }

  get nextStep() {
    let sibling = this.nextElementSibling;
    while (sibling) {
      if (sibling.matches("split-page-step")) return sibling;
      sibling = sibling.nextElementSibling
    }
  }

  get prevStep() {
    let sibling = this.previousElementSibling;
    while (sibling) {
      if (sibling.matches("split-page-step")) return sibling;
      sibling = sibling.previousElementSibling
    }
  }

}

var QuizSteps = class extends CustomHTMLElement {
  static get observedAttributes() {
    return ["open"];
  }
  constructor() {
    super();
    if (Shopify.designMode) {
      // this.rootDelegate.on("shopify:section:select", (event) => filterShopifyEvent(event, this, () => this.open = true));
      // this.rootDelegate.on("shopify:section:deselect", (event) => filterShopifyEvent(event, this, () => this.open = false));
    }
  }
  connectedCallback() {
    // this.delegate.on("click", ".openable__overlay", () => this.open = false);

    this.dogNames = [];

    this.buttonBuildQuiz = this.querySelector("[data-quiz-button-populate-dogs]");

    this.substepContainer = this.querySelector(".quiz-step--dogs-substeps");

    this.delegate.on("click", "[data-quiz-button-dog-complete]", this._dogComplete.bind(this));
    this.delegate.on("click", "[data-quiz-button-next]", this.next.bind(this));
    this.delegate.on("click", "[data-quiz-button-prev]", this.prev.bind(this));
    this.delegate.on("click", "[data-quiz-button-back]", this.back.bind(this));
    this.delegate.on("click", "[data-quiz-button-skip]", this.skipToResults.bind(this));

    this.delegate.on("click", "[data-quiz-button-update-dog]", this._onClickUpdateDog.bind(this));
    this.delegate.on("change", "[data-dog-sex]", this._onChangeDogSex.bind(this));

    // Steps
    this.addEventListener("quiz:quiz-steps:next-step:start", this._onQuizNextStep.bind(this));
    this.addEventListener("quiz:quiz-steps:next-step:transitioning", this._updateProgressBar.bind(this));
    this.addEventListener("quiz:quiz-steps:next-step:end", this._onQuizNextStepEnd.bind(this));

    this.addEventListener("quiz:quiz-steps:prev-step:start", this._onQuizPrevStep.bind(this));
    this.addEventListener("quiz:quiz-steps:prev-step:transitioning", this._updateProgressBar.bind(this));
    this.addEventListener("quiz:quiz-steps:prev-step:end", this._onQuizPrevStepEnd.bind(this));

    // Sub Navigation
    if (this.subNavigation) {
      // this.delegate.on("click", "quiz-step-tab", this.onClickSubstepTab.bind(this));
      this.subNavigation.addEventListener("quiz:quiz-steps:sub-step:start", this._onQuizSubstepStart.bind(this));
      this.subNavigation.addEventListener("quiz:quiz-steps:sub-step:arrive", this._onQuizSubstepArrive.bind(this));
      this.delegate.on("click", "quiz-step-tab", this._onClickSubstepTab.bind(this));
      // this.subNavigation.addEventListener("quiz:quiz-steps:next-substep", this._updateProgressBar.bind(this));
      // this.subNavigation.addEventListener("quiz:quiz-steps:prev-substep", this._updateProgressBar.bind(this));
      this.subNavigation.addEventListener("quiz:quiz-steps:sub-step:step-updated", this._updateProgressBar.bind(this));
    }

    if (window.quizVariables.customer) {

      // Logged In

    } else {

      // Not Logged In

      if (this.querySelector("#request-email-form")) {
        this.querySelector("#request-email-form").addEventListener("submit", this._onGuestEmailFormSubmit.bind(this));
      }

      if (this.querySelector("[data-quiz-request-email-skip]")) {
        this.querySelector("[data-quiz-request-email-skip]").addEventListener("click", this._onGuestEmailFormSkip.bind(this));
      }

    }

    this.addEventListener("quiz:quiz-steps:submit-quiz", this._submitQuiz.bind(this));

    if (this.buttonBuildQuiz) {
      this.buttonBuildQuiz.addEventListener("click", this._buildQuiz.bind(this));
    }

    this._setupNavigation();

  }

  _onGuestEmailFormSkip(event) {

    event.preventDefault();

    triggerEvent(this, "quiz:quiz-steps:submit-quiz");

  }

  _onGuestEmailFormSubmit(event) {

    event.preventDefault();

    triggerEvent(this, "quiz:quiz-steps:submit-quiz");

  }

  _onQuizPrevStep(event) {
    this.hideSubNavigation();
  }

  _onQuizNextStep(event) {
  }

  _onQuizPrevStepEnd(event) {
    this.hideSubNavigation();
    this._updateNavigation();
  }

  _onQuizNextStepEnd(event) {
    this._updateNavigation();
  }

  /*----- Building Quiz ----- */

  _buildQuiz(event) {

    this._storeDogNames();

    this._buildSubteps();
    this._buildSubNavigation();

    this.showSubNavigation();

  }

  _storeDogNames() {

    const dogNamesContainer = this.querySelector("#dog-names");
    const dogNames = dogNamesContainer.querySelectorAll("input");

    this.dogNames = [];

    dogNames.forEach(e => {
      this.dogNames.push(e.value);
    });

  };

  _buildSubteps() {

    /* ----- Prepare the steps HTML ----- */

    let html = "";

    let container = document.createElement('quiz-step-dog-details');
    container.classList.add("quiz-step__inner");

    for (let i = 0; i < this.dogNames.length; i++) {

      const dogName = this.dogNames[i];
      const dogNamePossessive = getPossessive(this.dogNames[i]);
      const dogNumber = i + 1;

      let temporaryElement = document.createElement('div');
      let dogHTML = window.quizVariables.steps.dog.replaceAll("$DOGNUM", dogNumber);
      temporaryElement.innerHTML = dogHTML;

      const dataNumber = temporaryElement.querySelectorAll('[data-dog]');
      const termsName = temporaryElement.querySelectorAll('[data-dog-name]');
      const termsNamePossessive = temporaryElement.querySelectorAll('[data-dog-name-possessive]');

      termsName.forEach(e => {
        e.innerText = dogName;
      });

      dataNumber.forEach(e => {
        e.dataset.dog = dogNumber;
      });

      termsNamePossessive.forEach(e => {
        e.dataset.dog = dogNamePossessive;
      });

      html += temporaryElement.innerHTML;

    }

    /* ----- Add the dog substeps HTML to the container ----- */

    container.innerHTML = html;

    this.substepContainer.innerHTML = "";
    this.substepContainer.appendChild(container);

    // Make the first substep visible.
    this.substepContainer.querySelector("[data-quiz-substep]").classList.add("quiz-substep--active");

  }

  _onChangeDogSex(event) {

    const input = event.target.closest("[data-dog-sex]");
    const substepContainer = input.closest('.quiz-step--dogs-substeps');
    const substep = input.closest('[data-quiz-substep]');
    const dogNumber = substep.dataset.dog;
    const dogSex = substep.querySelector('[data-dog-sex]').value;
    const substeps = substepContainer.querySelectorAll(`[data-quiz-substep][data-dog="${dogNumber}"]`);

    substeps.forEach(substep => {

      const termsNeutered = substep.querySelectorAll(`[data-term-neutered]`);

      termsNeutered.forEach(e => {
        e.innerText = dogSex === "Male" ? "neutered" : "spayed";
      });

    });

  }

  _onClickUpdateDog(event) {

    const button = event.target.closest("[data-quiz-button-update-dog]");
    const substep = button.closest('[data-quiz-substep]');

    const dogNumber = substep.dataset.dog;
    const dogSex = substep.querySelector('[data-dog-sex]').value;

    const substeps = this.querySelectorAll(`[data-quiz-substep][data-dog="${dogNumber}"]`)

    substeps.forEach(substep => {

      const termsNeutered = substep.querySelectorAll(`[data-term-neutered]`);
      const termsSexPossessive = substep.querySelectorAll(`[data-term-sex-possessive]`);
      const termsSexPronoun = substep.querySelectorAll(`[data-dog-sex-pronoun]`);

      termsSexPronoun.forEach(e => {
        e.innerText = dogSex === "Male" ? "He" : "She";
      });

      termsNeutered.forEach(e => {
        e.innerText = dogSex === "Male" ? "neutered" : "spayed";
      });

      termsSexPossessive.forEach(e => {
        e.innerText = dogSex === "Male" ? "His" : "Her";
      });

    });

  }

  /*----- Submitting Quiz ----- */

  _submitQuiz(event) {

    this._storeDogData();

    location.href = window.quizVariables.locations.post_data;
    return;

  }

  _storeDogData() {

    const data = this._sanitiseDogData(this._getDogData());
    const dataString = JSON.stringify(data);

    localStorage.setItem("dogData", dataString);

  }

  _getDogData() {

    let data = {
      "dogs": []
    };

    for (let i = 0; i < this.dogNames.length; i++) {

      const dogNumber = i + 1;

      let dogData = {};

      dogData.name = stripNewlines(document.querySelector(`quiz-step-dog-general[data-dog="${dogNumber}"] [data-dog-name]`).innerText);
      dogData.sex = stripNewlines(document.querySelector(`[name="dog-${dogNumber}-sex"]`).value);

      dogData.neutered = document.querySelector(`[name="dog-${dogNumber}-neutered"]`).value === "is" ? true : false;
      dogData.breed = stripNewlines(document.querySelector(`[name="dog-${dogNumber}-breed"]`).value);

      dogData.weight = Number(document.querySelector(`[name="dog-${dogNumber}-weight"]`).value);
      dogData.ideal_weight = Number(document.querySelector(`[name="dog-${dogNumber}-ideal-weight"]`).value);
      dogData.has_health_issue = document.querySelector(`[name="dog-${dogNumber}-has-health-issue"]`).value === "has" ? true : false;
      dogData.prescription_diet = stripNewlines(document.querySelector(`[name="dog-${dogNumber}-prescription-diet"]`).value);
      dogData.weight_profile = stripNewlines(document.querySelector(`[name="dog-${dogNumber}-weight-profile"]`).value);
      dogData.activity_level = stripNewlines(document.querySelector(`[name="dog-${dogNumber}-activity-level"]`).value);

      // Age
      const age = document.querySelector(`[name="dog-${dogNumber}-age"]`).value;
      const ageType = document.querySelector(`[name="dog-${dogNumber}-age-type"]`).value;

      if (ageType == "Year") {
        dogData.age_in_months = age * 12;
      }
      else
        if (ageType == "Month") {
          dogData.age_in_months = Number(age);
        }

      data.dogs.push(dogData);

    }

    let customerEmail;

    if (document.querySelector("#request_email")) {
      customerEmail = document.querySelector("#request_email").value;
    }
    else
      if (window.quizVariables.customer) {
        customerEmail = window.quizVariables.customer.email;
      }

    if (customerEmail) {
      data.customer = {
        email: customerEmail
      }
    }

    return data;

  }

  _sanitiseDogData(data) {

    // console.log('_sanitiseDogData');
    // console.log(data);

    if (!data) {
      return;
    }

    const dogData = data;

    for (let d = 0; d < dogData.dogs.length; d++) {
      const dog = dogData.dogs[d];
      dog.name = dog.name === "" ? "None" : dog.name;
      dog.sex = dog.sex === "" ? "None" : dog.sex;
      dog.neutered = dog.neutered === "" ? "None" : dog.neutered;
      dog.breed = dog.breed === "" ? "None" : dog.breed;
      dog.weight = dog.weight === "" ? "0" : dog.weight;
      dog.ideal_weight = dog.ideal_weight === "" ? "0" : dog.ideal_weight;
      dog.has_health_issue = dog.has_health_issue === "" ? "None" : dog.has_health_issue;
      dog.prescription_diet = dog.prescription_diet === "" ? "None" : dog.prescription_diet;
      dog.weight_profile = dog.weight_profile === "" ? "None" : dog.weight_profile;
      dog.activity_level = dog.activity_level === "" ? "None" : dog.activity_level;
      dog.age_in_months = dog.age_in_months === "" ? "None" : dog.age_in_months;
    }

    // console.log(dogData);

    return dogData;

  }

  /*----- Navigation ----- */

  get navigation() {

    return this.querySelector('.quiz-navigation');

  }

  _setupNavigation() {

    const navigationHeight = this.navigation ? this.navigation.clientHeight : undefined;
    const subnavigationHeight = this.subNavigation ? this.subNavigation.clientHeight : undefined;

    if (navigationHeight) {
      document.documentElement.style.setProperty('--quiz-navigation-height', navigationHeight + 'px');
    }

    if (subnavigationHeight) {
      document.documentElement.style.setProperty('--quiz-sub-navigation-height', subnavigationHeight + 'px');
    }

    this._updateNavigation();

  }

  _updateNavigation() {

    const backButton = this.querySelector('[data-quiz-button-back]');
    const backButtonLabel = backButton.querySelector('.quiz-navigation-button__text');

    if (this.stepNumber == 1) {
      backButton.classList.add("quiz-navigation-button--hidden");
    }
    else {
      backButton.classList.remove("quiz-navigation-button--hidden");
    }

  }

  /*----- ProgressBar ----- */

  get progressBar() {
    return document.querySelector('quiz-progress-bar');
  }

  get totalMainSteps() {
    return this.querySelectorAll('.quiz-step').length;
  }

  get currentMainStepNumber() {
    const steps = this.querySelectorAll('.quiz-step');
    for (let s = 0; s < steps.length; s++) {
      const currentStep = steps[s];
      if (currentStep.classList.contains('quiz-step--entering') == true) {
        return s;
      }
    }
    return 3;
  }

  get totalDogSteps() {
    return this.querySelectorAll('.quiz-substep').length;
  }

  get currentDogStepNumber() {
    const steps = this.querySelectorAll('.quiz-substep');
    for (let s = 0; s < steps.length; s++) {
      const currentStep = steps[s];
      if (currentStep.classList.contains('quiz-substep--active') == true) {
        return s + 1;
      }
    }
  }

  _updateProgressBar() {

    const barSegments = 2;

    const start = this.progressBar.dataset.startingProgressSegment ? Number(this.progressBar.dataset.startingProgressSegment) / barSegments : 1 / barSegments;

    let mainStepProgress = (this.currentMainStepNumber / this.totalMainSteps) * (1 / 5);
    let dogStepProgress = this.currentMainStepNumber >= 3 ? (this.currentDogStepNumber / this.totalDogSteps) * (4 / 5) : 0;

    let progress = start + (dogStepProgress + mainStepProgress) / barSegments;

    this.progressBar.progress(progress);

  }

  /*----- Sub Navigation ----- */

  get subNavigation() {
    return document.querySelector('quiz-sub-navigation');
  }
  get subNavigationTabs() {
    return document.querySelector('quiz-dog-navigation');
  }

  _buildSubNavigation() {

    this.subNavigationTabs.innerHTML = "";

    for (let i = 0; i < this.dogNames.length; i++) {

      const name = this.dogNames[i];
      const active = i === 0 ? 'active' : '';
      const disabled = active === 'active' ? '' : 'disabled';

      this.subNavigationTabs.insertAdjacentHTML('beforeend', Templates.quizStepTab({
        name: name,
        attributes: `data-dog="${i + 1}" ${disabled}`,
        classes: `${active}`
      }));

    }

    this.showSubNavigation();

  }

  showSubNavigation() {
    this.subNavigation.classList.remove('quiz-sub-navigation--hidden');
  }
  hideSubNavigation() {
    this.subNavigation.classList.add('quiz-sub-navigation--hidden');
  }

  _onClickSubstepTab(event) {

    const quizSteps = document.querySelector("quiz-steps");
    const tab = event.target.closest("quiz-step-tab");
    const dog = tab.dataset.dog;
    // const destinationTab = document.querySelector(`quiz-step-tab[data-dog="${dog}"]`); 
    const destinationStep = document.querySelector(`quiz-step-dog-general[data-dog="${dog}"]`);

    // tab.deactivate();
    // destinationTab.activate();

    quizSteps.gotoSubstep(destinationStep);

  }


  /*----- Steps ----- */

  get currentStep() {
    return this.querySelector(".quiz-step--active");
  }
  get stepNumber() {
    const steps = this.querySelectorAll('.quiz-step');
    for (let s = 0; s < steps.length; s++) {
      const currentStep = steps[s];
      if (currentStep.classList.contains('quiz-step--active') == true) {
        return s + 1;
      }
    }
  }
  get nextStep() {
    let sibling = this.currentStep.nextElementSibling;
    while (sibling) {
      if (sibling.matches(".quiz-step")) return sibling;
      sibling = sibling.nextElementSibling
    }
  }
  get prevStep() {
    let sibling = this.currentStep.previousElementSibling;
    while (sibling) {
      if (sibling.matches(".quiz-step")) return sibling;
      sibling = sibling.previousElementSibling
    }
  }
  get animatingStep() {
    return this.querySelector(".quiz-step--animating");
  }

  getStepType(button) {

    let stepType;

    if (button.closest("quiz-step-dog-details")) {
      stepType = 'substep';
    }
    else {
      stepType = 'step';
    }

    return stepType;

  }

  skipToResults() {
    location.href = window.quizVariables.locations.results;
  }

  scrollToTop() {
    window.scrollTo(0, 0);
  }

  next(event) {

    const button = event.target;
    let stepType = this.getStepType(button);;

    if (stepType == "step") {
      this.gotoNextStep(event);
    }
    else if (stepType == "substep") {
      this.gotoNextSubstep(event);
    }

    this.scrollToTop();

  }

  prev(event) {

    const button = event.target;
    let stepType = this.getStepType(button);;

    if (stepType == "step") {
      this.gotoPrevStep(event);
    }
    else
      if (stepType == "substep") {
        this.gotoPrevSubstep(event);
      }

    this.scrollToTop();

  }

  back(event) {

    // this.prev(event);

    const button = event.target;
    let stepType = this.getStepType(button);;

    if (this.currentStep.querySelector(".quiz-substep")) {
      // Current step has substeps.
      if (this.prevSubstep) {
        this.gotoPrevSubstep(event);
      }
      else {
        this.gotoPrevStep(event);
      }
    }
    else {
      this.gotoPrevStep(event);
    }

    triggerEvent(this, "quiz:quiz-steps:back");

  }

  async gotoNextStep(event) {

    const button = event.currentTarget;
    const nextStep = this.nextStep;
    const currentStep = this.currentStep;
    const animatingStep = this.animatingStep;

    if (!nextStep || animatingStep) {
      return;
    }

    triggerEvent(this, "quiz:quiz-steps:next-step:start");

    currentStep.classList.add("quiz-step--animating");
    currentStep.classList.add("quiz-step--leaving");

    nextStep.classList.add("quiz-step--animating");
    nextStep.classList.add("quiz-step--entering");

    triggerEvent(this, "quiz:quiz-steps:next-step:transitioning");

    if (window.matchMedia(window.themeVariables.breakpoints.phone).matches) {

      // console.log("Next - Mobile");

      const animation1 = new CustomAnimation(new CustomKeyframeEffect(currentStep,
        {
          visibility: ["visible", "hidden"],
          transform: ["translateX(0%)", "translateX(-100%)"],
          opacity: ["1", "0"]
        },
        {
          duration: 500,
          easing: "cubic-bezier(0.23, 1, 0.32, 1)"
        }
      ));

      animation1.play();
      await animation1.finished;

      currentStep.classList.remove("quiz-step--active");
      currentStep.classList.remove("quiz-step--animating");
      currentStep.classList.remove("quiz-step--leaving");

      const animation2 = new CustomAnimation(new CustomKeyframeEffect(nextStep,
        {
          visibility: ["hidden", "visible"],
          transform: ["translateX(100%)", "translateX(0)"],
          opacity: ["0", "1"]
        },
        {
          duration: 500,
          easing: "cubic-bezier(0.23, 1, 0.32, 1)"
        }
      ));

      animation2.play();
      await animation2.finished;

      triggerEvent(this, "quiz:quiz-steps:next-step:arrive");

      nextStep.classList.add("quiz-step--active");
      nextStep.classList.remove("quiz-step--animating");
      nextStep.classList.remove("quiz-step--entering");

      triggerEvent(this, "quiz:quiz-steps:next-step:end");

    }
    else {

      // console.log("Next - Desktop");

      const animation = new CustomAnimation(new ParallelEffect([
        new CustomKeyframeEffect(currentStep,
          {
            visibility: ["visible", "hidden"],
            transform: ["translateX(0%)", "translateX(-100%)"],
            opacity: ["1", "0"]
          },
          {
            duration: 1000,
            easing: "cubic-bezier(0.23, 1, 0.32, 1)"
          }
        ),
        new CustomKeyframeEffect(nextStep,
          {
            visibility: ["hidden", "visible"],
            transform: ["translateX(100%)", "translateX(0)"],
            opacity: ["0", "1"]
          },
          {
            duration: 1000,
            easing: "cubic-bezier(0.23, 1, 0.32, 1)"
          }
        )
      ]));

      animation.play();

      await animation.finished;

      triggerEvent(this, "quiz:quiz-steps:next-step:arrive");

      currentStep.classList.remove("quiz-step--active");
      currentStep.classList.remove("quiz-step--animating");
      currentStep.classList.remove("quiz-step--leaving");

      nextStep.classList.add("quiz-step--active");
      nextStep.classList.remove("quiz-step--animating");
      nextStep.classList.remove("quiz-step--entering");

      triggerEvent(this, "quiz:quiz-steps:next-step:end");

    }

  }
  async gotoPrevStep(event) {

    const button = event.currentTarget;
    const prevStep = this.prevStep;
    const currentStep = this.currentStep;
    const animatingStep = this.animatingStep;

    if (!prevStep || animatingStep) {
      return;
    }

    triggerEvent(this, "quiz:quiz-steps:prev-step:start");

    currentStep.classList.add("quiz-step--animating");
    currentStep.classList.add("quiz-step--leaving");

    prevStep.classList.add("quiz-step--animating");
    prevStep.classList.add("quiz-step--entering");

    triggerEvent(this, "quiz:quiz-steps:prev-step:transitioning");

    if (window.matchMedia(window.themeVariables.breakpoints.phone).matches) {

      // console.log("Prev - Mobile");

      const animation1 = new CustomAnimation(new CustomKeyframeEffect(currentStep,
        {
          visibility: ["visible", "hidden"],
          transform: ["translateX(0)", "translateX(100%)"],
          opacity: ["1", "0"]
        },
        {
          duration: 500,
          easing: "cubic-bezier(0.23, 1, 0.32, 1)"
        }
      ));

      animation1.play();
      await animation1.finished;

      currentStep.classList.remove("quiz-step--active");
      currentStep.classList.remove("quiz-step--animating");
      currentStep.classList.remove("quiz-step--leaving");

      const animation2 = new CustomAnimation(new CustomKeyframeEffect(prevStep,
        {
          visibility: ["hidden", "visible"],
          transform: ["translateX(-100%)", "translateX(0)"],
          opacity: ["0", "1"]
        },
        {
          duration: 500,
          easing: "cubic-bezier(0.23, 1, 0.32, 1)"
        }
      ));

      animation2.play();
      await animation2.finished;

      triggerEvent(this, "quiz:quiz-steps:next-step:arrive");

      prevStep.classList.add("quiz-step--active");
      prevStep.classList.remove("quiz-step--animating");
      prevStep.classList.remove("quiz-step--entering");

      triggerEvent(this, "quiz:quiz-steps:next-step:end");

    }
    else {

      // console.log("Prev - Desktop");

      const animation = new CustomAnimation(new ParallelEffect([
        new CustomKeyframeEffect(currentStep,
          {
            visibility: ["visible", "hidden"],
            transform: ["translateX(0)", "translateX(100%)"],
            opacity: ["1", "0"]
          },
          {
            duration: 1000,
            easing: "cubic-bezier(0.23, 1, 0.32, 1)"
          }
        ),
        new CustomKeyframeEffect(prevStep,
          {
            visibility: ["hidden", "visible"],
            transform: ["translateX(-100%)", "translateX(0)"],
            opacity: ["0", "1"]
          },
          {
            duration: 1000,
            easing: "cubic-bezier(0.23, 1, 0.32, 1)"
          }
        )
      ]));

      animation.play();

      await animation.finished;

      triggerEvent(this, "quiz:quiz-steps:next-step:arrive");

      currentStep.classList.remove("quiz-step--active");
      currentStep.classList.remove("quiz-step--animating");
      currentStep.classList.remove("quiz-step--leaving");

      prevStep.classList.add("quiz-step--active");
      prevStep.classList.remove("quiz-step--animating");
      prevStep.classList.remove("quiz-step--entering");

      triggerEvent(this, "quiz:quiz-steps:next-step:end");

    }

  }

  /*----- Sub Steps ----- */

  get currentSubstep() {
    return this.querySelector(".quiz-substep--active");
  }
  get nextSubstep() {
    let sibling = this.currentSubstep.nextElementSibling;
    while (sibling) {
      if (sibling.matches(".quiz-substep")) return sibling;
      sibling = sibling.nextElementSibling
    }
  }
  get prevSubstep() {
    let sibling = this.currentSubstep.previousElementSibling;
    while (sibling) {
      if (sibling.matches(".quiz-substep")) return sibling;
      sibling = sibling.previousElementSibling
    }
  }

  gotoNextSubstep(event) {

    if (this.nextSubstep) {
      this.gotoSubstep(this.nextSubstep);
      triggerEvent(this.subNavigationTabs, "quiz:quiz-steps:next-substep");
      return;
    }

    if (this.nextStep) {
      this.gotoNextStep(event);
    }
    else {
      triggerEvent(this, "quiz:quiz-steps:submit-quiz");
    }

  }

  gotoPrevSubstep() {
    this.gotoSubstep(this.prevSubstep);
    triggerEvent(this.subNavigationTabs, "quiz:quiz-steps:prev-substep");
  }

  _dogComplete(event) {

    const button = event.target.closest("button");

    const currentSubstep = this.currentSubstep;
    const nextSubstep = currentSubstep.nextElementSibling;
    const dogNumber = currentSubstep.dataset.dog;

    const currentTab = this.subNavigation.querySelector(`quiz-step-tab[data-dog="${dogNumber}"]`);
    const nextTab = currentTab.nextElementSibling;

    currentTab.deactivate();

    if (nextTab) {
      nextTab.activate();
      nextTab.enable();
    }

    if (!nextSubstep) {
      button._startTransition;
    }

  }

  _onQuizSubstepStart() {

  }

  _onQuizSubstepArrive() {

  }

  async gotoSubstep(substep) {

    const currentSubstep = this.currentSubstep;
    const destinationSubstep = substep;
    const animatingSubstep = this.animatingSubstep;

    if (!destinationSubstep || animatingSubstep) {
      return;
    }
    if (!destinationSubstep.classList.contains("quiz-substep")) {
      return;
    }

    triggerEvent(this.subNavigation, "quiz:quiz-steps:sub-step:start");

    currentSubstep.classList.add("quiz-substep--visible");
    currentSubstep.classList.add("quiz-substep--animating");

    const animation = new CustomAnimation(new CustomKeyframeEffect(currentSubstep,
      {
        visibility: ["visible", "hidden"],
        transform: ["translateY(0%)", "translateY(-20%)"],
        opacity: ["1", "0"]
      },
      {
        duration: 300,
        easing: "cubic-bezier(0.23, 1, 0.32, 1)"
      }
    ));

    triggerEvent(this, "quiz:quiz-steps:sub-step:leave");

    animation.play();

    await animation.finished;

    currentSubstep.classList.remove("quiz-substep--active");
    currentSubstep.classList.remove("quiz-substep--visible");
    currentSubstep.classList.remove("quiz-substep--animating");

    destinationSubstep.classList.add("quiz-substep--visible");
    destinationSubstep.classList.add("quiz-substep--active");
    destinationSubstep.classList.add("quiz-substep--animating");

    triggerEvent(this.subNavigation, "quiz:quiz-steps:sub-step:step-updated");

    const animation2 = new CustomAnimation(new CustomKeyframeEffect(destinationSubstep,
      {
        visibility: ["hidden", "visible"],
        transform: ["translateY(20%)", "translateY(0%)"],
        opacity: ["0", "1"]
      },
      {
        duration: 300,
        easing: "cubic-bezier(0.23, 1, 0.32, 1)"
      }
    ));

    animation2.play();

    destinationSubstep.classList.remove("quiz-substep--animating");

    await animation2.finished;

    triggerEvent(this.subNavigation, "quiz:quiz-steps:sub-step:arrive");

  }

};

var ExpandingInput = class extends CustomHTMLElement {
  constructor() {
    super();
    if (Shopify.designMode) {
      // this.rootDelegate.on("shopify:section:select", (event) => filterShopifyEvent(event, this, () => this.open = true));
      // this.rootDelegate.on("shopify:section:deselect", (event) => filterShopifyEvent(event, this, () => this.open = false));
    }
  }
  connectedCallback() {

    this.input = this.querySelector(".expanding-input__input");
    this.inputDisplay = this.querySelector(".expanding-input__display");
    this.line = this.closest("quiz-step-line");

    if (this.input.type == "number") {
      // this.max = this.input.max;
      this.delegate.on("keypress", ".expanding-input__display", this.onInputNumberValidate.bind(this));
      this.delegate.on("keyup", ".expanding-input__display", this.onInputNumber.bind(this));
    }
    else if (this.input.nodeName == "SELECT") {
      // this.delegate.on("click", ".expanding-input__display", this.onInputSelect.bind(this));
      this.delegate.on("change", ".expanding-input__input", this.onInputSelectChange.bind(this));
    }
    else {
      this.delegate.on("keyup", ".expanding-input__display", this.onInput.bind(this));
    }

  }
  onInput(event) {

    this.input.value = this.inputDisplay.innerText;
    triggerEvent(this, "quiz:expanding-input:change");

  }
  onInputSelectChange(event) {

    const displayValue = event.target.options[event.target.selectedIndex].dataset.displayValue;

    this.inputDisplay.innerHTML = displayValue ? displayValue : event.target.options[event.target.selectedIndex].value;

    triggerEvent(this, "quiz:expanding-input:change");

  }
  onInputNumberValidate(event) {
    if (isNaN(String.fromCharCode(event.which)) == true) {
      event.preventDefault();
      return false;
    }
  }
  onInputNumber(event) {

    const number = Number(this.inputDisplay.innerText);
    const max = Number(this.input.max);
    const min = Number(this.input.min);

    if (max) {
      if (number > max) {
        this.input.value = max;
        this.inputDisplay.innerText = max;
        return;
      }
    }

    if (min) {
      if (number < min && number != 0) {
        this.input.value = min;
        this.inputDisplay.innerText = min;
        return;
      }
    }

    this.input.value = this.inputDisplay.innerText;

    triggerEvent(this, "quiz:expanding-input:change");

  }

}

var QuizCustomerRegisterForm = class extends CustomHTMLElement {

  constructor() {
    super();
    if (Shopify.designMode) {
      // this.rootDelegate.on("shopify:section:select", (event) => filterShopifyEvent(event, this, () => this.open = true));
      // this.rootDelegate.on("shopify:section:deselect", (event) => filterShopifyEvent(event, this, () => this.open = false));
    }
  }
  connectedCallback() {

    console.log('QuizCustomerRegisterForm');

    this.registerFormContainer = this.querySelector('[data-register-form]');
    this.registerForm = this.registerFormContainer.querySelector("form");

    this.kycFormContainer = this.querySelector('[data-kyc-form]');
    this.kycForm = this.kycFormContainer.querySelector("form");

    this.registerButton = this.querySelector('[data-register-form-register-button]');
    this.continueButton = this.querySelector('[data-register-form-continue-button]');
    this.backButton = this.querySelector('[data-register-form-back-button]');

    this.backButton.addEventListener("click", this.showRegisterForm.bind(this));

    this.registerForm.addEventListener("submit", this.onRegisterFormSubmit.bind(this));
    this.kycForm.addEventListener("submit", this.onKYCFormSubmit.bind(this));

    this.realRegisterForm = this.closest(".quiz-customer-forms").querySelector("[data-real-form]");

  }

  onRegisterFormSubmit(event) {

    event.preventDefault();
    this.showKYCForm();

    return false;

  }

  onKYCFormSubmit(event) {

    event.preventDefault();
    this.realRegisterForm.submit();

    return false;

  }

  async showKYCForm() {

    const animateOut = new CustomAnimation(new CustomKeyframeEffect(this.registerFormContainer,
      {
        display: ["block", "none"],
        opacity: ["1", "0"]
      },
      {
        duration: 250,
        easing: "cubic-bezier(0.23, 1, 0.32, 1)"
      }
    ));
    animateOut.play();

    await animateOut.finished;

    this.registerFormContainer.classList.add("hidden");
    this.kycFormContainer.classList.remove("hidden");

    const animateIn = new CustomAnimation(new CustomKeyframeEffect(this.kycFormContainer,
      {
        display: ["none", "block"],
        opacity: ["0", "1"]
      },
      {
        duration: 250,
        easing: "cubic-bezier(0.23, 1, 0.32, 1)"
      }
    ));

    animateIn.play();

  }

  async showRegisterForm() {

    const animateOut = new CustomAnimation(new CustomKeyframeEffect(this.kycFormContainer,
      {
        display: ["block", "none"],
        opacity: ["1", "0"]
      },
      {
        duration: 250,
        easing: "cubic-bezier(0.23, 1, 0.32, 1)"
      }
    ));
    animateOut.play();

    await animateOut.finished;

    this.kycFormContainer.classList.add("hidden");
    this.registerFormContainer.classList.remove("hidden");

    const animateIn = new CustomAnimation(new CustomKeyframeEffect(this.registerFormContainer,
      {
        display: ["none", "block"],
        opacity: ["0", "1"]
      },
      {
        duration: 250,
        easing: "cubic-bezier(0.23, 1, 0.32, 1)"
      }
    ));

    animateIn.play();

  }

}

var QuizCustomerForms = class extends CustomHTMLElement {

  constructor() {
    super();
    if (Shopify.designMode) {
      // this.rootDelegate.on("shopify:section:select", (event) => filterShopifyEvent(event, this, () => this.open = true));
      // this.rootDelegate.on("shopify:section:deselect", (event) => filterShopifyEvent(event, this, () => this.open = false));
    }
  }
  connectedCallback() {

    // console.log("QuizCustomerForms");

    this.loginForm = this.querySelector('.quiz-login-form');
    this.loginButton = this.querySelector('[data-action-quiz-start-login-button]');
    this.registerForm = this.querySelector('.quiz-register-form');
    this.registerButton = this.querySelector('[data-action-quiz-start-register-button]');

    // this.loginButton.addEventListener("click", this.showLoginForm.bind(this));
    // this.registerButton.addEventListener("click", this.showRegisterForm.bind(this));

  }

  async showRegisterForm() {

    // console.log('showRegisterForm');

    const animateOut = new CustomAnimation(new CustomKeyframeEffect(this.loginForm,
      {
        display: ["block", "none"],
        transform: ["translateX(0)", "translateX(-20%)"],
        opacity: ["1", "0"]
      },
      {
        duration: 250,
        easing: "cubic-bezier(0.23, 1, 0.32, 1)"
      }
    ));
    animateOut.play();

    await animateOut.finished;

    this.loginForm.classList.add("hidden");
    this.registerForm.classList.remove("hidden");

    const animateIn = new CustomAnimation(new CustomKeyframeEffect(this.registerForm,
      {
        display: ["none", "block"],
        transform: ["translateX(20%)", "translateX(0%)"],
        opacity: ["0", "1"]
      },
      {
        duration: 250,
        easing: "cubic-bezier(0.23, 1, 0.32, 1)"
      }
    ));

    animateIn.play();

  }

  async showLoginForm() {

    // console.log('showLoginForm');

    const animateOut = new CustomAnimation(new CustomKeyframeEffect(this.registerForm,
      {
        display: ["block", "none"],
        transform: ["translateX(0)", "translateX(-20%)"],
        opacity: ["1", "0"]
      },
      {
        duration: 250,
        easing: "cubic-bezier(0.23, 1, 0.32, 1)"
      }
    ));
    animateOut.play();

    await animateOut.finished;

    this.registerForm.classList.add("hidden");
    this.loginForm.classList.remove("hidden");

    const animateIn = new CustomAnimation(new CustomKeyframeEffect(this.loginForm,
      {
        display: ["none", "block"],
        transform: ["translateX(20%)", "translateX(0%)"],
        opacity: ["0", "1"]
      },
      {
        duration: 250,
        easing: "cubic-bezier(0.23, 1, 0.32, 1)"
      }
    ));

    animateIn.play();

  }

}

var QuizStep = class extends CustomHTMLElement {

  constructor() {
    super();
    if (Shopify.designMode) {
      // this.rootDelegate.on("shopify:section:select", (event) => filterShopifyEvent(event, this, () => this.open = true));
      // this.rootDelegate.on("shopify:section:deselect", (event) => filterShopifyEvent(event, this, () => this.open = false));
    }
  }
  connectedCallback() {

    this.inputs = this.querySelectorAll("input, select, textarea");

  }

}

var QuizStepLine = class extends CustomHTMLElement {

  constructor() {
    super();

    // this.delegateElement = new main_default(document.body);
    // this.delegateElement.on("change", ".expanding-input__input", this._onValueChanged.bind(this));

    if (Shopify.designMode) {
      // this.rootDelegate.on("shopify:section:select", (event) => filterShopifyEvent(event, this, () => this.open = true));
      // this.rootDelegate.on("shopify:section:deselect", (event) => filterShopifyEvent(event, this, () => this.open = false));
    }
  }
  connectedCallback() {

    this.inputs.forEach((element) => {
      // element.addEventListener("change", this._onValueChanged.bind(this));
    })

    this.valid = false;

    this.addEventListener("quiz:expanding-input:change", this._onValueChanged.bind(this));
    this.addEventListener("quiz:step-line:valid", this._onValid.bind(this));
    this.addEventListener("quiz:step-line:invalid", this._onInvalid.bind(this));

  }

  _onValueChanged(event) {

    let input = event.target.querySelector(".expanding-input__input");

    if (this.nextLine) {
      if (this._showNextLine(input)) {
        this.nextLine.show();
      }
      else {
        this.nextLine.hide();
      }
    }

    this.validate();


  }

  validate() {

    if (!this.inputs.length) {
      triggerEvent(this, "quiz:step-line:valid");
      return true;
    }

    if (this.hasAttribute("optional")) {
      // Is optional, always valid.
      triggerEvent(this, "quiz:step-line:valid");
      return true;
    }

    if (this.hasAttribute("optional-if-hidden")) {

      // Is optional if hidden
      if (this.classList.contains("quiz-step-line--hidden") == true) {
        triggerEvent(this, "quiz:step-line:valid");
        return true;
      }

    }

    for (let i = 0; i < this.inputs.length; i++) {

      const input = this.inputs[i];

      if (!input.value) {
        triggerEvent(this, "quiz:step-line:invalid");
        return false;
      }

    }

    triggerEvent(this, "quiz:step-line:valid");
    return true;

  }

  _showNextLine(input) {

    let inputElement;

    if (input) {
      inputElement = input.tagName == "SELECT" ? input.options[input.selectedIndex] : input;
    }

    if (input.getAttribute("show-nextline-if")) {
      if (inputElement.value == input.getAttribute("show-nextline-if")) {
        return true;
      }
      else {
        return false;
      }
    }
    else {
      return true;
    }

  }

  _onInvalid() {

    this.valid = false;

    if (this.nextLine && this.nextLine.validate() == false) {
      this.nextLine.hide();
      return;
    }

    this.nextButton.disabled = true;

  }

  _onValid() {

    this.valid = true;

    if (!this.nextLine) {
      this.nextButton.disabled = false;
    }

    if (this.nextLine) {

      if (this.nextLine.hasAttribute("optional")) {
        this.nextLine.validate();
      }
      else
        if (this.nextLine.hasAttribute("optional-if-hidden") && this.nextLine.classList.contains("quiz-step-line--hidden") == true) {
          this.nextLine.validate();
        }

    }

  }

  show() {
    this.classList.remove("quiz-step-line--hidden");
  }

  hide() {
    this.classList.add("quiz-step-line--hidden");
  }

  get inputs() {
    return this.querySelectorAll("input, select, textarea");
  }

  get nextButton() {
    return this.closest(".quiz-step, .quiz-substep").querySelector("[data-quiz-button-next]");
  }

  get prevLine() {
    let sibling = this.previousElementSibling;
    while (sibling) {
      if (sibling.matches("quiz-step-line")) return sibling;
      sibling = sibling.previousElementSibling
    }
  }

  get nextLine() {
    let sibling = this.nextElementSibling;
    while (sibling) {
      if (sibling.matches("quiz-step-line")) return sibling;
      sibling = sibling.nextElementSibling
    }
  }

}

var QuizTile = class extends CustomHTMLElement {
  constructor() {
    super();
    if (Shopify.designMode) {
      // this.rootDelegate.on("shopify:section:select", (event) => filterShopifyEvent(event, this, () => this.open = true));
      // this.rootDelegate.on("shopify:section:deselect", (event) => filterShopifyEvent(event, this, () => this.open = false));
    }
  }
  connectedCallback() {

    this.input = this.querySelector(".quiz-tile__input");
    this.tiles = this.closest("quiz-tiles");

    this.addEventListener("click", this.onClick.bind(this));

  }
  onClick(event) {

    if (this.selected == false) {

      this.tiles.reset();
      this.tiles.input.value = this.dataset.value;
      this.tiles.updateHint(this.dataset.hint);

      this.classList.add("quiz-tile--selected");

    }

    triggerEvent(this, "quiz:tiles:change");

  }

  get selected() {
    return this.classList.contains("quiz-tile--selected");
  }

  get nextButton() {
    return this.closest(".quiz-step, .quiz-substep").querySelector("[data-quiz-button-next]");
  }

}

var QuizTiles = class extends CustomHTMLElement {

  constructor() {
    super();
  }

  connectedCallback() {

    this.tiles = this.querySelectorAll("quiz-tile");
    this.input = this.querySelector(".quiz-tiles__input");
    this.hint = this.querySelector(".quiz-tiles__hint");

    this.addEventListener("quiz:tiles:change", this._onValueChanged.bind(this));
    this.addEventListener("quiz:tiles:valid", this._onValid.bind(this));
    this.addEventListener("quiz:tiles:invalid", this._onInvalid.bind(this));

  }

  _onValueChanged(event) {

    if (this.validate() == true) {
      triggerEvent(this, "quiz:tiles:valid");
    }
    else {
      triggerEvent(this, "quiz:tiles:invalid");
    }

  }

  reset() {
    this.tiles.forEach((tile) => {
      tile.classList.remove("quiz-tile--selected");
    });
  }

  validate() {

    if (this.input.value) {
      return true;
    } else {
      return false;
    }

  }

  async resetHint() {

    const animateOut = new CustomAnimation(new CustomKeyframeEffect(this.hint,
      {
        visibility: ["visible", "hidden"],
        transform: ["translateY(0)", "translateY(-20%)"],
        opacity: ["1", "0"]
      },
      {
        duration: 250,
        easing: "cubic-bezier(0.23, 1, 0.32, 1)"
      }
    ));
    animateOut.play();

    await animateOut.finished;

    this.hint.innerText = this.hint.dataset.default;

    const animateIn = new CustomAnimation(new CustomKeyframeEffect(this.hint,
      {
        visibility: ["hidden", "visible"],
        transform: ["translateY(20%)", "translateY(0%)"],
        opacity: ["0", "1"]
      },
      {
        duration: 250,
        easing: "cubic-bezier(0.23, 1, 0.32, 1)"
      }
    ));
    animateIn.play();

  }

  async updateHint(hint) {

    if (!hint) {
      return;
    }

    const animateOut = new CustomAnimation(new CustomKeyframeEffect(this.hint,
      {
        visibility: ["visible", "hidden"],
        transform: ["translateY(0)", "translateY(-20%)"],
        opacity: ["1", "0"]
      },
      {
        duration: 250,
        easing: "cubic-bezier(0.23, 1, 0.32, 1)"
      }
    ));
    animateOut.play();

    await animateOut.finished;

    this.hint.innerText = hint;

    const animateIn = new CustomAnimation(new CustomKeyframeEffect(this.hint,
      {
        visibility: ["hidden", "visible"],
        transform: ["translateY(20%)", "translateY(0%)"],
        opacity: ["0", "1"]
      },
      {
        duration: 250,
        easing: "cubic-bezier(0.23, 1, 0.32, 1)"
      }
    ));
    animateIn.play();

  }

  _onInvalid() {

    if (this.nextLine && this.nextLine.validate() == false) {
      this.nextLine.hide();
    }

    if (!this.nextLine) {
      this.nextButton.disabled = true;
    }

  }

  _onValid() {

    if (this.nextLine) {
      this.nextLine.show();
    }

    if (!this.nextLine) {
      this.nextButton.disabled = false;
    }

  }

  show() {
    this.classList.remove("quiz-tiles--hidden");
  }

  hide() {
    this.classList.add("quiz-tiles--hidden");
  }

  get inputs() {
    return this.querySelectorAll("input, select, textarea");
  }

  get nextButton() {
    return this.closest(".quiz-step, .quiz-substep").querySelector("[data-quiz-button-next]");
  }

  get prevLine() {
    let sibling = this.previousElementSibling;
    while (sibling) {
      if (sibling.matches("quiz-tiles")) return sibling;
      sibling = sibling.previousElementSibling
    }
  }

  get nextLine() {
    let sibling = this.nextElementSibling;
    while (sibling) {
      if (sibling.matches("quiz-tiles")) return sibling;
      sibling = sibling.nextElementSibling
    }
  }

}

var QuizTile = class extends CustomHTMLElement {
  constructor() {
    super();
    if (Shopify.designMode) {
      // this.rootDelegate.on("shopify:section:select", (event) => filterShopifyEvent(event, this, () => this.open = true));
      // this.rootDelegate.on("shopify:section:deselect", (event) => filterShopifyEvent(event, this, () => this.open = false));
    }
  }
  connectedCallback() {

    this.input = this.querySelector(".quiz-tile__input");
    this.tiles = this.closest("quiz-tiles");

    this.addEventListener("click", this.onClick.bind(this));

  }
  onClick(event) {

    if (this.selected == false) {

      this.tiles.reset();
      this.tiles.input.value = this.dataset.value;
      this.tiles.updateHint(this.dataset.hint);

      this.classList.add("quiz-tile--selected");

    }

    triggerEvent(this, "quiz:tiles:change");

  }

  get selected() {
    return this.classList.contains("quiz-tile--selected");
  }

  get nextButton() {
    return this.closest(".quiz-step, .quiz-substep").querySelector("[data-quiz-button-next]");
  }

}

var QuizStepTab = class extends CustomHTMLElement {

  constructor() {
    super();
  }

  connectedCallback() {

    // this.addEventListener("click", this._onClickSubstepTab.bind(this));

  }

  enable() {
    this.removeAttribute("disabled");
  }

  disable() {
    this.disabled = true;
  }

  activate() {
    this.parentElement.querySelectorAll("quiz-step-tab").forEach(e => {
      e.classList.remove("active");
    });
    this.classList.add("active");
  }

  deactivate() {
    this.classList.remove("active");
  }

  /*

  _onClickSubstepTab(event) {

    const quizSteps = document.querySelector("quiz-steps");
    const tab = event.target.closest("quiz-step-tab");
    const dog = tab.dataset.dog;
    const destinationTab = document.querySelector(`quiz-step-tab[data-dog="${dog}"]`);
    const destinationStep = document.querySelector(`quiz-step-dog-general[data-dog="${dog}"]`);

    tab.deactivate();
    destinationTab.activate();

    quizSteps.gotoSubstep(destinationStep);

  }

  */

}

var QuizProgressBar = class extends CustomHTMLElement {

  constructor() {
    super();
    if (Shopify.designMode) {
      // this.rootDelegate.on("shopify:section:deselect", (event) => filterShopifyEvent(event, this, () => this.open = false));
    }
  }

  connectedCallback() {

  }

  progress(progress) {

    if (!(typeof progress == "number")) {
      return;
    }

    this.style.setProperty('--quiz-progress', progress);

  }

}


var QuizResults = class extends CustomHTMLElement {

  constructor() {
    super();
    if (Shopify.designMode) {
      // this.rootDelegate.on("shopify:section:deselect", (event) => filterShopifyEvent(event, this, () => this.open = false));
    }
  }

  connectedCallback() {

    if (!localStorage.getItem("dogData") && !Shopify.designMode) {
      location.href = window.quizVariables.locations.quiz;
    }

    this._addEventListeners();

    this._buildQuizResults()

    // Initial Button
    const button = this.querySelector('[data-quiz-button-back]');
    const buttonText = button.querySelector('.quiz-navigation-button__text');
    const prevDog = this.currentDog.previousElementSibling;

    if (!prevDog) {
      buttonText.innerHTML = "Start Over";
    }
    else {
      buttonText.innerHTML = "Back";
    }

  }

  get buttonNext() {
    return this.querySelector('.quiz-sticky-form [data-quiz-button-next]');
  }

  get buttonCheckout() {
    return this.querySelector('.quiz-sticky-form [data-quiz-button-checkout]');
  }

  /* ----- Navigation ----- */

  get stickyForm() {
    return this.querySelector('quiz-sticky-form');
  }

  /* ----- Navigation ----- */

  get calculator() {

    return document.querySelector('.quiz-feeding-calculator');

  }

  /* ----- Navigation ----- */

  _updateNavigation() {

    const backButton = this.querySelector('[data-quiz-button-back]');
    const backButtonLabel = backButton.querySelector('.quiz-navigation-button__text');

    if (this.currentDogNumber == 1) {
      backButtonLabel.innerHTML = "Start Over";
    }
    else {
      backButtonLabel.innerHTML = "Back";
    }

  }

  next(event) {

    const button = event.target;
    // let stepType = this.getStepType(button);;

    this.nextDog(event);

  }

  prev(event) {

    const button = event.target;
    // let stepType = this.getStepType(button);;

    this.prevDog();

  }

  back(event) {

    const button = event.target;
    const buttonText = button.querySelector('.quiz-navigation-button__text');

    const currentDog = this.currentDog;
    const prevDog = this.currentDog.previousElementSibling;

    if (prevDog) {
      this.prevDog();
    }
    else {
      location.href = window.quizVariables.locations.quiz;
    }

  }

  nextDog() {

    const currentDog = this.currentDog;
    const nextDog = this.currentDog.nextElementSibling;

    if (nextDog) {
      this.gotoDog(nextDog);
    }

  }

  prevDog() {

    const currentDog = this.currentDog;
    const prevDog = this.currentDog.previousElementSibling;

    if (prevDog) {
      this.gotoDog(prevDog);
    }

  }

  async gotoDog(dog) {

    const currentDog = this.currentDog;
    const destinationDog = dog;

    currentDog.classList.add("quiz-results-dog--animating");
    destinationDog.classList.add("quiz-results-dog--animating");

    triggerEvent(currentDog, "quiz:quiz-results:next-dog:start");

    const animation = new CustomAnimation(new CustomKeyframeEffect(currentDog,
      {
        visibility: ["visible", "hidden"],
        transform: ["translateY(0%)", "translateY(-20%)"],
        opacity: ["1", "0"]
      },
      {
        duration: 300,
        easing: "cubic-bezier(0.23, 1, 0.32, 1)"
      }
    ));

    animation.play();

    await animation.finished;

    currentDog.classList.remove("quiz-results-dog--active");
    currentDog.classList.remove("quiz-results-dog--animating");

    destinationDog.classList.add("quiz-results-dog--active");

    // triggerEvent(currentDog, "quiz:quiz-results:next-dog:in-transition", detail: { dog: dog });

    destinationDog.dispatchEvent(new CustomEvent("quiz:quiz-results:next-dog:in-transition", {
      bubbles: true,
      detail: {
        dog: {
          name: destinationDog.dataset.dogName
        }
      }
    }));

    const animation2 = new CustomAnimation(new CustomKeyframeEffect(destinationDog,
      {
        visibility: ["hidden", "visible"],
        transform: ["translateY(20%)", "translateY(0%)"],
        opacity: ["0", "1"]
      },
      {
        duration: 300,
        easing: "cubic-bezier(0.23, 1, 0.32, 1)"
      }
    ));

    animation2.play();

    destinationDog.classList.remove("quiz-results-dog--animating");

    await animation2.finished;

    triggerEvent(destinationDog, "quiz:quiz-results:next-dog:arrive");

  }

  _updateDogProgress() {

    const dogNumber = this.querySelectorAll('quiz-results-dog').length;
    const currentDog = this.currentDog;
    const nextDog = this.currentDog.nextElementSibling;

    this._updateProgressBar();

    if (dogNumber == 1) {
      this.buttonCheckout.disabled = false;
      this.buttonNext.style = "display:none";
    }

    if (!nextDog) {
      this.buttonNext.disabled = true;
      this.buttonCheckout.disabled = false;
    }
    else {
      this.buttonNext.disabled = false;
      this.buttonCheckout.disabled = true;
    }

  }

  get currentDog() {
    return this.querySelector(".quiz-results-dog--active");
  }

  get currentDogNumber() {
    const dogs = this.querySelectorAll('quiz-results-dog');
    for (let s = 0; s < dogs.length; s++) {
      const currentDog = dogs[s];
      if (currentDog.classList.contains('quiz-results-dog--active') == true) {
        return s + 1;
      }
    }
  }

  /*----- Sub Navigation ----- */

  get subNavigation() {
    return document.querySelector('quiz-sub-navigation');
  }
  get subNavigationTabs() {
    return document.querySelector('quiz-dog-navigation');
  }

  _buildSubNavigation() {

    let data = JSON.parse(localStorage.getItem("dogData"));

    if (Shopify.designMode) {
      data = defaultDogData;
    }

    this.subNavigationTabs.innerHTML = "";

    for (let i = 0; i < data.dogs.length; i++) {

      const name = data.dogs[i].name;
      const active = i === 0 ? 'active' : '';
      const disabled = '';

      this.subNavigationTabs.insertAdjacentHTML('beforeend', Templates.quizStepTab({
        name: name,
        attributes: `data-dog="${i + 1}" ${disabled}`,
        classes: `${active}`
      }));

    }

    this.showSubNavigation();

  }

  showSubNavigation() {
    this.subNavigation.classList.remove('quiz-sub-navigation--hidden');
  }
  hideSubNavigation() {
    this.subNavigation.classList.add('quiz-sub-navigation--hidden');
  }

  _onClickSubstepTab(event) {

    const tab = event.target.closest("quiz-step-tab");
    const tabs = this.subNavigationTabs;
    const tabActive = tabs.querySelector("quiz-step-tab.active");
    const dogIndex = tab.dataset.dog;
    const dog = document.querySelector(`quiz-results-dog[data-dog="${dogIndex}"]`);

    tabActive.deactivate();
    tab.activate();

    this.gotoDog(dog);

  }

  /* ----- Quiz Results ----- */

  async _buildQuizResults() {

    this._buildSubNavigation();

    await this._buildQuizResultsDogs();

    this.stickyForm.updateDog();

    console.log(this.calculator);

    this.calculator.updateSummary();
    this.calculator.updateProduct();

    // This centres the results on some screen sizes.
    triggerEvent(document.documentElement, "resize");

    setTimeout(() => {
      this.loadingOverlay.hide();
    }, 500);

  }

  async _buildQuizResultsDogs() {

    let data = JSON.parse(localStorage.getItem("dogData"));

    if (Shopify.designMode) {
      data = defaultDogData;
    }

    if (!data && !Shopify.designMode) {
      location.href = window.quizVariables.locations.quiz;
    }

    const container = document.querySelector('quiz-results-dogs');

    let dogs = data.dogs;

    /* ----- PRINT DOG STEPS ----- */

    let html = "";

    for (let i = 0; i < dogs.length; i++) {

      const dog = dogs[i];
      const dogName = dog.name;
      const dogNumber = i + 1;

      const productRecommendationLimit = 1;
      const productRecommendationNumber = 3;

      let temp = document.createElement('div');

      let dogHTML;

      if (dog.prescription_diet == "Not Listed") {
        dogHTML = window.quizVariables.results.dog_condition_not_listed;
      }
      else {
        dogHTML = window.quizVariables.results.dog;
      }

      dogHTML = dogHTML.replaceAll("$DOGINDEX", dogNumber);
      dogHTML = dogHTML.replaceAll("$DOGNAME_POSSESSIVE", getPossessive(dogName));
      dogHTML = dogHTML.replaceAll("$DOGNAME", dogName);
      dogHTML = dogHTML.replaceAll("$PRODUCT_RECOMMENDATION_LIMIT", productRecommendationLimit);
      dogHTML = dogHTML.replaceAll("$PRODUCT_RECOMMENDATION_NUMBER", productRecommendationNumber);

      temp.innerHTML = dogHTML;

      html += temp.innerHTML;

    }

    container.innerHTML = html;

    /* ----- POPULATE PRODUCTS ----- */

    // Fetch the dogs' product recommendations from the API.

    let dogsWithData = [];

    await Promise.allSettled(dogs.map(async (dog) => {

      const dogData = await this.fetchDogProductRecommendations(dog);
      const dogDataFinal = Object.assign({ quizResults: dogData }, dog)

      dogsWithData.push(dogDataFinal);

    }));

    /* ----- POPULATE CONTENT ----- */

    for (let i = 0; i < dogsWithData.length; i++) {

      const dog = dogsWithData[i];
      const products = dog.quizResults;

      const dogContainer = this.querySelector(`quiz-results-dog[data-dog-name="${dog.name}"]`);
      const productsContainer = this.querySelector(`quiz-results-dog[data-dog-name="${dog.name}"] .quiz-results__list`);
      const modalContainer = document.querySelector(`quiz-results-modals`);

      let dogResultsHTML = "";
      let modalsHTML = "";

      if (products.length > 0) {

        for (let d = 0; d < products.length; d++) {

          const product = products[d].node;
          const productJson = JSON.stringify(product);
          const product_id = products[d].node.id.replace("gid://shopify/Product/", "")
          const product_available = product.totalInventory == 0 ? false : true;

          let productHTML = Templates.quizResultsProduct({ product: product, index: (d + 1) });
          let el = document.createElement("div");

          el.innerHTML = productHTML;

          const productElement = el.querySelector('quiz-results-product');
          productElement.dataset.productJson = productJson;

          // Unavailable Product.

          if (product_available == false) {
            el.querySelector("quiz-results-product").classList.add("quiz-results-product--soldout");
          }

          dogResultsHTML += el.innerHTML;

          if (!modalContainer.querySelector(`#quiz-popup--recipe-${product_id}`)) {
            modalsHTML += Templates.quizResultsModal({ product: product });
          }

        }

        productsContainer.innerHTML = dogResultsHTML;
        modalContainer.insertAdjacentHTML('beforeend', modalsHTML);

        let firstProduct = dog.quizResults.length > 0 ? dog.quizResults[0].node : null;

        productsContainer.querySelector("quiz-results-product").classList.add("quiz-results-product--recommended");

        dogContainer.querySelector(".initial-recommendation").innerHTML = dog.quizResults.message;

      }
      else {

        dogResultsHTML += "No Results";

      }

    }

    // Only hide inactive dog steps after the DOM elements have been added to the page
    // so that their progress bars are properly initiated.

    for (let i = 0; i < container.querySelectorAll("quiz-results-dog").length; i++) {
      const element = container.querySelectorAll("quiz-results-dog")[i];
      if (i > 0) {
        element.classList.remove("quiz-results-dog--active");
      }
    }

    this._updateDogProgress();

  }



  async fetchDogProductRecommendations(dog) {

    /* ----- Get Dog vitals and use them to compile a list of tags.) ----- */

    let collectionID;
    let collectionMessage;

    let ageTag;
    let weightTag;

    if (dog.age_in_months < 12) {
      ageTag = "Puppy";
    } else
      if (dog.age_in_months < 84) {
        ageTag = "Adult";
      } else {
        ageTag = "Senior";
      }

    if (dog.weight_profile == "Overweight") {
      weightTag = "Overweight";
    } else
      if (dog.weight_profile == "Underweight") {
        weightTag = "Underweight";
      } else {
        weightTag = "Ideal";
      }

    let collection_tag;

    if (ageTag == "Puppy") {
      if (weightTag == "Underweight") {
        collection_tag = "HPU";
      }
      else
        if (weightTag == "Ideal") {
          collection_tag = "HPR";
        }
        else
          if (weightTag == "Overweight") {
            collection_tag = "HPO";
          }
    }
    else
      if (ageTag == "Adult") {
        if (weightTag == "Underweight") {
          collection_tag = "HAU";
        }
        else
          if (weightTag == "Ideal") {
            collection_tag = "HAR";
          }
          else
            if (weightTag == "Overweight") {
              collection_tag = "HAO";
            }
      }
      else
        if (ageTag == "Senior") {
          if (weightTag == "Underweight") {
            collection_tag = "HSU";
          }
          else
            if (weightTag == "Ideal") {
              collection_tag = "HSR";
            }
            else
              if (weightTag == "Overweight") {
                collection_tag = "HSO";
              }
        }

    if (dog.prescription_diet && dog.prescription_diet != "" && dog.prescription_diet != "None") {

      collection_tag = dog.prescription_diet

    }

    switch (collection_tag) {
      case 'HPU':
        collectionID = window.quizVariables.collections.hpu.id;
        collectionMessage = window.quizVariables.collections.hpu.message;
        break;
      case 'HPR':
        collectionID = window.quizVariables.collections.hpr.id;
        collectionMessage = window.quizVariables.collections.hpr.message;
        break;
      case 'HPO':
        collectionID = window.quizVariables.collections.hpo.id;
        collectionMessage = window.quizVariables.collections.hpo.message;
        break;
      case 'HAU':
        collectionID = window.quizVariables.collections.hau.id;
        collectionMessage = window.quizVariables.collections.hau.message;
        break;
      case 'HAR':
        collectionID = window.quizVariables.collections.har.id;
        collectionMessage = window.quizVariables.collections.har.message;
        break;
      case 'HAO':
        collectionID = window.quizVariables.collections.hao.id;
        collectionMessage = window.quizVariables.collections.hao.message;
        break;
      case 'HSU':
        collectionID = window.quizVariables.collections.hsu.id;
        collectionMessage = window.quizVariables.collections.hsu.message;
        break;
      case 'HSR':
        collectionID = window.quizVariables.collections.hsr.id;
        collectionMessage = window.quizVariables.collections.hsr.message;
        break;
      case 'HSO':
        collectionID = window.quizVariables.collections.hso.id;
        collectionMessage = window.quizVariables.collections.hso.message;
        break;
      case 'Allergies and/or Skin Issues':
        collectionID = window.quizVariables.collections.allergies.id;
        collectionMessage = window.quizVariables.collections.allergies.message;
        break;
      case 'Calcium Oxalate and/or Struvite Stones':
        collectionID = window.quizVariables.collections.calcium_oxolate.id;
        collectionMessage = window.quizVariables.collections.calcium_oxolate.message;
        break;
      case 'Cancer Related':
        collectionID = window.quizVariables.collections.cancer.id;
        collectionMessage = window.quizVariables.collections.cancer.message;
        break;
      case 'Grain Intolerance':
        collectionID = window.quizVariables.collections.grain_intolerance.id;
        collectionMessage = window.quizVariables.collections.grain_intolerance.message;
        break;
      case 'Heart Issues':
        collectionID = window.quizVariables.collections.heart.id;
        collectionMessage = window.quizVariables.collections.heart.message;
        break;
      case 'Joint Issues':
        collectionID = window.quizVariables.collections.joint.id;
        collectionMessage = window.quizVariables.collections.joint.message;
        break;
      case 'Kidney Disease':
        collectionID = window.quizVariables.collections.kidney.id;
        collectionMessage = window.quizVariables.collections.kidney.message;
        break;
      case 'Kidney Disease and Pancreatitis':
        collectionID = window.quizVariables.collections.kidney_pancreatitis.id;
        collectionMessage = window.quizVariables.collections.kidney_pancreatitis.message;
        break;
      case 'Liver Issues':
        collectionID = window.quizVariables.collections.liver.id;
        collectionMessage = window.quizVariables.collections.liver.message;
        break;
      case 'Pancreatitis':
        collectionID = window.quizVariables.collections.pancreatitis.id;
        collectionMessage = window.quizVariables.collections.pancreatitis.message;
        break;
      case 'Stomach and/or GI Issues':
        collectionID = window.quizVariables.collections.gi.id;
        collectionMessage = window.quizVariables.collections.gi.message;
        break;
      case 'Urate Stones':
        collectionID = window.quizVariables.collections.urate_stones.id;
        collectionMessage = window.quizVariables.collections.urate_stones.message;
        break;
    }

    const query = () => `
      {
        collection(id: "gid://shopify/Collection/${collectionID}") {
          products(first: 10) {
            edges {
              node {
                id
                title
                descriptionHtml
                onlineStoreUrl
                tags
                totalInventory
                cup_calories: metafield(namespace: "nutrition", key: "calories_cup") {
                  value
                }
                images(first: 1) {
                  edges {
                    node {
                      url
                    }
                  }
                }
                variants: variants(first: 100) {
                  edges {
                    node {
                      id
                      title
                      quantityAvailable
                      price {
                        amount
                      }
                      compareAtPrice {
                        amount
                      }
                      calories: metafield(namespace: "quiz", key: "calories") {
                        value
                      }
                    }
                  }
                }
                shortDescription: metafield(namespace: "custom", key: "short_description") {
                  value
                  type
                }
                productColor: metafield(namespace: "quiz", key: "product_color") {
                  value
                  type
                }
                quizResultImage: metafield(namespace: "custom", key: "quiz_results_image") {
                  reference {
                    ... on MediaImage {
                      image {
                        originalSrc
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
      `;

    const myquery = query();

    console.log(dog);
    /*
    // console.log('------------------------------');
    // console.log(collection_tag);
    // console.log(collectionID);
    // console.log(myquery);
    */

    const GRAPHQL_BODY = () => {

      return {
        'async': true,
        'crossDomain': true,
        'method': 'POST',
        'headers': {
          'X-Shopify-Storefront-Access-Token': API.STOREFRONT.CONFIG().ACCESS_TOKEN,
          'Content-Type': 'application/graphql',
        },
        'body': query()
      };

    };

    /*
    // console.log("BODY");
    */

    const response = await fetch(API.STOREFRONT.CONFIG().URL, GRAPHQL_BODY())
      .then(response => response.json())
      .then(products => {
        return products;
      });

    // console.log("RESPONSE");
    console.log(response);

    let responseProducts = response.data.collection.products.edges;

    responseProducts.message = collectionMessage;

    return responseProducts;
    // return filteredResponse;

  }

  get loadingOverlay() {
    return this.querySelector('quiz-loading-overlay');
  }

  /*----- ProgressBar ----- */

  get progressBar() {
    return document.querySelector('quiz-progress-bar');
  }

  _updateProgressBar() {

    const start = this.progressBar.dataset.startingProgressSegment ? Number(this.progressBar.dataset.startingProgressSegment) / 2 : 1 / 2;

    let dogs = document.querySelectorAll('quiz-results-dog');

    let totalStepsNumber = dogs.length == 1 ? 1 : dogs.length - 1;
    let currentStepNumber = 0;

    for (let i = 0; i < dogs.length; i++) {
      const element = dogs[i];
      if (element.classList.contains("quiz-results-dog--active") == false) {
        currentStepNumber++;
      }
      else if (element.classList.contains("quiz-results-dog--active") == true) {
        // currentStepNumber++;
        break;
      }
    }

    let progress = start + ((currentStepNumber / totalStepsNumber) / 2);

    this.progressBar.progress(progress);

  }

  /*----- Checkout ----- */

  async onClickCheckout(event) {

    const button = event.target.closest("button");

    button._startTransition();

    let hasUpsells = !this.popupUpsells ? false : true;

    if (hasUpsells == true) {
      this.popupUpsells.open = true;
    }
    else {
      await this.proceedToCheckout();
    }

    button._endTransition();

  }

  async onClickCheckoutUpsells(event) {

    const button = event.target.closest("button");
    const modal = button.closest('modal-content');
    const quizResults = document.querySelector("quiz-results");

    button._startTransition();

    await quizResults.proceedToCheckout();
    modal.open = false;

    button._endTransition();

  }

  get popupUpsells() {
    return document.querySelector("#quiz-popup--upsells");
  }

  async proceedToCheckout() {

    // Find all products to be added to cart.

    const selectedProducts = document.querySelectorAll(".quiz-results-product--selected");

    // Add all products to the cart via the cart API.

    let data = { items: [] };

    const dogBoxesID = uuid();

    for (let i = 0; i < selectedProducts.length; i++) {

      const product = selectedProducts[i];
      const dog = selectedProducts[i].closest('quiz-results-dog');

      const variant16oz = Number(product.dataset.variantId);
      const variant64oz = Number(product.dataset.variantIdLast);

      if (dog) {

        const dogData = dog.quizData;
        const dogName = dogData.name;

        const transitionCalories = calculateTransitionCalories(dog.quizData);
        const productCalories = product.calories;

        let baseQuantity = calculatePacketQuantity((transitionCalories / productCalories));
        let quantity1 = 1;
        let quantity2 = 0;

        if (baseQuantity <= 8) {
          quantity1 = baseQuantity;
          quantity2 = 0;
        }
        else {
          quantity1 = Math.floor(baseQuantity % 4);
          quantity2 = Math.floor(baseQuantity / 4);
        }

        // 16oz
        let item = {
          id: variant16oz,
          quantity: quantity1
        };

        item.properties = {
          "_dog": dogName,
          "_boxID": dogName + '_' + dogBoxesID,
          "Starter Box": `${dogName}'s Starter Box`
        };

        data.items.push(item);

        if (quantity2 != 0) {

          // 64oz
          let item2 = {
            id: variant64oz,
            quantity: quantity2
          };

          item2.properties = {
            "_dog": dogName,
            "_boxID": dogName + '_' + dogBoxesID,
            "Starter Box": `${dogName}'s Starter Box`
          };

          data.items.push(item2);

        }

        if (typeof StarterBoxFreebie != "undefined") {

          let item3 = {
            id: StarterBoxFreebie.variants[0].id,
            quantity: 1,
            properties: {
              "_dog": dogName,
              "_boxID": dogName + '_' + dogBoxesID,
              "Starter Box": `${dogName}'s Starter Box`
            }
          }

          data.items.push(item3);

        }

      } else {

        let item = {
          id: variant16oz,
          quantity: 1
        };

        data.items.push(item);

      }

    }

    const response = await fetch(`${window.themeVariables.routes.cartAddUrl}.js`, {
      body: JSON.stringify(data),
      method: "POST",
      headers: {
        'Content-Type': 'application/json',
        "X-Requested-With": "XMLHttpRequest"
      }
    });

    const cartContent = await response.json();

    // Open the cart/

    fetch(`${window.themeVariables.routes.cartUrl}.js`).then(async (response2) => {
      const cartContent = await response2.json();
      document.documentElement.dispatchEvent(new CustomEvent("cart:updated", {
        bubbles: true,
        detail: {
          cart: cartContent
        }
      }));
      document.documentElement.dispatchEvent(new CustomEvent("cart:refresh", {
        bubbles: true,
        detail: {
          cart: cartContent,
          openMiniCart: window.themeVariables.settings.cartType === "drawer" && this.closest(".drawer") === null
        }
      }));
    });

  }

  /*----- Utilities ----- */

  get getAllSelectedProducts() {

    return this.querySelectorAll('.quiz-results-product--selected');

  }

  get getDogSelectedProducts() {

    const dog = this.currentDog;
    return dog.querySelectorAll('.quiz-results-product--selected');

  }

  get totalPriceAll() {

  }

  get totalPriceDog() {



  }


  /*----- Initiation ----- */

  _addEventListeners() {

    this.delegate.on("click", "[data-quiz-button-back]", this.back.bind(this));
    this.delegate.on("click", "[data-quiz-button-prev]", this.prev.bind(this));
    this.delegate.on("click", "[data-quiz-button-next]", this.next.bind(this));

    this.delegate.on("click", "[data-quiz-button-checkout]", this.onClickCheckout.bind(this));

    this.addEventListener("quiz:quiz-results:next-dog:in-transition", function () {

      console.log('onNextDogTransition');
      console.log(this.currentDog);

      this._updateDogProgress();
      this._updateNavigation();

      this.stickyForm.updateDog(event.detail.dog);

      this.subNavigationTabs.reset();
      this.subNavigationTabs.activate(this.currentDog.name);

    }.bind(this));

    this.addEventListener("quiz:quiz-results:next-dog:start", function () {

      // console.log('onNextDogStart');
      // this.calculator.updateSummary();
      // this.calculator.updateProduct();

    }.bind(this));

    this.addEventListener("quiz:quiz-results:next-dog:in-transition", function () {


      this.stickyForm.updateDog();
      this.stickyForm.updatePrice();

      this.calculator.updateSummary();
      this.calculator.updateProduct();

    }.bind(this));

    this.delegate.on("click", "quiz-step-tab", this._onClickSubstepTab.bind(this));

    this.addEventListener("quiz:quiz-results-product:change", function (event) {

      this.stickyForm.updateDog();
      this.stickyForm.updatePrice();

      this.calculator.updateProduct();

    }.bind(this));

    document.documentElement.addEventListener("cart:open", function () {
      this.progressBar.progress(1)
    }.bind(this));

    document.documentElement.addEventListener("cart:close", function () {
      this._updateProgressBar();
    }.bind(this));

  }

}

var getPossessive = function (name) {

  if (!name || typeof name != "string") {
    return;
  }

  if (name.slice(-1) == "s") {
    name = name + "'";
  }
  else {
    name = name + "'s";
  }

  return name;

}

var QuizResultsStickyForm = class extends CustomHTMLElement {

  constructor() {
    super();
    if (Shopify.designMode) {
      // this.rootDelegate.on("shopify:section:deselect", (event) => filterShopifyEvent(event, this, () => this.open = false));
    }
  }

  connectedCallback() {
    this.dogNameElement = this.querySelector("[data-dog-name-possessive]");
    this.dogNameElement2 = this.querySelector("[data-dog-name]");
  }

  updatePrice(dog) {

    const quizResultsContainer = document.querySelector('quiz-results');
    const currentDog = (dog) ? dog : quizResultsContainer.currentDog;
    const totalPrice = currentDog.totalSelectedProductsPrice();
    const boxPriceElement = this.querySelector('[data-box-price]');
    const totalPriceFormatted = formatMoney(totalPrice);

    boxPriceElement.innerHTML = totalPriceFormatted;

  }

  calculateTotalPrice() {

    let price = 0;

    const quizResultsContainer = document.querySelector('quiz-results');
    const dog = quizResultsContainer.currentDog;
    const selectedProducts = dog.querySelectorAll('.quiz-results-product--selected');

    return price;

  }

  updateDog(dog) {

    let dogName;

    const activeDogStep = document.querySelector('.quiz-results-dog--active');

    if (!dog) {
      dogName = activeDogStep.dataset.dogName;
    }
    else {
      dogName = dog.name;
    }

    let dogNamePossessive = getPossessive(dogName);

    this.dogNameElement.innerText = dogNamePossessive;
    this.dogNameElement2.innerText = dogName;

  }

  get quizResultsDogs() {
    return document.querySelector("quiz-results-dogs");
  }

  get quizResults() {
    return document.querySelector("quiz-results");
  }

}

var QuizResultsDogs = class extends CustomHTMLElement {

  constructor() {
    super();
    if (Shopify.designMode) {
      // this.rootDelegate.on("shopify:section:deselect", (event) => filterShopifyEvent(event, this, () => this.open = false));
    }
  }

  connectedCallback() {



  }

}

var QuizResultsDog = class extends CustomHTMLElement {

  constructor() {
    super();
    if (Shopify.designMode) {
      // this.rootDelegate.on("shopify:section:deselect", (event) => filterShopifyEvent(event, this, () => this.open = false));
    }
  }

  get selectedProductsNumber() {
    return this.querySelectorAll('.quiz-results-product--selected').length;
  }

  get selectedProductsLimit() {
    const limit = this.getAttribute("limit") ? Number(this.getAttribute("limit")) : 0
    return limit;
  }

  get productsList() {
    return this.querySelector("quiz-results-products");
  }

  get tab() {

    const QuizResults = document.querySelector('quiz-results');

    return QuizResults.subNavigationTabs.querySelector(`quiz-step-tab[data-dog-name="${this.name}"]`);

  }

  get name() {
    return this.dataset.dogName;
  }

  get quizData() {

    if (!localStorage.getItem("dogData")) {
      return false;
    }

    let storage = JSON.parse(localStorage.getItem("dogData"));

    if (Shopify.designMode) {
      data = defaultDogData;
    }

    const dogs = storage.dogs;

    for (let i = 0; i < dogs.length; i++) {
      const dog = dogs[i];
      if (dog.name === this.name) {
        return dog;
      }
    }

  }

  connectedCallback() {

    this._addEventListeners();

  }

  totalSelectedProductsPrice() {

    // Calculate Price

    const selectedProducts = this.querySelectorAll('.quiz-results-product--selected');

    let totalPrice = 0;

    for (const product in selectedProducts) {

      if (Object.hasOwnProperty.call(selectedProducts, product)) {

        const element = selectedProducts[product];
        const data = element.data;

        let quantity = 1;

        const transitionCalories = calculateTransitionCalories(this.quizData);
        const productCalories = element.calories;

        quantity = calculatePacketQuantity((transitionCalories / productCalories));

        totalPrice += data.price * quantity;

      }

    }

    return totalPrice;

  }

  _addEventListeners() {

    this.addEventListener("quiz:quiz-results-product:change", this._onProductChange.bind(this));

  }

  _onProductChange(event) {

    // const product = event.target.closest("quiz-results-product");
    // const dog = product.closest("quiz-results-dog");
    // const dogs = dog.closest("quiz-results-dogs");

    this._checkLimit();

  }

  _checkLimit() {

    if (this.selectedProductsNumber >= this.selectedProductsLimit) {
      this.productsList.disable();
    }
    else {
      this.productsList.enable();
    }

  }

}

var QuizLoadingOverlay = class extends CustomHTMLElement {

  constructor() {
    super();
    if (Shopify.designMode) {
      // this.rootDelegate.on("shopify:section:deselect", (event) => filterShopifyEvent(event, this, () => this.open = false));
    }
  }

  connectedCallback() {

  }

  async hide() {

    this.classList.add("quiz-loading-overlay--animating");

    const animation = new CustomAnimation(new CustomKeyframeEffect(this,
      {
        visibility: ["visible", "hidden"],
        opacity: ["1", "0"]
      },
      {
        duration: 1000,
        easing: "cubic-bezier(0.23, 1, 0.32, 1)"
      }
    ));

    animation.play();

    await animation.finished;

    this.classList.add("quiz-loading-overlay--hidden");

    this.classList.remove("quiz-loading-overlay--visible");
    this.classList.remove("quiz-loading-overlay--animating");

  }

  async show() {

    this.classList.add("quiz-loading-overlay--animating");

    const animation = new CustomAnimation(new CustomKeyframeEffect(this,
      {
        visibility: ["hidden", "visible"],
        opacity: ["0", "1"]
      },
      {
        duration: 1000,
        easing: "cubic-bezier(0.23, 1, 0.32, 1)"
      }
    ));

    animation.play();

    await animation.finished;

    this.classList.add("quiz-loading-overlay--hidden");

    this.classList.remove("quiz-loading-overlay--visible");
    this.classList.remove("quiz-loading-overlay--animating");

  }

}

// js/custom-element/section/gallery/quiz-results-products.js
var QuizResultsProducts = class extends CustomHTMLElement {
  connectedCallback() {

    this.listItems = Array.from(this.querySelectorAll("quiz-results-product"));
    this.scrollBarElement = this.querySelector(".gallery__progress-bar");
    this.listWrapperElement = this.querySelector(".gallery__list-wrapper");

    if (this.listItems.length > 1) {
      this.addEventListener("scrollable-content:progress", this._updateProgressBar.bind(this));
      this.addEventListener("prev-next:prev", this.previous.bind(this));
      this.addEventListener("prev-next:next", this.next.bind(this));
      if (Shopify.designMode) {
        this.addEventListener("shopify:block:select", (event) => this.select(event.target.index, !event.detail.load));
      }
    }

  }

  previous() {
    this.select([...this.listItems].reverse().find((item) => item.isOnLeftHalfPartOfScreen).index);
  }
  next() {
    this.select(this.listItems.findIndex((item) => item.isOnRightHalfPartOfScreen));
  }
  select(index, animate = true) {
    const boundingRect = this.listItems[index].getBoundingClientRect();
    this.listWrapperElement.scrollBy({
      behavior: animate ? "smooth" : "auto",
      left: Math.floor(boundingRect.left - window.innerWidth / 2 + boundingRect.width / 2)
    });
  }
  _updateProgressBar(event) {
    var _a;
    (_a = this.scrollBarElement) == null ? void 0 : _a.style.setProperty("--transform", `${event.detail.progress}%`);
  }

  // Custom

  enable() {
    this.classList.remove("quiz-results-products--disabled");
  }

  disable() {
    this.classList.add("quiz-results-products--disabled");
  }

};


// js/custom-element/section/gallery/quiz-results-product.js
var QuizResultsProduct = class extends CustomHTMLElement {

  connectedCallback() {

    this._addEventListeners();

  }

  _onClickSelect(event) {

    this.select();

  }

  _addEventListeners() {

    this.delegate.on("click", "[data-quiz-results-product-checkbox]", this._onClickSelect.bind(this));

  }

  select() {

    const selected = this.classList.contains('quiz-results-product--selected');

    if (selected == false) {

      this.classList.add('quiz-results-product--selected');
      this.buttonLabel.innerText = window.quizVariables.components.quizResultsProduct.selected;

      triggerEvent(this, "quiz:quiz-results-product:selected");

    }
    else {

      this.classList.remove('quiz-results-product--selected');
      this.buttonLabel.innerText = window.quizVariables.components.quizResultsProduct.add_to_cart;

      triggerEvent(this, "quiz:quiz-results-product:deselected");

    }

    triggerEvent(this, "quiz:quiz-results-product:change");

  }

  // Data

  get data() {
    return JSON.parse(this.dataset.variantData);
  }

  get getPrice() {
    return JSON.parse(this.dataset.variantData).price;
  }

  get calories() {
    return this.dataset.calories;
  }

  // UI Elements

  get button() {
    return this.querySelector('[data-quiz-results-product-checkbox]');
  }

  get buttonLabel() {
    return this.querySelector('.quiz-results-product__checkbox-label');
  }

  get index() {
    return [...this.parentNode.children].indexOf(this);
  }

  get isOnRightHalfPartOfScreen() {
    if (window.themeVariables.settings.direction === "ltr") {
      return this.getBoundingClientRect().left > window.innerWidth / 2;
    } else {
      return this.getBoundingClientRect().right < window.innerWidth / 2;
    }
  }
  get isOnLeftHalfPartOfScreen() {
    if (window.themeVariables.settings.direction === "ltr") {
      return this.getBoundingClientRect().right < window.innerWidth / 2;
    } else {
      return this.getBoundingClientRect().left > window.innerWidth / 2;
    }
  }
};

// js/custom-element/section/gallery/quiz-results-products.js
var QuizResultsProducts = class extends CustomHTMLElement {
  connectedCallback() {

    this.listItems = Array.from(this.querySelectorAll("quiz-results-product"));
    this.scrollBarElement = this.querySelector(".gallery__progress-bar");
    this.listWrapperElement = this.querySelector(".gallery__list-wrapper");

    if (this.listItems.length > 1) {
      this.addEventListener("scrollable-content:progress", this._updateProgressBar.bind(this));
      this.addEventListener("prev-next:prev", this.previous.bind(this));
      this.addEventListener("prev-next:next", this.next.bind(this));
      if (Shopify.designMode) {
        this.addEventListener("shopify:block:select", (event) => this.select(event.target.index, !event.detail.load));
      }
    }

  }

  previous() {
    this.select([...this.listItems].reverse().find((item) => item.isOnLeftHalfPartOfScreen).index);
  }
  next() {
    this.select(this.listItems.findIndex((item) => item.isOnRightHalfPartOfScreen));
  }
  select(index, animate = true) {
    const boundingRect = this.listItems[index].getBoundingClientRect();
    this.listWrapperElement.scrollBy({
      behavior: animate ? "smooth" : "auto",
      left: Math.floor(boundingRect.left - window.innerWidth / 2 + boundingRect.width / 2)
    });
  }
  _updateProgressBar(event) {
    var _a;
    (_a = this.scrollBarElement) == null ? void 0 : _a.style.setProperty("--transform", `${event.detail.progress}%`);
  }

  // Custom

  enable() {
    this.classList.remove("quiz-results-products--disabled");
  }

  disable() {
    this.classList.add("quiz-results-products--disabled");
  }

};


// js/custom-element/section/gallery/quiz-results-product.js
var QuizFeedingCalculator = class extends CustomHTMLElement {

  connectedCallback() {

    this._addEventListeners();

    this.quizResults = document.querySelector("quiz-results");

  }

  _addEventListeners() {

    // this.delegate.on("click", "[data-quiz-results-product-checkbox]", this._onClickSelect.bind(this));

  }

  updateSummary() {

    const quizDog = document.querySelector("quiz-results").currentDog;
    const dog = quizDog.quizData;

    // const labelProductName = this.querySelector('[data-calculator-product-name]');
    const labelsDogName = this.querySelectorAll('[data-calculator-dog-name]');
    const labelDogWeight = this.querySelector('[data-calculator-dog-weight]');
    const labelDogAgeNumber = this.querySelector('[data-calculator-dog-age-number]');
    const labelDogAgeType = this.querySelector('[data-calculator-dog-age-type]');
    const labelDogActivity = this.querySelector('[data-calculator-dog-activity]');
    const labelDogCondition = this.querySelector('[data-calculator-condition]');
    const labelDogPronoun = this.querySelector('[data-calculator-dog-pronoun-possessive]');

    // const valueProductName = getProductName();
    const valueDogName = dog.name;
    const valueDogWeight = dog.weight;
    const valueDogCondition = getDogCondition(dog.prescription_diet);
    const valueDogAgeNumber = getDogAgeNumber(dog.age_in_months);
    const valueDogAgeType = getDogAgeType(dog.age_in_months);
    const valueDogActivity = getDogActivity(dog.activity_level);
    const valueDogPronoun = dog.sex === "Male" ? "him" : "her";

    // labelProductName.innerText = valueProductName;
    labelDogWeight.innerText = valueDogWeight;
    labelDogAgeNumber.innerText = valueDogAgeNumber;
    labelDogAgeType.innerText = valueDogAgeType;
    labelDogActivity.innerText = valueDogActivity;
    labelDogCondition.innerText = valueDogCondition;
    labelDogPronoun.innerText = valueDogPronoun;

    labelsDogName.forEach(element => {
      element.innerText = valueDogName;
    });

    function getDogActivity(value) {

      let activity;

      if (value == "Low") {
        activity = "not very active"
      }
      else
        if (value == "Normal") {
          activity = "moderately active"
        }
        else
          if (value == "High") {
            activity = "highly active"
          }

      return activity;

    }

    function getDogCondition(value) {

      let condition;

      if (value == "None") {
        condition = "no prescription diet"
      }
      else {
        condition = "a " + value + " diet";
      }

      return condition;

    }

    function getDogAgeNumber(ageInMonths) {

      let ageNumber;

      if (ageInMonths < 12) {
        ageNumber = ageInMonths;
      }
      else {
        ageNumber = Math.floor(ageInMonths / 12);
      }

      return ageNumber;
    }

    function getDogAgeType(ageInMonths) {

      return ageInMonths > 11 ? "years" : "months";

    }

  }

  updateProduct(data) {

    const quizDog = document.querySelector("quiz-results").currentDog;
    const dog = quizDog.quizData;

    const dogProducts = document.querySelector('.quiz-results-dog--active');
    const product = !dogProducts.querySelector('.quiz-results-product--selected') ? dogProducts.querySelector('.quiz-results-product') : dogProducts.querySelector('.quiz-results-product--selected');
    const productJson = JSON.parse(product.dataset.productJson);

    const daysInBox = window.quizVariables.boxes.days_in_box;
    const weeksInBox = daysInBox / 7;

    console.log(productJson);

    // Calculate Calories

    const totalCaloriesPerDay = calculateKCalPerDay(dog);

    const caloriesPeriod1 = totalCaloriesPerDay * 0.25;
    const caloriesPeriod2 = totalCaloriesPerDay * 0.5;
    const caloriesPeriod3 = totalCaloriesPerDay * 0.75;
    const caloriesPeriod4 = totalCaloriesPerDay * 1;

    const productCalories = product.calories;
    const caloriesPerOunce = productCalories / 16;
    const caloriesPerCup = Number(productJson.cup_calories.value);

    const ouncesPeriod1 = Math.round(caloriesPeriod1 / caloriesPerOunce);
    const ouncesPeriod2 = Math.round(caloriesPeriod2 / caloriesPerOunce);
    const ouncesPeriod3 = Math.round(caloriesPeriod3 / caloriesPerOunce);
    const ouncesPeriod4 = Math.round(caloriesPeriod4 / caloriesPerOunce);

    const cupsPeriod1 = caloriesPeriod1 / caloriesPerCup;
    const cupsPeriod2 = caloriesPeriod2 / caloriesPerCup;
    const cupsPeriod3 = caloriesPeriod3 / caloriesPerCup;
    const cupsPeriod4 = caloriesPeriod4 / caloriesPerCup;

    const transitionCalories = calculateTransitionCalories(dog);

    const quantity16Oz = calculatePacketQuantity((transitionCalories / productCalories));

    const totalOunces = quantity16Oz * 16;
    const totalCups = transitionCalories / caloriesPerCup;

    const totalOuncesPerDay = totalOunces / daysInBox;
    const totalCupsPerDay = totalCups / daysInBox;

    console.log(`

        ====================
        CALORIE CALCULATIONS
        ====================

        DOG
        
        Total Calories Needed Per Day - ${totalCaloriesPerDay}
        
        --------------------
        
        BOX
        
        Days Per Box - ${daysInBox}
        Weeks Per Box - ${weeksInBox}

        --------------------
        
        CALORIES
        TRANSITION CALORIES - ${transitionCalories}

        DAY 1-3 - ${caloriesPeriod1}
        DAY 4-6 - ${caloriesPeriod2}
        DAY 7-9 - ${caloriesPeriod3}
        DAY 10+ - ${caloriesPeriod4}

        --------------------
        
        OUNCES
        
        Period 1 Ounces - ${ouncesPeriod1}
        Period 2 Ounces - ${ouncesPeriod2}
        Period 3 Ounces - ${ouncesPeriod3}
        Period 4 Ounces - ${ouncesPeriod4}

        TOTAL OUNCES - ${totalOunces}

        --------------------

        CUPS
        
        Period 1 Cups - ${cupsPeriod1}
        Period 2 Cups - ${cupsPeriod2}
        Period 3 Cups - ${cupsPeriod3}
        Period 4 Cups - ${cupsPeriod4}

        Total Cups - ${totalCups}

        --------------------

        Calories in 16Oz - ${productCalories}
        Quantity of 16Oz - ${quantity16Oz}

        --------------------
      
      `);

    // Calculate Calories

    const labelProductName = this.querySelector('[data-calculator-product-name]');

    const labelTotalOunces = this.querySelector('[data-calculator-total-ounces]');
    const labelTotalCups = this.querySelector('[data-calculator-total-cups]');
    const labelPeriod1Ounces = this.querySelector('[data-calculator-period-1]').querySelector('[data-ounces]')
    const labelPeriod1Cups = this.querySelector('[data-calculator-period-1]').querySelector('[data-cups]')
    const labelPeriod2Ounces = this.querySelector('[data-calculator-period-2]').querySelector('[data-ounces]')
    const labelPeriod2Cups = this.querySelector('[data-calculator-period-2]').querySelector('[data-cups]')
    const labelPeriod3Ounces = this.querySelector('[data-calculator-period-3]').querySelector('[data-ounces]')
    const labelPeriod3Cups = this.querySelector('[data-calculator-period-3]').querySelector('[data-cups]')
    const labelPeriod4Ounces = this.querySelector('[data-calculator-period-4]').querySelector('[data-ounces]')
    const labelPeriod4Cups = this.querySelector('[data-calculator-period-4]').querySelector('[data-cups]')

    const valueProductName = getProductName();
    const valueTotalOunces = fractionQuarters(totalOuncesPerDay);
    const valueTotalCups = fractionQuarters(totalCupsPerDay);
    const valuePeriod1Ounces = fractionQuarters(ouncesPeriod1);
    const valuePeriod1Cups = fractionQuarters(cupsPeriod1);
    const valuePeriod2Ounces = fractionQuarters(ouncesPeriod2);
    const valuePeriod2Cups = fractionQuarters(cupsPeriod2);
    const valuePeriod3Ounces = fractionQuarters(ouncesPeriod3);
    const valuePeriod3Cups = fractionQuarters(cupsPeriod3);
    const valuePeriod4Ounces = fractionQuarters(ouncesPeriod4);
    const valuePeriod4Cups = fractionQuarters(cupsPeriod4);

    labelProductName.innerHTML = valueProductName;

    labelTotalOunces.innerHTML = valueTotalOunces;
    labelTotalCups.innerHTML = valueTotalCups;
    labelPeriod1Ounces.innerHTML = valuePeriod1Ounces;
    labelPeriod1Cups.innerHTML = valuePeriod1Cups;
    labelPeriod2Ounces.innerHTML = valuePeriod2Ounces;
    labelPeriod2Cups.innerHTML = valuePeriod2Cups;
    labelPeriod3Ounces.innerHTML = valuePeriod3Ounces;
    labelPeriod3Cups.innerHTML = valuePeriod3Cups;
    labelPeriod4Ounces.innerHTML = valuePeriod4Ounces;
    labelPeriod4Cups.innerHTML = valuePeriod4Cups;

    function getProductName() {

      let name;

      name = product.querySelector('.heading').innerHTML;

      return name;

    }

  }

};

var QuizSubNavigation = class extends CustomHTMLElement {

  constructor() {
    super();
    if (Shopify.designMode) {
      // this.rootDelegate.on("shopify:section:deselect", (event) => filterShopifyEvent(event, this, () => this.open = false));
    }
  }

  connectedCallback() {

  }

}

var QuizSubNavigationTabs = class extends CustomHTMLElement {

  constructor() {
    super();
    if (Shopify.designMode) {
      // this.rootDelegate.on("shopify:section:deselect", (event) => filterShopifyEvent(event, this, () => this.open = false));
    }
  }

  connectedCallback() {

  }

  get allTabs() {
    return this.querySelectorAll("quiz-step-tab");
  }

  activate(label) {

    if (!label || typeof label != 'string') {
      return;
    }

    const tab = this.querySelector(`[data-name="${label}"]`);

    if (tab) {
      tab.classList.add("active");
    }

  }

  reset() {

    this.allTabs.forEach(element => {
      element.classList.remove("active");
    });

  }

}

window.customElements.define("revealing-form-input", RevealingFormInput);
window.customElements.define("revealing-form", RevealingForm);
window.customElements.define("split-page-step", SplitPageStep);
window.customElements.define("quiz-steps", QuizSteps);
window.customElements.define("expanding-input", ExpandingInput);

window.customElements.define("quiz-customer-forms", QuizCustomerForms);
window.customElements.define("quiz-customer-register-form", QuizCustomerRegisterForm);
window.customElements.define("quiz-step", QuizStep);
window.customElements.define("quiz-step-line", QuizStepLine);
window.customElements.define("quiz-tiles", QuizTiles);
window.customElements.define("quiz-tile", QuizTile);
window.customElements.define("quiz-step-tab", QuizStepTab);
window.customElements.define("quiz-progress-bar", QuizProgressBar);
window.customElements.define("quiz-loading-overlay", QuizLoadingOverlay);

window.customElements.define("quiz-sticky-form", QuizResultsStickyForm);
window.customElements.define("quiz-results", QuizResults);
window.customElements.define("quiz-results-dogs", QuizResultsDogs);
window.customElements.define("quiz-results-dog", QuizResultsDog);
window.customElements.define("quiz-results-products", QuizResultsProducts);
window.customElements.define("quiz-results-product", QuizResultsProduct);
window.customElements.define("quiz-feeding-calculator", QuizFeedingCalculator);

window.customElements.define("quiz-sub-navigation", QuizSubNavigation);
window.customElements.define("quiz-dog-navigation", QuizSubNavigationTabs);

var QuizStepDogNumber = class extends CustomHTMLElement {

  constructor() {
    super();
    if (Shopify.designMode) {
      // this.rootDelegate.on("shopify:section:deselect", (event) => filterShopifyEvent(event, this, () => this.open = false));
    }
  }

  connectedCallback() {

    this.number = 0;

    this.quizSteps = this.closest("quiz-steps");
    this.dogNamesContainer = this.querySelector("#dog-names");
    this.dogNumberInput = this.querySelector("#dog-number").closest("expanding-input");

    this.dogNumberInput.addEventListener("quiz:expanding-input:change", this._onNumberChanged.bind(this));

  }

  _onNumberChanged(event) {

    let expandingInput = event.target;
    let input = event.target.querySelector("input");
    let number = input.value;

    this.number = Number(number);

    this._updateLine();

  }

  _updateLine() {

    // Update Inputs

    if (this.dogNamesContainer.querySelectorAll("expanding-input").length == this.number || this.number <= 0) {
      return;
    }

    while (this.dogNamesContainer.querySelectorAll("expanding-input").length != this.number) {
      if (this.dogNamesContainer.querySelectorAll("expanding-input").length > this.number) {

        this.dogNamesContainer.querySelectorAll("expanding-input")[this.dogNamesContainer.querySelectorAll("expanding-input").length - 1].remove();
        this.dogNamesContainer.querySelectorAll(".quiz-step-line__text")[this.dogNamesContainer.querySelectorAll(".quiz-step-line__text").length - 1].remove();

      }
      else {

        this.dogNamesContainer.insertAdjacentHTML('beforeend', Templates.expandingInput({
          id: `dog-name-${this.dogNamesContainer.querySelectorAll("expanding-input").length + 1}`,
          name: `dog-name-${this.dogNamesContainer.querySelectorAll("expanding-input").length + 1}`,
          placeholder: "Pet's Name",
        }));

        this.dogNamesContainer.insertAdjacentHTML('beforeend', Templates.quizStepLineText({ placeholder: "." }));

      }
    }

    // Update Text Lines

    for (let i = 0; i < this.dogNamesContainer.querySelectorAll(".quiz-step-line__text").length; i++) {
      const element = this.dogNamesContainer.querySelectorAll(".quiz-step-line__text")[i];
      if (i == 0) {
        element.innerText = "named";
      }
      else
        if (i == this.dogNamesContainer.querySelectorAll(".quiz-step-line__text").length - 1) {
          element.innerText = ".";
        }
        else
          if (i == this.dogNamesContainer.querySelectorAll(".quiz-step-line__text").length - 2) {
            element.innerText = "&";
          }
          else {
            element.innerText = ",";
          }
    }

  }

}

var QuizStepDogSubstep = class extends CustomHTMLElement {

  constructor() {
    super();
    if (Shopify.designMode) {
      // this.rootDelegate.on("shopify:section:deselect", (event) => filterShopifyEvent(event, this, () => this.open = false));
    }
  }

  connectedCallback() {

  }

}

var QuizStepDogDetails = class extends QuizStepDogSubstep {

  constructor() {
    super();
    if (Shopify.designMode) {
      // this.rootDelegate.on("shopify:section:deselect", (event) => filterShopifyEvent(event, this, () => this.open = false));
    }
  }

  connectedCallback() {

  }

}

var QuizStepDogGeneral = class extends QuizStepDogSubstep {

  constructor() {
    super();
    if (Shopify.designMode) {
      // this.rootDelegate.on("shopify:section:deselect", (event) => filterShopifyEvent(event, this, () => this.open = false));
    }
  }

  connectedCallback() {

  }

}

var QuizStepDogActivity = class extends QuizStepDogSubstep {

  constructor() {
    super();
    if (Shopify.designMode) {
      // this.rootDelegate.on("shopify:section:deselect", (event) => filterShopifyEvent(event, this, () => this.open = false));
    }
  }

  connectedCallback() {

  }

}

var QuizStepDogWeightProfile = class extends QuizStepDogSubstep {

  constructor() {
    super();
    if (Shopify.designMode) {
      // this.rootDelegate.on("shopify:section:deselect", (event) => filterShopifyEvent(event, this, () => this.open = false));
    }
  }

  connectedCallback() {

  }

}

var QuizStepDogWeightDetails = class extends QuizStepDogSubstep {

  constructor() {
    super();
    if (Shopify.designMode) {
      // this.rootDelegate.on("shopify:section:deselect", (event) => filterShopifyEvent(event, this, () => this.open = false));
    }
  }

  connectedCallback() {

  }

}

var QuizStepDogHealth = class extends QuizStepDogSubstep {

  constructor() {
    super();
    if (Shopify.designMode) {
      // this.rootDelegate.on("shopify:section:deselect", (event) => filterShopifyEvent(event, this, () => this.open = false));
    }
  }

  connectedCallback() {

  }

}

var StyledSelect = class extends CustomHTMLElement {

  constructor() {
    super();
    if (Shopify.designMode) {
      // this.rootDelegate.on("shopify:section:deselect", (event) => filterShopifyEvent(event, this, () => this.open = false));
    }
  }

  connectedCallback() {

    this.select = this.querySelector("select");

    // console.log(this.select);

    let options = {};

    options.searchable = this.hasAttribute("searchable") ? true : false;

    this.niceselect = NiceSelect.bind(this.select, options);

  }

  update() {
    this.niceselect.update();
  }

}

window.customElements.define("quiz-step-dog-number", QuizStepDogNumber);

window.customElements.define("quiz-step-dog-substep", QuizStepDogSubstep);
window.customElements.define("quiz-step-dog-details", QuizStepDogDetails);

window.customElements.define("quiz-step-dog-general", QuizStepDogGeneral);
window.customElements.define("quiz-step-dog-activity", QuizStepDogActivity);
window.customElements.define("quiz-step-dog-weight-profile", QuizStepDogWeightProfile);
window.customElements.define("quiz-step-dog-weight-details", QuizStepDogWeightDetails);
window.customElements.define("quiz-step-dog-health", QuizStepDogHealth);

document.addEventListener("DOMContentLoaded", (e) => {
  window.customElements.define("styled-select", StyledSelect);
});

// js/components/input-binding-manager.js
var InvisibleInputBindingManager = class {

  constructor() {

    this.delegateElement = new main_default(document.body);
    this.delegateElement.on("input", "[data-bound-hidden-input]", this._onValueChanged.bind(this));
    this.delegateElement.on("change", "[data-bound-hidden-input]", this._onChanged.bind(this));

  }

  _onValueChanged(event, target) {

    const sourceInput = event.target;
    const boundElement = document.getElementById(target.getAttribute("data-bound-hidden-input"));

    if (boundElement) {
      if (target.tagName === "SELECT") {
        target = target.options[target.selectedIndex];
      }
      boundElement.value = target.hasAttribute("title") ? target.getAttribute("title") : target.value;
    }

  }

  _onChanged(event, target) {

    const sourceInput = event.target;
    const boundElement = document.getElementById(target.getAttribute("data-bound-hidden-radio"));

    if (boundElement) {
      if (target.tagName === "SELECT") {
        target = target.options[target.selectedIndex];
      }
      boundElement.value = target.hasAttribute("title") ? target.getAttribute("title") : target.value;
    }

  }

};

var FullNameBindingManager = class {

  constructor() {

    this.delegateElement = new main_default(document.body);
    this.delegateElement.on("input", "[data-bound-split-name-input]", this._onValueChanged.bind(this));

  }

  _onValueChanged(event, target) {

    const boundElementFirstName = document.getElementById(target.getAttribute("data-bound-first-name-input"));
    const boundElementLastName = document.getElementById(target.getAttribute("data-bound-last-name-input"));

    const value = target.value;

    let splitName = value.split(" ");
    let firstName;
    let lastName;

    if (value.indexOf(" ") != -1) {
      firstName = splitName[0];
      lastName = splitName[splitName.length - 1] ? splitName[splitName.length - 1] : "";
    }
    else {
      firstName = value;
      lastName = "";
    }

    if (boundElementFirstName) {
      boundElementFirstName.setAttribute("value", firstName);
    }

    if (boundElementLastName) {
      boundElementLastName.setAttribute("value", lastName);
    }

  }

};


(() => {
  new InvisibleInputBindingManager();
  new FullNameBindingManager();
})();

/*

const kycOptions = document.querySelectorAll('.kyc-option-radio');

kycOptions.forEach(element => {
  
  element.addEventListener("change", event => {

    // Hide all other sub-options
    document.querySelectorAll(".input--sub-option").forEach(e => {
      e.value = "";
      e.style = "display: none;";
    });

    // Show current selection's sub-options
    const subOptionContainer = event.target.closest(".input").nextElementSibling;
    const subOptionInput = subOptionContainer.querySelector("[data-sub-option-input]");
    const hiddenNoteInput = document.querySelector("[data-hidden-note-input]");
    
    if (subOptionContainer.classList.contains("input--sub-option")) {
      subOptionContainer.style = "display: block;";
      hiddenNoteInput.setAttribute("name", "");

      var event = new Event('input', {
        bubbles: true,
        cancelable: true,
      });

      subOptionInput.dispatchEvent(event);

    }
    else {
      hiddenNoteInput.removeAttribute("name");
      hiddenNoteInput.removeAttribute("value");
    }

  });

});

*/


const inputsWithSubOptions = document.querySelectorAll('[data-sub-option-container]');

inputsWithSubOptions.forEach(element => {

  element.addEventListener("change", event => {

    const input = event.target;
    const subOptionContainerId = input.getAttribute('data-sub-option-container')
    let subOptionContainer;

    if (subOptionContainerId) {
      subOptionContainer = document.getElementById(subOptionContainerId)
    }

    hideAllSubOptions();

    if (!subOptionContainer) {
      return;
    }

    subOptionContainer.style = "display: block;";

  });

});

function hideAllSubOptions() {

  // These are inputs, not containers.
  document.querySelectorAll("[data-sub-option-container]").forEach(e => {

    const subOptionContainerId = e.getAttribute('data-sub-option-container')
    const subOptionContainer = document.getElementById(subOptionContainerId); // This is the container.

    if (!subOptionContainer) {
      return;
    }

    subOptionContainer.value = "";
    subOptionContainer.style = "display: none;";

    const subOptionInputs = subOptionContainer.querySelectorAll("[data-sub-option-input]");

    if (subOptionInputs.length > 0) {
      subOptionInputs.forEach(subOptionInput => {

        subOptionInput.value = ""; // Clears the visible form input.
        subOptionInput.dispatchEvent(new Event('change')); // Clears the hidden form input.

        if (subOptionInput.closest("styled-select")) {
          subOptionInput.closest("styled-select").update(); // Updates the styled select.
        }

      });
    }

  });

}

const inputSubOptions = document.querySelectorAll('[data-sub-option-input]');

inputSubOptions.forEach(element => {

  element.addEventListener("change", event => {

    const visibleNoteInput = event.target;
    const hiddenNoteInput = document.querySelector("[data-hidden-note-input]");

    let newValue = visibleNoteInput.value.trim() == "" ? visibleNoteInput.value.trim() : visibleNoteInput.value.trim() + ",";

    const revealValue = element.getAttribute("data-sub-option-input-reveal-value");
    const revealInput = document.getElementById(element.getAttribute("data-sub-option-input-reveal-input"));

    if (revealValue && revealInput) {
      if (element.value == revealValue) {
        revealInput.classList.remove("hidden");
        revealInput.querySelector("input").focus();
      }
      else {
        revealInput.classList.add("hidden");
      }
    }

    hiddenNoteInput.setAttribute("name", visibleNoteInput.getAttribute("name"));
    hiddenNoteInput.setAttribute("value", newValue);

  });

});