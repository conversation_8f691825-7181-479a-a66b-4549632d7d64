/* File: gulpfile.js */

/* ========== Paths ========== */

const devmode = true;

const projectName = "wynwood",

      sourceLiquid = './**/*.liquid',
      sourceSCSSQuiz = './scss/quiz/**/*.{scss,sass}',
      sourceSCSSAccount = './scss/account/**/*.{scss,sass}',
      sourceSCSSCustom = './scss/custom/**/*.{scss,sass}',

      sourceJSQuiz = './js/quiz/**/*.js',
      sourceJSQuizAccount = './js/quiz-account/**/*.js',

      sourceJSVendor =
        [
          './js/vendor/**/*.js'
        ],

      destinationJS = './../assets',
      destinationCSS = './../assets',

      filenameCSSQuiz = 'quiz.css',
      filenameCSSQuizMin = 'quiz.min.css',
      filenameCSSAccount = 'quiz-account.css',
      filenameCSSAccountMin = 'quiz-account.min.css',
      filenameCSSCustom = 'custom.css',
      filenameCSSCustomMin = 'custom.min.css',

      filenameJSVendor = 'vendor.js',
      filenameJSVendorMin = 'vendor.min.js';
      filenameJSQuiz = 'quiz.js',
      filenameJSQuizMin = 'quiz.min.js';
      filenameJSQuizAccount = 'quiz-account.js',
      filenameJSQuizAccountMin = 'quiz-account.min.js';


/* ========== Modules ========== */

const gulp = require('gulp'),
      { parallel } = require('gulp'),
      concat = require("gulp-concat"),
      replace = require("gulp-replace"),
      postcss = require("gulp-postcss"),
      autoprefixer = require("autoprefixer"),
      cssnano = require("cssnano"),
      sourcemaps = require("gulp-sourcemaps"),
      sass = require('gulp-sass')(require('sass')),
      uglify = require('gulp-uglify'),
      gulpif = require('gulp-if');


/* ========== SCSS ========== */

// Styles

function styleCustom() {

  if (devmode == true) {

    return gulp.src(sourceSCSSCustom)
      .pipe(sourcemaps.init()) // Registers sourcemaps
      .pipe(sass()).on("error", sass.logError) // Logs SCSS errors succinctly to terminal.
      .pipe(postcss([autoprefixer()])) // Runs compiled CSS through PostCSS for post-processing.
      .pipe(concat(filenameCSSCustom))
      .pipe(gulp.dest(destinationCSS))
      .pipe(concat(filenameCSSCustomMin))
      .pipe(postcss([cssnano()])) // Runs compiled CSS through PostCSS for post-processing.
      .pipe(sourcemaps.write('./')) // Generates sourcemaps for in-browser debugging
      .pipe(gulp.dest(destinationCSS))
      ;

  }
  else {

    return gulp.src(sourceSCSSCustom)
      .pipe(sass()).on("error", sass.logError) // Logs SCSS errors succinctly to terminal.
      .pipe(postcss([autoprefixer(), cssnano()])) // Runs compiled CSS through PostCSS for post-processing.
      .pipe(concat(filenameCSSCustomMin)) // Changes the file to .liquid so Liquid tags can understand it.
      .pipe(gulp.dest(destinationCSS))
      ;

  }

}

function styleQuiz() {

  if (devmode == true) {

    return gulp.src(sourceSCSSQuiz)
      .pipe(sourcemaps.init()) // Registers sourcemaps
      .pipe(sass()).on("error", sass.logError) // Logs SCSS errors succinctly to terminal.
      .pipe(postcss([autoprefixer()])) // Runs compiled CSS through PostCSS for post-processing.
      .pipe(concat(filenameCSSQuiz))
      .pipe(gulp.dest(destinationCSS))
      .pipe(concat(filenameCSSQuizMin))
      .pipe(postcss([cssnano()])) // Runs compiled CSS through PostCSS for post-processing.
      .pipe(sourcemaps.write('./')) // Generates sourcemaps for in-browser debugging
      .pipe(gulp.dest(destinationCSS))
      ;

  }
  else {

    return gulp.src(sourceSCSSQuiz)
      .pipe(sass()).on("error", sass.logError) // Logs SCSS errors succinctly to terminal.
      .pipe(postcss([autoprefixer(), cssnano()])) // Runs compiled CSS through PostCSS for post-processing.
      .pipe(concat(filenameCSSQuizMin)) // Changes the file to .liquid so Liquid tags can understand it.
      .pipe(gulp.dest(destinationCSS))
      ;

  }

}

function styleAccount() {

  if (devmode == true) {

    return gulp.src(sourceSCSSAccount)
      .pipe(sourcemaps.init()) // Registers sourcemaps
      .pipe(sass()).on("error", sass.logError) // Logs SCSS errors succinctly to terminal.
      .pipe(postcss([autoprefixer()])) // Runs compiled CSS through PostCSS for post-processing.
      .pipe(concat(filenameCSSAccount))
      .pipe(gulp.dest(destinationCSS))
      .pipe(concat(filenameCSSAccountMin))
      .pipe(postcss([cssnano()])) // Runs compiled CSS through PostCSS for post-processing.
      .pipe(sourcemaps.write('./')) // Generates sourcemaps for in-browser debugging
      .pipe(gulp.dest(destinationCSS))
      ;

  }
  else {

    return gulp.src(sourceSCSSAccount)
      .pipe(sass()).on("error", sass.logError) // Logs SCSS errors succinctly to terminal.
      .pipe(postcss([autoprefixer(), cssnano()])) // Runs compiled CSS through PostCSS for post-processing.
      .pipe(concat(filenameCSSAccountMin)) // Changes the file to .liquid so Liquid tags can understand it.
      .pipe(gulp.dest(destinationCSS))
      ;

  }

}

function scriptsQuiz() {

  if (devmode == true) {

    return gulp.src(sourceJSQuiz)
      // .pipe(sourcemaps.init()) // Registers sourcemaps
      .pipe(concat(filenameJSQuiz))
      // .pipe(gulpif('*.js', uglify()))
      // .pipe(sourcemaps.write('./')) // Generates sourcemaps for in-browser debugging
      .pipe(gulp.dest(destinationJS));

  }
  else {

    return gulp.src(sourceJSQuiz)
      .pipe(concat(filenameJSQuiz))
      .pipe(gulp.dest(destinationJS));

  }

}

function scriptsQuizAccount() {

  console.log("scriptsQuizAccount");

  if (devmode == false) {

    return gulp.src(sourceJSQuizAccount)
      .pipe(sourcemaps.init()) // Registers sourcemaps
      .pipe(concat(filenameJSQuizAccount))
      .pipe(gulpif('*.js', uglify()))
      .pipe(sourcemaps.write('./')) // Generates sourcemaps for in-browser debugging
      .pipe(gulp.dest(destinationJS));

  }
  else {

    return gulp.src(sourceJSQuizAccount)
      .pipe(concat(filenameJSQuizAccount))
      .pipe(gulp.dest(destinationJS));

  }

}

function scriptsVendor() {

  if (devmode == true) {

    return gulp.src(sourceJSVendor)
      .pipe(sourcemaps.init()) // Registers sourcemaps
      .pipe(concat(filenameJSVendor))
      .pipe(gulp.dest(destinationJS))
      .pipe(gulpif('*.js', uglify()))
      .pipe(concat(filenameJSVendorMin))
      .pipe(sourcemaps.write('./')) // Generates sourcemaps for in-browser debugging
      .pipe(gulp.dest(destinationJS));

  }
  else {

    return gulp.src(sourceJSVendor)
      .pipe(concat(filenameJSVendorMin))
      .pipe(gulpif('*.js', uglify()))
      .pipe(gulp.dest(destinationJS));

  }

}

/* ========== Watch ========== */

function watch() {

  gulp.watch(sourceSCSSQuiz, styleQuiz);
  gulp.watch(sourceSCSSAccount, styleAccount);
  gulp.watch(sourceSCSSCustom, styleCustom);

  gulp.watch(sourceJSQuiz, scriptsQuiz);
  gulp.watch(sourceJSQuizAccount, scriptsQuizAccount);
  
  gulp.watch(sourceJSVendor, scriptsVendor);

}

exports.watch = watch;
exports.scriptsVendor = scriptsVendor;

exports.build = parallel(

  styleQuiz,
  styleCustom,
  styleAccount,

  scriptsVendor,
  scriptsQuizAccount,
  scriptsQuiz

);

exports.default = watch;
