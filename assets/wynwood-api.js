console.log("WYNWOOD API");

const API_CONFIG = {

  STOREFRONT: {

    CONFIG: function () {
      return {
        URL: `https://${Shopify.shop}/api/2023-01/graphql.json`,
        ACCESS_TOKEN: '1cb123225badac915eecc29ce09149a1'
      };
    }

  }

};

var wynwood_api = {

  async call(graphql_query) {

    const url = API_CONFIG.STOREFRONT.CONFIG().URL;

    const body = {
      'async': true,
      'crossDomain': true,
      'method': 'POST',
      'headers': {
        'X-Shopify-Storefront-Access-Token': API_CONFIG.STOREFRONT.CONFIG().ACCESS_TOKEN,
        'Content-Type': 'application/graphql',
      },
      'body': graphql_query
    }

    const response = await fetch(url, body)
      .then(response => response.json())
      .then(payload => {
        return payload;
      });

    console.log(response);

    return response;

  }

};

const w_api = {

  async test() {

    wynwood_api.call(`
      {
        shop {
          name
        }
      }
    `);

  },

  async test2() {

    const data = JSON.stringify({
      query: `query GetPost($id: ID!) {
        post (id: $id) {
          id
          title
        }
      }`,
      variables: {
        id
      }
    });

    wynwood_api.call(data);

  },

  async customerCreate() {

    const data = `
      mutation {
        customerCreate(
          input: {
            firstName: "_API",
            lastName: "Test",
            email: "<EMAIL>",
            password: "qtestapi"
          }
        ){
          customer {
            id
            firstName
            lastName
            email
          }
          userErrors {
            field
            message
          }
          customer {
            id
          }
        }
      }
    `;

    console.log(data);

    wynwood_api.call(data);

  },

  async customerCreate2() {

    const data = JSON.stringify({
      query: `
        customerCreate(input: $input) {
          customerUserErrors {
            code
            field
            message
          }
          customer {
            id
          }
        }
      }`,
      variables: {
        "input": {
          "email": "<EMAIL>",
          "password": "HiZqFuDvDdQ7"
        }
      }
    });

    console.log(data);

    wynwood_api.call(data);

  },
  
  async template() {

    const data = JSON.stringify({
      query: `query GetPost($id: ID!) {
        post (id: $id) {
          id
          title
        }
      }`,
      variables: {
        id
      }
    });

    wynwood_api.call(data);

  }
  
};