(() => {

  async function postQuizData() {

    const dogData = JSON.parse(localStorage.getItem('dogData'));
    const vetInfo = captureUnlistedVetInfo();

    if (!dogData) {
      location.href = window.quizVariables.locations.results;
    }

    let flock = [];
    let field_names_stubs = [
      "name",
      "sex",
      "age_in_months",
      "breed",
      "neutered",
      "weight_profile",
      "weight",
      "ideal_weight",
      "activity_level",
      "has_health_issue",
      "prescription_diet"
    ];

    for (let d = 0; d < dogData.dogs.length; d++) {

      const dog = dogData.dogs[d];
      const dog_name_stub = `dog_${d + 1}_`

      for (let f = 0; f < field_names_stubs.length; f++) {

        const field_stub = field_names_stubs[f];

        let value = sanitiseInput(dog[field_stub]);

        const field_name = dog_name_stub + field_stub;
        const field_raven_data = Object.assign({}, FieldsRaven.definitions[field_name], { value: value });

        flock.push(field_raven_data);
        
      }
      
    }

    let requestParams = {
      flock: flock
    };

    if (vetInfo.contactDetails) {
      requestParams.flock.push(Object.assign({}, FieldsRaven.definitions.customer_veterinarian_rec, { value: vetInfo.vetName }));
      // requestParams.flock.push(Object.assign({}, FieldsRaven.definitions.customer_veterinarian_rec, { value: vetInfo.vetName }, { customer_email: customer_email }));
    }

    if (vetInfo.contactDetails) {
      requestParams.flock.push(Object.assign({}, FieldsRaven.definitions.unlisted_vet_contact_details, { value: vetInfo.contactDetails }));
      // requestParams.flock.push(Object.assign({}, FieldsRaven.definitions.unlisted_vet_contact_details, { value: vetInfo.contactDetails }, { customer_email: customer_email }));
    }

    if (vetInfo.hospitalName) {
      requestParams.flock.push(Object.assign({}, FieldsRaven.definitions.unlisted_vet_hospital_name, { value: vetInfo.hospitalName }));
      // requestParams.flock.push(Object.assign({}, FieldsRaven.definitions.unlisted_vet_hospital_name, { value: vetInfo.hospitalName }, { customer_email: customer_email }));
    }

    if (vetInfo.vetName) {
      requestParams.flock.push(Object.assign({}, FieldsRaven.definitions.unlisted_vet_vet_name, { value: vetInfo.vetName }));
      // requestParams.flock.push(Object.assign({}, FieldsRaven.definitions.unlisted_vet_vet_name, { value: vetInfo.vetName }, { customer_email: customer_email }));
    }

    if (dogData.customer) {
      if (dogData.customer.email) {
        requestParams.customer_email = dogData.customer.email;
      }
    }

    const response = fetch(FieldsRaven.endpoints.create_multiple, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(requestParams)
    });

    response
      .then(res => res.json())
      .then(resJson => {
        location.href = window.quizVariables.locations.results;
      })
      .finally(response => {
        console.log(response);
      });

  }

  postQuizData();

  function sanitiseInput(value) {
    
    if (typeof value == 'boolean') {
      if (value == true) {
        value = "true"
      }
      else {
        value = "false"
      }
    }

    return value;

  }

  function captureUnlistedVetInfo() {

    const storedUnlistedVetInfo = localStorage.getItem('unlistedVetInfo');

    let vetInfo = {};

    // Parse info from local storage
    if (storedUnlistedVetInfo) {

      try {
        vetInfo = JSON.parse(storedUnlistedVetInfo);
      }
      catch {}

    }

    return vetInfo;

  }
    
})();

console.log("Quiz Post Data Script");