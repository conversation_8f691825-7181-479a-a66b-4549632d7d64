/* =============== API Details =============== */

const CONFIG = {

  STOREFRONT: {
    VERSION: "2025-01"
  }

};

const API = {

  STOREFRONT: {

    CONFIG: function () {
      return {
        API_VERSION: CONFIG.STOREFRONT.VERSION,
        URL: `https://${Shopify.shop}/api/${CONFIG.STOREFRONT.VERSION}/graphql.json`,
        ACCESS_TOKEN: '1cb123225badac915eecc29ce09149a1'
      };
    }

  }

}

const defaultDogData = {

  dogs: [
    {
      "name": "Dog One",
      "sex": "Male",
      "neutered": false,
      "breed": "Mixed",
      "weight": 20,
      "ideal_weight": 20,
      "has_health_issue": false,
      "prescription_diet": "",
      "weight_profile": "Ideal",
      "activity_level": "Normal",
      "age_in_months": 3
    },
    {
      "name": "Dog Two",
      "sex": "Female",
      "neutered": true,
      "breed": "Affenpinscher",
      "weight": 40,
      "ideal_weight": 50,
      "has_health_issue": false,
      "prescription_diet": "Cancer Related",
      "weight_profile": "Underweight",
      "activity_level": "Low",
      "age_in_months": 120
    }
  ]

};



/* =============== Utilities =============== */

const uuid = function () {

  return ([1e7] + -1e3 + -4e3 + -8e3 + -1e11).replace(/[018]/g, c =>
    (c ^ crypto.getRandomValues(new Uint8Array(1))[0] & 15 >> c / 4).toString(16)
  );

}

const stripNewlines = function (string) {

  return string.replace(/(\r\n|\n|\r)/gm, "");

}

const roundUpToQuarter = function (number) {

  const value = (Math.round(number * 4) / 4).toFixed(2);

  return Number(value);

}

const lbsToKg = function (lbs) {

  if (!lbs) {
    return;
  }

  return (lbs / 2.2);

}

function titlecase(str) {
  if (!str || typeof str != "string") {
    return ""
  }
  return str.toLowerCase().split(' ').map(function (word) {
     return word.charAt(0).toUpperCase().concat(word.substr(1));
  }).join(' ');
}

const fractionQuarters = function (number) {

  let n = parseInt(number);

  let decimal = number - n;
  let d = roundUpToQuarter(decimal);
  let dFraction = new Fraction(d);

  let returnValue = "";

  let roundedFractionIsWhole = false;

  if (d == 1) {
    n++;
  }

  if (n > 0) {
    returnValue += n.toString();
  }

  if (d > 0 && d != 1) {
    returnValue += `<span class="fraction">${dFraction.numerator}/${dFraction.denominator}</span>`
  }

  return returnValue;

}

const roundCups = function (cups) {

}

const roundOunces = function (ounces) {

  return ounces;

}

const ouncesToCups = function (ounces) {

  if (typeof ounces != "number") {
    return;
  }

  return ounces / 8;

}


/* =============== Quiz + Calorie Calculations =============== */


const calculateAgeFactor = function (age_in_months) {

  let ageFactor;

  if (age_in_months < 3) {
    ageFactor = 2;
  }
  else
  if (age_in_months < 6) {
    ageFactor = 1.5;
  }
  else
  if (age_in_months < 12) {
    ageFactor = 1.25;
  }
  else
  if (age_in_months < 84) {
    ageFactor = 1;
  } 
  else {
    ageFactor = 0.9;
  }

  return ageFactor;

}

const calculateWeightFactor = function (weightInLbs) {

  if (!weightInLbs) {
    return;
  }

  let weightInKgs = lbsToKg(weightInLbs);
  let weightFactor = Math.pow(weightInKgs, .75);

  return weightFactor;

}

calculateDogActivityFactor = function (activity_level) {

  let activityFactor;

  if (activity_level == "Low") {
    activityFactor = 1.3;
  }
  else
  if (activity_level == "Normal") {
    activityFactor = 1.3;
  }
  else
  if (activity_level == "High") {
    activityFactor = 1.4;
  } 
  else {
    activityFactor = 1.3;
  }

  return activityFactor;

}

const calculateKCalPerDay = function(dog) {

  const factorAge = !dog.age_in_months ? dog.age_factor : calculateAgeFactor(dog.age_in_months); // If age_in_months not supplied, assume age_factor is present and use that.
  const factorWeight = calculateWeightFactor(dog.weight);
  const factorActivity = calculateDogActivityFactor(dog.activity_level);
  const factorNeuter = dog.neutered == true ? 1 : 1.125;
  const factorFreshDogFood = 0.8;
  const caloriesPerDay = 70 * factorWeight * factorNeuter * factorActivity * factorAge * factorFreshDogFood;

  // console.log(`70 * ${factorWeight} * ${factorNeuter} * ${factorActivity} * ${factorAge} * ${factorFreshDogFood}`);
  // console.log(caloriesPerDay);

  return caloriesPerDay;

}

const calculateTransitionCalories = function (dog) {

  let transitionCalories = 0;

  const caloriesPerDay = calculateKCalPerDay(dog);
  const days = window.quizVariables?.boxes?.days_in_box;

  transitionCalories += (days * caloriesPerDay * 1); // 7 Equal Days
  transitionCalories = Math.ceil(transitionCalories);

  return transitionCalories;

}

const calculatePacketQuantity = function (number) {

  /*
  
    This function takes the number of 16Oz packets from a calorie calculation
    and rounds them.

    - If the decimal under 0.2 (e.g. 1.2 16Oz packets), round that down (to 1x 16Oz packet)
    - If the decimal over 0.2 (e.g. 1.3 16Oz packets), round that up (to 2x 16Oz packets)
  
    number: 
      Quantity of 16Oz packets from a calorie calculation.
      
  */

  const decimal = number - Math.floor(number);
  const quantity = decimal > 0.2 ? Math.ceil(number) : Math.floor(number);

  return quantity;

}

const asyncTimeout = (ms) => {
  return new Promise((resolve) => {
    setTimeout(resolve, ms);
  });
};