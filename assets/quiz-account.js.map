{"version": 3, "sources": ["quiz-account.js"], "names": ["Object", "defineProperty", "Delegate", "root", "this", "listenerMap", "handle", "prototype", "bind", "_removedListeners", "matchesTag", "tagName", "element", "toLowerCase", "matchesId", "id", "let", "eventType", "rootElement", "hasOwnProperty", "removeEventListener", "addEventListener", "captureForType", "indexOf", "on", "selector", "handler", "useCapture", "matcher", "matcher<PERSON><PERSON><PERSON>", "TypeError", "test", "slice", "Element", "matches", "window", "document", "documentElement", "push", "off", "i", "listener", "listenerList", "singleEventType", "length", "splice", "event", "l", "type", "target", "eventIgnore", "nodeType", "parentNode", "correspondingUseElement", "eventPhase", "currentTarget", "concat", "toFire", "hasAttribute", "call", "parentElement", "HTMLDocument", "ret", "fire", "apply", "preventDefault", "destroy", "main_default", "triggerEvent", "name", "data", "dispatchEvent", "CustomEvent", "bubbles", "detail", "CustomHTMLElement", "HTMLElement", "constructor", "super", "_hasSectionReloaded", "Shopify", "designMode", "rootDelegate", "parentSection", "closest", "load", "_rootDelegate", "delegate", "_delegate", "showLoadingBar", "hideLoadingBar", "untilVisible", "intersectionObserverOptions", "rootMargin", "threshold", "onBecameVisible", "classList", "add", "style", "opacity", "Promise", "IntersectionObserver", "intersectionObserver", "isIntersecting", "disconnect", "requestAnimationFrame", "resolve", "observe", "disconnectedCallback", "_a", "msMatchesSelector", "webkitMatchesSelector", "trapQueue", "CustomAnimation", "effect", "_effect", "_playState", "_finished", "finished", "animationEffects", "CustomKeyframeEffect", "cancel", "for<PERSON>ach", "animationEffect", "finish", "play", "then", "keyframes", "options", "_animation", "Animation", "KeyframeEffect", "animate", "pause", "visibility", "onfinish", "startTime", "GroupEffect", "childrenEffects", "_childrenEffects", "flatMap", "ParallelEffect", "promises", "all", "customElements", "define", "observedAttributes", "connectedCallback", "console", "log", "buttonsNext", "querySelectorAll", "buttonsPrev", "buttonsBack", "button", "gotoNextStep", "gotoPrevStep", "currentStep", "nextStep", "animation", "transform", "duration", "easing", "await", "remove", "prevStep", "querySelector", "sibling", "nextElement<PERSON><PERSON>ling", "previousElementSibling"], "mappings": "CAAA,KACAA,OAAAC,eAQA,SAAAC,EAAAC,GACAC,KAAAC,YAAA,CAAA,GAAA,IACAF,GACAC,KAAAD,KAAAA,CAAA,EAEAC,KAAAE,OAAAJ,EAAAK,UAAAD,OAAAE,KAAAJ,IAAA,EACAA,KAAAK,kBAAA,EACA,CAkNA,SAAAC,EAAAC,EAAAC,GACA,OAAAD,EAAAE,YAAA,IAAAD,EAAAD,QAAAE,YAAA,CACA,CAOA,SAAAC,EAAAC,EAAAH,GACA,OAAAG,IAAAH,EAAAG,EACA,CA5NAb,EAAAK,UAAAJ,KAAA,SAAAA,GACA,IAAAE,EAAAD,KAAAC,YACAW,IAAAC,EACA,GAAAb,KAAAc,YAAA,CACA,IAAAD,KAAAZ,EAAA,GACAA,EAAA,GAAAc,eAAAF,CAAA,GACAb,KAAAc,YAAAE,oBAAAH,EAAAb,KAAAE,OAAA,CAAA,CAAA,EAGA,IAAAW,KAAAZ,EAAA,GACAA,EAAA,GAAAc,eAAAF,CAAA,GACAb,KAAAc,YAAAE,oBAAAH,EAAAb,KAAAE,OAAA,CAAA,CAAA,CAGA,CACA,GAAAH,GAAAA,EAAAkB,iBAAA,CAOA,IAAAJ,KADAb,KAAAc,YAAAf,EACAE,EAAA,GACAA,EAAA,GAAAc,eAAAF,CAAA,GACAb,KAAAc,YAAAG,iBAAAJ,EAAAb,KAAAE,OAAA,CAAA,CAAA,EAGA,IAAAW,KAAAZ,EAAA,GACAA,EAAA,GAAAc,eAAAF,CAAA,GACAb,KAAAc,YAAAG,iBAAAJ,EAAAb,KAAAE,OAAA,CAAA,CAAA,CATA,MAJAF,KAAAc,aACA,OAAAd,KAAAc,YAeA,OAAAd,IACA,EACAF,EAAAK,UAAAe,eAAA,SAAAL,GACA,MAAA,CAAA,IAAA,CAAA,OAAA,QAAA,QAAA,OAAA,SAAA,UAAAM,QAAAN,CAAA,CACA,EACAf,EAAAK,UAAAiB,GAAA,SAAAP,EAAAQ,EAAAC,EAAAC,GACAX,IAAAb,EACAE,EACAW,IAAAY,EACAC,EACA,GAAA,CAAAZ,EACA,MAAA,IAAAa,UAAA,uBAAAb,CAAA,EAUA,GARA,YAAA,OAAAQ,IACAE,EAAAD,EACAA,EAAAD,EACAA,EAAA,MAEA,KAAA,IAAAE,IACAA,EAAAvB,KAAAkB,eAAAL,CAAA,GAEA,YAAA,OAAAS,EACA,MAAA,IAAAI,UAAA,oCAAA,EA6BA,OA3BA3B,EAAAC,KAAAc,aACAb,EAAAD,KAAAC,YAAAsB,EAAA,EAAA,IACAV,KACAd,GACAA,EAAAkB,iBAAAJ,EAAAb,KAAAE,OAAAqB,CAAA,EAEAtB,EAAAY,GAAA,IAOAW,EALAH,EAGA,YAAAM,KAAAN,CAAA,GACAI,EAAAJ,EACAf,GACA,mBAAAqB,KAAAN,CAAA,GACAI,EAAAJ,EAAAO,MAAA,CAAA,EACAlB,IAEAe,EAAAJ,EACAQ,QAAA1B,UAAA2B,UAVAL,EAAA,KAmJA,SAAAJ,EAAAb,GACA,OAAAR,KAAAc,cAAAiB,OAGA/B,KAAAc,cAAAN,EAFAA,IAAAwB,UAAAxB,IAAAwB,SAAAC,iBAAAzB,IAAAuB,MAGA,EAvJA3B,KAAAJ,IAAA,GAWAC,EAAAY,GAAAqB,KAAA,CACAb,SAAAA,EACAC,QAAAA,EACAE,QAAAA,EACAC,aAAAA,CACA,CAAA,EACAzB,IACA,EACAF,EAAAK,UAAAgC,IAAA,SAAAtB,EAAAQ,EAAAC,EAAAC,GACAX,IAAAwB,EACAxB,IAAAyB,EACApC,EACAqC,EACA1B,IAAA2B,EAMA,GALA,YAAA,OAAAlB,IACAE,EAAAD,EACAA,EAAAD,EACAA,EAAA,MAEA,KAAA,IAAAE,EACAvB,KAAAmC,IAAAtB,EAAAQ,EAAAC,EAAA,CAAA,CAAA,EACAtB,KAAAmC,IAAAtB,EAAAQ,EAAAC,EAAA,CAAA,CAAA,OAIA,GADArB,EAAAD,KAAAC,YAAAsB,EAAA,EAAA,GACAV,GASA,IADAyB,EAAArC,EAAAY,KACAyB,EAAAE,OAAA,CAGA,IAAAJ,EAAAE,EAAAE,OAAA,EAAA,GAAAJ,EAAAA,CAAA,GACAC,EAAAC,EAAAF,GACAf,GAAAA,IAAAgB,EAAAhB,UAAAC,GAAAA,IAAAe,EAAAf,UACAtB,KAAAK,kBAAA6B,KAAAG,CAAA,EACAC,EAAAG,OAAAL,EAAA,CAAA,GAGAE,EAAAE,SACA,OAAAvC,EAAAY,GACAb,KAAAc,aACAd,KAAAc,YAAAE,oBAAAH,EAAAb,KAAAE,OAAAqB,CAAA,EAXA,CAAA,MAVA,IAAAgB,KAAAtC,EACAA,EAAAc,eAAAwB,CAAA,GACAvC,KAAAmC,IAAAI,EAAAlB,EAAAC,CAAA,EAsBA,OAAAtB,IACA,EACAF,EAAAK,UAAAD,OAAA,SAAAwC,GACA9B,IAAAwB,EACAO,EACA,IACA5C,EAEAsC,EAHAO,EAAAF,EAAAE,KAKAhC,IAAA0B,EAAA,GACAO,EACA,IAAAC,EAAA,uBACA,GAAA,CAAA,IAAAJ,EAAAI,GAAA,CAYA,QAPAD,EADA,KADAA,EAAAH,EAAAG,QACAE,SACAF,EAAAG,WAEAH,GAAAI,0BACAJ,EAAAA,EAAAI,yBAEAlD,EAAAC,KAAAc,YACA4B,EAAAQ,aAAAR,EAAAG,SAAAH,EAAAS,cAAA,EAAA,IAEA,KAAA,EACAb,EAAAtC,KAAAC,YAAA,GAAA2C,GACA,MACA,KAAA,EACA5C,KAAAC,YAAA,IAAAD,KAAAC,YAAA,GAAA2C,KACAN,EAAAA,EAAAc,OAAApD,KAAAC,YAAA,GAAA2C,EAAA,GAEA5C,KAAAC,YAAA,IAAAD,KAAAC,YAAA,GAAA2C,KACAN,EAAAA,EAAAc,OAAApD,KAAAC,YAAA,GAAA2C,EAAA,GAEA,MACA,KAAA,EACAN,EAAAtC,KAAAC,YAAA,GAAA2C,EAEA,CACAhC,IAAAyC,EAAA,GAEA,IADAV,EAAAL,EAAAE,OACAK,GAAAF,GAAA,CACA,IAAAP,EAAA,EAAAA,EAAAO,IACAN,EAAAC,EAAAF,IADAA,CAAA,GAKAS,EAAAtC,SAAA,CAAA,EAAA,CAAA,SAAA,QAAA,SAAA,YAAAY,QAAA0B,EAAAtC,QAAAE,YAAA,CAAA,GAAAoC,EAAAS,aAAA,UAAA,EACAD,EAAA,GACAhB,EAAAb,QAAA+B,KAAAV,EAAAR,EAAAZ,aAAAoB,CAAA,GACAQ,EAAAnB,KAAA,CAAAQ,EAAAG,EAAAR,EAAA,EAGA,GAAAQ,IAAA9C,EACA,MAIA,GAFA4C,EAAAL,EAAAE,QACAK,EAAAA,EAAAW,eAAAX,EAAAG,sBACAS,aACA,KAEA,CACA7C,IAAA8C,EACA,IAAAtB,EAAA,EAAAA,EAAAiB,EAAAb,OAAAJ,CAAA,GACA,GAAApC,EAAA,CAAA,EAAAA,KAAAK,kBAAAc,QAAAkC,EAAAjB,GAAA,EAAA,IAIA,CAAA,IADApC,KAAA2D,KAAAC,MAAA5D,KAAAqD,EAAAjB,EAAA,EACA,CACAiB,EAAAjB,GAAA,GAAAU,GAAA,CAAA,EACAO,EAAAjB,GAAA,GAAAyB,eAAA,EACAH,EAAA,CAAA,EACA,KACA,CAEA,OAAAA,CA9DA,CA+DA,EACA5D,EAAAK,UAAAwD,KAAA,SAAAjB,EAAAG,EAAAR,GACA,OAAAA,EAAAf,QAAAiC,KAAAV,EAAAH,EAAAG,CAAA,CACA,EAaA/C,EAAAK,UAAA2D,QAAA,WACA9D,KAAAmC,IAAA,EACAnC,KAAAD,KAAA,CACA,EACA,IAAAgE,EAAAjE,EAoBA,SAAAkE,EAAAxD,EAAAyD,EAAAC,EAAA,IACA1D,EAAA2D,cAAA,IAAAC,YAAAH,EAAA,CACAI,QAAA,CAAA,EACAC,OAAAJ,CACA,CAAA,CAAA,CACA,CASA,IAAAK,gBAAAC,YACAC,cACAC,MAAA,EACA1E,KAAA2E,oBAAA,CAAA,EACAC,QAAAC,YACA7E,KAAA8E,aAAA1D,GAAA,yBAAA,IACA,IAAA2D,EAAA/E,KAAAgF,QAAA,kBAAA,EACAtC,EAAAG,SAAAkC,GAAArC,EAAA4B,OAAAW,OACAjF,KAAA2E,oBAAA,CAAA,EAEA,CAAA,CAEA,CACAG,mBACA,OAAA9E,KAAAkF,cAAAlF,KAAAkF,eAAA,IAAAnB,EAAA/B,SAAAC,eAAA,CACA,CACAkD,eACA,OAAAnF,KAAAoF,UAAApF,KAAAoF,WAAA,IAAArB,EAAA/D,IAAA,CACA,CACAqF,iBACArB,EAAAhC,SAAAC,gBAAA,qBAAA,CACA,CACAqD,iBACAtB,EAAAhC,SAAAC,gBAAA,mBAAA,CACA,CACAsD,aAAAC,EAAA,CAAAC,WAAA,WAAAC,UAAA,CAAA,GACA,MAAAC,EAAA,KACA3F,KAAA4F,UAAAC,IAAA,gBAAA,EACA7F,KAAA8F,MAAAC,QAAA,GACA,EACA,OAAA,IAAAC,QAAA,IACAjE,OAAAkE,sBACAjG,KAAAkG,qBAAA,IAAAD,qBAAA,IACAvD,EAAA,GAAAyD,iBACAnG,KAAAkG,qBAAAE,WAAA,EACAC,sBAAA,KACAC,EAAA,EACAX,EAAA,CACA,CAAA,EAEA,EAAAH,CAAA,EACAxF,KAAAkG,qBAAAK,QAAAvG,IAAA,IAEAsG,EAAA,EACAX,EAAA,EAEA,CAAA,CACA,CACAa,uBACA,IAAAC,EACAzG,KAAAmF,SAAArB,QAAA,EACA9D,KAAA8E,aAAAhB,QAAA,EACA,OAAA2C,EAAAzG,KAAAkG,uBAAAO,EAAAL,WAAA,EACA,OAAApG,KAAAoF,UACA,OAAApF,KAAAkF,aACA,CACA,EAKA,aAAA,OAAArD,SACAA,QAAA1B,UAAA2B,SAAAD,QAAA1B,UAAAuG,mBAAA7E,QAAA1B,UAAAwG,sBAiNAC,EAAA,GADA,IACAA,EAyZAC,QACApC,YAAAqC,GACA9G,KAAA+G,QAAAD,EACA9G,KAAAgH,WAAA,OACAhH,KAAAiH,UAAAjB,QAAAM,QAAA,CACA,CACAY,eACA,OAAAlH,KAAAiH,SACA,CACAE,uBACA,OAAAnH,KAAA+G,mBAAAK,EAAA,CAAApH,KAAA+G,SAAA/G,KAAA+G,QAAAI,gBACA,CACAE,SACArH,KAAAmH,iBAAAG,QAAA,GAAAC,EAAAF,OAAA,CAAA,CACA,CACAG,SACAxH,KAAAmH,iBAAAG,QAAA,GAAAC,EAAAC,OAAA,CAAA,CACA,CACAC,OACAzH,KAAAgH,WAAA,UACAhH,KAAA+G,QAAAU,KAAA,EACAzH,KAAAiH,UAAAjH,KAAA+G,QAAAG,SACAlH,KAAAiH,UAAAS,KAAA,KACA1H,KAAAgH,WAAA,UACA,EAAA,IACAhH,KAAAgH,WAAA,MACA,CAAA,CACA,CACA,EACAI,QACA3C,YAAA5B,EAAA8E,EAAAC,EAAA,IACA/E,IAGA,cAAAd,OACA/B,KAAA6H,WAAA,IAAAC,UAAA,IAAAC,eAAAlF,EAAA8E,EAAAC,CAAA,CAAA,GAEAA,EAAA,KAAA,WACA5H,KAAA6H,WAAAhF,EAAAmF,QAAAL,EAAAC,CAAA,EACA5H,KAAA6H,WAAAI,MAAA,GAEAjI,KAAA6H,WAAA5G,iBAAA,SAAA,KACA4B,EAAAiD,MAAAC,QAAA4B,EAAA5G,eAAA,SAAA,EAAA4G,EAAA,QAAAA,EAAA,QAAAnF,OAAA,GAAA,KACAK,EAAAiD,MAAAoC,WAAAP,EAAA5G,eAAA,YAAA,EAAA4G,EAAA,WAAAA,EAAA,WAAAnF,OAAA,GAAA,IACA,CAAA,EACA,CACA0E,eACA,OAAAlH,KAAA6H,WAGA7H,KAAA6H,WAAAX,UAAA,IAAAlB,QAAA,GAAAhG,KAAA6H,WAAAM,SAAA7B,CAAA,EAFAN,QAAAM,QAAA,CAGA,CACAmB,OACAzH,KAAA6H,aACA7H,KAAA6H,WAAAO,UAAA,KACApI,KAAA6H,WAAAJ,KAAA,EAEA,CACAJ,SACArH,KAAA6H,YACA7H,KAAA6H,WAAAR,OAAA,CAEA,CACAG,SACAxH,KAAA6H,YACA7H,KAAA6H,WAAAL,OAAA,CAEA,CACA,EACAa,QACA5D,YAAA6D,GACAtI,KAAAuI,iBAAAD,EACAtI,KAAAiH,UAAAjB,QAAAM,QAAA,CACA,CACAY,eACA,OAAAlH,KAAAiH,SACA,CACAE,uBACA,OAAAnH,KAAAuI,iBAAAC,QAAA,GACA1B,aAAAM,EAAAN,EAAAA,EAAAK,gBACA,CACA,CACA,EACAsB,gBAAAJ,EACAZ,OACA,IAAAiB,EAAA,GACA,IAAA,MAAA5B,KAAA9G,KAAAuI,iBACAzB,EAAAW,KAAA,EACAiB,EAAAxG,KAAA4E,EAAAI,QAAA,EAEAlH,KAAAiH,UAAAjB,QAAA2C,IAAAD,CAAA,CACA,CACA,EAuJA3G,OAAA6G,eAAAC,OAAA,2BAtIAtE,EACAuE,gCACA,MAAA,CAAA,OACA,CACArE,cACAC,MAAA,EACAE,QAAAC,UAIA,CACAkE,oBACAC,QAAAC,IAAA,WAAA,EAGAjJ,KAAAkJ,YAAAlJ,KAAAmJ,iBAAA,yBAAA,EACAnJ,KAAAoJ,YAAApJ,KAAAmJ,iBAAA,yBAAA,EACAnJ,KAAAqJ,YAAArJ,KAAAmJ,iBAAA,yBAAA,EAEAnJ,KAAAkJ,YAAA5B,QAAAgC,IACAA,EAAArI,iBAAA,QAAAjB,KAAAuJ,aAAAnJ,KAAAJ,IAAA,CAAA,CACA,CAAA,EAEAA,KAAAoJ,YAAA9B,QAAAgC,IACAA,EAAArI,iBAAA,QAAAjB,KAAAwJ,aAAApJ,KAAAJ,IAAA,CAAA,CACA,CAAA,EAEAA,KAAAqJ,YAAA/B,QAAAgC,IACAA,EAAArI,iBAAA,QAAAjB,KAAAwJ,aAAApJ,KAAAJ,IAAA,CAAA,CACA,CAAA,CAEA,CACAuJ,mBAAA7G,GAEAsG,QAAAC,IAAA,cAAA,EAEAvG,EAAAS,cAEAnD,KAAAyJ,YAAA7D,UAAAC,IAAA,sBAAA,EACA7F,KAAA0J,SAAA9D,UAAAC,IAAA,sBAAA,EAEA8D,EAAA,IAAA9C,EAAA,IAAA4B,EAAA,CACA,IAAArB,EAAApH,KAAAyJ,YACA,CACAvB,WAAA,CAAA,UAAA,UACA0B,UAAA,CAAA,gBAAA,qBACA7D,QAAA,CAAA,IAAA,IACA,EACA,CACA8D,SAAA,IACAC,OAAA,gCACA,CACA,EACA,IAAA1C,EAAApH,KAAA0J,SACA,CACAxB,WAAA,CAAA,SAAA,WACA0B,UAAA,CAAA,mBAAA,iBACA7D,QAAA,CAAA,IAAA,IACA,EACA,CACA8D,SAAA,IACAC,OAAA,gCACA,CACA,EACA,CAAA,EAEAH,EAAAlC,KAAA,EAEAsC,MAAAJ,EAAAzC,SAEAlH,KAAAyJ,YAAA7D,UAAAoE,OAAA,mBAAA,EACAhK,KAAA0J,SAAA9D,UAAAC,IAAA,mBAAA,EAEA7F,KAAAyJ,YAAA7D,UAAAoE,OAAA,sBAAA,EACAhK,KAAA0J,SAAA9D,UAAAoE,OAAA,sBAAA,CAEA,CACAR,mBAAA9G,GAEAA,EAAAS,cAEAwG,EAAA,IAAA9C,EAAA,IAAA4B,EAAA,CACA,IAAArB,EAAApH,KAAAyJ,YACA,CACAvB,WAAA,CAAA,UAAA,UACA0B,UAAA,CAAA,iBAAA,oBACA7D,QAAA,CAAA,IAAA,IACA,EACA,CACA8D,SAAA,IACAC,OAAA,gCACA,CACA,EACA,IAAA1C,EAAApH,KAAAiK,SACA,CACA/B,WAAA,CAAA,SAAA,WACA0B,UAAA,CAAA,oBAAA,kBACA7D,QAAA,CAAA,IAAA,IACA,EACA,CACA8D,SAAA,IACAC,OAAA,gCACA,CACA,EACA,CAAA,EAEAH,EAAAlC,KAAA,EAEAsC,MAAAJ,EAAAzC,SAEAlH,KAAAyJ,YAAA7D,UAAAoE,OAAA,mBAAA,EACAhK,KAAAiK,SAAArE,UAAAC,IAAA,mBAAA,CAEA,CACA4D,kBACA,OAAAzJ,KAAAkK,cAAA,oBAAA,CACA,CACAR,eACA9I,IAAAuJ,EAAAnK,KAAAyJ,YAAAW,mBACA,KAAAD,GAAA,CACA,GAAAA,EAAArI,QAAA,YAAA,EAAA,OAAAqI,EACAA,EAAAA,EAAAC,kBACA,CACA,CACAH,eACArJ,IAAAuJ,EAAAnK,KAAAyJ,YAAAY,uBACA,KAAAF,GAAA,CACA,GAAAA,EAAArI,QAAA,YAAA,EAAA,OAAAqI,EACAA,EAAAA,EAAAE,sBACA,CACA,CAEA,CAEA,CAEA,GAAA", "file": "quiz-account.js", "sourcesContent": ["(() => {\n  var __defProp = Object.defineProperty;\n  var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\n  var __publicField = (obj, key, value) => {\n    __defNormalProp(obj, typeof key !== \"symbol\" ? key + \"\" : key, value);\n    return value;\n  };\n\n  // node_modules/ftdomdelegate/main.js\n  function Delegate(root) {\n    this.listenerMap = [{}, {}];\n    if (root) {\n      this.root(root);\n    }\n    this.handle = Delegate.prototype.handle.bind(this);\n    this._removedListeners = [];\n  }\n  Delegate.prototype.root = function (root) {\n    const listenerMap = this.listenerMap;\n    let eventType;\n    if (this.rootElement) {\n      for (eventType in listenerMap[1]) {\n        if (listenerMap[1].hasOwnProperty(eventType)) {\n          this.rootElement.removeEventListener(eventType, this.handle, true);\n        }\n      }\n      for (eventType in listenerMap[0]) {\n        if (listenerMap[0].hasOwnProperty(eventType)) {\n          this.rootElement.removeEventListener(eventType, this.handle, false);\n        }\n      }\n    }\n    if (!root || !root.addEventListener) {\n      if (this.rootElement) {\n        delete this.rootElement;\n      }\n      return this;\n    }\n    this.rootElement = root;\n    for (eventType in listenerMap[1]) {\n      if (listenerMap[1].hasOwnProperty(eventType)) {\n        this.rootElement.addEventListener(eventType, this.handle, true);\n      }\n    }\n    for (eventType in listenerMap[0]) {\n      if (listenerMap[0].hasOwnProperty(eventType)) {\n        this.rootElement.addEventListener(eventType, this.handle, false);\n      }\n    }\n    return this;\n  };\n  Delegate.prototype.captureForType = function (eventType) {\n    return [\"blur\", \"error\", \"focus\", \"load\", \"resize\", \"scroll\"].indexOf(eventType) !== -1;\n  };\n  Delegate.prototype.on = function (eventType, selector, handler, useCapture) {\n    let root;\n    let listenerMap;\n    let matcher;\n    let matcherParam;\n    if (!eventType) {\n      throw new TypeError(\"Invalid event type: \" + eventType);\n    }\n    if (typeof selector === \"function\") {\n      useCapture = handler;\n      handler = selector;\n      selector = null;\n    }\n    if (useCapture === void 0) {\n      useCapture = this.captureForType(eventType);\n    }\n    if (typeof handler !== \"function\") {\n      throw new TypeError(\"Handler must be a type of Function\");\n    }\n    root = this.rootElement;\n    listenerMap = this.listenerMap[useCapture ? 1 : 0];\n    if (!listenerMap[eventType]) {\n      if (root) {\n        root.addEventListener(eventType, this.handle, useCapture);\n      }\n      listenerMap[eventType] = [];\n    }\n    if (!selector) {\n      matcherParam = null;\n      matcher = matchesRoot.bind(this);\n    } else if (/^[a-z]+$/i.test(selector)) {\n      matcherParam = selector;\n      matcher = matchesTag;\n    } else if (/^#[a-z0-9\\-_]+$/i.test(selector)) {\n      matcherParam = selector.slice(1);\n      matcher = matchesId;\n    } else {\n      matcherParam = selector;\n      matcher = Element.prototype.matches;\n    }\n    listenerMap[eventType].push({\n      selector,\n      handler,\n      matcher,\n      matcherParam\n    });\n    return this;\n  };\n  Delegate.prototype.off = function (eventType, selector, handler, useCapture) {\n    let i;\n    let listener;\n    let listenerMap;\n    let listenerList;\n    let singleEventType;\n    if (typeof selector === \"function\") {\n      useCapture = handler;\n      handler = selector;\n      selector = null;\n    }\n    if (useCapture === void 0) {\n      this.off(eventType, selector, handler, true);\n      this.off(eventType, selector, handler, false);\n      return this;\n    }\n    listenerMap = this.listenerMap[useCapture ? 1 : 0];\n    if (!eventType) {\n      for (singleEventType in listenerMap) {\n        if (listenerMap.hasOwnProperty(singleEventType)) {\n          this.off(singleEventType, selector, handler);\n        }\n      }\n      return this;\n    }\n    listenerList = listenerMap[eventType];\n    if (!listenerList || !listenerList.length) {\n      return this;\n    }\n    for (i = listenerList.length - 1; i >= 0; i--) {\n      listener = listenerList[i];\n      if ((!selector || selector === listener.selector) && (!handler || handler === listener.handler)) {\n        this._removedListeners.push(listener);\n        listenerList.splice(i, 1);\n      }\n    }\n    if (!listenerList.length) {\n      delete listenerMap[eventType];\n      if (this.rootElement) {\n        this.rootElement.removeEventListener(eventType, this.handle, useCapture);\n      }\n    }\n    return this;\n  };\n  Delegate.prototype.handle = function (event) {\n    let i;\n    let l;\n    const type = event.type;\n    let root;\n    let phase;\n    let listener;\n    let returned;\n    let listenerList = [];\n    let target;\n    const eventIgnore = \"ftLabsDelegateIgnore\";\n    if (event[eventIgnore] === true) {\n      return;\n    }\n    target = event.target;\n    if (target.nodeType === 3) {\n      target = target.parentNode;\n    }\n    if (target.correspondingUseElement) {\n      target = target.correspondingUseElement;\n    }\n    root = this.rootElement;\n    phase = event.eventPhase || (event.target !== event.currentTarget ? 3 : 2);\n    switch (phase) {\n      case 1:\n        listenerList = this.listenerMap[1][type];\n        break;\n      case 2:\n        if (this.listenerMap[0] && this.listenerMap[0][type]) {\n          listenerList = listenerList.concat(this.listenerMap[0][type]);\n        }\n        if (this.listenerMap[1] && this.listenerMap[1][type]) {\n          listenerList = listenerList.concat(this.listenerMap[1][type]);\n        }\n        break;\n      case 3:\n        listenerList = this.listenerMap[0][type];\n        break;\n    }\n    let toFire = [];\n    l = listenerList.length;\n    while (target && l) {\n      for (i = 0; i < l; i++) {\n        listener = listenerList[i];\n        if (!listener) {\n          break;\n        }\n        if (target.tagName && [\"button\", \"input\", \"select\", \"textarea\"].indexOf(target.tagName.toLowerCase()) > -1 && target.hasAttribute(\"disabled\")) {\n          toFire = [];\n        } else if (listener.matcher.call(target, listener.matcherParam, target)) {\n          toFire.push([event, target, listener]);\n        }\n      }\n      if (target === root) {\n        break;\n      }\n      l = listenerList.length;\n      target = target.parentElement || target.parentNode;\n      if (target instanceof HTMLDocument) {\n        break;\n      }\n    }\n    let ret;\n    for (i = 0; i < toFire.length; i++) {\n      if (this._removedListeners.indexOf(toFire[i][2]) > -1) {\n        continue;\n      }\n      returned = this.fire.apply(this, toFire[i]);\n      if (returned === false) {\n        toFire[i][0][eventIgnore] = true;\n        toFire[i][0].preventDefault();\n        ret = false;\n        break;\n      }\n    }\n    return ret;\n  };\n  Delegate.prototype.fire = function (event, target, listener) {\n    return listener.handler.call(target, event, target);\n  };\n  function matchesTag(tagName, element) {\n    return tagName.toLowerCase() === element.tagName.toLowerCase();\n  }\n  function matchesRoot(selector, element) {\n    if (this.rootElement === window) {\n      return element === document || element === document.documentElement || element === window;\n    }\n    return this.rootElement === element;\n  }\n  function matchesId(id, element) {\n    return id === element.id;\n  }\n  Delegate.prototype.destroy = function () {\n    this.off();\n    this.root();\n  };\n  var main_default = Delegate;\n\n  // js/components/input-binding-manager.js\n  var InputBindingManager = class {\n    constructor() {\n      this.delegateElement = new main_default(document.body);\n      this.delegateElement.on(\"change\", \"[data-bind-value]\", this._onValueChanged.bind(this));\n    }\n    _onValueChanged(event, target) {\n      const boundElement = document.getElementById(target.getAttribute(\"data-bind-value\"));\n      if (boundElement) {\n        if (target.tagName === \"SELECT\") {\n          target = target.options[target.selectedIndex];\n        }\n        boundElement.innerHTML = target.hasAttribute(\"title\") ? target.getAttribute(\"title\") : target.value;\n      }\n    }\n  };\n\n  // js/helper/event.js\n  function triggerEvent(element, name, data = {}) {\n    element.dispatchEvent(new CustomEvent(name, {\n      bubbles: true,\n      detail: data\n    }));\n  }\n  function triggerNonBubblingEvent(element, name, data = {}) {\n    element.dispatchEvent(new CustomEvent(name, {\n      bubbles: false,\n      detail: data\n    }));\n  }\n\n  // js/custom-element/custom-html-element.js\n  var CustomHTMLElement = class extends HTMLElement {\n    constructor() {\n      super();\n      this._hasSectionReloaded = false;\n      if (Shopify.designMode) {\n        this.rootDelegate.on(\"shopify:section:select\", (event) => {\n          const parentSection = this.closest(\".shopify-section\");\n          if (event.target === parentSection && event.detail.load) {\n            this._hasSectionReloaded = true;\n          }\n        });\n      }\n    }\n    get rootDelegate() {\n      return this._rootDelegate = this._rootDelegate || new main_default(document.documentElement);\n    }\n    get delegate() {\n      return this._delegate = this._delegate || new main_default(this);\n    }\n    showLoadingBar() {\n      triggerEvent(document.documentElement, \"theme:loading:start\");\n    }\n    hideLoadingBar() {\n      triggerEvent(document.documentElement, \"theme:loading:end\");\n    }\n    untilVisible(intersectionObserverOptions = { rootMargin: \"30px 0px\", threshold: 0 }) {\n      const onBecameVisible = () => {\n        this.classList.add(\"became-visible\");\n        this.style.opacity = \"1\";\n      };\n      return new Promise((resolve) => {\n        if (window.IntersectionObserver) {\n          this.intersectionObserver = new IntersectionObserver((event) => {\n            if (event[0].isIntersecting) {\n              this.intersectionObserver.disconnect();\n              requestAnimationFrame(() => {\n                resolve();\n                onBecameVisible();\n              });\n            }\n          }, intersectionObserverOptions);\n          this.intersectionObserver.observe(this);\n        } else {\n          resolve();\n          onBecameVisible();\n        }\n      });\n    }\n    disconnectedCallback() {\n      var _a;\n      this.delegate.destroy();\n      this.rootDelegate.destroy();\n      (_a = this.intersectionObserver) == null ? void 0 : _a.disconnect();\n      delete this._delegate;\n      delete this._rootDelegate;\n    }\n  };\n\n  // node_modules/tabbable/dist/index.esm.js\n  var candidateSelectors = [\"input\", \"select\", \"textarea\", \"a[href]\", \"button\", \"[tabindex]\", \"audio[controls]\", \"video[controls]\", '[contenteditable]:not([contenteditable=\"false\"])', \"details>summary:first-of-type\", \"details\"];\n  var candidateSelector = /* @__PURE__ */ candidateSelectors.join(\",\");\n  var matches = typeof Element === \"undefined\" ? function () {\n  } : Element.prototype.matches || Element.prototype.msMatchesSelector || Element.prototype.webkitMatchesSelector;\n  var getCandidates = function getCandidates2(el, includeContainer, filter) {\n    var candidates = Array.prototype.slice.apply(el.querySelectorAll(candidateSelector));\n    if (includeContainer && matches.call(el, candidateSelector)) {\n      candidates.unshift(el);\n    }\n    candidates = candidates.filter(filter);\n    return candidates;\n  };\n  var isContentEditable = function isContentEditable2(node) {\n    return node.contentEditable === \"true\";\n  };\n  var getTabindex = function getTabindex2(node) {\n    var tabindexAttr = parseInt(node.getAttribute(\"tabindex\"), 10);\n    if (!isNaN(tabindexAttr)) {\n      return tabindexAttr;\n    }\n    if (isContentEditable(node)) {\n      return 0;\n    }\n    if ((node.nodeName === \"AUDIO\" || node.nodeName === \"VIDEO\" || node.nodeName === \"DETAILS\") && node.getAttribute(\"tabindex\") === null) {\n      return 0;\n    }\n    return node.tabIndex;\n  };\n  var sortOrderedTabbables = function sortOrderedTabbables2(a, b) {\n    return a.tabIndex === b.tabIndex ? a.documentOrder - b.documentOrder : a.tabIndex - b.tabIndex;\n  };\n  var isInput = function isInput2(node) {\n    return node.tagName === \"INPUT\";\n  };\n  var isHiddenInput = function isHiddenInput2(node) {\n    return isInput(node) && node.type === \"hidden\";\n  };\n  var isDetailsWithSummary = function isDetailsWithSummary2(node) {\n    var r = node.tagName === \"DETAILS\" && Array.prototype.slice.apply(node.children).some(function (child) {\n      return child.tagName === \"SUMMARY\";\n    });\n    return r;\n  };\n  var getCheckedRadio = function getCheckedRadio2(nodes, form) {\n    for (var i = 0; i < nodes.length; i++) {\n      if (nodes[i].checked && nodes[i].form === form) {\n        return nodes[i];\n      }\n    }\n  };\n  var isTabbableRadio = function isTabbableRadio2(node) {\n    if (!node.name) {\n      return true;\n    }\n    var radioScope = node.form || node.ownerDocument;\n    var queryRadios = function queryRadios2(name) {\n      return radioScope.querySelectorAll('input[type=\"radio\"][name=\"' + name + '\"]');\n    };\n    var radioSet;\n    if (typeof window !== \"undefined\" && typeof window.CSS !== \"undefined\" && typeof window.CSS.escape === \"function\") {\n      radioSet = queryRadios(window.CSS.escape(node.name));\n    } else {\n      try {\n        radioSet = queryRadios(node.name);\n      } catch (err) {\n        console.error(\"Looks like you have a radio button with a name attribute containing invalid CSS selector characters and need the CSS.escape polyfill: %s\", err.message);\n        return false;\n      }\n    }\n    var checked = getCheckedRadio(radioSet, node.form);\n    return !checked || checked === node;\n  };\n  var isRadio = function isRadio2(node) {\n    return isInput(node) && node.type === \"radio\";\n  };\n  var isNonTabbableRadio = function isNonTabbableRadio2(node) {\n    return isRadio(node) && !isTabbableRadio(node);\n  };\n  var isHidden = function isHidden2(node, displayCheck) {\n    if (getComputedStyle(node).visibility === \"hidden\") {\n      return true;\n    }\n    var isDirectSummary = matches.call(node, \"details>summary:first-of-type\");\n    var nodeUnderDetails = isDirectSummary ? node.parentElement : node;\n    if (matches.call(nodeUnderDetails, \"details:not([open]) *\")) {\n      return true;\n    }\n    if (!displayCheck || displayCheck === \"full\") {\n      while (node) {\n        if (getComputedStyle(node).display === \"none\") {\n          return true;\n        }\n        node = node.parentElement;\n      }\n    } else if (displayCheck === \"non-zero-area\") {\n      var _node$getBoundingClie = node.getBoundingClientRect(), width = _node$getBoundingClie.width, height = _node$getBoundingClie.height;\n      return width === 0 && height === 0;\n    }\n    return false;\n  };\n  var isDisabledFromFieldset = function isDisabledFromFieldset2(node) {\n    if (isInput(node) || node.tagName === \"SELECT\" || node.tagName === \"TEXTAREA\" || node.tagName === \"BUTTON\") {\n      var parentNode = node.parentElement;\n      while (parentNode) {\n        if (parentNode.tagName === \"FIELDSET\" && parentNode.disabled) {\n          for (var i = 0; i < parentNode.children.length; i++) {\n            var child = parentNode.children.item(i);\n            if (child.tagName === \"LEGEND\") {\n              if (child.contains(node)) {\n                return false;\n              }\n              return true;\n            }\n          }\n          return true;\n        }\n        parentNode = parentNode.parentElement;\n      }\n    }\n    return false;\n  };\n  var isNodeMatchingSelectorFocusable = function isNodeMatchingSelectorFocusable2(options, node) {\n    if (node.disabled || isHiddenInput(node) || isHidden(node, options.displayCheck) || isDetailsWithSummary(node) || isDisabledFromFieldset(node)) {\n      return false;\n    }\n    return true;\n  };\n  var isNodeMatchingSelectorTabbable = function isNodeMatchingSelectorTabbable2(options, node) {\n    if (!isNodeMatchingSelectorFocusable(options, node) || isNonTabbableRadio(node) || getTabindex(node) < 0) {\n      return false;\n    }\n    return true;\n  };\n  var tabbable = function tabbable2(el, options) {\n    options = options || {};\n    var regularTabbables = [];\n    var orderedTabbables = [];\n    var candidates = getCandidates(el, options.includeContainer, isNodeMatchingSelectorTabbable.bind(null, options));\n    candidates.forEach(function (candidate, i) {\n      var candidateTabindex = getTabindex(candidate);\n      if (candidateTabindex === 0) {\n        regularTabbables.push(candidate);\n      } else {\n        orderedTabbables.push({\n          documentOrder: i,\n          tabIndex: candidateTabindex,\n          node: candidate\n        });\n      }\n    });\n    var tabbableNodes = orderedTabbables.sort(sortOrderedTabbables).map(function (a) {\n      return a.node;\n    }).concat(regularTabbables);\n    return tabbableNodes;\n  };\n  var focusableCandidateSelector = /* @__PURE__ */ candidateSelectors.concat(\"iframe\").join(\",\");\n  var isFocusable = function isFocusable2(node, options) {\n    options = options || {};\n    if (!node) {\n      throw new Error(\"No node provided\");\n    }\n    if (matches.call(node, focusableCandidateSelector) === false) {\n      return false;\n    }\n    return isNodeMatchingSelectorFocusable(options, node);\n  };\n\n  // node_modules/focus-trap/dist/focus-trap.esm.js\n  function ownKeys(object, enumerableOnly) {\n    var keys = Object.keys(object);\n    if (Object.getOwnPropertySymbols) {\n      var symbols = Object.getOwnPropertySymbols(object);\n      if (enumerableOnly) {\n        symbols = symbols.filter(function (sym) {\n          return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n        });\n      }\n      keys.push.apply(keys, symbols);\n    }\n    return keys;\n  }\n  function _objectSpread2(target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i] != null ? arguments[i] : {};\n      if (i % 2) {\n        ownKeys(Object(source), true).forEach(function (key) {\n          _defineProperty(target, key, source[key]);\n        });\n      } else if (Object.getOwnPropertyDescriptors) {\n        Object.defineProperties(target, Object.getOwnPropertyDescriptors(source));\n      } else {\n        ownKeys(Object(source)).forEach(function (key) {\n          Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n        });\n      }\n    }\n    return target;\n  }\n  function _defineProperty(obj, key, value) {\n    if (key in obj) {\n      Object.defineProperty(obj, key, {\n        value,\n        enumerable: true,\n        configurable: true,\n        writable: true\n      });\n    } else {\n      obj[key] = value;\n    }\n    return obj;\n  }\n  var activeFocusTraps = function () {\n    var trapQueue = [];\n    return {\n      activateTrap: function activateTrap(trap) {\n        if (trapQueue.length > 0) {\n          var activeTrap = trapQueue[trapQueue.length - 1];\n          if (activeTrap !== trap) {\n            activeTrap.pause();\n          }\n        }\n        var trapIndex = trapQueue.indexOf(trap);\n        if (trapIndex === -1) {\n          trapQueue.push(trap);\n        } else {\n          trapQueue.splice(trapIndex, 1);\n          trapQueue.push(trap);\n        }\n      },\n      deactivateTrap: function deactivateTrap(trap) {\n        var trapIndex = trapQueue.indexOf(trap);\n        if (trapIndex !== -1) {\n          trapQueue.splice(trapIndex, 1);\n        }\n        if (trapQueue.length > 0) {\n          trapQueue[trapQueue.length - 1].unpause();\n        }\n      }\n    };\n  }();\n  var isSelectableInput = function isSelectableInput2(node) {\n    return node.tagName && node.tagName.toLowerCase() === \"input\" && typeof node.select === \"function\";\n  };\n  var isEscapeEvent = function isEscapeEvent2(e) {\n    return e.key === \"Escape\" || e.key === \"Esc\" || e.keyCode === 27;\n  };\n  var isTabEvent = function isTabEvent2(e) {\n    return e.key === \"Tab\" || e.keyCode === 9;\n  };\n  var delay = function delay2(fn) {\n    return setTimeout(fn, 0);\n  };\n  var findIndex = function findIndex2(arr, fn) {\n    var idx = -1;\n    arr.every(function (value, i) {\n      if (fn(value)) {\n        idx = i;\n        return false;\n      }\n      return true;\n    });\n    return idx;\n  };\n  var valueOrHandler = function valueOrHandler2(value) {\n    for (var _len = arguments.length, params = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n      params[_key - 1] = arguments[_key];\n    }\n    return typeof value === \"function\" ? value.apply(void 0, params) : value;\n  };\n  var getActualTarget = function getActualTarget2(event) {\n    return event.target.shadowRoot && typeof event.composedPath === \"function\" ? event.composedPath()[0] : event.target;\n  };\n  var createFocusTrap = function createFocusTrap2(elements, userOptions) {\n    var doc = (userOptions === null || userOptions === void 0 ? void 0 : userOptions.document) || document;\n    var config = _objectSpread2({\n      returnFocusOnDeactivate: true,\n      escapeDeactivates: true,\n      delayInitialFocus: true\n    }, userOptions);\n    var state = {\n      containers: [],\n      tabbableGroups: [],\n      nodeFocusedBeforeActivation: null,\n      mostRecentlyFocusedNode: null,\n      active: false,\n      paused: false,\n      delayInitialFocusTimer: void 0\n    };\n    var trap;\n    var getOption = function getOption2(configOverrideOptions, optionName, configOptionName) {\n      return configOverrideOptions && configOverrideOptions[optionName] !== void 0 ? configOverrideOptions[optionName] : config[configOptionName || optionName];\n    };\n    var containersContain = function containersContain2(element) {\n      return !!(element && state.containers.some(function (container) {\n        return container.contains(element);\n      }));\n    };\n    var getNodeForOption = function getNodeForOption2(optionName) {\n      var optionValue = config[optionName];\n      if (typeof optionValue === \"function\") {\n        for (var _len2 = arguments.length, params = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {\n          params[_key2 - 1] = arguments[_key2];\n        }\n        optionValue = optionValue.apply(void 0, params);\n      }\n      if (!optionValue) {\n        if (optionValue === void 0 || optionValue === false) {\n          return optionValue;\n        }\n        throw new Error(\"`\".concat(optionName, \"` was specified but was not a node, or did not return a node\"));\n      }\n      var node = optionValue;\n      if (typeof optionValue === \"string\") {\n        node = doc.querySelector(optionValue);\n        if (!node) {\n          throw new Error(\"`\".concat(optionName, \"` as selector refers to no known node\"));\n        }\n      }\n      return node;\n    };\n    var getInitialFocusNode = function getInitialFocusNode2() {\n      var node = getNodeForOption(\"initialFocus\");\n      if (node === false) {\n        return false;\n      }\n      if (node === void 0) {\n        if (containersContain(doc.activeElement)) {\n          node = doc.activeElement;\n        } else {\n          var firstTabbableGroup = state.tabbableGroups[0];\n          var firstTabbableNode = firstTabbableGroup && firstTabbableGroup.firstTabbableNode;\n          node = firstTabbableNode || getNodeForOption(\"fallbackFocus\");\n        }\n      }\n      if (!node) {\n        throw new Error(\"Your focus-trap needs to have at least one focusable element\");\n      }\n      return node;\n    };\n    var updateTabbableNodes = function updateTabbableNodes2() {\n      state.tabbableGroups = state.containers.map(function (container) {\n        var tabbableNodes = tabbable(container);\n        if (tabbableNodes.length > 0) {\n          return {\n            container,\n            firstTabbableNode: tabbableNodes[0],\n            lastTabbableNode: tabbableNodes[tabbableNodes.length - 1]\n          };\n        }\n        return void 0;\n      }).filter(function (group) {\n        return !!group;\n      });\n      if (state.tabbableGroups.length <= 0 && !getNodeForOption(\"fallbackFocus\")) {\n        throw new Error(\"Your focus-trap must have at least one container with at least one tabbable node in it at all times\");\n      }\n    };\n    var tryFocus = function tryFocus2(node) {\n      if (node === false) {\n        return;\n      }\n      if (node === doc.activeElement) {\n        return;\n      }\n      if (!node || !node.focus) {\n        tryFocus2(getInitialFocusNode());\n        return;\n      }\n      node.focus({\n        preventScroll: !!config.preventScroll\n      });\n      state.mostRecentlyFocusedNode = node;\n      if (isSelectableInput(node)) {\n        node.select();\n      }\n    };\n    var getReturnFocusNode = function getReturnFocusNode2(previousActiveElement) {\n      var node = getNodeForOption(\"setReturnFocus\", previousActiveElement);\n      return node ? node : node === false ? false : previousActiveElement;\n    };\n    var checkPointerDown = function checkPointerDown2(e) {\n      var target = getActualTarget(e);\n      if (containersContain(target)) {\n        return;\n      }\n      if (valueOrHandler(config.clickOutsideDeactivates, e)) {\n        trap.deactivate({\n          returnFocus: config.returnFocusOnDeactivate && !isFocusable(target)\n        });\n        return;\n      }\n      if (valueOrHandler(config.allowOutsideClick, e)) {\n        return;\n      }\n      e.preventDefault();\n    };\n    var checkFocusIn = function checkFocusIn2(e) {\n      var target = getActualTarget(e);\n      var targetContained = containersContain(target);\n      if (targetContained || target instanceof Document) {\n        if (targetContained) {\n          state.mostRecentlyFocusedNode = target;\n        }\n      } else {\n        e.stopImmediatePropagation();\n        tryFocus(state.mostRecentlyFocusedNode || getInitialFocusNode());\n      }\n    };\n    var checkTab = function checkTab2(e) {\n      var target = getActualTarget(e);\n      updateTabbableNodes();\n      var destinationNode = null;\n      if (state.tabbableGroups.length > 0) {\n        var containerIndex = findIndex(state.tabbableGroups, function (_ref) {\n          var container = _ref.container;\n          return container.contains(target);\n        });\n        if (containerIndex < 0) {\n          if (e.shiftKey) {\n            destinationNode = state.tabbableGroups[state.tabbableGroups.length - 1].lastTabbableNode;\n          } else {\n            destinationNode = state.tabbableGroups[0].firstTabbableNode;\n          }\n        } else if (e.shiftKey) {\n          var startOfGroupIndex = findIndex(state.tabbableGroups, function (_ref2) {\n            var firstTabbableNode = _ref2.firstTabbableNode;\n            return target === firstTabbableNode;\n          });\n          if (startOfGroupIndex < 0 && state.tabbableGroups[containerIndex].container === target) {\n            startOfGroupIndex = containerIndex;\n          }\n          if (startOfGroupIndex >= 0) {\n            var destinationGroupIndex = startOfGroupIndex === 0 ? state.tabbableGroups.length - 1 : startOfGroupIndex - 1;\n            var destinationGroup = state.tabbableGroups[destinationGroupIndex];\n            destinationNode = destinationGroup.lastTabbableNode;\n          }\n        } else {\n          var lastOfGroupIndex = findIndex(state.tabbableGroups, function (_ref3) {\n            var lastTabbableNode = _ref3.lastTabbableNode;\n            return target === lastTabbableNode;\n          });\n          if (lastOfGroupIndex < 0 && state.tabbableGroups[containerIndex].container === target) {\n            lastOfGroupIndex = containerIndex;\n          }\n          if (lastOfGroupIndex >= 0) {\n            var _destinationGroupIndex = lastOfGroupIndex === state.tabbableGroups.length - 1 ? 0 : lastOfGroupIndex + 1;\n            var _destinationGroup = state.tabbableGroups[_destinationGroupIndex];\n            destinationNode = _destinationGroup.firstTabbableNode;\n          }\n        }\n      } else {\n        destinationNode = getNodeForOption(\"fallbackFocus\");\n      }\n      if (destinationNode) {\n        e.preventDefault();\n        tryFocus(destinationNode);\n      }\n    };\n    var checkKey = function checkKey2(e) {\n      if (isEscapeEvent(e) && valueOrHandler(config.escapeDeactivates, e) !== false) {\n        e.preventDefault();\n        trap.deactivate();\n        return;\n      }\n      if (isTabEvent(e)) {\n        checkTab(e);\n        return;\n      }\n    };\n    var checkClick = function checkClick2(e) {\n      if (valueOrHandler(config.clickOutsideDeactivates, e)) {\n        return;\n      }\n      var target = getActualTarget(e);\n      if (containersContain(target)) {\n        return;\n      }\n      if (valueOrHandler(config.allowOutsideClick, e)) {\n        return;\n      }\n      e.preventDefault();\n      e.stopImmediatePropagation();\n    };\n    var addListeners = function addListeners2() {\n      if (!state.active) {\n        return;\n      }\n      activeFocusTraps.activateTrap(trap);\n      state.delayInitialFocusTimer = config.delayInitialFocus ? delay(function () {\n        tryFocus(getInitialFocusNode());\n      }) : tryFocus(getInitialFocusNode());\n      doc.addEventListener(\"focusin\", checkFocusIn, true);\n      doc.addEventListener(\"mousedown\", checkPointerDown, {\n        capture: true,\n        passive: false\n      });\n      doc.addEventListener(\"touchstart\", checkPointerDown, {\n        capture: true,\n        passive: false\n      });\n      doc.addEventListener(\"click\", checkClick, {\n        capture: true,\n        passive: false\n      });\n      doc.addEventListener(\"keydown\", checkKey, {\n        capture: true,\n        passive: false\n      });\n      return trap;\n    };\n    var removeListeners = function removeListeners2() {\n      if (!state.active) {\n        return;\n      }\n      doc.removeEventListener(\"focusin\", checkFocusIn, true);\n      doc.removeEventListener(\"mousedown\", checkPointerDown, true);\n      doc.removeEventListener(\"touchstart\", checkPointerDown, true);\n      doc.removeEventListener(\"click\", checkClick, true);\n      doc.removeEventListener(\"keydown\", checkKey, true);\n      return trap;\n    };\n    trap = {\n      activate: function activate(activateOptions) {\n        if (state.active) {\n          return this;\n        }\n        var onActivate = getOption(activateOptions, \"onActivate\");\n        var onPostActivate = getOption(activateOptions, \"onPostActivate\");\n        var checkCanFocusTrap = getOption(activateOptions, \"checkCanFocusTrap\");\n        if (!checkCanFocusTrap) {\n          updateTabbableNodes();\n        }\n        state.active = true;\n        state.paused = false;\n        state.nodeFocusedBeforeActivation = doc.activeElement;\n        if (onActivate) {\n          onActivate();\n        }\n        var finishActivation = function finishActivation2() {\n          if (checkCanFocusTrap) {\n            updateTabbableNodes();\n          }\n          addListeners();\n          if (onPostActivate) {\n            onPostActivate();\n          }\n        };\n        if (checkCanFocusTrap) {\n          checkCanFocusTrap(state.containers.concat()).then(finishActivation, finishActivation);\n          return this;\n        }\n        finishActivation();\n        return this;\n      },\n      deactivate: function deactivate(deactivateOptions) {\n        if (!state.active) {\n          return this;\n        }\n        clearTimeout(state.delayInitialFocusTimer);\n        state.delayInitialFocusTimer = void 0;\n        removeListeners();\n        state.active = false;\n        state.paused = false;\n        activeFocusTraps.deactivateTrap(trap);\n        var onDeactivate = getOption(deactivateOptions, \"onDeactivate\");\n        var onPostDeactivate = getOption(deactivateOptions, \"onPostDeactivate\");\n        var checkCanReturnFocus = getOption(deactivateOptions, \"checkCanReturnFocus\");\n        if (onDeactivate) {\n          onDeactivate();\n        }\n        var returnFocus = getOption(deactivateOptions, \"returnFocus\", \"returnFocusOnDeactivate\");\n        var finishDeactivation = function finishDeactivation2() {\n          delay(function () {\n            if (returnFocus) {\n              tryFocus(getReturnFocusNode(state.nodeFocusedBeforeActivation));\n            }\n            if (onPostDeactivate) {\n              onPostDeactivate();\n            }\n          });\n        };\n        if (returnFocus && checkCanReturnFocus) {\n          checkCanReturnFocus(getReturnFocusNode(state.nodeFocusedBeforeActivation)).then(finishDeactivation, finishDeactivation);\n          return this;\n        }\n        finishDeactivation();\n        return this;\n      },\n      pause: function pause() {\n        if (state.paused || !state.active) {\n          return this;\n        }\n        state.paused = true;\n        removeListeners();\n        return this;\n      },\n      unpause: function unpause() {\n        if (!state.paused || !state.active) {\n          return this;\n        }\n        state.paused = false;\n        updateTabbableNodes();\n        addListeners();\n        return this;\n      },\n      updateContainerElements: function updateContainerElements(containerElements) {\n        var elementsAsArray = [].concat(containerElements).filter(Boolean);\n        state.containers = elementsAsArray.map(function (element) {\n          return typeof element === \"string\" ? doc.querySelector(element) : element;\n        });\n        if (state.active) {\n          updateTabbableNodes();\n        }\n        return this;\n      }\n    };\n    trap.updateContainerElements(elements);\n    return trap;\n  };\n\n  // js/helper/animation.js\n  var CustomAnimation = class {\n    constructor(effect) {\n      this._effect = effect;\n      this._playState = \"idle\";\n      this._finished = Promise.resolve();\n    }\n    get finished() {\n      return this._finished;\n    }\n    get animationEffects() {\n      return this._effect instanceof CustomKeyframeEffect ? [this._effect] : this._effect.animationEffects;\n    }\n    cancel() {\n      this.animationEffects.forEach((animationEffect) => animationEffect.cancel());\n    }\n    finish() {\n      this.animationEffects.forEach((animationEffect) => animationEffect.finish());\n    }\n    play() {\n      this._playState = \"running\";\n      this._effect.play();\n      this._finished = this._effect.finished;\n      this._finished.then(() => {\n        this._playState = \"finished\";\n      }, (rejection) => {\n        this._playState = \"idle\";\n      });\n    }\n  };\n  var CustomKeyframeEffect = class {\n    constructor(target, keyframes, options = {}) {\n      if (!target) {\n        return;\n      }\n      if (\"Animation\" in window) {\n        this._animation = new Animation(new KeyframeEffect(target, keyframes, options));\n      } else {\n        options[\"fill\"] = \"forwards\";\n        this._animation = target.animate(keyframes, options);\n        this._animation.pause();\n      }\n      this._animation.addEventListener(\"finish\", () => {\n        target.style.opacity = keyframes.hasOwnProperty(\"opacity\") ? keyframes[\"opacity\"][keyframes[\"opacity\"].length - 1] : null;\n        target.style.visibility = keyframes.hasOwnProperty(\"visibility\") ? keyframes[\"visibility\"][keyframes[\"visibility\"].length - 1] : null;\n      });\n    }\n    get finished() {\n      if (!this._animation) {\n        return Promise.resolve();\n      }\n      return this._animation.finished ? this._animation.finished : new Promise((resolve) => this._animation.onfinish = resolve);\n    }\n    play() {\n      if (this._animation) {\n        this._animation.startTime = null;\n        this._animation.play();\n      }\n    }\n    cancel() {\n      if (this._animation) {\n        this._animation.cancel();\n      }\n    }\n    finish() {\n      if (this._animation) {\n        this._animation.finish();\n      }\n    }\n  };\n  var GroupEffect = class {\n    constructor(childrenEffects) {\n      this._childrenEffects = childrenEffects;\n      this._finished = Promise.resolve();\n    }\n    get finished() {\n      return this._finished;\n    }\n    get animationEffects() {\n      return this._childrenEffects.flatMap((effect) => {\n        return effect instanceof CustomKeyframeEffect ? effect : effect.animationEffects;\n      });\n    }\n  };\n  var ParallelEffect = class extends GroupEffect {\n    play() {\n      const promises = [];\n      for (const effect of this._childrenEffects) {\n        effect.play();\n        promises.push(effect.finished);\n      }\n      this._finished = Promise.all(promises);\n    }\n  };\n  var SequenceEffect = class extends GroupEffect {\n    play() {\n      this._finished = new Promise(async (resolve, reject) => {\n        try {\n          for (const effect of this._childrenEffects) {\n            effect.play();\n            await effect.finished;\n          }\n          resolve();\n        } catch (exception) {\n          reject();\n        }\n      });\n    }\n  };\n\n  var QuizSteps = class extends CustomHTMLElement {\n    static get observedAttributes() {\n      return [\"open\"];\n    }\n    constructor() {\n      super();\n      if (Shopify.designMode) {\n        // this.rootDelegate.on(\"shopify:section:select\", (event) => filterShopifyEvent(event, this, () => this.open = true));\n        // this.rootDelegate.on(\"shopify:section:deselect\", (event) => filterShopifyEvent(event, this, () => this.open = false));\n      }\n    }\n    connectedCallback() {\n      console.log(\"QuizSteps\");\n      // this.delegate.on(\"click\", \".openable__overlay\", () => this.open = false);\n\n      this.buttonsNext = this.querySelectorAll(\"[data-quiz-button-next]\");\n      this.buttonsPrev = this.querySelectorAll(\"[data-quiz-button-prev]\");\n      this.buttonsBack = this.querySelectorAll(\"[data-quiz-button-back]\");\n\n      this.buttonsNext.forEach(button => {\n        button.addEventListener(\"click\", this.gotoNextStep.bind(this));\n      });\n\n      this.buttonsPrev.forEach(button => {\n        button.addEventListener(\"click\", this.gotoPrevStep.bind(this));\n      });\n\n      this.buttonsBack.forEach(button => {\n        button.addEventListener(\"click\", this.gotoPrevStep.bind(this));\n      });\n\n    }\n    async gotoNextStep(event) {\n\n      console.log(\"gotoNextStep\");\n\n      const button = event.currentTarget;\n\n      this.currentStep.classList.add(\"quiz-step--animating\");\n      this.nextStep.classList.add(\"quiz-step--animating\");\n\n      const animation = new CustomAnimation(new ParallelEffect([\n        new CustomKeyframeEffect(this.currentStep, \n          { \n            visibility: [\"visible\", \"hidden\"],\n            transform: [\"translateX(0)\", \"translateX(-100%)\"], \n            opacity: [\"1\", \"0\"]\n          }, \n          { \n            duration: 1000, \n            easing: \"cubic-bezier(0.23, 1, 0.32, 1)\" \n          }\n        ),\n        new CustomKeyframeEffect(this.nextStep, \n          {\n            visibility: [\"hidden\", \"visible\"],\n            transform: [\"translateX(100%)\", \"translateX(0)\"],\n            opacity: [\"0\", \"1\"]\n          }, \n          {\n            duration: 1000,\n            easing: \"cubic-bezier(0.23, 1, 0.32, 1)\" \n          }\n        )\n      ]));\n\n      animation.play();\n\n      await animation.finished;\n\n      this.currentStep.classList.remove(\"quiz-step--active\");\n      this.nextStep.classList.add(\"quiz-step--active\");\n\n      this.currentStep.classList.remove(\"quiz-step--animating\");\n      this.nextStep.classList.remove(\"quiz-step--animating\");\n\n    }\n    async gotoPrevStep(event) {\n\n      const button = event.currentTarget;\n\n      const animation = new CustomAnimation(new ParallelEffect([\n        new CustomKeyframeEffect(this.currentStep,\n          {\n            visibility: [\"visible\", \"hidden\"],\n            transform: [\"translateX(0%)\", \"translateX(100%)\"],\n            opacity: [\"1\", \"0\"]\n          },\n          {\n            duration: 1000,\n            easing: \"cubic-bezier(0.23, 1, 0.32, 1)\"\n          }\n        ),\n        new CustomKeyframeEffect(this.prevStep,\n          {\n            visibility: [\"hidden\", \"visible\"],\n            transform: [\"translateX(-100%)\", \"translateX(0%)\"],\n            opacity: [\"0\", \"1\"]\n          },\n          {\n            duration: 1000,\n            easing: \"cubic-bezier(0.23, 1, 0.32, 1)\"\n          }\n        )\n      ]));\n\n      animation.play();\n\n      await animation.finished;\n\n      this.currentStep.classList.remove(\"quiz-step--active\");\n      this.prevStep.classList.add(\"quiz-step--active\");\n\n    }\n    get currentStep() {\n      return this.querySelector(\".quiz-step--active\");\n    }\n    get nextStep() {\n      let sibling = this.currentStep.nextElementSibling;\n      while (sibling) {\n        if (sibling.matches(\".quiz-step\")) return sibling;\n        sibling = sibling.nextElementSibling\n      }\n    }\n    get prevStep() {\n      let sibling = this.currentStep.previousElementSibling;\n      while (sibling) {\n        if (sibling.matches(\".quiz-step\")) return sibling;\n        sibling = sibling.previousElementSibling\n      }\n    }\n    \n  };\n\n  window.customElements.define(\"quiz-steps\", QuizSteps);\n\n})();"]}