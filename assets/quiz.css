@charset "UTF-8";
/* 1. Variables */
/*
$site-width: 1600px;
$container-width: 1200px;
$container-narrow-width: 800px;
$container-extra-narrow-width: 600px;

$container-gutter--desktop: 24px;
$container-gutter--mobile: 24px;

$section-spacer--desktop: 50px;
$section-spacer--mobile: 25px;
*/
/*  ------------------------------
    Grid Variables
    ------------------------------ */
/**
 * Define your breakpoints. The first value is the prefix that shall be used for
 * your classes (e.g. `.small--one-half`), the second value is the media query
 * that the breakpoint fires at.
 */
/**
 * Define which namespaced breakpoints you would like to generate for each of
 * widths, push and pull. This is handy if you only need pull on, say, desk, or
 * you only need a new width breakpoint at mobile sizes. It allows you to only
 * compile as much CSS as you need. All are turned on by default, but you can
 * add and remove breakpoints at will.
 *
 * Push and pull shall only be used if `$push` and/or `$pull` and `$responsive`
 * have been set to ‘true’.
 */
/* 2. Mixins */
/*

  1. Utilities
  2. Responsive
  3. UI Elements
    3.1. Buttons

*/
/*  ==============================
    1. Utilities
    ============================== */
/*  ==============================
    2. Responsive
    ============================== */
/*================ Responsive Show/Hide Helper ================*/
/*================ Responsive Text Alignment Helper ================*/
/*  ==============================
    3. UI Elements
    ============================== */
/*  ------------------------------
    3.1. Buttons
    ------------------------------ */
/* ------------------------------
   Headings
   ------------------------------ */
/* ------------------------------
   Labels
   ------------------------------ */
/* ------------------------------
   Inputs
   ------------------------------ */
/* ------------------------------
   RTE
   ------------------------------ */
/*  ------------------------------
    3.3. Shopify
    ------------------------------ */
/* 3. Fonts  */
/* 4. Basic Styles */
/*  ==============================
    1. Root Styles
    ============================== */
.quiz {
  background: RGB(var(--section-block-background));
  font-weight: var(---font-weight-body);
  font-style: var(---font-style-body);
  letter-spacing: var(---letter-spacing--body);
  font-size: var(---font-size-body--mobile);
  line-height: var(---line-height-body--mobile);
  scroll-behavior: smooth;
}
@media only screen and (min-width: 1001px) {
  .quiz {
    font-size: var(---font-size-body--desktop);
    line-height: var(---line-height-body--desktop);
  }
}
.quiz ::-moz-selection {
  color: var(---color--highlight);
  background: RGBA(var(---color--highlight--rgb), 0.2);
}
.quiz ::selection {
  color: var(---color--highlight);
  background: RGBA(var(---color--highlight--rgb), 0.2);
}

.quiz {
  /*  ------------------------------
    1. Inputs
    ------------------------------ */
  /*  ------------------------------
      2. Labels
      ------------------------------ */
  /*  ------------------------------
      3. Fieldsets
      ------------------------------ */
}
.quiz textarea[disabled],
.quiz select[disabled],
.quiz input[type=text][disabled],
.quiz input[type=number][disabled],
.quiz input[type=email][disabled],
.quiz input[type=tel][disabled],
.quiz input[type=password][disabled],
.quiz input[type=date][disabled] {
  cursor: not-allowed;
}
.quiz .input {
  margin-bottom: 0px;
  margin-top: 30px;
}
.quiz .input .input__field {
  margin-bottom: 0;
  padding-left: 15px;
  padding-right: 15px;
  border: 0;
  letter-spacing: 0;
  border-radius: 0;
  box-shadow: none;
  border-bottom: 1px solid var(---color-text--light);
  height: unset;
  line-height: unset;
  font-size: var(---font-size-h4--mobile);
}
@media only screen and (min-width: 741px) {
  .quiz .input .input__field {
    font-size: var(---font-size-h4--desktop);
  }
}
.quiz .input .input__field:not(input) {
  border-bottom-color: transparent;
  padding-left: 0;
  padding-right: 0;
  margin-bottom: 0.5em;
}
.quiz .input .input__field::-moz-placeholder {
  color: var(---color-text--light);
  opacity: 1;
}
.quiz .input .input__field::placeholder {
  color: var(---color-text--light);
  opacity: 1;
}
.quiz .input .input__field:not([disabled]):focus {
  border: 0;
  border-bottom: 1px solid var(---color-text--light);
  outline: none;
}
.quiz .input .input__field.input--rounded {
  border-radius: var(---input-border-radius);
}
.quiz .input .input__field[required=required] + .input__label:after {
  content: "*";
}
.quiz .input .input__label {
  left: 0;
  transform: scale(0.733) translateY(calc(-32px - 0.5em));
  font-weight: var(---font-weight-body);
  color: RGB(var(--text-color));
  font-size: var(---font-size-body--mobile);
}
@media only screen and (min-width: 741px) {
  .quiz .input .input__label {
    font-size: var(---font-size-body--desktop);
  }
}
.quiz .input .button {
  margin: 0;
}
.quiz select {
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  background: transparent;
  background-image: var(---icon--chevron-down);
  background-position: right 1em top 50%;
  background-repeat: no-repeat;
  background-size: 14px;
  border: 0;
  letter-spacing: 0;
  border-radius: 0;
  box-shadow: none;
  border-bottom: 1px solid var(---color-text--light);
  height: unset;
  line-height: unset;
  font-size: var(---font-size-h4--mobile);
}
@media only screen and (min-width: 741px) {
  .quiz select {
    font-size: var(---font-size-h4--desktop);
  }
}
.quiz select:not(input) {
  border-bottom-color: transparent;
  padding-left: 0;
  padding-right: 0;
  margin-bottom: 0.5em;
}
.quiz select::-moz-placeholder {
  color: var(---color-text--light);
  opacity: 1;
}
.quiz select::placeholder {
  color: var(---color-text--light);
  opacity: 1;
}
.quiz select:not([disabled]):focus {
  border: 0;
  border-bottom: 1px solid var(---color-text--light);
  outline: none;
}
.quiz select.input--rounded {
  border-radius: var(---input-border-radius);
}
.quiz option,
.quiz optgroup {
  font-size: 1rem;
}
.quiz .select-wrapper .select {
  color: var(---color-text--dark);
  border: 1px solid var(---color-line);
  border-radius: 0;
}
.quiz .form__actions {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
  text-align: center;
}
.quiz .form__actions .button {
  display: inline-flex;
}
.quiz fieldset {
  display: block;
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  border: none;
  margin: 0;
  padding: 0;
}

.quiz img, .quiz .img {
  width: 100%;
  vertical-align: top;
}

/* 5. Layout */
html.supports-no-cookies .supports-no-cookies {
  display: none;
}
html.supports-cookies .supports-cookies {
  display: none;
}

/* 6. Sections */
.quiz {
  /* ----- Days ----- */
  /* ----- Per Day Labels ----- */
}
.quiz .section__header .heading span {
  color: #fff;
}
.quiz .section__header .text--large span {
  color: var(---color--highlight);
}
.quiz .feeding-calculator {
  --block-border-radius: 20px;
  --text-color: #2E2E2E;
  --heading-color: #2E2E2E;
  display: block;
  color: #2E2E2E;
  max-width: 800px;
  margin: auto;
  padding: 40px;
  background: #fff;
  border-radius: var(--block-border-radius);
}
.quiz .feeding-calculator .text--subdued {
  color: RGB(var(---color-text--light--rgb));
}
.quiz .feeding-calculator__header {
  display: flex;
  width: 100%;
  justify-content: center;
  align-items: center;
  gap: 2em;
}
@media (max-width: 800px) {
  .quiz .feeding-calculator__header {
    flex-direction: column;
    gap: 1.5em;
  }
}
.quiz .feeding-calculator__body hr {
  width: 400px;
  margin: 2em auto;
}
.quiz .feeding-calculator__footer {
  margin-top: 30px;
  text-align: center;
}
.quiz .feeding-calculator-days {
  display: flex;
  gap: 20px;
  justify-content: center;
  flex-wrap: wrap;
  margin-top: 40px;
}
.quiz .feeding-calculator-day {
  flex: 1 0 20%;
  padding: 20px 10px;
  border: 1px solid var(---color-line--light);
  text-align: center;
  border-radius: var(--block-border-radius);
}
.quiz .feeding-calculator-day p {
  margin: 0;
}
@media (max-width: 800px) {
  .quiz .feeding-calculator-day {
    min-width: 40%;
  }
}
@media (max-width: 400px) {
  .quiz .feeding-calculator-day {
    min-width: 100%;
  }
}
.quiz .feeding-calculator-day__image img {
  max-width: 70px;
  margin: 20px 0;
}
.quiz .feeding-calculator-day__amounts {
  margin-top: 0.5em;
}
.quiz .feeding-calculator-day__amounts > div {
  line-height: 1.4;
}
.quiz .feeding-calculator-perday {
  display: flex;
  gap: 10px;
  align-items: center;
}
@media (max-width: 800px) {
  .quiz .feeding-calculator-perday {
    flex-direction: column;
  }
}
.quiz .feeding-calculator-perday__label {
  margin: 0;
}
.quiz .feeding-calculator-perday__quantity {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 0.5em;
  height: 50px;
  font-size: 1.4em;
  letter-spacing: -0.1em;
  font-weight: var(---font-weight-body--bold);
  /* Light Gray */
  border: 1px solid #E6E6E6;
  border-radius: 8px;
}

.quiz {
  /* ----- Products ----- */
  /* ----- Product ----- */
  /* ----- Inner ----- */
  /* ----- Checkbox ----- */
  /* ----- View Details ----- */
  /* ----- Sticky Form ----- */
  /* ----- Quiz Results - Dogs ----- */
  /* ----- Quiz Results - Dog ----- */
}
.quiz .quiz-results-product__footer {
  min-height: 50px;
}
.quiz quiz-results-products .gallery__list-wrapper {
  padding: var(--vertical-breather) 0;
}
@media only screen and (min-width: 1201px) {
  .quiz quiz-results-products .gallery__list {
    justify-content: center;
  }
}
.quiz quiz-results-products.quiz-results-products--disabled quiz-results-product:not(.quiz-results-product--selected) {
  opacity: 0.5;
  pointer-events: none;
}
.quiz .quiz-results-product {
  --quiz-results-product-color: var(---color--highlight);
  --section-block-background: var(---background-color--content-1--rgb);
  --text-color: RGB(var(---color-text--dark--rgb));
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  width: 350px;
  background: RGB(var(--section-block-background));
  color: var(--text-color);
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.15);
  border-radius: 8px;
  transform: scale3d(1);
  transition: transform 0.25s ease-in-out, box-shadow 0.25s ease-in-out, opacity 0.25s ease-in-out;
}
.quiz .quiz-results-product .quiz-ribbon--recommended {
  display: none;
}
.quiz .quiz-results-product .quiz-results-product-button--soldout {
  display: none;
}
.quiz .quiz-results-product .quiz-results-product-button--add-to-cart {
  display: flex;
}
.quiz .quiz-results-product.quiz-results-product--recommended {
  transform: scale(1.05);
}
.quiz .quiz-results-product.quiz-results-product--recommended:hover {
  transform: scale(1.075) !important;
}
.quiz .quiz-results-product.quiz-results-product--recommended .quiz-ribbon--recommended,
.quiz .quiz-results-product.quiz-results-product--recommended .quiz-results-tag--recommended {
  display: inline-flex;
}
.quiz .quiz-results-product.quiz-results-product--soldout .quiz-results-tag--soldout {
  display: inline-flex;
}
.quiz .quiz-results-product.quiz-results-product--soldout .quiz-results-tag__inner {
  display: inline-block;
}
.quiz .quiz-results-product.quiz-results-product--soldout .quiz-results-product-button--soldout {
  display: flex;
}
.quiz .quiz-results-product.quiz-results-product--soldout .quiz-results-product-button--add-to-cart {
  display: none;
}
.quiz .quiz-results-product:hover {
  transform: scale(1.025);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  opacity: 1 !important;
}
.quiz .quiz-results-product:not(:first-child) {
  margin-left: 30px;
}
.quiz .quiz-results-product:before {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  margin: auto;
  display: inline-block;
  content: "";
  width: 140px;
  height: 140px;
  background: var(--quiz-results-product-color);
  z-index: 0;
  outline: 3px solid var(--quiz-results-product-color);
  border-radius: 100%;
  transform: translateY(-15%);
}
.quiz .quiz-results-product:not(.quiz-results-product--selected) .quiz-results-product__checkbox-button:hover .quiz-results-product__checkbox-input {
  background-color: RGBA(var(---color--brand-2--rgb), 0.25);
}
.quiz .quiz-results-product:not(.quiz-results-product--selected) .quiz-results-product__checkbox-button:hover .quiz-results-product__checkbox-input:before {
  opacity: 1;
}
.quiz .quiz-results-product .gallery__figure {
  padding: 30px;
}
.quiz .quiz-results-product .price--highlight {
  color: var(---color-price);
}
.quiz .quiz-results-product .quiz-results-product__checkbox-label {
  font-weight: var(---font-weight-body--bold);
}
@media only screen and (max-width: 740px) {
  .quiz .quiz-results-product {
    max-width: 80vw;
  }
}
.quiz .quiz-results-product .quiz-results-product__tags {
  margin: 1em 0;
}
.quiz .quiz-results-product .quiz-results-tag {
  padding: 0.5em 1.25em;
  border-radius: 100px;
  display: none;
  font-weight: bold;
}
.quiz .quiz-results-product .quiz-results-tag.quiz-results-tag--recommended {
  background: RGB(var(---color--highlight--rgb));
}
.quiz .quiz-results-product .quiz-results-tag.quiz-results-tag--soldout {
  background: RGB(var(---color--danger--rgb));
  color: RGB(var(---color-text--reversed--rgb));
}
.quiz .quiz-results-product .quiz-results-tag__inner {
  display: inline-block;
}
.quiz .quiz-results-product--selected .quiz-results-product__checkbox-input {
  background-color: RGB(var(---color--brand-2--rgb));
}
.quiz .quiz-results-product--selected .quiz-results-product__checkbox-input:before {
  opacity: 1;
}
.quiz .quiz-results-product__inner {
  position: relative;
  z-index: 1;
}
.quiz .quiz-results-product__header {
  position: relative;
  padding-bottom: 80px;
  background: var(--quiz-results-product-color);
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
}
.quiz .quiz-results-product__footer {
  border-top: 1px solid var(---color-line--light);
}
.quiz .quiz-results-product__image {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  z-index: 1;
  margin: auto;
  transform: translateY(-15%);
  width: 140px;
  height: 140px;
  border: 10px solid transparent;
  border-radius: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}
.quiz .quiz-results-product__checkbox-button {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
  min-height: 50px;
  width: 100%;
}
.quiz .quiz-results-product__checkbox {
  display: flex;
  align-items: center;
  gap: 0.5em;
}
.quiz .quiz-results-product__checkbox-input {
  display: flex;
  width: 24px;
  height: 24px;
  border-radius: 4px;
  background-color: RGB(var(---color-line--light--rgb));
  transition: 0.25s background-color ease-in-out;
}
.quiz .quiz-results-product__checkbox-input:before {
  content: "";
  display: block;
  flex: 1 0 auto;
  background-image: url("data:image/svg+xml,%3Csvg width='16' height='13' viewBox='0 0 16 13' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M15 1L5.375 12L1 7' stroke='white' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E%0A");
  background-repeat: no-repeat;
  background-position: center;
  opacity: 0;
  transition: 0.25s opacity ease-in-out;
}
.quiz .quiz-results-product__view-details {
  display: flex;
  justify-content: center;
  text-align: center;
  margin-top: 12px;
}
.quiz .quiz-results-product__details {
  position: relative;
  margin: 0;
  padding: 50px 20px 20px;
  text-align: center;
  background: var(---background-color--content-1);
}
.quiz .quiz-ribbon {
  --ribbon-color: var(--section-block-background);
  display: inline-flex;
  justify-content: center;
  align-items: center;
  position: absolute;
  width: 36px;
  height: 55px;
  font-size: 24px;
  font-weight: 700;
  background: RGB(var(--ribbon-color));
}
.quiz .quiz-ribbon.quiz-ribbon--number {
  --ribbon-color: var(--section-block-background);
  left: 30px;
}
.quiz .quiz-ribbon.quiz-ribbon--recommended {
  --ribbon-color: var(---color--highlight--rgb);
  right: 30px;
}
.quiz .quiz-ribbon:after {
  bottom: 0;
  left: 50%;
  border: solid transparent;
  content: "";
  height: 0;
  width: 0;
  position: absolute;
  pointer-events: none;
  border-color: rgba(136, 183, 213, 0);
  border-bottom-color: var(--quiz-results-product-header-color);
  border-width: 16px;
  margin-left: -16px;
  transform: scaleY(0.7);
  transform-origin: bottom;
}
.quiz .quiz-ribbon > span {
  margin-top: -5px;
  text-align: center;
  letter-spacing: -0.15em;
  width: 100%;
}
.quiz .quiz-ribbon .quiz-ribbon__bottom svg {
  position: absolute;
  width: 100%;
  bottom: 2px;
  left: 0;
  right: 0;
  transform: translateY(100%);
}
.quiz .quiz-ribbon .quiz-ribbon__bottom svg * {
  fill: RGB(var(--ribbon-color));
}
.quiz .quiz-sticky-form {
  top: unset;
  bottom: 0;
  padding: 20px 0;
  background: RGB(var(--background));
  border-top: RBB(var(--border-color));
}
@media only screen and (min-width: 1001px) {
  .quiz .quiz-sticky-form {
    padding: 30px 0;
  }
}
.quiz .quiz-sticky-form .quiz-sticky-form__title {
  display: inline-block;
  margin-bottom: 0.25em;
  margin-top: 0;
}
.quiz .quiz-sticky-form .quiz-sticky-form__title span, .quiz .quiz-sticky-form .quiz-sticky-form__title strong {
  color: RGB(var(---color--highlight--rgb));
}
.quiz .quiz-sticky-form .unit-price-measurement {
  color: #9a9a9a;
}
.quiz .quiz-sticky-form .unit-price-measurement {
  line-height: 1;
  vertical-align: baseline;
}
.quiz .quiz-sticky-form .unit-price-measurement a {
  color: var(---color--highlight);
  text-decoration: underline;
}
.quiz .quiz-sticky-form .unit-price-measurement .unit-price-measurement__link {
  display: flex;
  flex-direction: column;
  justify-content: center;
  margin-left: 0.5em;
}
@media (max-width: 1000px) {
  .quiz .quiz-sticky-form .product-sticky-form__content-wrapper {
    margin-bottom: 20px;
  }
}
.quiz .quiz-sticky-form .product-sticky-form__payment-container {
  display: flex;
  gap: 15px;
}
@media only screen and (max-width: 740px) {
  .quiz .quiz-sticky-form .product-sticky-form__payment-container {
    flex-direction: column;
  }
}
@media only screen and (max-width: 740px) {
  .quiz .quiz-sticky-form .button-checkout {
    width: 100%;
  }
}
.quiz quiz-results-dog {
  display: none;
}
.quiz quiz-results-dog.quiz-results-dog--active {
  display: block;
}
.quiz quiz-results-dog .quiz-results-dog__header {
  text-align: center;
  padding: var(--vertical-breather-tight) 0;
}
.quiz quiz-results-dog .quiz-results-dog__title span {
  color: RGB(var(---color--highlight--rgb));
}
.quiz quiz-results-dog .quiz-results-dog__content {
  margin: var(--container-gutter) 0;
}

/* 7. Page-Specific Styles */
/* 8. Components */
.product-sticky-form button,
.product-sticky-form .select {
  min-height: var(--button-height);
  height: var(--button-height);
}

@media only screen and (min-width: 1001px) {
  body.on-quiz .quiz.drawer {
    justify-content: flex-end;
    top: auto;
    bottom: 0;
    max-height: var(--quiz-drawer-height);
  }
}

.quiz--flow {
  position: relative;
  color: RGB(var(--text-color));
}

.quiz--account .quiz-navigation__inner {
  align-items: center;
}

.quiz-navigation {
  position: sticky;
  z-index: 4;
  top: 0;
  top: var(--header-height);
  left: 0;
  right: 0;
  width: 100%;
  background: var(---background-color--content-reversed-1);
  box-shadow: 0px 4px 12px 4px rgba(0, 0, 0, 0.25);
  color: RGB(var(---color-text--reversed--rgb));
  transition: transform 0.25s;
}

.quiz-navigation__inner {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  max-width: var(--container-max-width);
  padding-left: var(--container-gutter);
  padding-right: var(--container-gutter);
  margin: auto;
}
@media only screen and (max-width: 740px) {
  .quiz-navigation__inner {
    align-items: center;
  }
}

.quiz-navigation__actions-left,
.quiz-navigation__actions-right {
  flex: 1 0 30%;
}
@media only screen and (max-width: 740px) {
  .quiz-navigation__actions-left,
  .quiz-navigation__actions-right {
    top: 20px;
  }
}

.quiz-navigation__center {
  align-items: center;
}

.quiz-navigation-button {
  display: flex;
  align-items: center;
  gap: 0.5em;
  padding: 1em 0;
  font-weight: var(---font-weight-body--bold);
}
@media only screen and (max-width: 740px) {
  .quiz-navigation-button {
    padding: 0;
  }
}
.quiz-navigation-button:focus, .quiz-navigation-button:hover {
  opacity: 0.7;
}
.quiz-navigation-button.quiz-navigation-button--hidden {
  display: none;
}

.quiz-navigation__actions-left {
  display: flex;
  justify-content: flex-start;
  text-align: left;
}
@media only screen and (max-width: 740px) {
  .quiz-navigation__actions-left {
    position: absolute;
    left: var(--container-gutter);
  }
}

.quiz-navigation__actions-right {
  display: flex;
  justify-content: flex-end;
  text-align: right;
}
@media only screen and (max-width: 740px) {
  .quiz-navigation__actions-right {
    position: absolute;
    right: var(--container-gutter);
  }
}

.quiz-navigation__logo {
  padding: 5px 0;
}
@media only screen and (max-width: 740px) {
  .quiz-navigation__logo {
    padding-top: 20px;
  }
}
.quiz-navigation__logo img {
  max-width: 120px;
  margin-bottom: 10px;
}
@media only screen and (max-width: 740px) {
  .quiz-navigation__logo img {
    max-width: 100px;
  }
}
@media only screen and (max-width: 740px) {
  .quiz-navigation__logo {
    padding-top: 20px;
    position: absolute;
    top: 0;
  }
  .quiz-navigation__logo a:hover {
    opacity: 0.75;
  }
  .quiz-navigation__logo img,
  .quiz-navigation__logo svg {
    width: 100px;
  }
}

.quiz-navigation__progress {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 30px 0 40px;
  max-width: 630px;
  width: 100%;
}
@media only screen and (min-width: 741px) {
  .quiz-navigation__progress {
    padding-top: 15px;
  }
}
@media only screen and (max-width: 740px) {
  .quiz-navigation__progress {
    padding-top: 60px;
    padding-bottom: 10px;
  }
}

.quiz-navigation__actions-left,
.quiz-navigation__actions-right {
  top: 14px;
}

/* ----- Progress Bar ----- */
.quiz-progress-bar {
  position: relative;
  max-width: 630px;
  width: 100%;
}

.quiz-progress-bar__label {
  position: absolute;
  max-width: 100px;
  text-align: center;
  padding-top: 12px;
}
@media only screen and (max-width: 740px) {
  .quiz-progress-bar__label {
    display: none;
  }
}
@media only screen and (min-width: 741px) {
  .quiz-progress-bar__label {
    padding-top: 24px;
  }
}

.quiz-progress-bar__label-start {
  text-align: left;
  left: 0;
}
@media only screen and (min-width: 741px) {
  .quiz-progress-bar__label-start {
    transform: translateX(-50%);
  }
}

.quiz-progress-bar__label-middle {
  left: 0;
  right: 0;
  margin: auto;
}

.quiz-progress-bar__label-end {
  text-align: right;
  right: 0;
}
@media only screen and (min-width: 741px) {
  .quiz-progress-bar__label-end {
    transform: translateX(50%);
  }
}

.quiz-progress-bar__sections {
  display: flex;
  justify-content: stretch;
  height: 12px;
  transform: translateY(-12px);
}
@media only screen and (max-width: 740px) {
  .quiz-progress-bar__sections {
    height: 8px;
    transform: translateY(-8px);
  }
}

.quiz-progress-bar__section {
  width: 100%;
  text-align: center;
  position: relative;
}

.quiz-progress-bar__section__label {
  position: absolute;
  left: 0;
  right: 0;
  bottom: -5px;
  transform: translateY(100%);
}
@media only screen and (max-width: 740px) {
  .quiz-progress-bar__section__label {
    display: none;
  }
}
@media only screen and (min-width: 741px) {
  .quiz-progress-bar__section__label {
    padding-top: 8px;
  }
}

.quiz-progress-bar__dot {
  position: absolute;
  top: 0;
  left: calc(100% * var(--quiz-progress));
  z-index: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  transform: translate(-50%, -25%);
  vertical-align: top;
  transition: 0.25s left;
  background: var(---color--highlight);
  border-radius: 100%;
}
.quiz-progress-bar__dot svg {
  width: 24px;
  opacity: 0.75;
}
.quiz-progress-bar__dot svg * {
  fill: #000;
}
@media only screen and (max-width: 740px) {
  .quiz-progress-bar__dot {
    width: 18px;
    height: 18px;
    border-radius: 100%;
    background: var(---color--brand-1);
  }
  .quiz-progress-bar__dot svg {
    display: none;
  }
}

.quiz-progress-bar__progress-track {
  position: relative;
  display: block;
  height: 12px;
  box-shadow: 0 0 0 1px var(---color--brand-3) inset;
  border-radius: 24px;
  overflow: hidden;
}
@media only screen and (max-width: 740px) {
  .quiz-progress-bar__progress-track {
    height: 8px;
  }
}

.quiz-progress-bar__section__divider {
  display: block;
  width: 1px;
  height: 12px;
  background: var(---color--brand-3);
  transform: rotate(15deg);
}
@media only screen and (max-width: 740px) {
  .quiz-progress-bar__section__divider {
    height: 8px;
  }
}

.quiz-progress-bar__progress {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  background: var(---color--brand-1);
  transform: scaleX(var(--quiz-progress));
  transform-origin: left;
  transition: transform 0.25s;
}
@media only screen and (max-width: 740px) {
  .quiz-progress-bar__progress {
    padding: 10px 0;
  }
}

body.on-quiz .shopify-section--announcement-bar,
body.on-quiz store-header {
  display: none !important;
}

.quiz {
  display: flex;
  flex-direction: column;
  justify-content: center;
  --quiz-navigation-height: 104px;
  /* ----- Basic Styling ----- */
  /* ----- Sub Navigation ----- */
  /* ----- Steps ----- */
  /* ----- Tabs ----- */
  /* ----- Step Content Elements ----- */
  /* ----- Inputs ----- */
  /* ----- Decoration ----- */
}
@media only screen and (max-width: 740px) {
  .quiz {
    --quiz-navigation-height: 85px;
  }
}
.quiz.quiz--flow {
  background: var(---background-color--content-reversed-1);
  --text-color: var(---color-text--reversed--rgb);
}
.quiz.quiz--account .quiz-modal-footer {
  display: none;
}
.quiz.quiz--account .quiz-navigation__center {
  visibility: hidden;
  pointer-events: none;
}
.quiz.quiz--account .quiz-steps__inner .quiz-step {
  justify-content: center;
  position: absolute;
}
.quiz.quiz--has-navigation {
  min-height: calc(100vh - var(--header-height) - var(--quiz-navigation-height));
}
.quiz quiz-steps.quiz-steps {
  display: block;
}
.quiz .quiz-steps__inner {
  position: relative;
  overflow: hidden;
  min-height: calc(100vh - var(--quiz-navigation-height) - 60px);
}
@media only screen and (max-width: 1000px) {
  .quiz .quiz-steps__inner .quiz-step {
    position: static;
  }
}
.quiz .quiz-sub-navigation {
  display: block;
  position: sticky;
  top: calc(var(--header-height) + var(--quiz-navigation-height) - 10px);
  z-index: 3;
  width: 100%;
  background: linear-gradient(0deg, rgba(var(---background-color--content-reversed-1--rgb), 0) 0%, rgba(var(---background-color--content-reversed-1--rgb), 1) 90%);
  pointer-events: none;
  transition: transform 0.25s;
}
@media only screen and (max-width: 740px) {
  .quiz .quiz-sub-navigation {
    padding-bottom: 40px;
  }
}
@media only screen and (min-width: 741px) {
  .quiz .quiz-sub-navigation {
    padding-top: 10px;
  }
}
.quiz .quiz-sub-navigation.quiz-sub-navigation--hidden {
  transform: translateY(-100%);
}
.quiz .quiz-step {
  display: none;
  flex-direction: column;
  justify-content: center;
  visibility: hidden;
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0;
  right: 0;
}
@media only screen and (max-width: 740px) {
  .quiz .quiz-step {
    overflow-y: scroll;
    justify-content: center;
  }
}
.quiz .quiz-step--home {
  overflow: hidden;
}
.quiz .quiz-step--home .quiz-step__footer {
  background: none;
}
.quiz .quiz-step--active {
  display: flex;
  visibility: visible;
}
.quiz .quiz-step--animating {
  display: flex;
}
@media only screen and (max-width: 1000px) {
  .quiz .quiz-step--leaving {
    display: flex;
  }
}
.quiz .quiz-step__inner {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
  z-index: 1;
}
.quiz .quiz-step__inner .h1 {
  line-height: 1;
}
.quiz .quiz-step__header {
  position: absolute;
  top: 0;
  max-width: var(--container-max-width);
  width: 100%;
  margin: 0 auto auto auto;
}
.quiz .quiz-step__body {
  text-align: center;
  padding: 20px var(--container-gutter);
}
.quiz .quiz-step__body > .quiz-step-actions {
  flex-direction: column;
  align-items: center;
}
@media only screen and (min-width: 1401px) {
  .quiz .quiz-step__body {
    padding-top: 5px;
    padding-bottom: 0px;
  }
}
.quiz .quiz-step__body--forms {
  width: 100%;
}
.quiz .quiz-step__body--forms .quiz-step-description {
  max-width: 800px;
  margin: 20px auto 40px;
}
.quiz .quiz-step__body--forms + .quiz-step__footer .quiz-step-actions__inner {
  max-width: 500px;
  margin: 20px auto;
}
.quiz .quiz-step__body--narrow {
  max-width: 600px;
}
.quiz .quiz-step__footer {
  position: sticky;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 10px;
  width: 100%;
  padding: 20px var(--container-gutter);
  margin-top: auto;
  text-align: center;
  background: linear-gradient(180deg, rgba(var(---background-color--content-reversed-1--rgb), 0) 0%, rgba(var(---background-color--content-reversed-1--rgb), 1) 20%);
}
@media only screen and (max-width: 1000px) {
  .quiz .quiz-step__footer .button {
    width: 100%;
  }
  .quiz .quiz-step__footer .quiz-step-actions {
    order: 1;
  }
  .quiz .quiz-step__footer .quiz-step-actions-account {
    order: 0;
  }
}
.quiz .quiz-customer-forms {
  display: flex;
  justify-content: center;
  gap: 30px;
}
@media only screen and (max-width: 1000px) {
  .quiz .quiz-customer-forms {
    flex-direction: column;
  }
}
.quiz .quiz-customer-form {
  --block-border-radius: 20px;
  --section-block-background: var(---background-color--content-reversed-3--rgb);
  background: var(---background-color--content-reversed-3);
  border-radius: var(--block-border-radius);
  display: flex;
  flex-direction: column;
  justify-content: stretch;
  max-width: 490px;
  width: 100%;
  padding: 30px;
}
.quiz .quiz-customer-form form {
  height: 100%;
  display: flex;
  flex-direction: column;
}
.quiz .quiz-customer-form .input {
  margin-top: 20px;
  margin-bottom: 20px;
}
.quiz .quiz-customer-form .input .input__field {
  padding: 0.25em 0.5em;
}
.quiz .quiz-customer-form .input:last-child {
  margin-top: 0;
  margin-bottom: 0;
  padding-top: 0;
}
@media (max-width: 1000px) {
  .quiz .quiz-customer-form {
    margin: auto;
  }
}
.quiz .quiz-customer-form .input__field {
  font-size: 20px !important;
  padding: 0.5em 1em;
}
.quiz .quiz-customer-form__step {
  padding: 1em 0;
}
.quiz .quiz-customer-form__step .input--radio label {
  font-size: var(---font-size-body-large--desktop);
}
.quiz .quiz-customer-form__step .kyc-option-radio {
  margin: 0.5em 0 !important;
}
.quiz .quiz-customer-form__step .input__field {
  padding-left: 0 !important;
}
.quiz .quiz-step__tabs:before, .quiz .quiz-step__tabs:after {
  display: inline-block;
  position: absolute;
  top: 0;
  z-index: 3;
  content: "";
  width: 50px;
  height: 100%;
  pointer-events: none;
}
.quiz .quiz-step__tabs:before {
  left: 0;
  background: linear-gradient(270deg, rgba(var(---background-color--content-reversed-1--rgb), 0), rgba(var(---background-color--content-reversed-1--rgb), 1) 100%);
}
.quiz .quiz-step__tabs:after {
  right: 0;
  background: linear-gradient(90deg, rgba(var(---background-color--content-reversed-1--rgb), 0), rgba(var(---background-color--content-reversed-1--rgb), 1) 100%);
}
.quiz .quiz-step__tabs-inner {
  display: flex;
  gap: 20px;
  scroll-snap-type: x mandatory;
  scroll-snap-align: center;
  overflow: scroll;
  display: flex;
  align-items: flex-start;
  justify-content: center;
  padding: 10px 50px;
  gap: 30px;
  width: 100%;
}
.quiz .quiz-step__tabs-inner::-webkit-scrollbar {
  display: none;
}
@media only screen and (max-width: 1000px) {
  .quiz .quiz-step__tabs-inner {
    padding: 0;
  }
}
.quiz .quiz-step__tabs-inner .quiz-step-tab {
  display: flex;
}
.quiz .quiz-step-tab {
  display: block;
  scroll-snap-align: center;
  white-space: nowrap;
  pointer-events: all;
}
@media only screen and (max-width: 1000px) {
  .quiz .quiz-step-tab {
    padding: 0 20px;
  }
}
.quiz .quiz-step-logo {
  max-width: 200px;
  margin-top: 50px;
  margin-bottom: 30px;
}
@media only screen and (min-width: 1001px) {
  .quiz .quiz-step-logo {
    margin-top: 100px;
    margin-bottom: 50px;
  }
}
.quiz .quiz-step-icon {
  width: 150px;
  height: 150px;
  -webkit-margin-after: 20px;
          margin-block-end: 20px;
}
@media (max-width: 700px) {
  .quiz .quiz-step-icon .quiz-step-icon {
    width: 120px;
    height: 120px;
    margin-bottom: -20px;
  }
}
.quiz .quiz-step-title {
  margin-bottom: 0.5em;
  margin-top: 0;
}
.quiz .quiz-step-title strong, .quiz .quiz-step-title span {
  color: RGB(var(---color--brand-1--rgb));
}
.quiz .quiz-step-description {
  margin-top: 30px;
  margin-bottom: 30px;
}
.quiz .quiz-step-line {
  display: block;
  margin: 10px 0;
  line-height: 1.2;
  font-size: var(---font-size-h4--mobile);
  opacity: 1;
  transition: transform 0.25s ease-in-out, opacity 0.25s ease-in-out;
}
@media only screen and (min-width: 741px) {
  .quiz .quiz-step-line {
    font-size: var(---font-size-h4--desktop);
  }
}
.quiz .quiz-step-line select {
  font-size: 1rem !important;
}
.quiz .quiz-step-line.quiz-step-line--hidden {
  opacity: 0;
  transform: translateY(-10%);
  pointer-events: none;
}
.quiz .quiz-step-line > * {
  display: inline-block;
}
.quiz .quiz-step-line .quiz-step-line__hint {
  display: block;
}
.quiz .quiz-step-line__hint {
  margin-top: 1em;
  font-size: var(---font-size-body--desktop);
}
.quiz .quiz-step-hint {
  margin: 30px auto;
}
@media only screen and (max-width: 740px) {
  .quiz .quiz-step-hint {
    margin: 0 auto;
    max-width: 200px;
  }
}
.quiz .quiz-step-actions {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 1em;
  width: 100%;
  padding-top: 20px;
  padding-bottom: 20px;
}
@media only screen and (min-width: 741px) {
  .quiz .quiz-step-actions {
    padding-top: 30px;
  }
}
@media only screen and (min-width: 1401px) {
  .quiz .quiz-step-actions {
    padding-top: 5px;
  }
}
.quiz .quiz-step-actions-account {
  margin: 10px auto;
}
.quiz .quiz-step-form {
  padding-bottom: var(--container-gutter);
}
.quiz .quiz-input {
  display: inline-block;
  background-color: transparent;
  border: none;
  min-width: 10px;
  border-bottom: 2px solid var(---color--secondary);
  margin: 10px 0;
  font-weight: var(--text-font-bold-weight);
  color: var(---color--highlight);
  transition: 0.25s border-color;
}
.quiz .quiz-input::-moz-placeholder {
  font-weight: var(--text-font-weight);
  opacity: 1;
}
.quiz .quiz-input::placeholder {
  font-weight: var(--text-font-weight);
  opacity: 1;
}
.quiz .quiz-input:focus, .quiz .quiz-input:hover {
  outline: none;
  border-color: var(---color--highlight);
}
.quiz select.quiz-input {
  padding-right: 50px;
  background-image: url("data:image/svg+xml,%3Csvg width='12' height='6' viewBox='0 0 12 6' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M1 0.5L6 5.5L11 0.5' stroke='%23E6E6E6' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E%0A");
  background-size: 15px;
  background-repeat: no-repeat;
  background-position: calc(100% - 10px);
}
.quiz quiz-step-dog-health .quiz-step-hint__final {
  display: none;
}
.quiz quiz-step-dog-health .quiz-step-hint__in-progress {
  display: block;
}
.quiz quiz-step-dog-health:last-of-type .quiz-step-hint__final {
  display: block;
}
.quiz quiz-step-dog-health:last-of-type .quiz-step-hint__in-progress {
  display: none;
}
.quiz .quiz-decoration {
  content: "";
  position: absolute;
  z-index: 0;
  pointer-events: none;
}
.quiz .quiz-decoration--1 {
  width: 850px;
  height: 100%;
  left: 0;
  top: 0;
  bottom: 0;
  z-index: 0;
  transform: scale(1.1) translate(0%);
}
.quiz .quiz-decoration--1 svg {
  transform: translateX(-40%);
}
.quiz .quiz-decoration--2 {
  width: 850px;
  height: 100%;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: 0;
  transform: scale(1.1) translate(0%);
}
.quiz .quiz-decoration--2 svg {
  transform: translateX(30%) rotate(160deg);
}
@media only screen and (max-width: 740px) {
  .quiz .quiz-decoration--1 {
    width: 100%;
    height: 400px;
    left: 0;
    top: auto;
    bottom: 0;
    z-index: 0;
    transform-origin: 50% 50%;
    transform: scale(1.2) translateX(0%) translateY(30%) rotate(230deg);
  }
  .quiz .quiz-decoration--1 svg {
    transform: translateX(0);
  }
  .quiz .quiz-decoration--2 {
    display: none;
  }
}

/* ----- Tiles ----- */
.quiz-tiles .quiz-tiles__container {
  display: flex;
  margin: var(--container-gutter) 0;
  gap: 30px;
  flex-wrap: wrap;
  justify-content: center;
}
@media only screen and (max-width: 1000px) {
  .quiz-tiles .quiz-tiles__container {
    gap: 15px;
  }
}

.quiz-tiles__input {
  position: absolute !important;
  overflow: hidden;
  clip: rect(0 0 0 0);
  height: 1px;
  width: 1px;
  max-width: 1px;
  max-height: 1px;
  font-size: 0;
  margin: -1px;
  padding: 0;
  border: 0;
}

.quiz-tile {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
  border: 2px solid var(---color--brand-3);
  border-radius: 8px;
  background-color: var(---color--brand-2);
  transition: 0.25s border-color;
  cursor: pointer;
}
.quiz-tile:hover {
  background: var(---color--primary--dark);
}
.quiz-tile:focus {
  border-color: var(---color--highlight);
}
.quiz-tile.quiz-tile--selected, .quiz-tile:focus {
  border-color: var(---color--highlight);
}
.quiz-tile.quiz-tile--selected .quiz-tile__icon--default:not(:only-child), .quiz-tile:focus .quiz-tile__icon--default:not(:only-child) {
  opacity: 0;
}
.quiz-tile.quiz-tile--selected .quiz-tile__icon--hover, .quiz-tile:focus .quiz-tile__icon--hover {
  opacity: 1;
}
.quiz-tile .quiz-tile__icon {
  position: relative;
}
.quiz-tile .quiz-tile__icon--hover {
  position: absolute;
  left: 0;
  top: 0;
  opacity: 0;
}
.quiz-tile .quiz-tile__text__hint {
  display: block;
}
.quiz-tile .quiz-tile__text__title {
  display: block;
}
@media only screen and (min-width: 741px) {
  .quiz-tile .quiz-tile__text__hint {
    display: none;
  }
}
@media only screen and (max-width: 740px) {
  .quiz-tile {
    flex-direction: row;
    gap: 20px;
    width: 100%;
    padding: 10px;
  }
  .quiz-tile .quiz-tile__text {
    text-align: left;
  }
  .quiz-tile .quiz-tile__icon {
    flex: 1 0 150px;
    max-height: 100px;
    max-width: 100px;
    width: 100%;
    height: 100%;
  }
  .quiz-tile .quiz-tile__icon img {
    max-height: 100px;
    max-width: 100px;
    width: 100%;
    height: 100%;
  }
}

.quiz-tile__icon img {
  height: 150px;
  width: 150px;
}

.quiz-substep {
  display: none;
  visibility: hidden;
}

.quiz-substep--active {
  display: block;
  visibility: visible;
}

.quiz-substep--visible {
  display: block;
  visibility: visible;
}

.quiz-substep--animating {
  display: block;
}

quiz-loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 100;
  width: 100vw;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(---background-color--content-reversed-1);
}

.quiz-loading-overlay__inner {
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.quiz-loading-overlay__image {
  max-width: 180px;
}
@media only screen and (min-width: 1001px) {
  .quiz-loading-overlay__image {
    max-width: 290px;
  }
}

.quiz-loading-overlay__text .heading {
  margin: 0;
}

.quiz {
  /* ----- Quiz Elements ----- */
  /* ----- Radio Buttons ----- */
  /* ----- Radio Buttons ----- */
  /* ----- HRs ----- */
}
.quiz .price {
  font-weight: 500;
  letter-spacing: 0;
}
.quiz .quiz-page-header {
  text-align: center;
  margin-bottom: 20px;
}
.quiz .quiz-page-footer {
  text-align: center;
  margin-bottom: 20px;
  margin-top: 20px;
}
.quiz .quiz-terms {
  margin-top: 20px;
  max-width: 350px;
  margin: auto;
  text-align: center;
}
.quiz .quiz-sticky-form {
  --background: var(---background-color--content-reversed-1--rgb);
  --border-color: var(---color-text--rgb);
  border-bottom: 0;
}
.quiz .input--sub-option {
  margin: 0;
  padding-left: 30px;
  text-align: left;
  margin: 0 !important;
}
.quiz .input--sub-option .nice-select, .quiz .input--sub-option select, .quiz .input--sub-option input {
  font-size: var(---font-size-body--desktop) !important;
}
.quiz .input--sub-option input,
.quiz .input--sub-option textarea,
.quiz .input--sub-option select, .quiz .input--sub-option .select {
  padding: 0.5em 3em 0.5em 1.4em;
  display: block;
  font-size: 18px;
  background-color: rgba(var(--section-block-background), 0.8);
  margin-bottom: 1em;
  border-radius: 8px;
  border: 1px solid rgba(var(--text-color), 0.25);
}
.quiz .input--sub-option input:hover,
.quiz .input--sub-option textarea:hover,
.quiz .input--sub-option select:hover, .quiz .input--sub-option .select:hover {
  background-color: rgba(var(--section-block-background), 1);
  border: 1px solid rgba(var(--text-color), 0.5);
}
.quiz .input--sub-option input:focus,
.quiz .input--sub-option textarea:focus,
.quiz .input--sub-option select:focus, .quiz .input--sub-option .select:focus {
  border: 1px solid rgba(var(--text-color), 0.5);
}
.quiz .input--sub-option .select,
.quiz .input--sub-option select {
  background-image: var(---icon--chevron);
  background-position: right 1em top 50%;
  background-repeat: no-repeat;
  background-size: 14px;
}
.quiz .input--radio {
  margin: 0;
  margin-bottom: 0.5em;
}
.quiz .input--radio label {
  font-size: var(---font-size-label-radio--mobile);
}
@media (min-width: 1000px) {
  .quiz .input--radio label {
    font-size: var(---font-size-label-radio--desktop);
  }
}
.quiz .input--radio input[type=radio] {
  background: #fff;
  position: relative;
  top: -1px;
}
.quiz .input--radio input[type=radio]:after {
  content: "";
  display: inline-block;
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  margin: auto;
  color: #000;
  width: 10px;
  height: 10px;
  border-radius: 100%;
  background: currentColor;
  transform: scale(0);
  opacity: 0.5;
  transition: transform 0.25s, opacity 0.25s;
}
.quiz .input--radio input[type=radio]:checked:after, .quiz .input--radio input[type=radio]:hover:after {
  transform: scale(1);
}
.quiz .input--radio input[type=radio]:checked:after {
  opacity: 1;
}
.quiz .input--radio input[type=radio]:checked + label {
  font-weight: 700;
}
.quiz .input--radio .checkbox-container {
  align-items: center;
}
.quiz .input--radio .checkbox {
  width: 16px;
  height: 16px;
  border-radius: 100%;
}
@media (min-width: 1000px) {
  .quiz .input--radio .checkbox {
    width: 18px;
    height: 18px;
  }
}
.quiz .banner {
  border-radius: 20px;
  font-weight: var(---font-weight-body--bold);
}
.quiz .banner .banner__content a {
  text-decoration: underline;
}
.quiz .banner.banner--success {
  color: var(---color-text--strong);
  background-color: var(---color--highlight);
}
.quiz .banner.banner--error {
  color: var(---color-text--reversed-strong);
  background-color: var(---color--danger);
}

/* 9. Apps  */
/* 10. Utility Classes */
/* 11. Third-Party Styles */
/* 12. Animations */
@keyframes fade-in {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
@keyframes fade-out {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}
@keyframes rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}