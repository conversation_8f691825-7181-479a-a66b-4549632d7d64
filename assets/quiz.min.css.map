{"version": 3, "sources": ["basic-styles/_basic-styles.scss", "quiz.css", "utilities/_mixins.scss", "basic-styles/_form-elements.scss", "basic-styles/_images.scss", "layout/_body-modifiers.scss", "sections/_feeding-calculator.scss", "sections/_quiz-results.scss", "components/_theme-sticky-form.scss", "components/_drawers.scss", "components/_quiz-progress.scss", "components/_quiz-step.scss", "components/_quiz-tile.scss", "components/_quiz-substep.scss", "components/_quiz-loading-overlay.scss", "components/__components.scss", "utilities/_animations.scss"], "names": [], "mappings": "AAIE,MAEE,+CAAA,CAMA,wCAAA,CAHA,kCAAA,CADA,oCAAA,CAEA,2CAAA,CAGA,4CAAA,CAOA,sBC+DJ,CCZM,0CFnEJ,MAYI,yCAAA,CACA,6CCuEJ,CACF,CDnEI,uBAEE,iDAAA,CADA,8BCsEN,CDvEI,kBAEE,iDAAA,CADA,8BCsEN,CE3EI,4PACE,kBFgGN,CE3FE,aAEE,eAAA,CACA,eF4FJ,CE1FI,2BDsaF,QAAA,CASA,iDAAA,CANA,eAAA,CACA,eAAA,CASA,sCAAA,CAHA,YAAA,CARA,gBAAA,CASA,iBAAA,CC/aI,eAAA,CACA,iBAAA,CACA,kBFmGN,CC7DM,yCC1CF,2BDqbA,uCD1UF,CACF,CC4UE,sCACE,+BAAA,CAGA,kBAAA,CAFA,cAAA,CACA,eDzUJ,CC6UE,6CACE,+BAAA,CACA,SD3UJ,CCyUE,wCACE,+BAAA,CACA,SD3UJ,CC+UI,iDACE,QAAA,CACA,iDAAA,CACA,YD7UN,CCiVE,0CACE,yCD/UJ,CEpHU,kEACE,WFsHZ,CE/GI,2BAME,4BAAA,CAEA,wCAAA,CAHA,oCAAA,CAHA,MAAA,CACA,oDFmHN,CC9FM,yCCxBF,2BAWI,yCF+GN,CACF,CE3GI,qBACE,QF6GN,CErGE,aAEE,uBAAA,CAAA,oBAAA,CAAA,eAAA,CACA,sBAAA,CACA,2CAAA,CACA,qCAAA,CACA,2BAAA,CACA,oBAAA,CDoXF,QAAA,CASA,iDAAA,CANA,eAAA,CACA,eAAA,CASA,sCAAA,CAHA,YAAA,CARA,gBAAA,CASA,iBDjRF,CCtHM,yCCCJ,aD0YE,uCDjRF,CACF,CCmRE,wBACE,+BAAA,CAGA,kBAAA,CAFA,cAAA,CACA,eDhRJ,CCoRE,+BACE,+BAAA,CACA,SDlRJ,CCgRE,0BACE,+BAAA,CACA,SDlRJ,CCsRI,mCACE,QAAA,CACA,iDAAA,CACA,YDpRN,CCwRE,4BACE,yCDtRJ,CE/HE,4BAEE,cFiIJ,CE5HI,8BAGE,mCAAA,CACA,eAAA,CAFA,8BF+HN,CEvHE,qBAIE,kBAAA,CAFA,YAAA,CACA,qBAAA,CAEA,QAAA,CAIA,iBFqHJ,CEnHI,6BACE,mBFqHN,CE9FE,eAGE,uBAAA,CAAA,oBAAA,CAAA,eAAA,CACA,WAAA,CAFA,aAAA,CAGA,QAAA,CACA,SF+FJ,CG5OE,qBAEE,kBAAA,CADA,UHgPJ,CI1OI,sFACE,YJiPN,CKtPM,qCACE,UL8PR,CKxPM,yCACE,8BL0PR,CKpPE,0BAEE,0BAAA,CAEA,oBAAA,CACA,uBAAA,CAQA,eAAA,CAEA,wCAAA,CAPA,aAAA,CADA,aAAA,CAIA,WAAA,CADA,eAAA,CAEA,YLoPJ,CK/OI,yCACE,yCLiPN,CKvOE,kCAIE,kBAAA,CAHA,YAAA,CAIA,OAAA,CAFA,sBAAA,CADA,UL4OJ,CKvOI,yBAPF,kCAQI,qBAAA,CACA,SL0OJ,CACF,CKrOI,mCAEE,eAAA,CADA,WLwON,CKlOE,kCACE,eAAA,CACA,iBLoOJ,CK/NE,+BACE,YAAA,CAGA,cAAA,CAFA,QAAA,CACA,sBAAA,CAGA,eLgOJ,CK7NE,8BAKE,0CAAA,CAIA,wCAAA,CAPA,YAAA,CAEA,iBAAA,CAGA,iBL6NJ,CKzNI,gCACE,QL2NN,CKxNI,yBAfF,8BAgBI,aL2NJ,CACF,CKzNI,yBAnBF,8BAoBI,cL4NJ,CACF,CKvNI,yCAEE,aAAA,CADA,cL0NN,CKrNE,uCACE,eLuNJ,CKtNI,2CACE,eLwNN,CKlNE,iCAIE,kBAAA,CAHA,YAAA,CACA,QLqNJ,CKjNI,yBANF,iCAOI,qBLoNJ,CACF,CKhNE,wCACE,QLkNJ,CK/ME,2CAGE,kBAAA,CAWA,wBAAA,CACA,iBAAA,CAbA,YAAA,CAOA,eAAA,CAEA,0CAAA,CAJA,WAAA,CAHA,sBAAA,CAMA,oBAAA,CAJA,cLsNJ,CMxWE,oCACE,eNqXJ,CM9WI,mDACE,kCNgXN,CCpTM,0CKzDF,2CAEI,sBN+WN,CACF,CMxWQ,sHACE,UAAA,CACA,mBN0WV,CM9VE,4BAEE,qDAAA,CACA,mEAAA,CACA,+CAAA,CAQA,+CAAA,CAIA,iBAAA,CAFA,kCAAA,CADA,uBAAA,CANA,YAAA,CACA,qBAAA,CACA,6BAAA,CAHA,iBAAA,CAYA,oBAAA,CAEA,0FACE,CAXF,WNoWJ,CMjVI,wHACE,YNsVN,CMnVI,sEACE,YNqVN,CMlVI,8DAEE,qBNmVN,CMjVM,oEACE,gCNmVR,CMvUM,0QACE,mBN6UR,CM1UM,mFACE,oBN4UR,CMzUM,gGACE,YN2UR,CMxUM,oGACE,YN0UR,CMrUI,kCAEE,qCAAA,CACA,mBAAA,CAFA,sBNyUN,CMpUI,8CACE,gBNsUN,CMnUI,mCAWE,4CAAA,CAGA,kBAAA,CANA,UAAA,CADA,oBAAA,CAGA,YAAA,CANA,MAAA,CAEA,WAAA,CAOA,mDAAA,CAXA,iBAAA,CAGA,OAAA,CAFA,KAAA,CAYA,0BAAA,CANA,WAAA,CAGA,SNuUN,CM7TU,oJACE,sDN+TZ,CM7TY,2JACE,SN+Td,CMxTI,6CACE,YN0TN,CMvTI,8CACE,yBNyTN,CMtTI,kEACE,0CNwTN,CCnZM,yCKjCJ,4BAgII,cNwTJ,CACF,CMpTI,wDACE,YNsTN,CMnTI,8CAGE,mBAAA,CACA,YAAA,CACA,eAAA,CAHA,mBNuTN,CMlTM,4EACE,6CNoTR,CMjTM,wEACE,0CAAA,CACA,4CNmTR,CM9SI,qDACE,oBNgTN,CMzSI,4EACE,iDN2SN,CMzSM,mFACE,SN2SR,CM/RE,mCAEE,iBAAA,CACA,SNgSJ,CM5RE,oCAIE,4CAAA,CAEA,0BAAA,CACA,2BAAA,CAJA,mBAAA,CADA,iBNiSJ,CMxRE,oCACE,8CN0RJ,CMvRE,mCAaE,6BAAA,CACA,kBAAA,CAFA,YAAA,CATA,MAAA,CAIA,WAAA,CAQA,mBAAA,CAAA,gBAAA,CAbA,iBAAA,CAEA,OAAA,CACA,KAAA,CAIA,0BAAA,CAEA,WAAA,CALA,SN+RJ,CMhRE,6CAIE,kBAAA,CAFA,YAAA,CAGA,QAAA,CAFA,sBAAA,CAIA,eAAA,CACA,UNgRJ,CM5QE,sCAGE,kBAAA,CADA,YAAA,CAEA,QN6QJ,CMzQE,4CAOE,oDAAA,CADA,iBAAA,CAJA,YAAA,CAGA,WAAA,CAIA,4CAAA,CALA,UN6QJ,CMtQI,mDAKE,oQAAA,CAEA,uBAAA,CADA,2BAAA,CAJA,UAAA,CACA,aAAA,CACA,aAAA,CAIA,SAAA,CACA,mCNuQN,CM/PE,0CAEE,YAAA,CACA,sBAAA,CAEA,eAAA,CADA,iBNiQJ,CM5PE,qCAOE,8CAAA,CAJA,QAAA,CACA,sBAAA,CAFA,iBAAA,CAIA,iBN6PJ,CMxPE,mBAEE,8CAAA,CAIA,kBAAA,CASA,mCAAA,CAXA,mBAAA,CAQA,cAAA,CACA,eAAA,CAHA,WAAA,CALA,sBAAA,CAGA,iBAAA,CACA,UN2PJ,CMnPI,uCACE,8CAAA,CACA,SNqPN,CMlPI,4CACE,4CAAA,CACA,UNoPN,CMjPI,yBAWE,qCAAA,CAAA,iEAAA,CAVA,QAAA,CAGA,UAAA,CACA,QAAA,CAHA,QAAA,CAUA,iBAAA,CAJA,mBAAA,CADA,iBAAA,CAOA,oBAAA,CACA,uBAAA,CATA,ON2PN,CM/OI,wBAGE,qBAAA,CAFA,eAAA,CACA,iBAAA,CAEA,UNiPN,CM5OM,4CAGE,UAAA,CACA,MAAA,CAHA,iBAAA,CAIA,OAAA,CACA,0BAAA,CAJA,UNkPR,CM5OQ,8CACE,6BN8OV,CMnOI,wBAME,iCAAA,CACA,mCAAA,CAJA,QAAA,CACA,cAAA,CAFA,SNwON,CCrjBM,0CK2UF,wBAUI,cNoON,CACF,CMlOM,iDAEE,oBAAA,CACA,mBAAA,CACA,YNmOR,CMjOQ,8GACE,wCNmOV,CM9NM,gDACE,aAAA,CAIA,aAAA,CACA,uBN2NR,CMzNQ,kDACE,8BAAA,CACA,yBN+NV,CM5NQ,8EAEE,YAAA,CACA,qBAAA,CACA,sBAAA,CAEA,gBN4NV,CMtNQ,0BADF,8DAEI,kBNyNR,CACF,CMtNM,gEACE,YAAA,CACA,QNwNR,CC5lBM,yCKkYA,gEAII,qBN0NR,CMtNI,yCAEI,UNyNR,CAJF,CMzMI,uBAEE,YN+MN,CM7MM,gDACE,aN+MR,CM5MM,iDAEE,wCAAA,CADA,iBN+MR,CM1MQ,qDACE,wCN4MV,CMxMM,kDACE,gCN0MR,CO3rBE,yDAGE,2BAAA,CADA,+BPisBJ,CC9nBM,0COrEJ,0BAMM,QAAA,CAFA,wBAAA,CAIA,oCAAA,CAHA,QRssBN,CACF,CS9sBA,YAEE,4BAAA,CADA,iBTktBF,CS7sBE,uCACE,kBTgtBJ,CS5sBA,iBAWE,uDAAA,CACA,yCAAA,CAEA,4CAAA,CAPA,MAAA,CALA,eAAA,CAMA,OAAA,CAJA,KAAA,CACA,wBAAA,CAWA,yBAAA,CAPA,UAAA,CANA,STutBF,CSlsBA,wBAIE,sBAAA,CAFA,YAAA,CACA,6BAAA,CAMA,WAAA,CAHA,oCAAA,CACA,oCAAA,CACA,qCTosBF,CCxqBM,yCQpCN,wBAYI,kBTosBF,CACF,CShsBA,+DAEE,YTmsBF,CClrBM,yCQnBN,+DAII,QTssBF,CACF,CSnsBA,yBACE,kBTssBF,CSnsBA,wBAGE,kBAAA,CADA,YAAA,CAKA,0CAAA,CAHA,QAAA,CACA,aTssBF,CCpsBM,yCQPN,wBAUI,STqsBF,CACF,CSnsBE,4DAEI,UTosBN,CSjsBE,uDACE,YTmsBJ,CS9rBA,+BACE,YAAA,CACA,0BAAA,CACA,eTisBF,CCrtBM,yCQiBN,+BAMM,4BAAA,CADA,iBTosBJ,CACF,CShsBA,gCACE,YAAA,CACA,wBAAA,CACA,gBTmsBF,CCjuBM,yCQ2BN,gCAKM,iBAAA,CACA,6BTqsBJ,CACF,CSlsBA,uBAEE,aTosBF,CC3uBM,yCQqCN,uBAKI,gBTqsBF,CACF,CSnsBE,2BAGE,kBAAA,CADA,eTqsBJ,CCpvBM,yCQ6CJ,2BAMI,eTqsBJ,CSntBF,uBAqBI,gBAAA,CAEA,iBAAA,CACA,KTksBF,CS/rBI,+BACE,WTisBN,CS7rBE,sDAEE,WT+rBJ,CAbF,CS3qBA,2BAIE,kBAAA,CAFA,YAAA,CACA,qBAAA,CAIA,eAAA,CADA,mBAAA,CAEA,UT0rBF,CChxBM,yCQ8EN,2BAWI,gBT2rBF,CACF,CCrxBM,yCQ8EN,2BAgBI,mBAAA,CADA,gBT6rBF,CACF,CSzrBA,+DAEE,QT4rBF,CSvrBA,mBAEE,eAAA,CADA,iBAAA,CAEA,UT2rBF,CSxrBA,0BAGE,eAAA,CAEA,gBAAA,CAHA,iBAAA,CAEA,iBT2rBF,CC9yBM,yCQ+GN,0BAQI,YT2rBF,CACF,CCnzBM,yCQ+GN,0BAYI,gBT4rBF,CACF,CSxrBA,gCAEE,MAAA,CADA,eT4rBF,CC7zBM,yCQgIN,gCAKI,0BT4rBF,CACF,CSzrBA,iCACE,MAAA,CAEA,WAAA,CADA,OT6rBF,CSzrBA,8BAEE,OAAA,CADA,gBT6rBF,CC70BM,yCQ+IN,8BAKI,yBT6rBF,CACF,CS1rBA,6BAEE,YAAA,CAGA,WAAA,CAFA,uBAAA,CAIA,2BT0rBF,CCz1BM,yCQwJN,6BAUI,UAAA,CACA,0BT2rBF,CACF,CSvrBA,4BAIE,iBAAA,CADA,iBAAA,CADA,UT2rBF,CSrrBA,mCAIE,WAAA,CAFA,MAAA,CADA,iBAAA,CAEA,OAAA,CAEA,0BTwrBF,CC72BM,yCQgLN,mCAOI,YT0rBF,CACF,CCl3BM,yCQgLN,mCAUI,eT4rBF,CACF,CSzrBA,wBAQE,kBAAA,CAWA,mCAAA,CACA,kBAAA,CAbA,YAAA,CAIA,WAAA,CAFA,sBAAA,CALA,oCAAA,CAFA,iBAAA,CACA,KAAA,CAUA,8BAAA,CAIA,oBAAA,CAFA,kBAAA,CALA,UAAA,CALA,STqsBF,CSprBE,4BAEE,WAAA,CADA,UTurBJ,CSprBI,8BACE,STsrBN,CC/4BM,yCQ8LN,wBAmCI,iCAAA,CADA,kBAAA,CADA,WAAA,CADA,UTwrBF,CSprBE,4BACI,YTsrBN,CACF,CS9qBA,mCAQE,kBAAA,CAFA,iDAAA,CAHA,aAAA,CACA,WAAA,CAKA,eAAA,CAPA,iBTqrBF,CCn6BM,yCQ4ON,mCAYI,UT+qBF,CACF,CS3qBA,qCAIE,iCAAA,CAHA,aAAA,CAEA,WAAA,CAEA,uBAAA,CAHA,STirBF,CCh7BM,yCQ6PN,qCAQI,UT+qBF,CACF,CS3qBA,6BAME,iCAAA,CAFA,WAAA,CADA,MAAA,CAFA,iBAAA,CACA,KAAA,CAKA,sCAAA,CACA,qBAAA,CACA,yBAAA,CAJA,UTkrBF,CCj8BM,yCQ0QN,6BAYI,cT+qBF,CACF,CU3gCE,0EAEE,sBV8gCJ,CU1gCA,MAME,8BAAA,CAJA,YAAA,CACA,qBAAA,CACA,sBVohCF,CCz9BM,yCS/DN,MASI,6BVmhCF,CACF,CU7gCE,iBAEE,8CAAA,CADA,uDVghCJ,CUzgCI,uCACE,YV2gCN,CUxgCI,6CAEE,mBAAA,CADA,iBV2gCN,CUtgCM,kDACE,sBAAA,CACA,iBVwgCR,CUlgCE,2BACE,6EVogCJ,CUjgCE,4BACE,aVmgCJ,CUhgCE,yBAME,6DAAA,CAHA,eAAA,CADA,iBVmgCJ,CCx/BM,0CSFA,oCACE,eV6/BN,CACF,CUr/BE,2BAOE,0JAAA,CANA,aAAA,CAQA,mBAAA,CAPA,eAAA,CACA,qEAAA,CAOA,yBAAA,CAJA,UAAA,CADA,SV0/BJ,CCvgCM,yCSQJ,2BAaI,mBVs/BJ,CACF,CC5gCM,yCSQJ,2BAiBI,gBVu/BJ,CACF,CUr/BI,uDACE,2BVu/BN,CUh/BE,iBAEE,YAAA,CACA,qBAAA,CAOA,WAAA,CANA,sBAAA,CAOA,MAAA,CAHA,iBAAA,CAIA,OAAA,CANA,iBAAA,CAGA,UVk/BJ,CC/hCM,yCSoCJ,iBAgBI,sBAAA,CADA,iBVi/BJ,CACF,CUp+BE,uBAEE,eVq+BJ,CUn+BI,0CACE,eVq+BN,CUh+BE,yBACE,YAAA,CACA,kBVk+BJ,CU/9BE,4BACE,YVi+BJ,CCljCM,0CSqFJ,0BAEI,YV+9BJ,CACF,CU59BE,wBAOE,kBAAA,CAFA,YAAA,CACA,qBAAA,CAEA,sBAAA,CAEA,iBAAA,CACA,SVy9BJ,CUv9BI,4BACE,aVy9BN,CU58BE,yBAOE,kBAAA,CAFA,oCAAA,CAHA,iBAAA,CACA,KAAA,CAGA,UV68BJ,CUv8BE,uBAGE,oCAAA,CADA,iBVy8BJ,CUt8BI,0CAEE,kBAAA,CADA,qBVy8BN,CCjlCM,0CSkIJ,uBAYI,gBAAA,CADA,eVy8BJ,CACF,CUp8BE,8BACE,UVs8BJ,CUp8BI,qDAEE,qBAAA,CADA,eVu8BN,CUn8BI,2EAEE,gBAAA,CADA,eVs8BN,CUj8BE,+BAEE,eVk8BJ,CU97BE,yBAOE,kBAAA,CAiBA,4JAAA,CArBA,QAAA,CAEA,YAAA,CACA,qBAAA,CAGA,QAAA,CADA,sBAAA,CAWA,eAAA,CAPA,oCAAA,CAVA,eAAA,CAmBA,iBAAA,CAVA,UVi8BJ,CCnnCM,0CSmMA,iCACE,UVm7BN,CUh7BI,4CACE,OVk7BN,CU96BI,oDACE,OVg7BN,CACF,CU16BE,2BAEE,YAAA,CAEA,QAAA,CADA,sBV46BJ,CCnoCM,0CSoNJ,2BAOI,qBV46BJ,CACF,CUx6BE,0BAEE,0BAAA,CACA,4EAAA,CAEA,uDAAA,CACA,wCAAA,CAEA,YAAA,CACA,qBAAA,CACA,uBAAA,CACA,eAAA,CAGA,YAAA,CAFA,UVw6BJ,CUp6BI,+BAEE,YAAA,CACA,qBAAA,CAFA,WVw6BN,CUn6BI,iCAGE,kBAAA,CADA,eVq6BN,CUl6BM,+CACE,kBVo6BR,CUj6BM,4CAEE,eAAA,CADA,YAAA,CAEA,aVm6BR,CUt5BI,0BA/CF,0BAgDI,WVy5BJ,CACF,CUv5BI,wCACE,wBAAA,CACA,gBVy5BN,CUp5BE,gCACE,aVs5BJ,CUn5BM,oDACE,+CVq5BR,CUj5BI,kDACE,uBVm5BN,CUh5BI,8CACE,wBVk5BN,CU14BI,2DAQE,UAAA,CALA,oBAAA,CAOA,WAAA,CAEA,mBAAA,CARA,iBAAA,CACA,KAAA,CAIA,UAAA,CAHA,SV84BN,CUp4BI,8BAEE,2JAAA,CADA,MVu4BN,CUn4BI,6BAEE,0JAAA,CADA,OVs4BN,CU/3BE,6BAWE,sBAAA,CADA,YAAA,CAPA,QAAA,CAWA,QAAA,CAFA,sBAAA,CAJA,eAAA,CAKA,iBAAA,CAPA,wBAAA,CADA,4BAAA,CAWA,UV43BJ,CU13BI,gDACE,YV43BN,CC5tCM,0CS6UJ,6BAuBI,SV43BJ,CACF,CU13BI,4CACE,YV43BN,CUv3BE,qBAEE,aAAA,CAKA,kBAAA,CAJA,wBAAA,CAEA,kBVw3BJ,CC1uCM,0CS6WJ,qBAUI,cVu3BJ,CACF,CUn3BE,sBAGE,kBAAA,CADA,eAAA,CADA,eVu3BJ,CCpvCM,0CS4XJ,sBAOI,kBAAA,CADA,gBVu3BJ,CACF,CUl3BE,sBAKE,yBAAA,CAFA,YAAA,CAEA,qBAAA,CAHA,WVq3BJ,CUh3BI,yBAEE,sCAEE,YAAA,CACA,mBAAA,CAFA,WVm3BN,CACF,CUv2BE,uBAEE,kBAAA,CACA,YVw2BJ,CUt2BI,0DACE,sCVw2BN,CUn2BE,6BAGE,kBAAA,CADA,eVq2BJ,CUh2BE,sBAEE,aAAA,CAIA,sCAAA,CAFA,eAAA,CADA,aAAA,CASA,SAAA,CAEA,8DV01BJ,CCzxCM,yCSibJ,sBASI,uCVm2BJ,CACF,CU51BI,6BACE,wBV81BN,CU31BI,6CACE,SAAA,CAEA,mBAAA,CADA,0BV81BN,CU11BI,wBACE,oBV41BN,CUz1BI,4CACE,aV21BN,CUt1BE,4BAEE,yCAAA,CADA,cVy1BJ,CUr1BE,sBAEE,gBVs1BJ,CCnzCM,yCS2dJ,sBAKI,aAAA,CACA,eVu1BJ,CACF,CUn1BE,yBAEE,YAAA,CACA,cAAA,CAEA,OAAA,CADA,sBAAA,CAKA,mBAAA,CADA,gBAAA,CADA,UVq1BJ,CCl0CM,yCSseJ,yBAYI,gBVo1BJ,CACF,CCv0CM,0CSseJ,yBAgBI,eVq1BJ,CACF,CUj1BE,iCACE,gBVm1BJ,CUh1BE,sBACE,sCVk1BJ,CU70BE,kBAGE,4BAAA,CACA,WAAA,CAGA,gDAAA,CAKA,8BAAA,CAVA,oBAAA,CAQA,wCAAA,CAFA,aAAA,CAFA,cAAA,CAOA,4BV20BJ,CUz0BI,oCACE,mCAAA,CACA,SV20BN,CU70BI,+BACE,mCAAA,CACA,SV20BN,CUx0BI,gDAGE,qCAAA,CADA,YV00BN,CUp0BE,wBAEE,gPAAA,CAGA,qCAAA,CADA,2BAAA,CADA,oBAAA,CAFA,kBV00BJ,CUj0BI,kDACE,YVm0BN,CU3zBM,uHACE,aVg0BR,CU7zBM,qEACE,YV+zBR,CUtzBE,uBAEE,UAAA,CAIA,mBAAA,CAHA,iBAAA,CACA,SVwzBJ,CUlzBE,0BAME,QAAA,CAHA,WAAA,CACA,MAAA,CACA,KAAA,CAIA,iCAAA,CAPA,WAAA,CAKA,SVozBJ,CUhzBI,8BACE,0BVkzBN,CU7yBE,0BAME,QAAA,CAHA,WAAA,CACA,OAAA,CACA,KAAA,CAIA,iCAAA,CAPA,WAAA,CAKA,SV+yBJ,CU3yBI,8BACE,wCV6yBN,CCt5CM,yCSgnBF,0BAME,QAAA,CAHA,YAAA,CACA,MAAA,CACA,QAAA,CAMA,iEAAA,CAFA,wBAAA,CAPA,UAAA,CAKA,SV0yBJ,CUpyBI,8BACE,uBVsyBN,CUjyBE,0BACE,YVmyBJ,CACF,CW3+CE,mCAEE,YAAA,CAGA,cAAA,CADA,QAAA,CAEA,sBAAA,CAHA,gCXi/CJ,CCj7CM,0CUnEJ,mCASI,QX++CJ,CACF,CWz+CA,mBVgBE,kBAAA,CAQA,QAAA,CAHA,WAAA,CAJA,UAAA,CAKA,WAAA,CAFA,cAAA,CADA,aAAA,CAJA,eAAA,CAQA,SAAA,CATA,2BAAA,CAIA,SDq+CF,CWn/CA,WAIE,kBAAA,CAMA,uCAAA,CAHA,uCAAA,CAEA,iBAAA,CAIA,cAAA,CAXA,YAAA,CACA,qBAAA,CAGA,YAAA,CAKA,4BXo/CF,CWh/CE,iBACE,uCXk/CJ,CW3+CE,gDAGE,qCX8+CJ,CW5+CI,sIACE,SX8+CN,CW3+CI,gGACE,SX6+CN,CWx+CE,4BACE,iBX0+CJ,CWv+CE,mCAEE,MAAA,CAEA,SAAA,CAHA,iBAAA,CAEA,KX0+CJ,CWl+CE,qEACE,aXu+CJ,CC9+CM,yCUYF,kCACE,YXq+CJ,CACF,CCn/CM,yCU/CN,WAmEI,kBAAA,CACA,QAAA,CAEA,YAAA,CADA,UXo+CF,CWj+CE,4BACE,eXm+CJ,CWh+CE,4BAEE,cXq+CJ,CW/9CI,4DAFA,WAAA,CAHA,gBAAA,CACA,eAAA,CACA,UXw+CJ,CACF,CW19CE,qBACE,YAAA,CACA,WX69CJ,CYvlDA,cAEE,YAAA,CACA,iBZylDF,CYhlDA,6CACE,aAAA,CACA,kBZwlDF,CYrlDA,yBACE,aZwlDF,Ca1mDA,qBAUE,kBAAA,CAGA,uDAAA,CAJA,YAAA,CAFA,YAAA,CAIA,sBAAA,CAPA,MAAA,CAFA,cAAA,CACA,KAAA,CAGA,WAAA,CADA,WbknDF,CatmDA,6BAIE,kBAAA,CAFA,YAAA,CACA,qBAAA,CAFA,iBb4mDF,CatmDA,6BAEE,ebwmDF,CC3jDM,0CY/CN,6BAKI,ebymDF,CACF,CapmDE,qCACE,QbumDJ,CczoDE,aACE,eAAA,CACA,gBdkpDJ,CcxoDE,gDAHE,kBAAA,CADA,iBdqpDJ,CcjpDE,wBAGE,ed8oDJ,Cc3oDE,kBAGE,WAAA,CADA,eAAA,CAEA,iBd6oDJ,Cc1oDE,wBACE,8DAAA,CACA,sCAAA,CACA,ed4oDJ,CcvoDE,yBACE,QAAA,CAGA,kBAAA,CAFA,iBAAA,CACA,ed0oDJ,CcvoDI,qGACI,mDdyoDR,CctoDI,kIAME,yDAAA,CAGA,4CAAA,CADA,iBAAA,CAJA,aAAA,CACA,cAAA,CAEA,iBAAA,CAJA,2Bd8oDN,CctoDM,0JACE,wDAAA,CACA,2Cd0oDR,CcvoDM,0JACE,2Cd2oDR,CctoDI,iEAGE,sCAAA,CACA,qCAAA,CACA,2BAAA,CACA,oBduoDN,Cc/nDE,oBAGE,edgoDJ,Cc9nDI,0BAEE,+Cd+nDN,Cc7nDM,0BAJF,0BAKI,gDdgoDN,CACF,Cc5nDI,sCAEE,eAAA,CACA,iBAAA,CACA,Qd6nDN,Cc3nDM,4CAgBE,uBAAA,CADA,kBAAA,CANA,QAAA,CAGA,UAAA,CAVA,UAAA,CAEA,oBAAA,CAUA,WAAA,CARA,MAAA,CAIA,WAAA,CASA,UAAA,CAdA,iBAAA,CAGA,OAAA,CADA,KAAA,CAWA,kBAAA,CAGA,sCAAA,CARA,UdgoDR,CclnDQ,sGACE,kBdonDV,Cc/mDQ,oDACE,SdinDV,Cc9mDQ,oDACE,edgnDV,Cc3mDI,wCACE,kBd6mDN,Cc1mDI,8BAGE,kBAAA,CADA,WAAA,CADA,Ud8mDN,Cc1mDM,0BALF,8BAOI,WAAA,CADA,Ud8mDN,CACF,CclkDE,cAEE,kBAAA,CACA,0CdmkDJ,CchkDM,iCACE,yBdkkDR,Cc9jDI,8BAEE,yCAAA,CADA,gCdikDN,Cc7jDI,4BAEE,sCAAA,CADA,yCdgkDN,CezxDA,mBACE,GACE,SfgyDF,Ce9xDA,GACE,SfgyDF,CACF,Ce7xDA,oBACE,GACE,Sf+xDF,Ce7xDA,GACE,Sf+xDF,CACF,Ce5xDA,kBAEE,GACE,sBf6xDF,Ce3xDA,GACE,uBf6xDF,CACF", "file": "quiz.min.css", "sourcesContent": ["/*  ==============================\n    1. Root Styles\n    ============================== */\n\n  .quiz {\n    \n    background: RGB(var(--section-block-background));\n\n    font-weight: var(---font-weight-body);\n    font-style: var(---font-style-body);\n    letter-spacing: var(---letter-spacing--body);\n\n    font-size: var(---font-size-body--mobile);\n    line-height: var(---line-height-body--mobile);\n\n    @include respond-to($medium-up) {\n      font-size: var(---font-size-body--desktop);\n      line-height: var(---line-height-body--desktop);\n    }\n\n    scroll-behavior: smooth;\n\n    ::selection {\n      color: var(---color--highlight);\n      background: RGBA(var(---color--highlight--rgb), 0.2); \n    }\n\n  }", "@charset \"UTF-8\";\n/* 1. Variables */\n/*\n$site-width: 1600px;\n$container-width: 1200px;\n$container-narrow-width: 800px;\n$container-extra-narrow-width: 600px;\n\n$container-gutter--desktop: 24px;\n$container-gutter--mobile: 24px;\n\n$section-spacer--desktop: 50px;\n$section-spacer--mobile: 25px;\n*/\n/*  ------------------------------\n    Grid Variables\n    ------------------------------ */\n/**\n * Define your breakpoints. The first value is the prefix that shall be used for\n * your classes (e.g. `.small--one-half`), the second value is the media query\n * that the breakpoint fires at.\n */\n/**\n * Define which namespaced breakpoints you would like to generate for each of\n * widths, push and pull. This is handy if you only need pull on, say, desk, or\n * you only need a new width breakpoint at mobile sizes. It allows you to only\n * compile as much CSS as you need. All are turned on by default, but you can\n * add and remove breakpoints at will.\n *\n * Push and pull shall only be used if `$push` and/or `$pull` and `$responsive`\n * have been set to ‘true’.\n */\n/* 2. Mixins */\n/*\n\n  1. Utilities\n  2. Responsive\n  3. UI Elements\n    3.1. Buttons\n\n*/\n/*  ==============================\n    1. Utilities\n    ============================== */\n/*  ==============================\n    2. Responsive\n    ============================== */\n/*================ Responsive Show/Hide Helper ================*/\n/*================ Responsive Text Alignment Helper ================*/\n/*  ==============================\n    3. UI Elements\n    ============================== */\n/*  ------------------------------\n    3.1. Buttons\n    ------------------------------ */\n/* ------------------------------\n   Headings\n   ------------------------------ */\n/* ------------------------------\n   Labels\n   ------------------------------ */\n/* ------------------------------\n   Inputs\n   ------------------------------ */\n/* ------------------------------\n   RTE\n   ------------------------------ */\n/*  ------------------------------\n    3.3. Shopify\n    ------------------------------ */\n/* 3. Fonts  */\n/* 4. Basic Styles */\n/*  ==============================\n    1. Root Styles\n    ============================== */\n.quiz {\n  background: RGB(var(--section-block-background));\n  font-weight: var(---font-weight-body);\n  font-style: var(---font-style-body);\n  letter-spacing: var(---letter-spacing--body);\n  font-size: var(---font-size-body--mobile);\n  line-height: var(---line-height-body--mobile);\n  scroll-behavior: smooth;\n}\n@media only screen and (min-width: 1001px) {\n  .quiz {\n    font-size: var(---font-size-body--desktop);\n    line-height: var(---line-height-body--desktop);\n  }\n}\n.quiz ::selection {\n  color: var(---color--highlight);\n  background: RGBA(var(---color--highlight--rgb), 0.2);\n}\n\n.quiz {\n  /*  ------------------------------\n    1. Inputs\n    ------------------------------ */\n  /*  ------------------------------\n      2. Labels\n      ------------------------------ */\n  /*  ------------------------------\n      3. Fieldsets\n      ------------------------------ */\n}\n.quiz textarea[disabled],\n.quiz select[disabled],\n.quiz input[type=text][disabled],\n.quiz input[type=number][disabled],\n.quiz input[type=email][disabled],\n.quiz input[type=tel][disabled],\n.quiz input[type=password][disabled],\n.quiz input[type=date][disabled] {\n  cursor: not-allowed;\n}\n.quiz .input {\n  margin-bottom: 0px;\n  margin-top: 30px;\n}\n.quiz .input .input__field {\n  margin-bottom: 0;\n  padding-left: 15px;\n  padding-right: 15px;\n  border: 0;\n  letter-spacing: 0;\n  border-radius: 0;\n  box-shadow: none;\n  border-bottom: 1px solid var(---color-text--light);\n  height: unset;\n  line-height: unset;\n  font-size: var(---font-size-h4--mobile);\n}\n@media only screen and (min-width: 741px) {\n  .quiz .input .input__field {\n    font-size: var(---font-size-h4--desktop);\n  }\n}\n.quiz .input .input__field:not(input) {\n  border-bottom-color: transparent;\n  padding-left: 0;\n  padding-right: 0;\n  margin-bottom: 0.5em;\n}\n.quiz .input .input__field::placeholder {\n  color: var(---color-text--light);\n  opacity: 1;\n}\n.quiz .input .input__field:not([disabled]):focus {\n  border: 0;\n  border-bottom: 1px solid var(---color-text--light);\n  outline: none;\n}\n.quiz .input .input__field.input--rounded {\n  border-radius: var(---input-border-radius);\n}\n.quiz .input .input__field[required=required] + .input__label:after {\n  content: \"*\";\n}\n.quiz .input .input__label {\n  left: 0;\n  transform: scale(0.733) translateY(calc(-32px - 0.5em));\n  font-weight: var(---font-weight-body);\n  color: RGB(var(--text-color));\n  font-size: var(---font-size-body--mobile);\n}\n@media only screen and (min-width: 741px) {\n  .quiz .input .input__label {\n    font-size: var(---font-size-body--desktop);\n  }\n}\n.quiz .input .button {\n  margin: 0;\n}\n.quiz select {\n  appearance: none;\n  background: transparent;\n  background-image: var(---icon--chevron-down);\n  background-position: right 1em top 50%;\n  background-repeat: no-repeat;\n  background-size: 14px;\n  border: 0;\n  letter-spacing: 0;\n  border-radius: 0;\n  box-shadow: none;\n  border-bottom: 1px solid var(---color-text--light);\n  height: unset;\n  line-height: unset;\n  font-size: var(---font-size-h4--mobile);\n}\n@media only screen and (min-width: 741px) {\n  .quiz select {\n    font-size: var(---font-size-h4--desktop);\n  }\n}\n.quiz select:not(input) {\n  border-bottom-color: transparent;\n  padding-left: 0;\n  padding-right: 0;\n  margin-bottom: 0.5em;\n}\n.quiz select::placeholder {\n  color: var(---color-text--light);\n  opacity: 1;\n}\n.quiz select:not([disabled]):focus {\n  border: 0;\n  border-bottom: 1px solid var(---color-text--light);\n  outline: none;\n}\n.quiz select.input--rounded {\n  border-radius: var(---input-border-radius);\n}\n.quiz option,\n.quiz optgroup {\n  font-size: 1rem;\n}\n.quiz .select-wrapper .select {\n  color: var(---color-text--dark);\n  border: 1px solid var(---color-line);\n  border-radius: 0;\n}\n.quiz .form__actions {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 10px;\n  text-align: center;\n}\n.quiz .form__actions .button {\n  display: inline-flex;\n}\n.quiz fieldset {\n  display: block;\n  appearance: none;\n  border: none;\n  margin: 0;\n  padding: 0;\n}\n\n.quiz img, .quiz .img {\n  width: 100%;\n  vertical-align: top;\n}\n\n/* 5. Layout */\nhtml.supports-no-cookies .supports-no-cookies {\n  display: none;\n}\nhtml.supports-cookies .supports-cookies {\n  display: none;\n}\n\n/* 6. Sections */\n.quiz {\n  /* ----- Days ----- */\n  /* ----- Per Day Labels ----- */\n}\n.quiz .section__header .heading span {\n  color: #fff;\n}\n.quiz .section__header .text--large span {\n  color: var(---color--highlight);\n}\n.quiz .feeding-calculator {\n  --block-border-radius: 20px;\n  --text-color: #2E2E2E;\n  --heading-color: #2E2E2E;\n  display: block;\n  color: #2E2E2E;\n  max-width: 800px;\n  margin: auto;\n  padding: 40px;\n  background: #fff;\n  border-radius: var(--block-border-radius);\n}\n.quiz .feeding-calculator .text--subdued {\n  color: RGB(var(---color-text--light--rgb));\n}\n.quiz .feeding-calculator__header {\n  display: flex;\n  width: 100%;\n  justify-content: center;\n  align-items: center;\n  gap: 2em;\n}\n@media (max-width: 800px) {\n  .quiz .feeding-calculator__header {\n    flex-direction: column;\n    gap: 1.5em;\n  }\n}\n.quiz .feeding-calculator__body hr {\n  width: 400px;\n  margin: 2em auto;\n}\n.quiz .feeding-calculator__footer {\n  margin-top: 30px;\n  text-align: center;\n}\n.quiz .feeding-calculator-days {\n  display: flex;\n  gap: 20px;\n  justify-content: center;\n  flex-wrap: wrap;\n  margin-top: 40px;\n}\n.quiz .feeding-calculator-day {\n  flex: 1 0 20%;\n  padding: 20px 10px;\n  border: 1px solid var(---color-line--light);\n  text-align: center;\n  border-radius: var(--block-border-radius);\n}\n.quiz .feeding-calculator-day p {\n  margin: 0;\n}\n@media (max-width: 800px) {\n  .quiz .feeding-calculator-day {\n    min-width: 40%;\n  }\n}\n@media (max-width: 400px) {\n  .quiz .feeding-calculator-day {\n    min-width: 100%;\n  }\n}\n.quiz .feeding-calculator-day__image img {\n  max-width: 70px;\n  margin: 20px 0;\n}\n.quiz .feeding-calculator-day__amounts {\n  margin-top: 0.5em;\n}\n.quiz .feeding-calculator-day__amounts > div {\n  line-height: 1.4;\n}\n.quiz .feeding-calculator-perday {\n  display: flex;\n  gap: 10px;\n  align-items: center;\n}\n@media (max-width: 800px) {\n  .quiz .feeding-calculator-perday {\n    flex-direction: column;\n  }\n}\n.quiz .feeding-calculator-perday__label {\n  margin: 0;\n}\n.quiz .feeding-calculator-perday__quantity {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 0 0.5em;\n  height: 50px;\n  font-size: 1.4em;\n  letter-spacing: -0.1em;\n  font-weight: var(---font-weight-body--bold);\n  /* Light Gray */\n  border: 1px solid #E6E6E6;\n  border-radius: 8px;\n}\n\n.quiz {\n  /* ----- Products ----- */\n  /* ----- Product ----- */\n  /* ----- Inner ----- */\n  /* ----- Checkbox ----- */\n  /* ----- View Details ----- */\n  /* ----- Sticky Form ----- */\n  /* ----- Quiz Results - Dogs ----- */\n  /* ----- Quiz Results - Dog ----- */\n}\n.quiz .quiz-results-product__footer {\n  min-height: 50px;\n}\n.quiz quiz-results-products .gallery__list-wrapper {\n  padding: var(--vertical-breather) 0;\n}\n@media only screen and (min-width: 1201px) {\n  .quiz quiz-results-products .gallery__list {\n    justify-content: center;\n  }\n}\n.quiz quiz-results-products.quiz-results-products--disabled quiz-results-product:not(.quiz-results-product--selected) {\n  opacity: 0.5;\n  pointer-events: none;\n}\n.quiz .quiz-results-product {\n  --quiz-results-product-color: var(---color--highlight);\n  --section-block-background: var(---background-color--content-1--rgb);\n  --text-color: RGB(var(---color-text--dark--rgb));\n  position: relative;\n  display: flex;\n  flex-direction: column;\n  justify-content: space-between;\n  width: 350px;\n  background: RGB(var(--section-block-background));\n  color: var(--text-color);\n  box-shadow: 0 0 5px rgba(0, 0, 0, 0.15);\n  border-radius: 8px;\n  transform: scale3d(1);\n  transition: transform 0.25s ease-in-out, box-shadow 0.25s ease-in-out, opacity 0.25s ease-in-out;\n}\n.quiz .quiz-results-product .quiz-ribbon--recommended {\n  display: none;\n}\n.quiz .quiz-results-product .quiz-results-product-button--soldout {\n  display: none;\n}\n.quiz .quiz-results-product .quiz-results-product-button--add-to-cart {\n  display: flex;\n}\n.quiz .quiz-results-product.quiz-results-product--recommended {\n  transform: scale(1.05);\n}\n.quiz .quiz-results-product.quiz-results-product--recommended:hover {\n  transform: scale(1.075) !important;\n}\n.quiz .quiz-results-product.quiz-results-product--recommended .quiz-ribbon--recommended,\n.quiz .quiz-results-product.quiz-results-product--recommended .quiz-results-tag--recommended {\n  display: inline-flex;\n}\n.quiz .quiz-results-product.quiz-results-product--soldout .quiz-results-tag--soldout {\n  display: inline-flex;\n}\n.quiz .quiz-results-product.quiz-results-product--soldout .quiz-results-tag__inner {\n  display: inline-block;\n}\n.quiz .quiz-results-product.quiz-results-product--soldout .quiz-results-product-button--soldout {\n  display: flex;\n}\n.quiz .quiz-results-product.quiz-results-product--soldout .quiz-results-product-button--add-to-cart {\n  display: none;\n}\n.quiz .quiz-results-product:hover {\n  transform: scale(1.025);\n  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);\n  opacity: 1 !important;\n}\n.quiz .quiz-results-product:not(:first-child) {\n  margin-left: 30px;\n}\n.quiz .quiz-results-product:before {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  margin: auto;\n  display: inline-block;\n  content: \"\";\n  width: 140px;\n  height: 140px;\n  background: var(--quiz-results-product-color);\n  z-index: 0;\n  outline: 3px solid var(--quiz-results-product-color);\n  border-radius: 100%;\n  transform: translateY(-15%);\n}\n.quiz .quiz-results-product:not(.quiz-results-product--selected) .quiz-results-product__checkbox-button:hover .quiz-results-product__checkbox-input {\n  background-color: RGBA(var(---color--brand-2--rgb), 0.25);\n}\n.quiz .quiz-results-product:not(.quiz-results-product--selected) .quiz-results-product__checkbox-button:hover .quiz-results-product__checkbox-input:before {\n  opacity: 1;\n}\n.quiz .quiz-results-product .gallery__figure {\n  padding: 30px;\n}\n.quiz .quiz-results-product .price--highlight {\n  color: var(---color-price);\n}\n.quiz .quiz-results-product .quiz-results-product__checkbox-label {\n  font-weight: var(---font-weight-body--bold);\n}\n@media only screen and (max-width: 740px) {\n  .quiz .quiz-results-product {\n    max-width: 80vw;\n  }\n}\n.quiz .quiz-results-product .quiz-results-product__tags {\n  margin: 1em 0;\n}\n.quiz .quiz-results-product .quiz-results-tag {\n  padding: 0.5em 1.25em;\n  border-radius: 100px;\n  display: none;\n  font-weight: bold;\n}\n.quiz .quiz-results-product .quiz-results-tag.quiz-results-tag--recommended {\n  background: RGB(var(---color--highlight--rgb));\n}\n.quiz .quiz-results-product .quiz-results-tag.quiz-results-tag--soldout {\n  background: RGB(var(---color--danger--rgb));\n  color: RGB(var(---color-text--reversed--rgb));\n}\n.quiz .quiz-results-product .quiz-results-tag__inner {\n  display: inline-block;\n}\n.quiz .quiz-results-product--selected .quiz-results-product__checkbox-input {\n  background-color: RGB(var(---color--brand-2--rgb));\n}\n.quiz .quiz-results-product--selected .quiz-results-product__checkbox-input:before {\n  opacity: 1;\n}\n.quiz .quiz-results-product__inner {\n  position: relative;\n  z-index: 1;\n}\n.quiz .quiz-results-product__header {\n  position: relative;\n  padding-bottom: 80px;\n  background: var(--quiz-results-product-color);\n  border-top-left-radius: 8px;\n  border-top-right-radius: 8px;\n}\n.quiz .quiz-results-product__footer {\n  border-top: 1px solid var(---color-line--light);\n}\n.quiz .quiz-results-product__image {\n  position: absolute;\n  left: 0;\n  right: 0;\n  top: 0;\n  z-index: 1;\n  margin: auto;\n  transform: translateY(-15%);\n  width: 140px;\n  height: 140px;\n  border: 10px solid transparent;\n  border-radius: 100%;\n  object-fit: cover;\n}\n.quiz .quiz-results-product__checkbox-button {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  gap: 10px;\n  min-height: 50px;\n  width: 100%;\n}\n.quiz .quiz-results-product__checkbox {\n  display: flex;\n  align-items: center;\n  gap: 0.5em;\n}\n.quiz .quiz-results-product__checkbox-input {\n  display: flex;\n  width: 24px;\n  height: 24px;\n  border-radius: 4px;\n  background-color: RGB(var(---color-line--light--rgb));\n  transition: 0.25s background-color ease-in-out;\n}\n.quiz .quiz-results-product__checkbox-input:before {\n  content: \"\";\n  display: block;\n  flex: 1 0 auto;\n  background-image: url(\"data:image/svg+xml,%3Csvg width='16' height='13' viewBox='0 0 16 13' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M15 1L5.375 12L1 7' stroke='white' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E%0A\");\n  background-repeat: no-repeat;\n  background-position: center;\n  opacity: 0;\n  transition: 0.25s opacity ease-in-out;\n}\n.quiz .quiz-results-product__view-details {\n  display: flex;\n  justify-content: center;\n  text-align: center;\n  margin-top: 12px;\n}\n.quiz .quiz-results-product__details {\n  position: relative;\n  margin: 0;\n  padding: 50px 20px 20px;\n  text-align: center;\n  background: var(---background-color--content-1);\n}\n.quiz .quiz-ribbon {\n  --ribbon-color: var(--section-block-background);\n  display: inline-flex;\n  justify-content: center;\n  align-items: center;\n  position: absolute;\n  width: 36px;\n  height: 55px;\n  font-size: 24px;\n  font-weight: 700;\n  background: RGB(var(--ribbon-color));\n}\n.quiz .quiz-ribbon.quiz-ribbon--number {\n  --ribbon-color: var(--section-block-background);\n  left: 30px;\n}\n.quiz .quiz-ribbon.quiz-ribbon--recommended {\n  --ribbon-color: var(---color--highlight--rgb);\n  right: 30px;\n}\n.quiz .quiz-ribbon:after {\n  bottom: 0;\n  left: 50%;\n  border: solid transparent;\n  content: \"\";\n  height: 0;\n  width: 0;\n  position: absolute;\n  pointer-events: none;\n  border-color: rgba(136, 183, 213, 0);\n  border-bottom-color: var(--quiz-results-product-header-color);\n  border-width: 16px;\n  margin-left: -16px;\n  transform: scaleY(0.7);\n  transform-origin: bottom;\n}\n.quiz .quiz-ribbon > span {\n  margin-top: -5px;\n  text-align: center;\n  letter-spacing: -0.15em;\n  width: 100%;\n}\n.quiz .quiz-ribbon .quiz-ribbon__bottom svg {\n  position: absolute;\n  width: 100%;\n  bottom: 2px;\n  left: 0;\n  right: 0;\n  transform: translateY(100%);\n}\n.quiz .quiz-ribbon .quiz-ribbon__bottom svg * {\n  fill: RGB(var(--ribbon-color));\n}\n.quiz .quiz-sticky-form {\n  top: unset;\n  bottom: 0;\n  padding: 20px 0;\n  background: RGB(var(--background));\n  border-top: RBB(var(--border-color));\n}\n@media only screen and (min-width: 1001px) {\n  .quiz .quiz-sticky-form {\n    padding: 30px 0;\n  }\n}\n.quiz .quiz-sticky-form .quiz-sticky-form__title {\n  display: inline-block;\n  margin-bottom: 0.25em;\n  margin-top: 0;\n}\n.quiz .quiz-sticky-form .quiz-sticky-form__title span, .quiz .quiz-sticky-form .quiz-sticky-form__title strong {\n  color: RGB(var(---color--highlight--rgb));\n}\n.quiz .quiz-sticky-form .unit-price-measurement {\n  color: #9a9a9a;\n}\n.quiz .quiz-sticky-form .unit-price-measurement {\n  line-height: 1;\n  vertical-align: baseline;\n}\n.quiz .quiz-sticky-form .unit-price-measurement a {\n  color: var(---color--highlight);\n  text-decoration: underline;\n}\n.quiz .quiz-sticky-form .unit-price-measurement .unit-price-measurement__link {\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  margin-left: 0.5em;\n}\n@media (max-width: 1000px) {\n  .quiz .quiz-sticky-form .product-sticky-form__content-wrapper {\n    margin-bottom: 20px;\n  }\n}\n.quiz .quiz-sticky-form .product-sticky-form__payment-container {\n  display: flex;\n  gap: 15px;\n}\n@media only screen and (max-width: 740px) {\n  .quiz .quiz-sticky-form .product-sticky-form__payment-container {\n    flex-direction: column;\n  }\n}\n@media only screen and (max-width: 740px) {\n  .quiz .quiz-sticky-form .button-checkout {\n    width: 100%;\n  }\n}\n.quiz quiz-results-dog {\n  display: none;\n}\n.quiz quiz-results-dog.quiz-results-dog--active {\n  display: block;\n}\n.quiz quiz-results-dog .quiz-results-dog__header {\n  text-align: center;\n  padding: var(--vertical-breather-tight) 0;\n}\n.quiz quiz-results-dog .quiz-results-dog__title span {\n  color: RGB(var(---color--highlight--rgb));\n}\n.quiz quiz-results-dog .quiz-results-dog__content {\n  margin: var(--container-gutter) 0;\n}\n\n/* 7. Page-Specific Styles */\n/* 8. Components */\n.product-sticky-form button,\n.product-sticky-form .select {\n  min-height: var(--button-height);\n  height: var(--button-height);\n}\n\n@media only screen and (min-width: 1001px) {\n  body.on-quiz .quiz.drawer {\n    justify-content: flex-end;\n    top: auto;\n    bottom: 0;\n    max-height: var(--quiz-drawer-height);\n  }\n}\n\n.quiz--flow {\n  position: relative;\n  color: RGB(var(--text-color));\n}\n\n.quiz--account .quiz-navigation__inner {\n  align-items: center;\n}\n\n.quiz-navigation {\n  position: sticky;\n  z-index: 4;\n  top: 0;\n  top: var(--header-height);\n  left: 0;\n  right: 0;\n  width: 100%;\n  background: var(---background-color--content-reversed-1);\n  box-shadow: 0px 4px 12px 4px rgba(0, 0, 0, 0.25);\n  color: RGB(var(---color-text--reversed--rgb));\n  transition: transform 0.25s;\n}\n\n.quiz-navigation__inner {\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-start;\n  max-width: var(--container-max-width);\n  padding-left: var(--container-gutter);\n  padding-right: var(--container-gutter);\n  margin: auto;\n}\n@media only screen and (max-width: 740px) {\n  .quiz-navigation__inner {\n    align-items: center;\n  }\n}\n\n.quiz-navigation__actions-left,\n.quiz-navigation__actions-right {\n  flex: 1 0 30%;\n}\n@media only screen and (max-width: 740px) {\n  .quiz-navigation__actions-left,\n  .quiz-navigation__actions-right {\n    top: 20px;\n  }\n}\n\n.quiz-navigation__center {\n  align-items: center;\n}\n\n.quiz-navigation-button {\n  display: flex;\n  align-items: center;\n  gap: 0.5em;\n  padding: 1em 0;\n  font-weight: var(---font-weight-body--bold);\n}\n@media only screen and (max-width: 740px) {\n  .quiz-navigation-button {\n    padding: 0;\n  }\n}\n.quiz-navigation-button:focus, .quiz-navigation-button:hover {\n  opacity: 0.7;\n}\n.quiz-navigation-button.quiz-navigation-button--hidden {\n  display: none;\n}\n\n.quiz-navigation__actions-left {\n  display: flex;\n  justify-content: flex-start;\n  text-align: left;\n}\n@media only screen and (max-width: 740px) {\n  .quiz-navigation__actions-left {\n    position: absolute;\n    left: var(--container-gutter);\n  }\n}\n\n.quiz-navigation__actions-right {\n  display: flex;\n  justify-content: flex-end;\n  text-align: right;\n}\n@media only screen and (max-width: 740px) {\n  .quiz-navigation__actions-right {\n    position: absolute;\n    right: var(--container-gutter);\n  }\n}\n\n.quiz-navigation__logo {\n  padding: 5px 0;\n}\n@media only screen and (max-width: 740px) {\n  .quiz-navigation__logo {\n    padding-top: 20px;\n  }\n}\n.quiz-navigation__logo img {\n  max-width: 120px;\n  margin-bottom: 10px;\n}\n@media only screen and (max-width: 740px) {\n  .quiz-navigation__logo img {\n    max-width: 100px;\n  }\n}\n@media only screen and (max-width: 740px) {\n  .quiz-navigation__logo {\n    padding-top: 20px;\n    position: absolute;\n    top: 0;\n  }\n  .quiz-navigation__logo a:hover {\n    opacity: 0.75;\n  }\n  .quiz-navigation__logo img,\n  .quiz-navigation__logo svg {\n    width: 100px;\n  }\n}\n\n.quiz-navigation__progress {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  padding: 30px 0 40px;\n  max-width: 630px;\n  width: 100%;\n}\n@media only screen and (min-width: 741px) {\n  .quiz-navigation__progress {\n    padding-top: 15px;\n  }\n}\n@media only screen and (max-width: 740px) {\n  .quiz-navigation__progress {\n    padding-top: 60px;\n    padding-bottom: 10px;\n  }\n}\n\n.quiz-navigation__actions-left,\n.quiz-navigation__actions-right {\n  top: 14px;\n}\n\n/* ----- Progress Bar ----- */\n.quiz-progress-bar {\n  position: relative;\n  max-width: 630px;\n  width: 100%;\n}\n\n.quiz-progress-bar__label {\n  position: absolute;\n  max-width: 100px;\n  text-align: center;\n  padding-top: 12px;\n}\n@media only screen and (max-width: 740px) {\n  .quiz-progress-bar__label {\n    display: none;\n  }\n}\n@media only screen and (min-width: 741px) {\n  .quiz-progress-bar__label {\n    padding-top: 24px;\n  }\n}\n\n.quiz-progress-bar__label-start {\n  text-align: left;\n  left: 0;\n}\n@media only screen and (min-width: 741px) {\n  .quiz-progress-bar__label-start {\n    transform: translateX(-50%);\n  }\n}\n\n.quiz-progress-bar__label-middle {\n  left: 0;\n  right: 0;\n  margin: auto;\n}\n\n.quiz-progress-bar__label-end {\n  text-align: right;\n  right: 0;\n}\n@media only screen and (min-width: 741px) {\n  .quiz-progress-bar__label-end {\n    transform: translateX(50%);\n  }\n}\n\n.quiz-progress-bar__sections {\n  display: flex;\n  justify-content: stretch;\n  height: 12px;\n  transform: translateY(-12px);\n}\n@media only screen and (max-width: 740px) {\n  .quiz-progress-bar__sections {\n    height: 8px;\n    transform: translateY(-8px);\n  }\n}\n\n.quiz-progress-bar__section {\n  width: 100%;\n  text-align: center;\n  position: relative;\n}\n\n.quiz-progress-bar__section__label {\n  position: absolute;\n  left: 0;\n  right: 0;\n  bottom: -5px;\n  transform: translateY(100%);\n}\n@media only screen and (max-width: 740px) {\n  .quiz-progress-bar__section__label {\n    display: none;\n  }\n}\n@media only screen and (min-width: 741px) {\n  .quiz-progress-bar__section__label {\n    padding-top: 8px;\n  }\n}\n\n.quiz-progress-bar__dot {\n  position: absolute;\n  top: 0;\n  left: calc(100% * var(--quiz-progress));\n  z-index: 1;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 32px;\n  height: 32px;\n  transform: translate(-50%, -25%);\n  vertical-align: top;\n  transition: 0.25s left;\n  background: var(---color--highlight);\n  border-radius: 100%;\n}\n.quiz-progress-bar__dot svg {\n  width: 24px;\n  opacity: 0.75;\n}\n.quiz-progress-bar__dot svg * {\n  fill: #000;\n}\n@media only screen and (max-width: 740px) {\n  .quiz-progress-bar__dot {\n    width: 18px;\n    height: 18px;\n    border-radius: 100%;\n    background: var(---color--brand-1);\n  }\n  .quiz-progress-bar__dot svg {\n    display: none;\n  }\n}\n\n.quiz-progress-bar__progress-track {\n  position: relative;\n  display: block;\n  height: 12px;\n  box-shadow: 0 0 0 1px var(---color--brand-3) inset;\n  border-radius: 24px;\n  overflow: hidden;\n}\n@media only screen and (max-width: 740px) {\n  .quiz-progress-bar__progress-track {\n    height: 8px;\n  }\n}\n\n.quiz-progress-bar__section__divider {\n  display: block;\n  width: 1px;\n  height: 12px;\n  background: var(---color--brand-3);\n  transform: rotate(15deg);\n}\n@media only screen and (max-width: 740px) {\n  .quiz-progress-bar__section__divider {\n    height: 8px;\n  }\n}\n\n.quiz-progress-bar__progress {\n  position: absolute;\n  top: 0;\n  left: 0;\n  height: 100%;\n  width: 100%;\n  background: var(---color--brand-1);\n  transform: scaleX(var(--quiz-progress));\n  transform-origin: left;\n  transition: transform 0.25s;\n}\n@media only screen and (max-width: 740px) {\n  .quiz-progress-bar__progress {\n    padding: 10px 0;\n  }\n}\n\nbody.on-quiz .shopify-section--announcement-bar,\nbody.on-quiz store-header {\n  display: none !important;\n}\n\n.quiz {\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  --quiz-navigation-height: 104px;\n  /* ----- Basic Styling ----- */\n  /* ----- Sub Navigation ----- */\n  /* ----- Steps ----- */\n  /* ----- Tabs ----- */\n  /* ----- Step Content Elements ----- */\n  /* ----- Inputs ----- */\n  /* ----- Decoration ----- */\n}\n@media only screen and (max-width: 740px) {\n  .quiz {\n    --quiz-navigation-height: 85px;\n  }\n}\n.quiz.quiz--flow {\n  background: var(---background-color--content-reversed-1);\n  --text-color: var(---color-text--reversed--rgb);\n}\n.quiz.quiz--account .quiz-modal-footer {\n  display: none;\n}\n.quiz.quiz--account .quiz-navigation__center {\n  visibility: hidden;\n  pointer-events: none;\n}\n.quiz.quiz--account .quiz-steps__inner .quiz-step {\n  justify-content: center;\n  position: absolute;\n}\n.quiz.quiz--has-navigation {\n  min-height: calc(100vh - var(--header-height) - var(--quiz-navigation-height));\n}\n.quiz quiz-steps.quiz-steps {\n  display: block;\n}\n.quiz .quiz-steps__inner {\n  position: relative;\n  overflow: hidden;\n  min-height: calc(100vh - var(--quiz-navigation-height) - 60px);\n}\n@media only screen and (max-width: 1000px) {\n  .quiz .quiz-steps__inner .quiz-step {\n    position: static;\n  }\n}\n.quiz .quiz-sub-navigation {\n  display: block;\n  position: sticky;\n  top: calc(var(--header-height) + var(--quiz-navigation-height) - 10px);\n  z-index: 3;\n  width: 100%;\n  background: linear-gradient(0deg, rgba(var(---background-color--content-reversed-1--rgb), 0) 0%, rgba(var(---background-color--content-reversed-1--rgb), 1) 90%);\n  pointer-events: none;\n  transition: transform 0.25s;\n}\n@media only screen and (max-width: 740px) {\n  .quiz .quiz-sub-navigation {\n    padding-bottom: 40px;\n  }\n}\n@media only screen and (min-width: 741px) {\n  .quiz .quiz-sub-navigation {\n    padding-top: 10px;\n  }\n}\n.quiz .quiz-sub-navigation.quiz-sub-navigation--hidden {\n  transform: translateY(-100%);\n}\n.quiz .quiz-step {\n  display: none;\n  flex-direction: column;\n  justify-content: center;\n  visibility: hidden;\n  position: absolute;\n  width: 100%;\n  height: 100%;\n  left: 0;\n  right: 0;\n}\n@media only screen and (max-width: 740px) {\n  .quiz .quiz-step {\n    overflow-y: scroll;\n    justify-content: center;\n  }\n}\n.quiz .quiz-step--home {\n  overflow: hidden;\n}\n.quiz .quiz-step--home .quiz-step__footer {\n  background: none;\n}\n.quiz .quiz-step--active {\n  display: flex;\n  visibility: visible;\n}\n.quiz .quiz-step--animating {\n  display: flex;\n}\n@media only screen and (max-width: 1000px) {\n  .quiz .quiz-step--leaving {\n    display: flex;\n  }\n}\n.quiz .quiz-step__inner {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  position: relative;\n  z-index: 1;\n}\n.quiz .quiz-step__inner .h1 {\n  line-height: 1;\n}\n.quiz .quiz-step__header {\n  position: absolute;\n  top: 0;\n  max-width: var(--container-max-width);\n  width: 100%;\n  margin: 0 auto auto auto;\n}\n.quiz .quiz-step__body {\n  text-align: center;\n  padding: 20px var(--container-gutter);\n}\n.quiz .quiz-step__body > .quiz-step-actions {\n  flex-direction: column;\n  align-items: center;\n}\n@media only screen and (min-width: 1401px) {\n  .quiz .quiz-step__body {\n    padding-top: 5px;\n    padding-bottom: 0px;\n  }\n}\n.quiz .quiz-step__body--forms {\n  width: 100%;\n}\n.quiz .quiz-step__body--forms .quiz-step-description {\n  max-width: 800px;\n  margin: 20px auto 40px;\n}\n.quiz .quiz-step__body--forms + .quiz-step__footer .quiz-step-actions__inner {\n  max-width: 500px;\n  margin: 20px auto;\n}\n.quiz .quiz-step__body--narrow {\n  max-width: 600px;\n}\n.quiz .quiz-step__footer {\n  position: sticky;\n  bottom: 0;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  gap: 10px;\n  width: 100%;\n  padding: 20px var(--container-gutter);\n  margin-top: auto;\n  text-align: center;\n  background: linear-gradient(180deg, rgba(var(---background-color--content-reversed-1--rgb), 0) 0%, rgba(var(---background-color--content-reversed-1--rgb), 1) 20%);\n}\n@media only screen and (max-width: 1000px) {\n  .quiz .quiz-step__footer .button {\n    width: 100%;\n  }\n  .quiz .quiz-step__footer .quiz-step-actions {\n    order: 1;\n  }\n  .quiz .quiz-step__footer .quiz-step-actions-account {\n    order: 0;\n  }\n}\n.quiz .quiz-customer-forms {\n  display: flex;\n  justify-content: center;\n  gap: 30px;\n}\n@media only screen and (max-width: 1000px) {\n  .quiz .quiz-customer-forms {\n    flex-direction: column;\n  }\n}\n.quiz .quiz-customer-form {\n  --block-border-radius: 20px;\n  --section-block-background: var(---background-color--content-reversed-3--rgb);\n  background: var(---background-color--content-reversed-3);\n  border-radius: var(--block-border-radius);\n  display: flex;\n  flex-direction: column;\n  justify-content: stretch;\n  max-width: 490px;\n  width: 100%;\n  padding: 30px;\n}\n.quiz .quiz-customer-form form {\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n}\n.quiz .quiz-customer-form .input {\n  margin-top: 20px;\n  margin-bottom: 20px;\n}\n.quiz .quiz-customer-form .input .input__field {\n  padding: 0.25em 0.5em;\n}\n.quiz .quiz-customer-form .input:last-child {\n  margin-top: 0;\n  margin-bottom: 0;\n  padding-top: 0;\n}\n@media (max-width: 1000px) {\n  .quiz .quiz-customer-form {\n    margin: auto;\n  }\n}\n.quiz .quiz-customer-form .input__field {\n  font-size: 20px !important;\n  padding: 0.5em 1em;\n}\n.quiz .quiz-customer-form__step {\n  padding: 1em 0;\n}\n.quiz .quiz-customer-form__step .input--radio label {\n  font-size: var(---font-size-body-large--desktop);\n}\n.quiz .quiz-customer-form__step .kyc-option-radio {\n  margin: 0.5em 0 !important;\n}\n.quiz .quiz-customer-form__step .input__field {\n  padding-left: 0 !important;\n}\n.quiz .quiz-step__tabs:before, .quiz .quiz-step__tabs:after {\n  display: inline-block;\n  position: absolute;\n  top: 0;\n  z-index: 3;\n  content: \"\";\n  width: 50px;\n  height: 100%;\n  pointer-events: none;\n}\n.quiz .quiz-step__tabs:before {\n  left: 0;\n  background: linear-gradient(270deg, rgba(var(---background-color--content-reversed-1--rgb), 0), rgba(var(---background-color--content-reversed-1--rgb), 1) 100%);\n}\n.quiz .quiz-step__tabs:after {\n  right: 0;\n  background: linear-gradient(90deg, rgba(var(---background-color--content-reversed-1--rgb), 0), rgba(var(---background-color--content-reversed-1--rgb), 1) 100%);\n}\n.quiz .quiz-step__tabs-inner {\n  display: flex;\n  gap: 20px;\n  scroll-snap-type: x mandatory;\n  scroll-snap-align: center;\n  overflow: scroll;\n  display: flex;\n  align-items: flex-start;\n  justify-content: center;\n  padding: 10px 50px;\n  gap: 30px;\n  width: 100%;\n}\n.quiz .quiz-step__tabs-inner::-webkit-scrollbar {\n  display: none;\n}\n@media only screen and (max-width: 1000px) {\n  .quiz .quiz-step__tabs-inner {\n    padding: 0;\n  }\n}\n.quiz .quiz-step__tabs-inner .quiz-step-tab {\n  display: flex;\n}\n.quiz .quiz-step-tab {\n  display: block;\n  scroll-snap-align: center;\n  white-space: nowrap;\n  pointer-events: all;\n}\n@media only screen and (max-width: 1000px) {\n  .quiz .quiz-step-tab {\n    padding: 0 20px;\n  }\n}\n.quiz .quiz-step-logo {\n  max-width: 200px;\n  margin-top: 50px;\n  margin-bottom: 30px;\n}\n@media only screen and (min-width: 1001px) {\n  .quiz .quiz-step-logo {\n    margin-top: 100px;\n    margin-bottom: 50px;\n  }\n}\n.quiz .quiz-step-icon {\n  width: 150px;\n  height: 150px;\n  margin-block-end: 20px;\n}\n@media (max-width: 700px) {\n  .quiz .quiz-step-icon .quiz-step-icon {\n    width: 120px;\n    height: 120px;\n    margin-bottom: -20px;\n  }\n}\n.quiz .quiz-step-title {\n  margin-bottom: 0.5em;\n  margin-top: 0;\n}\n.quiz .quiz-step-title strong, .quiz .quiz-step-title span {\n  color: RGB(var(---color--brand-1--rgb));\n}\n.quiz .quiz-step-description {\n  margin-top: 30px;\n  margin-bottom: 30px;\n}\n.quiz .quiz-step-line {\n  display: block;\n  margin: 10px 0;\n  line-height: 1.2;\n  font-size: var(---font-size-h4--mobile);\n  opacity: 1;\n  transition: transform 0.25s ease-in-out, opacity 0.25s ease-in-out;\n}\n@media only screen and (min-width: 741px) {\n  .quiz .quiz-step-line {\n    font-size: var(---font-size-h4--desktop);\n  }\n}\n.quiz .quiz-step-line select {\n  font-size: 1rem !important;\n}\n.quiz .quiz-step-line.quiz-step-line--hidden {\n  opacity: 0;\n  transform: translateY(-10%);\n  pointer-events: none;\n}\n.quiz .quiz-step-line > * {\n  display: inline-block;\n}\n.quiz .quiz-step-line .quiz-step-line__hint {\n  display: block;\n}\n.quiz .quiz-step-line__hint {\n  margin-top: 1em;\n  font-size: var(---font-size-body--desktop);\n}\n.quiz .quiz-step-hint {\n  margin: 30px auto;\n}\n@media only screen and (max-width: 740px) {\n  .quiz .quiz-step-hint {\n    margin: 0 auto;\n    max-width: 200px;\n  }\n}\n.quiz .quiz-step-actions {\n  display: flex;\n  flex-wrap: wrap;\n  justify-content: center;\n  gap: 1em;\n  width: 100%;\n  padding-top: 20px;\n  padding-bottom: 20px;\n}\n@media only screen and (min-width: 741px) {\n  .quiz .quiz-step-actions {\n    padding-top: 30px;\n  }\n}\n@media only screen and (min-width: 1401px) {\n  .quiz .quiz-step-actions {\n    padding-top: 5px;\n  }\n}\n.quiz .quiz-step-actions-account {\n  margin: 10px auto;\n}\n.quiz .quiz-step-form {\n  padding-bottom: var(--container-gutter);\n}\n.quiz .quiz-input {\n  display: inline-block;\n  background-color: transparent;\n  border: none;\n  min-width: 10px;\n  border-bottom: 2px solid var(---color--secondary);\n  margin: 10px 0;\n  font-weight: var(--text-font-bold-weight);\n  color: var(---color--highlight);\n  transition: 0.25s border-color;\n}\n.quiz .quiz-input::placeholder {\n  font-weight: var(--text-font-weight);\n  opacity: 1;\n}\n.quiz .quiz-input:focus, .quiz .quiz-input:hover {\n  outline: none;\n  border-color: var(---color--highlight);\n}\n.quiz select.quiz-input {\n  padding-right: 50px;\n  background-image: url(\"data:image/svg+xml,%3Csvg width='12' height='6' viewBox='0 0 12 6' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M1 0.5L6 5.5L11 0.5' stroke='%23E6E6E6' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E%0A\");\n  background-size: 15px;\n  background-repeat: no-repeat;\n  background-position: calc(100% - 10px);\n}\n.quiz quiz-step-dog-health .quiz-step-hint__final {\n  display: none;\n}\n.quiz quiz-step-dog-health .quiz-step-hint__in-progress {\n  display: block;\n}\n.quiz quiz-step-dog-health:last-of-type .quiz-step-hint__final {\n  display: block;\n}\n.quiz quiz-step-dog-health:last-of-type .quiz-step-hint__in-progress {\n  display: none;\n}\n.quiz .quiz-decoration {\n  content: \"\";\n  position: absolute;\n  z-index: 0;\n  pointer-events: none;\n}\n.quiz .quiz-decoration--1 {\n  width: 850px;\n  height: 100%;\n  left: 0;\n  top: 0;\n  bottom: 0;\n  z-index: 0;\n  transform: scale(1.1) translate(0%);\n}\n.quiz .quiz-decoration--1 svg {\n  transform: translateX(-40%);\n}\n.quiz .quiz-decoration--2 {\n  width: 850px;\n  height: 100%;\n  right: 0;\n  top: 0;\n  bottom: 0;\n  z-index: 0;\n  transform: scale(1.1) translate(0%);\n}\n.quiz .quiz-decoration--2 svg {\n  transform: translateX(30%) rotate(160deg);\n}\n@media only screen and (max-width: 740px) {\n  .quiz .quiz-decoration--1 {\n    width: 100%;\n    height: 400px;\n    left: 0;\n    top: auto;\n    bottom: 0;\n    z-index: 0;\n    transform-origin: 50% 50%;\n    transform: scale(1.2) translateX(0%) translateY(30%) rotate(230deg);\n  }\n  .quiz .quiz-decoration--1 svg {\n    transform: translateX(0);\n  }\n  .quiz .quiz-decoration--2 {\n    display: none;\n  }\n}\n\n/* ----- Tiles ----- */\n.quiz-tiles .quiz-tiles__container {\n  display: flex;\n  margin: var(--container-gutter) 0;\n  gap: 30px;\n  flex-wrap: wrap;\n  justify-content: center;\n}\n@media only screen and (max-width: 1000px) {\n  .quiz-tiles .quiz-tiles__container {\n    gap: 15px;\n  }\n}\n\n.quiz-tiles__input {\n  position: absolute !important;\n  overflow: hidden;\n  clip: rect(0 0 0 0);\n  height: 1px;\n  width: 1px;\n  max-width: 1px;\n  max-height: 1px;\n  font-size: 0;\n  margin: -1px;\n  padding: 0;\n  border: 0;\n}\n\n.quiz-tile {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  padding: 20px;\n  border: 2px solid var(---color--brand-3);\n  border-radius: 8px;\n  background-color: var(---color--brand-2);\n  transition: 0.25s border-color;\n  cursor: pointer;\n}\n.quiz-tile:hover {\n  background: var(---color--primary--dark);\n}\n.quiz-tile:focus {\n  border-color: var(---color--highlight);\n}\n.quiz-tile.quiz-tile--selected, .quiz-tile:focus {\n  border-color: var(---color--highlight);\n}\n.quiz-tile.quiz-tile--selected .quiz-tile__icon--default:not(:only-child), .quiz-tile:focus .quiz-tile__icon--default:not(:only-child) {\n  opacity: 0;\n}\n.quiz-tile.quiz-tile--selected .quiz-tile__icon--hover, .quiz-tile:focus .quiz-tile__icon--hover {\n  opacity: 1;\n}\n.quiz-tile .quiz-tile__icon {\n  position: relative;\n}\n.quiz-tile .quiz-tile__icon--hover {\n  position: absolute;\n  left: 0;\n  top: 0;\n  opacity: 0;\n}\n.quiz-tile .quiz-tile__text__hint {\n  display: block;\n}\n.quiz-tile .quiz-tile__text__title {\n  display: block;\n}\n@media only screen and (min-width: 741px) {\n  .quiz-tile .quiz-tile__text__hint {\n    display: none;\n  }\n}\n@media only screen and (max-width: 740px) {\n  .quiz-tile {\n    flex-direction: row;\n    gap: 20px;\n    width: 100%;\n    padding: 10px;\n  }\n  .quiz-tile .quiz-tile__text {\n    text-align: left;\n  }\n  .quiz-tile .quiz-tile__icon {\n    flex: 1 0 150px;\n    max-height: 100px;\n    max-width: 100px;\n    width: 100%;\n    height: 100%;\n  }\n  .quiz-tile .quiz-tile__icon img {\n    max-height: 100px;\n    max-width: 100px;\n    width: 100%;\n    height: 100%;\n  }\n}\n\n.quiz-tile__icon img {\n  height: 150px;\n  width: 150px;\n}\n\n.quiz-substep {\n  display: none;\n  visibility: hidden;\n}\n\n.quiz-substep--active {\n  display: block;\n  visibility: visible;\n}\n\n.quiz-substep--visible {\n  display: block;\n  visibility: visible;\n}\n\n.quiz-substep--animating {\n  display: block;\n}\n\nquiz-loading-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  z-index: 100;\n  width: 100vw;\n  height: 100vh;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background: var(---background-color--content-reversed-1);\n}\n\n.quiz-loading-overlay__inner {\n  text-align: center;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n}\n\n.quiz-loading-overlay__image {\n  max-width: 180px;\n}\n@media only screen and (min-width: 1001px) {\n  .quiz-loading-overlay__image {\n    max-width: 290px;\n  }\n}\n\n.quiz-loading-overlay__text .heading {\n  margin: 0;\n}\n\n.quiz {\n  /* ----- Quiz Elements ----- */\n  /* ----- Radio Buttons ----- */\n  /* ----- Radio Buttons ----- */\n  /* ----- HRs ----- */\n}\n.quiz .price {\n  font-weight: 500;\n  letter-spacing: 0;\n}\n.quiz .quiz-page-header {\n  text-align: center;\n  margin-bottom: 20px;\n}\n.quiz .quiz-page-footer {\n  text-align: center;\n  margin-bottom: 20px;\n  margin-top: 20px;\n}\n.quiz .quiz-terms {\n  margin-top: 20px;\n  max-width: 350px;\n  margin: auto;\n  text-align: center;\n}\n.quiz .quiz-sticky-form {\n  --background: var(---background-color--content-reversed-1--rgb);\n  --border-color: var(---color-text--rgb);\n  border-bottom: 0;\n}\n.quiz .input--sub-option {\n  margin: 0;\n  padding-left: 30px;\n  text-align: left;\n  margin: 0 !important;\n}\n.quiz .input--sub-option .nice-select, .quiz .input--sub-option select, .quiz .input--sub-option input {\n  font-size: var(---font-size-body--desktop) !important;\n}\n.quiz .input--sub-option input,\n.quiz .input--sub-option textarea,\n.quiz .input--sub-option select, .quiz .input--sub-option .select {\n  padding: 0.5em 3em 0.5em 1.4em;\n  display: block;\n  font-size: 18px;\n  background-color: rgba(var(--section-block-background), 0.8);\n  margin-bottom: 1em;\n  border-radius: 8px;\n  border: 1px solid rgba(var(--text-color), 0.25);\n}\n.quiz .input--sub-option input:hover,\n.quiz .input--sub-option textarea:hover,\n.quiz .input--sub-option select:hover, .quiz .input--sub-option .select:hover {\n  background-color: rgba(var(--section-block-background), 1);\n  border: 1px solid rgba(var(--text-color), 0.5);\n}\n.quiz .input--sub-option input:focus,\n.quiz .input--sub-option textarea:focus,\n.quiz .input--sub-option select:focus, .quiz .input--sub-option .select:focus {\n  border: 1px solid rgba(var(--text-color), 0.5);\n}\n.quiz .input--sub-option .select,\n.quiz .input--sub-option select {\n  background-image: var(---icon--chevron);\n  background-position: right 1em top 50%;\n  background-repeat: no-repeat;\n  background-size: 14px;\n}\n.quiz .input--radio {\n  margin: 0;\n  margin-bottom: 0.5em;\n}\n.quiz .input--radio label {\n  font-size: var(---font-size-label-radio--mobile);\n}\n@media (min-width: 1000px) {\n  .quiz .input--radio label {\n    font-size: var(---font-size-label-radio--desktop);\n  }\n}\n.quiz .input--radio input[type=radio] {\n  background: #fff;\n  position: relative;\n  top: -1px;\n}\n.quiz .input--radio input[type=radio]:after {\n  content: \"\";\n  display: inline-block;\n  position: absolute;\n  left: 0;\n  top: 0;\n  right: 0;\n  bottom: 0;\n  margin: auto;\n  color: #000;\n  width: 10px;\n  height: 10px;\n  border-radius: 100%;\n  background: currentColor;\n  transform: scale(0);\n  opacity: 0.5;\n  transition: transform 0.25s, opacity 0.25s;\n}\n.quiz .input--radio input[type=radio]:checked:after, .quiz .input--radio input[type=radio]:hover:after {\n  transform: scale(1);\n}\n.quiz .input--radio input[type=radio]:checked:after {\n  opacity: 1;\n}\n.quiz .input--radio input[type=radio]:checked + label {\n  font-weight: 700;\n}\n.quiz .input--radio .checkbox-container {\n  align-items: center;\n}\n.quiz .input--radio .checkbox {\n  width: 16px;\n  height: 16px;\n  border-radius: 100%;\n}\n@media (min-width: 1000px) {\n  .quiz .input--radio .checkbox {\n    width: 18px;\n    height: 18px;\n  }\n}\n.quiz .banner {\n  border-radius: 20px;\n  font-weight: var(---font-weight-body--bold);\n}\n.quiz .banner .banner__content a {\n  text-decoration: underline;\n}\n.quiz .banner.banner--success {\n  color: var(---color-text--strong);\n  background-color: var(---color--highlight);\n}\n.quiz .banner.banner--error {\n  color: var(---color-text--reversed-strong);\n  background-color: var(---color--danger);\n}\n\n/* 9. Apps  */\n/* 10. Utility Classes */\n/* 11. Third-Party Styles */\n/* 12. Animations */\n@keyframes fade-in {\n  0% {\n    opacity: 0;\n  }\n  100% {\n    opacity: 1;\n  }\n}\n@keyframes fade-out {\n  0% {\n    opacity: 1;\n  }\n  100% {\n    opacity: 0;\n  }\n}\n@keyframes rotate {\n  0% {\n    transform: rotate(0deg);\n  }\n  100% {\n    transform: rotate(360deg);\n  }\n}", "/*\n\n  1. Utilities\n  2. Responsive\n  3. UI Elements\n    3.1. Buttons\n\n*/\n\n/*  ==============================\n    1. Utilities\n    ============================== */\n\n@mixin hide-scrollbars() {\n  -ms-overflow-style: none;  /* Internet Explorer 10+ */\n  scrollbar-width: none;  /* Firefox */\n  &::-webkit-scrollbar {\n    display: none;  /* Safari and Chrome */\n  }\n}\n\n@mixin clearfix() {\n  &::after {\n    content: '';\n    display: table;\n    clear: both;\n  }\n\n  // sass-lint:disable\n  *zoom: 1;\n}\n\n@mixin visually-hidden() {\n  // sass-lint:disable no-important\n  position: absolute !important;\n  overflow: hidden;\n  clip: rect(0 0 0 0);\n  height: 1px;\n  width: 1px;\n  max-width: 1px;\n  max-height: 1px;\n  font-size: 0;\n  margin: -1px;\n  padding: 0;\n  border: 0;\n}\n\n@mixin visually-shown($position: inherit) {\n  // sass-lint:disable no-important\n  position: $position !important;\n  overflow: auto;\n  clip: auto;\n  width: auto;\n  height: auto;\n  margin: 0;\n}\n\n/*  ==============================\n    2. Responsive\n    ============================== */\n\n@mixin respond-to($media-query) {\n  $breakpoint-found: false;\n\n  @each $breakpoint in $breakpoints {\n    $name: nth($breakpoint, 1);\n    $declaration: nth($breakpoint, 2);\n\n    @if $media-query == $name and $declaration {\n      $breakpoint-found: true;\n\n      @media only screen and #{$declaration} {\n        @content;\n      }\n    }\n  }\n\n  @if $breakpoint-found == false {\n    @warn 'Breakpoint \"#{$media-query}\" does not exist';\n  }\n}\n\n/*================ Responsive Show/Hide Helper ================*/\n@mixin responsive-display-helper($breakpoint: '') {\n  // sass-lint:disable no-important\n  .#{$breakpoint}shown {\n    display: block !important;\n  }\n\n  .#{$breakpoint}hidden {\n    display: none !important;\n  }\n}\n\n\n/*================ Responsive Text Alignment Helper ================*/\n@mixin responsive-text-align-helper($breakpoint: '') {\n  // sass-lint:disable no-important\n  .#{$breakpoint}text-left {\n    text-align: left !important;\n  }\n\n  .#{$breakpoint}text-right {\n    text-align: right !important;\n  }\n\n  .#{$breakpoint}text-center {\n    text-align: center !important;\n  }\n}\n\n/*  ==============================\n    3. UI Elements\n    ============================== */\n\n/*  ------------------------------\n    3.1. Buttons\n    ------------------------------ */\n\n@mixin button-structure() {\n\n  // --button-text-color: var(---color--secondary--rgb);\n  // --button-text-color--hover: var(---color-text--reversed--rgb);\n  // --button-background: var(---color--secondary--rgb);\n  // --hover-effect-color: var(---color--secondary--rgb);\n\n  overflow: hidden;\n  position: relative;\n  display: inline-flex;\n  gap: 5px;\n  align-items: center;\n  justify-content: center;\n\n  margin: 0;\n  width: auto;\n  min-width: var(---button-min-width);\n\n  height: auto;\n  min-height: 0;\n  max-height: none;\n\n  border: 0;\n\n  font-family: var(---font-family-heading-alternate);\n  font-style: var(---font-style-heading-alternate);\n  font-weight: var(---font-weight-heading-alternate);\n\n  line-height: 1.2;\n  text-align: center;\n  text-transform: uppercase;\n\n  // text-transform: uppercase;\n  letter-spacing: var(---button-letter-spacing);\n  vertical-align: top;\n  // white-space: nowrap;\n\n  border-radius: var(--button-border-radius);\n  // background: transparent;\n\n  transition: all 0.2s ease-in-out;\n  appearance: none;\n\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n  font-smoothing: antialiased;\n\n  cursor: pointer;\n\n  padding: 1.6em 2.2em;\n  \n  margin-top: 0.25em;\n  margin-bottom: 0.25em;\n  margin-right: 0.5em;\n  margin-left: 0;\n\n\n  font-size: var(---font-size-button--mobile);\n  @include respond-to($small-up) {\n    font-size: var(---font-size-button--desktop);\n  }\n\n  &:last-child {\n    margin-right: 0 !important;\n  }\n  &:only-child {\n    margin-bottom: 0 !important;\n  }\n\n  // Sizes\n  &.button--tiny {\n    padding: 0.8em 1.2em;\n  }\n\n  &.button--small {\n    padding: 1.2em 1.8em;\n  }\n\n  &.button--large {\n    padding: 2em 2.8em;\n  }\n\n  &.button--huge {\n    padding: 1.4em 1.8em;\n    font-size: var(---font-size-button-large--mobile);\n    @include respond-to($medium-up) {\n      font-size: var(---font-size-button-large--mobile);\n    }\n  }\n\n  &.button--full {\n    display: flex;\n    width: 100%;\n    margin-left: 0 !important;\n    margin-right: 0 !important;\n  }\n\n  &.disabled,\n  &[disabled] {\n\n    opacity: 0.5;\n    cursor: not-allowed;\n\n  }\n\n  // Style\n\n  .loader-button__text {\n    gap: 0.5em;\n  }\n\n  svg {\n    position: relative;\n    top: -1px;\n    max-height: 16px;\n    fill: currentColor;\n    stroke: currentColor;\n  }\n\n  + .button {\n    margin-left: 0.25em;\n  }\n\n}\n\n@mixin button-style($color){\n\n  --button--background: var(---color--#{$color}--rgb);\n  --master-color--light--rgb: var(---color--#{$color}--light--rgb);\n  --master-color--dark--rgb: var(---color--#{$color}--dark--rgb);\n  --master-color-background--rgb: var(---background-color--#{$color}--rgb);\n\n  --button-background: var(--button--background);\n  --button-background--hover: var(--master-color--light--rgb);\n  --button-background--active: var(--master-color--dark--rgb);\n  --button-text-color: var(---color-text--reversed--rgb);\n  --button-text-color--hover: var(---color-text--reversed--rgb);\n\n  &:not(.button--inverted) {\n    --border-color: transparent;\n    --color-text--hover: var(---color-text--reversed--rgb);\n  }\n  \n  &:not([disabled]) {\n  \n    background-image: \n\n    linear-gradient(178deg, \n      rgb(var(--button-background)), \n      rgb(var(--button-background)) 10%, \n      rgba(var(--button-background--hover),1) 10%, \n      rgba(var(--button-background--hover),1) 100%),\n\n      linear-gradient(rgb(var(--button-background)), rgb(var(--button-background)));\n      \n  }\n\n  background-color: RGB(var(--button-background));\n  \n}\n\n@mixin button-style--inverted(){\n\n  background-color: transparent;\n  --button-text-color: var(--button--background);\n\n  box-shadow: 0 0 0 1px RGBA(var(--button--background), 1) inset;\n\n  &:not([disabled]) {\n\n    background-image: \n      linear-gradient(178deg, \n      rgba(var(--button--background), 0), \n      rgba(var(--button--background), 0) 10%, \n      rgba(var(--button--background), 1) 10%, \n      rgba(var(--button--background), 1) 100%),\n      linear-gradient(rgba(var(--button--background), 0), rgba(var(--button--background), 0));\n\n      --button-text-color: var(--button--background);\n\n    background-size: 100% 200%, 100% 100%;\n    background-position: 100% -100%, 100% 100%;\n    background-repeat: no-repeat;\n    transition: background-position 0.3s cubic-bezier(0.215, 0.61, 0.355, 1);\n    transform: translateZ(0); /* Make sure to promote the button on its own layer */\n\n    &:focus,\n    &:focus-within,\n    &:hover {\n\n      --button-text-color: var(---color-text--reversed--rgb);\n\n      background-position: 100% 25%, 100% 100%;\n\n    }\n\n    &:active {\n\n      background-position: 100% 25%, 100% 100%;\n\n    }\n\n  }\n\n}\n\n@mixin button-style--light(){\n\n  --button-background: var(--master-color-background--rgb);\n  --button-text-color: var(--button--background);\n\n  &:not([disabled]) {\n\n    background-image: \n\n    linear-gradient(178deg, \n      rgb(var(--button-background)), \n      rgb(var(--button-background)) 10%, \n      rgba(var(--button-background--hover),0.35) 10%, \n      rgba(var(--button-background--hover),0.35) 100%),\n\n      linear-gradient(rgb(var(--button-background)), rgb(var(--button-background)));\n\n    background-size: 100% 200%, 100% 100%;\n    background-position: 100% -100%, 100% 100%;\n    background-repeat: no-repeat;\n    transition: background-position 0.3s cubic-bezier(0.215, 0.61, 0.355, 1);\n    transform: translateZ(0); /* Make sure to promote the button on its own layer */\n\n    &:active,\n    &:focus,\n    &:focus-within,\n    &:hover {\n\n      // --button-background: var(--button--background);\n      // --button-text-color: var(---color-text--reversed--rgb);\n\n      background-position: 100% 25%,100% 100%;\n\n    }\n\n  }\n\n}\n\n/* ------------------------------\n   Headings\n   ------------------------------ */\n\n@mixin heading-style--1() {\n\n  \n\n}\n\n@mixin heading-style--2() {\n\n  font-family: var(---font-family-body);\n  font-style: var(---font-style-body);\n  // font-weight: var(---font-weight-body--bolder);\n\n  letter-spacing: var(---letter-spacing-body--mobile);\n  line-height: var(---line-height-body--mobile);\n\n  span.heading--alternate {\n    // color: var(---color-heading-2);\n    text-transform: none;\n    font-weight: var(---font-weight-body);\n  }\n\n  a {\n    // color: var(---color-link);\n  }\n\n  @include respond-to($small-up) {\n    letter-spacing: var(---letter-spacing-heading--desktop);\n    line-height: var(---line-height-heading--desktop);\n  }\n\n}\n\n\n/* ------------------------------\n   Labels\n   ------------------------------ */\n\n@mixin label-structure() {\n\n  // display: block;\n  // width: 100%;\n\n}\n\n@mixin label-style() {\n\n\n\n}\n\n/* ------------------------------\n   Inputs\n   ------------------------------ */\n\n@mixin input-structure() {\n\n  $border-color: var(---color--secondary);\n  $border-color-hover: var(---color--secondary-hover);\n\n  display: block;\n  // margin-bottom: 1em;\n  // width: 100%;\n  padding: 1em 1.4em;\n\n  font-size: var(---font-size-button--mobile);\n\n  border-width: 1px;\n\n  &:not([disabled]){\n    &:focus,\n    &:hover {\n      border-color: $border-color-hover;\n    }\n  }\n\n  @include respond-to($medium-up) {\n    font-size: var(---font-size-button--desktop);\n  }\n\n}\n\n@mixin input-style--1() {\n\n  border: 0;\n\n  letter-spacing: 0;\n  border-radius: 0;\n  box-shadow: none;\n\n  // padding-left: 0;\n  // padding-right: 0;\n\n  border-bottom: 1px solid var(---color-text--light);\n  height: unset;\n  line-height: unset;\n\n  font-size: var(---font-size-h4--mobile);\n  @include respond-to($small-up) {\n    font-size: var(---font-size-h4--desktop);\n  }\n\n  &:not(input) {\n    border-bottom-color: transparent;\n    padding-left: 0;\n    padding-right: 0;\n    margin-bottom: 0.5em;\n  }\n\n  &::placeholder {\n    color: var(---color-text--light);\n    opacity: 1;\n  }\n\n  &:not([disabled]){\n    &:focus {\n      border: 0;\n      border-bottom: 1px solid var(---color-text--light);\n      outline: none;\n    }\n  }\n\n  &.input--rounded {\n    border-radius: var(---input-border-radius);\n  }\n\n}\n\n@mixin input-style--2() {\n\n  border: 0;\n  padding: 0.5em 0.2em;\n\n  border-bottom: 1px solid var(---color--black);\n\n  font-size: var(---font-size-body--mobile);\n  @include respond-to($medium-up) {\n    font-size: var(---font-size-body--desktop);\n  }\n\n}\n\n/* ------------------------------\n   RTE\n   ------------------------------ */\n\n    @mixin link-style() {\n\n      &:after {\n\n        content: '';\n        display: block;\n        height: 2px;\n        width: 100%;\n        background: currentColor;\n        color: var(---color-link);\n        position: absolute;\n        bottom: 0;\n        left: 0;\n        right: 0;\n\n        transition: transform 0.25s;\n        transform-origin: left;\n        transform: scale(0);\n\n      }\n\n      &:hover {\n        color: var(---color-link--hover);\n        &:after {\n          transform: scale(1);\n        }\n      }\n\n    }\n\n/*  ------------------------------\n    3.3. Shopify\n    ------------------------------ */\n\n@mixin when-logged-in {\n\n  body.logged-in & {\n    @content;\n  }\n\n}\n\n@mixin when-logged-out {\n\n  body.logged-out & {\n    @content;\n  }\n\n}\n", ".quiz {\n\n  /*  ------------------------------\n    1. Inputs\n    ------------------------------ */\n\n  // 1.1. General\n\n  textarea,\n  select,\n  input[type=\"text\"],\n  input[type=\"number\"],\n  input[type=\"email\"],\n  input[type=\"tel\"],\n  input[type=\"password\"],\n  // input[type=\"search\"],\n  input[type=\"date\"] {\n\n    &[disabled] {\n      cursor: not-allowed;\n    }\n\n  }\n\n  .input {\n\n    margin-bottom: 0px;\n    margin-top: 30px;\n\n    .input__field {\n\n      margin-bottom: 0;\n      padding-left: 15px;\n      padding-right: 15px;\n\n      @include input-style--1();\n\n      &[required=\"required\"] {\n        +.input__label {\n          &:after {\n            content: \"*\";\n          }\n        }\n      }\n\n    }\n\n    .input__label {\n\n      left: 0;\n      transform: scale(.733) translateY(calc(-32px - 0.5em));\n\n      font-weight: var(---font-weight-body);\n      color: RGB(var(--text-color));\n\n      font-size: var(---font-size-body--mobile);\n\n      @include respond-to($small-up) {\n        font-size: var(---font-size-body--desktop);\n      }\n\n    }\n\n    .button {\n      margin: 0;\n    }\n\n  }\n\n\n  // 1.2. Selects\n\n  select {\n    \n    appearance: none;\n    background: transparent;\n    background-image: var(---icon--chevron-down);\n    background-position: right 1em top 50%;\n    background-repeat: no-repeat;\n    background-size: 14px;\n\n    @include input-style--1();\n\n  }\n\n  option,\n  optgroup {\n    font-size: 1rem;\n  }\n\n  .select-wrapper {\n\n    .select {\n\n      color: var(---color-text--dark);\n      border: 1px solid var(---color-line);\n      border-radius: 0;\n\n    }\n\n  }\n\n  .form__actions {\n\n    display: flex;\n    flex-direction: column;\n    align-items: center;\n    gap: 10px;\n\n    // margin-top: var(--vertical-breather);\n  \n    text-align: center;\n  \n    .button {\n      display: inline-flex;\n    }\n  \n  }\n\n\n  /*  ------------------------------\n      2. Labels\n      ------------------------------ */\n\n  label {\n    @include label-structure();\n  }\n\n  .label-style {\n    @include label-style();\n  }\n\n\n  /*  ------------------------------\n      3. Fieldsets\n      ------------------------------ */\n\n  fieldset {\n\n    display: block;\n    appearance: none;\n    border: none;\n    margin: 0;\n    padding: 0;\n\n  }\n\n\n}", ".quiz {\n\n  img, .img {\n    width: 100%;\n    vertical-align: top;\n  }\n\n}\n", "html {\n\n  &.supports-no-cookies {\n    .supports-no-cookies {\n      display: none;\n    }\n  }\n\n  &.supports-cookies {\n    .supports-cookies {\n      display: none;\n    }\n  }\n\n}\n\nbody {\n\n  &.logged-out {\n    \n  }\n\n  &.logged-in {\n\n  }\n\n  &.in-theme-customizer {\n\n  }\n\n}\n", ".quiz {\n\n  .section__header {\n\n    .heading {\n      span {\n        color: #fff;\n      }\n\n    }\n\n    .text--large {\n      span {\n        color: var(---color--highlight);\n      }\n    }\n\n  }\n\n  .feeding-calculator {\n\n    --block-border-radius: 20px;\n\n    --text-color: #2E2E2E;\n    --heading-color: #2E2E2E;\n\n    display: block;\n    color: #2E2E2E;\n\n    max-width: 800px;\n    margin: auto;\n    padding: 40px;\n    background: #fff;\n\n    border-radius: var(--block-border-radius);\n\n    .text--subdued {\n      color: RGB(var(---color-text--light--rgb));\n    }\n\n  }\n\n  .feeding-calculator__inner {\n\n  }\n\n\n  .feeding-calculator__header {\n    display: flex;\n    width: 100%;\n    justify-content: center;\n    align-items: center;\n    gap: 2em;\n\n    @media(max-width: 800px) {\n      flex-direction: column;\n      gap: 1.5em;\n    }\n  }\n\n  .feeding-calculator__body {\n\n    hr {\n      width: 400px;\n      margin: 2em auto;\n    }\n\n  }\n\n  .feeding-calculator__footer {\n    margin-top: 30px;\n    text-align: center;\n  }\n\n  /* ----- Days ----- */\n\n  .feeding-calculator-days {\n    display: flex;\n    gap: 20px;\n    justify-content: center;\n    flex-wrap: wrap;\n    \n    margin-top: 40px;\n  }\n\n  .feeding-calculator-day {\n\n    flex: 1 0 20%;\n\n    padding: 20px 10px;\n    border: 1px solid var(---color-line--light);\n\n    text-align: center;\n\n    border-radius: var(--block-border-radius);\n\n    p {\n      margin: 0;\n    }\n\n    @media (max-width: 800px) {\n      min-width: 40%;\n    }\n\n    @media (max-width: 400px) {\n      min-width: 100%;\n    }\n\n  }\n\n  .feeding-calculator-day__image {\n    img {\n      max-width: 70px;\n      margin: 20px 0;\n    }\n  }\n\n  .feeding-calculator-day__amounts {\n    margin-top: 0.5em;\n    > div {\n      line-height: 1.4;\n    }\n  }\n  \n  /* ----- Per Day Labels ----- */\n\n  .feeding-calculator-perday {\n    display: flex;\n    gap: 10px;\n\n    align-items: center;\n\n    @media(max-width: 800px) {\n      flex-direction: column;\n    }\n\n  }\n\n  .feeding-calculator-perday__label {\n    margin: 0;\n  }\n\n  .feeding-calculator-perday__quantity {\n\n    display: flex;\n    align-items: center;\n    justify-content: center;\n\n    padding: 0 0.5em;\n    height: 50px;\n\n    font-size: 1.4em;\n    letter-spacing: -0.1em;\n    font-weight: var(---font-weight-body--bold);\n\n    /* Light Gray */\n    border: 1px solid #E6E6E6;\n    border-radius: 8px;\n  }\n}", ".quiz {\n\n  .quiz-results-product__footer {\n    min-height: 50px;\n  }\n\n  /* ----- Products ----- */\n\n  quiz-results-products {\n\n    .gallery__list-wrapper {\n      padding: var(--vertical-breather) 0;\n    }\n    \n    .gallery__list {\n      @include respond-to($large-up) {\n        justify-content: center;;\n      }\n    }\n\n    &.quiz-results-products--disabled {\n\n      quiz-results-product {\n\n        &:not(.quiz-results-product--selected) {\n          opacity: 0.5;\n          pointer-events: none;\n        }\n\n      }\n\n    }\n\n  }\n\n\n  /* ----- Product ----- */\n\n  .quiz-results-product {\n\n    --quiz-results-product-color: var(---color--highlight);\n    --section-block-background: var(---background-color--content-1--rgb);\n    --text-color: RGB(var(---color-text--dark--rgb));\n\n    position: relative;\n    display: flex;\n    flex-direction: column;\n    justify-content: space-between;\n    width: 350px;\n\n    background: RGB(var(--section-block-background));\n    color: var(--text-color);\n    box-shadow: 0 0 5px rgba(0, 0, 0, .15);\n\n    border-radius: 8px;\n\n    transform: scale3d(1);\n\n    transition:\n      transform 0.25s ease-in-out,\n      box-shadow 0.25s ease-in-out,\n      opacity 0.25s ease-in-out;\n\n    .quiz-ribbon--recommended {\n      display: none;\n    }\n\n    .quiz-results-product-button--soldout {\n      display: none;\n    }\n\n    .quiz-results-product-button--add-to-cart {\n      display: flex;\n    }\n\n    &.quiz-results-product--recommended {\n    \n      transform: scale(1.05);\n  \n      &:hover {\n        transform: scale(1.075) !important;\n      }\n\n      .quiz-ribbon--recommended,\n      .quiz-results-tag--recommended {\n        display: inline-flex;\n      }\n\n    }\n\n    &.quiz-results-product--soldout {\n    \n      .quiz-results-tag--soldout {\n        display: inline-flex;\n      }\n\n      .quiz-results-tag__inner {\n        display: inline-block;\n      }\n\n      .quiz-results-product-button--soldout {\n        display: flex;\n      }\n\n      .quiz-results-product-button--add-to-cart {\n        display: none;\n      }\n\n    }\n\n    &:hover {\n      transform: scale(1.025);\n      box-shadow: 0 10px 25px rgba(0, 0, 0, .1);\n      opacity: 1 !important;\n    }\n\n    &:not(:first-child) {\n      margin-left: 30px;\n    }\n\n    &:before {\n\n      position: absolute;\n      top: 0;\n      left: 0;\n      right: 0;\n      margin: auto;\n      display: inline-block;\n      content: '';\n      width: 140px;\n      height: 140px;\n      background: var(--quiz-results-product-color);\n      z-index: 0;\n      outline: 3px solid var(--quiz-results-product-color);\n      border-radius: 100%;\n      transform: translateY(-15%);\n      \n    }\n\n    &:not(.quiz-results-product--selected) {\n      .quiz-results-product__checkbox-button {\n        &:hover {\n          .quiz-results-product__checkbox-input {\n            background-color: RGBA(var(---color--brand-2--rgb), 0.25);\n\n            &:before {\n              opacity: 1;\n            }\n          }\n        }\n      }\n    }\n\n    .gallery__figure {\n      padding: 30px;\n    }\n\n    .price--highlight {\n      color: var(---color-price);\n    }\n\n    .quiz-results-product__checkbox-label {\n      font-weight: var(---font-weight-body--bold);\n    }\n\n    @include respond-to($small-down) {\n      max-width: 80vw;\n    }\n\n    // Tags\n\n    .quiz-results-product__tags {\n      margin: 1em 0;\n    }\n\n    .quiz-results-tag {\n      \n      padding: 0.5em 1.25em;\n      border-radius: 100px;\n      display: none; // hidden by default\n      font-weight: bold;\n\n      &.quiz-results-tag--recommended {\n        background: RGB(var(---color--highlight--rgb));\n      }\n\n      &.quiz-results-tag--soldout {\n        background: RGB(var(---color--danger--rgb));\n        color: RGB(var(---color-text--reversed--rgb));\n      }\n\n    }\n\n    .quiz-results-tag__inner {\n      display: inline-block;\n    }\n\n  }\n\n  .quiz-results-product--selected {\n\n    .quiz-results-product__checkbox-input {\n      background-color: RGB(var(---color--brand-2--rgb));\n\n      &:before {\n        opacity: 1;\n      }\n    }\n\n    ~.quiz-results-product:not(.quiz-results-product--selected) {\n      // opacity: 0.5;\n    }\n\n  }\n\n  /* ----- Inner ----- */\n\n  .quiz-results-product__inner {\n\n    position: relative;\n    z-index: 1;\n\n  }\n\n  .quiz-results-product__header {\n\n    position: relative;\n    padding-bottom: 80px;\n    background: var(--quiz-results-product-color);\n\n    border-top-left-radius: 8px;\n    border-top-right-radius: 8px;\n\n  }\n\n  .quiz-results-product__footer {\n    border-top: 1px solid var(---color-line--light);\n  }\n\n  .quiz-results-product__image {\n\n    position: absolute;\n    left: 0;\n    right: 0;\n    top: 0;\n    z-index: 1;\n    margin: auto;\n\n    transform: translateY(-15%);\n\n    width: 140px;\n    height: 140px;\n    border: 10px solid transparent;\n    border-radius: 100%;\n    object-fit: cover;\n\n  }\n\n  /* ----- Checkbox ----- */\n\n  .quiz-results-product__checkbox-button {\n\n    display: flex;\n    justify-content: center;\n    align-items: center;\n    gap: 10px;\n\n    min-height: 50px;\n    width: 100%;\n\n  }\n\n  .quiz-results-product__checkbox {\n\n    display: flex;\n    align-items: center;\n    gap: 0.5em;\n\n  }\n\n  .quiz-results-product__checkbox-input {\n\n    display: flex;\n\n    width: 24px;\n    height: 24px;\n    border-radius: 4px;\n    background-color: RGB(var(---color-line--light--rgb));\n\n    transition: 0.25s background-color ease-in-out;\n\n    &:before {\n\n      content: '';\n      display: block;\n      flex: 1 0 auto;\n      background-image: url(\"data:image/svg+xml,%3Csvg width='16' height='13' viewBox='0 0 16 13' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M15 1L5.375 12L1 7' stroke='white' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E%0A\");\n      background-repeat: no-repeat;\n      background-position: center;\n      opacity: 0;\n      transition: 0.25s opacity ease-in-out;\n\n    }\n\n  }\n\n    /* ----- View Details ----- */\n\n  .quiz-results-product__view-details {\n\n    display: flex;\n    justify-content: center;\n    text-align: center;\n    margin-top: 12px;\n\n  }\n\n  .quiz-results-product__details {\n\n    position: relative;\n    margin: 0;\n    padding: 50px 20px 20px;\n\n    text-align: center;\n    background: var(---background-color--content-1);\n\n  }\n\n  .quiz-ribbon {\n\n    --ribbon-color: var(--section-block-background);\n\n    display: inline-flex;\n    justify-content: center;\n    align-items: center;\n\n    position: absolute;\n    width: 36px;\n    height: 55px;\n\n    font-size: 24px;\n    font-weight: 700;\n\n    background: RGB(var(--ribbon-color));\n\n    &.quiz-ribbon--number {\n      --ribbon-color: var(--section-block-background);\n      left: 30px;\n    }\n  \n    &.quiz-ribbon--recommended {\n      --ribbon-color: var(---color--highlight--rgb);\n      right: 30px;\n    }\n\n    &:after {\n      bottom: 0;\n      left: 50%;\n      border: solid transparent;\n      content: \"\";\n      height: 0;\n      width: 0;\n      position: absolute;\n      pointer-events: none;\n      border-color: rgba(136, 183, 213, 0);\n      border-bottom-color: var(--quiz-results-product-header-color);\n      border-width: 16px;\n      margin-left: -16px;\n\n      transform: scaleY(0.70);\n      transform-origin: bottom;\n    }\n\n    > span {\n      margin-top: -5px;\n      text-align: center;\n      letter-spacing: -.15em;\n      width: 100%;\n    }\n\n    .quiz-ribbon__bottom {\n\n      svg {\n        position: absolute;\n        width: 100%;\n        bottom: 2px;\n        left: 0;\n        right: 0;\n        transform: translateY(100%);\n\n        * {\n          fill: RGB(var(--ribbon-color));\n        }\n      }\n\n    }\n\n  }\n\n\n  /* ----- Sticky Form ----- */\n\n    .quiz-sticky-form {\n  \n      top: unset;\n      bottom: 0;\n      padding: 20px 0;\n  \n      background: RGB(var(--background));\n      border-top: RBB(var(--border-color));\n  \n      @include respond-to($medium-up) {\n        padding: 30px 0;\n      }\n\n      .quiz-sticky-form__title {\n        \n        display: inline-block;\n        margin-bottom: 0.25em;\n        margin-top: 0;\n\n        span, strong {\n          color: RGB(var(---color--highlight--rgb));\n        }\n\n      }\n  \n      .unit-price-measurement {\n        color: #9a9a9a;\n      }\n\n      .unit-price-measurement {\n        line-height: 1;\n        vertical-align: baseline;\n\n        a {\n          color: var(---color--highlight);\n          text-decoration: underline;\n        }\n\n        .unit-price-measurement__link {\n\n          display: flex;\n          flex-direction: column;\n          justify-content: center;\n          \n          margin-left: 0.5em;\n\n        }\n      }\n  \n      .product-sticky-form__content-wrapper {\n        @media (max-width: 1000px) {\n          margin-bottom: 20px;\n        }\n      }\n  \n      .product-sticky-form__payment-container {\n        display: flex;\n        gap: 15px;\n        @include respond-to($small-down) {\n          flex-direction: column;\n        }\n      }\n  \n      .button-checkout {\n        @include respond-to($small-down) {\n          width: 100%;\n        }\n      }\n  \n    }\n\n\n    /* ----- Quiz Results - Dogs ----- */\n    \n    \n    /* ----- Quiz Results - Dog ----- */\n\n    quiz-results-dog {\n\n      display: none;\n\n      &.quiz-results-dog--active {\n        display: block;\n      }\n\n      .quiz-results-dog__header {\n        text-align: center;\n        padding: var(--vertical-breather-tight) 0;\n      }\n\n      .quiz-results-dog__title {\n        span {\n          color: RGB(var(---color--highlight--rgb));\n        }\n      }\n\n      .quiz-results-dog__content {\n        margin: var(--container-gutter) 0;\n      }\n\n    }\n  \n\n}", ".product-sticky-form {\n\n  button,\n  .select {\n    min-height: var(--button-height);\n    height: var(--button-height);\n  }\n\n}", "body.on-quiz {\n\n  .quiz.drawer {\n\n    @include respond-to($medium-up) {\n\n        justify-content: flex-end;\n        top: auto;\n        bottom: 0;\n      \n        max-height: var(--quiz-drawer-height);\n      \n    }\n  \n  }\n\n}", ".quiz--flow {\n  position: relative;\n  color: RGB(var(--text-color));\n}\n\n.quiz--account {\n  .quiz-navigation__inner {\n    align-items: center;\n  }\n}\n\n.quiz-navigation {\n\n  position: sticky;\n  z-index: 4;\n  top: 0;\n  top: var(--header-height);\n  // top: var(--header-height-quiz);\n  left: 0;\n  right: 0;\n  width: 100%;\n\n  background: var(---background-color--content-reversed-1);\n  box-shadow: 0px 4px 12px 4px rgba(0, 0, 0, 0.25);\n\n  color: RGB(var(---color-text--reversed--rgb));\n\n  transition: transform 0.25s;\n\n}\n\n.quiz-navigation--hidden {\n  // transform: translateY(-100%);\n}\n\n.quiz-navigation__inner {\n  \n  display: flex;\n  justify-content: space-between;\n  align-items: flex-start;\n  \n  max-width: var(--container-max-width);\n  padding-left: var(--container-gutter);\n  padding-right: var(--container-gutter);\n  margin: auto;\n  \n  @include respond-to($small-down) {\n    align-items: center;\n  }\n  \n}\n\n.quiz-navigation__actions-left,\n.quiz-navigation__actions-right {\n  flex: 1 0 30%;\n  @include respond-to($small-down) {\n    top: 20px;\n  }\n}\n\n.quiz-navigation__center {\n  align-items: center;\n}\n\n.quiz-navigation-button {\n  \n  display: flex;\n  align-items: center;\n  gap: 0.5em;\n  padding: 1em 0;\n  \n  font-weight: var(---font-weight-body--bold);\n\n  @include respond-to($small-down) {\n    padding: 0;\n  }\n  \n  &:focus,\n  &:hover {\n      opacity: 0.7;\n  }\n\n  &.quiz-navigation-button--hidden {\n    display: none;\n  }\n  \n}\n\n.quiz-navigation__actions-left {\n  display: flex;\n  justify-content: flex-start;\n  text-align: left;\n  @include respond-to($small-down) {\n      position: absolute;\n      left: var(--container-gutter);\n  }\n}\n\n.quiz-navigation__actions-right {\n  display: flex;\n  justify-content: flex-end;\n  text-align: right;\n  @include respond-to($small-down) {\n      position: absolute;\n      right: var(--container-gutter);\n  }\n}\n\n.quiz-navigation__logo {\n  \n  padding: 5px 0;\n\n  @include respond-to($small-down) {\n    padding-top: 20px;\n  }\n\n  img {\n    \n    max-width: 120px;\n    margin-bottom: 10px;\n\n    @include respond-to($small-down) {\n      max-width: 100px;\n    }\n\n  }\n  \n  @include respond-to($small-down) {\n    \n    padding-top: 20px;\n\n    position: absolute;\n    top: 0;\n\n    a {\n      &:hover {\n        opacity: 0.75;\n      }\n    }\n    \n    img,\n    svg {\n      width: 100px;\n    }\n\n  }\n\n}\n\n.quiz-navigation__progress {\n  \n  display: flex;\n  flex-direction: column;\n  align-items: center;\n\n  padding: 30px 0 40px;\n  max-width: 630px;\n  width: 100%;\n\n  @include respond-to($small-up) {\n    padding-top: 15px;\n  }\n\n  @include respond-to($small-down) {\n    padding-top: 60px;\n    padding-bottom: 10px;\n  }\n}\n\n.quiz-navigation__actions-left,\n.quiz-navigation__actions-right {\n  top: 14px;\n}\n\n/* ----- Progress Bar ----- */\n\n.quiz-progress-bar {\n  position: relative;\n  max-width: 630px;\n  width: 100%;\n}\n\n.quiz-progress-bar__label {\n\n  position: absolute;\n  max-width: 100px;\n  text-align: center;\n  padding-top: 12px;\n\n  @include respond-to($small-down) {\n    display: none;\n  }\n\n  @include respond-to($small-up) {\n    padding-top: 24px;\n  }\n\n}\n\n.quiz-progress-bar__label-start {\n  text-align: left;\n  left: 0;\n\n  @include respond-to($small-up) {\n    transform: translateX(-50%);\n  }\n}\n\n.quiz-progress-bar__label-middle {\n  left: 0;\n  right: 0;\n  margin: auto;\n}\n\n.quiz-progress-bar__label-end {\n  text-align: right;\n  right: 0;\n\n  @include respond-to($small-up) {\n    transform: translateX(50%);\n  }\n}\n\n.quiz-progress-bar__sections {\n  \n  display: flex;\n  justify-content: stretch;\n  \n  height: 12px;\n  \n  transform: translateY(-12px);\n\n  @include respond-to($small-down) {\n    height: 8px;\n    transform: translateY(-8px);\n  }\n  \n}\n\n.quiz-progress-bar__section {\n  \n  width: 100%;\n  text-align: center;\n  position: relative;\n  \n}\n\n.quiz-progress-bar__section__label {\n  position: absolute;\n  left: 0;\n  right: 0;\n  bottom: -5px;\n  transform: translateY(100%);\n  @include respond-to($small-down) {\n    display: none;\n  }\n  @include respond-to($small-up) {\n    padding-top: 8px;\n  }\n}\n\n.quiz-progress-bar__dot {\n  \n  position: absolute;\n  top: 0;\n  left: calc(100% * var(--quiz-progress));\n  z-index: 1;\n\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 32px;\n  height: 32px;\n\n  transform: translate(-50%, -25%);\n  \n  vertical-align: top;\n  \n  transition: 0.25s left;\n\n  background: var(---color--highlight);\n  border-radius: 100%;\n\n  svg {\n    width: 24px;\n    opacity: 0.75;\n\n    * {\n      fill: #000;\n    }\n  }\n\n  @include respond-to($small-down) {\n    width: 18px;\n    height: 18px;\n    border-radius: 100%;\n    background: var(---color--brand-1);\n    svg {\n        display: none;\n    }\n  }\n\n  \n  \n  \n}\n\n.quiz-progress-bar__progress-track {\n  \n  position: relative;\n  display: block;\n  height: 12px;\n  \n  box-shadow: 0 0 0 1px var(---color--brand-3) inset;\n  \n  border-radius: 24px;\n  overflow: hidden;\n\n  @include respond-to($small-down) {\n    height: 8px;\n  }\n  \n}\n\n.quiz-progress-bar__section__divider {\n  display: block;\n  width: 1px;\n  height: 12px;\n  background: var(---color--brand-3);\n  transform: rotate(15deg);\n\n  @include respond-to($small-down) {\n    height: 8px;\n  }\n\n}\n\n.quiz-progress-bar__progress {\n  position: absolute;\n  top: 0;\n  left: 0;\n  height: 100%;\n  width: 100%;\n  background: var(---color--brand-1);\n  transform: scaleX(var(--quiz-progress));\n  transform-origin: left;\n  transition: transform 0.25s;\n\n  @include respond-to($small-down) {\n    padding: 10px 0;\n  }\n\n}", "body.on-quiz {\n\n  .shopify-section--announcement-bar,\n  store-header {\n    display: none !important;\n  }\n}\n\n.quiz {\n\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n\n  --quiz-navigation-height: 104px;\n\n  @include respond-to($small-down) {\n    --quiz-navigation-height: 85px;\n  }\n\n\n\n  /* ----- Basic Styling ----- */\n\n  &.quiz--flow {\n    background: var(---background-color--content-reversed-1);\n    --text-color: var(---color-text--reversed--rgb);\n  }\n\n  &.quiz--account {\n\n    // .quiz-navigation__center,\n    .quiz-modal-footer {\n      display: none;\n    }\n\n    .quiz-navigation__center {\n      visibility: hidden;\n      pointer-events: none;\n    }\n    \n    .quiz-steps__inner {\n      .quiz-step {\n        justify-content: center;\n        position: absolute;\n      }\n    }\n\n  }\n\n  &.quiz--has-navigation {\n    min-height: calc(100vh - var(--header-height) - var(--quiz-navigation-height));\n  }\n\n  quiz-steps.quiz-steps {\n    display: block;\n  }\n\n  .quiz-steps__inner {\n\n    position: relative;\n    overflow: hidden;\n    // min-height: calc(100vh - var(--quiz-navigation-height));\n    // min-height: calc(100vh - var(--quiz-navigation-height) - var(--quiz-sub-navigation-height));\n    min-height: calc(100vh - var(--quiz-navigation-height) - 60px);\n    // min-height: calc(100vh - var(--header-height)); \n    // min-height: calc(100vh - var(--header-height) - var(--quiz-navigation-height));\n\n    @include respond-to($medium-down) {\n      .quiz-step {\n        position: static;\n        // justify-content: flex-start;\n      }\n    }\n\n  }\n\n  /* ----- Sub Navigation ----- */\n\n  .quiz-sub-navigation {\n    display: block;\n    position: sticky;\n    top: calc(var(--header-height) + var(--quiz-navigation-height) - 10px);\n\n    z-index: 3;\n    width: 100%;\n    background: linear-gradient(0deg, rgba(var(---background-color--content-reversed-1--rgb), 0) 0%, rgba(var(---background-color--content-reversed-1--rgb), 1) 90%);\n\n    pointer-events: none;\n    transition: transform 0.25s;\n\n    @include respond-to($small-down) {\n      padding-bottom: 40px;\n    }\n\n    @include respond-to($small-up) {\n      padding-top: 10px;\n    }\n\n    &.quiz-sub-navigation--hidden {\n      transform: translateY(-100%);\n    }\n\n  }\n\n  /* ----- Steps ----- */\n\n  .quiz-step {\n\n    display: none;\n    flex-direction: column;\n    justify-content: center;\n\n    visibility: hidden;\n\n    position: absolute;\n    width: 100%;\n    height: 100%;\n    left: 0;\n    right: 0;\n\n    @include respond-to($small-down) {\n      overflow-y: scroll;\n      justify-content: center;\n    }\n\n    @include respond-to($wide-up) {\n      // justify-content: flex-start;\n    }\n\n  }\n\n  .quiz--account {\n    \n  }\n\n  .quiz-step--home {\n    \n    overflow: hidden; // Handle the horizontal scrollbars from the decorations. (Makes the sticky footer unworkable on this step)\n\n    .quiz-step__footer {\n      background: none;\n    }\n\n  }\n\n  .quiz-step--active {\n    display: flex;\n    visibility: visible;\n  }\n\n  .quiz-step--animating {\n    display: flex;\n    // position: absolute;\n  }\n\n  .quiz-step--leaving {\n    @include respond-to($medium-down) {\n      display: flex;\n    }\n  }\n\n  .quiz-step__inner {\n\n    // min-height: calc(100vh - var(--header-height));\n    // min-height: calc(100vh - var(--header-height) - var(--quiz-navigation-height));\n\n    display: flex;\n    flex-direction: column;\n    align-items: center;\n    justify-content: center;\n\n    position: relative;\n    z-index: 1;\n\n    .h1 {\n      line-height: 1;\n    }\n\n    @include respond-to($medium-down) {\n\n      // height: unset;\n      // max-height: unset;\n      // min-height: calc(100vh - var(--header-height));\n\n    }\n\n  }\n\n  .quiz-step__header {\n\n    position: absolute;\n    top: 0;\n\n    max-width: var(--container-max-width);\n    width: 100%;\n    margin: 0 auto auto auto;\n\n  }\n\n\n  .quiz-step__body {\n\n    text-align: center;\n    padding: 20px var(--container-gutter);\n\n    >.quiz-step-actions {\n      flex-direction: column;\n      align-items: center;\n    }\n\n    @include respond-to($wide-up) {\n      padding-top: 5px;\n      padding-bottom: 0px;\n    }\n\n  }\n\n  .quiz-step__body--forms {\n    width: 100%;\n\n    .quiz-step-description {\n      max-width: 800px;\n      margin: 20px auto 40px;;\n    }\n\n    +.quiz-step__footer .quiz-step-actions__inner {\n      max-width: 500px;\n      margin: 20px auto;\n    }\n  }\n\n  .quiz-step__body--narrow {\n\n    max-width: 600px;\n\n  }\n\n  .quiz-step__footer {\n\n    position: sticky;\n    bottom: 0;\n\n    display: flex;\n    flex-direction: column;\n    align-items: center;\n    justify-content: center;\n    gap: 10px;\n\n    width: 100%;\n    padding: 20px var(--container-gutter);\n\n    // padding-top: var(--container-gutter);\n    // padding-bottom: var(--container-gutter);\n    // padding-left: var(--container-gutter);\n    // padding-right: var(--container-gutter);\n\n    margin-top: auto;\n\n    text-align: center;\n\n    // background: linear-gradient(180deg, rgba(var(---background-color--content-reversed-1--rgb), 0) 0%, rgba(var(---background-color--content-reversed-1--rgb), 1) 50%);\n    background: linear-gradient(180deg, rgba(var(---background-color--content-reversed-1--rgb), 0) 0%, rgba(var(---background-color--content-reversed-1--rgb), 1) 20%);\n\n    @include respond-to($medium-down) {\n\n      .button {\n        width: 100%;\n      }\n\n      .quiz-step-actions {\n        order: 1;\n\n      }\n\n      .quiz-step-actions-account {\n        order: 0;\n      }\n\n    }\n\n  }\n\n  .quiz-customer-forms {\n\n    display: flex;\n    justify-content: center;\n    gap: 30px;\n\n    @include respond-to($medium-down) {\n      flex-direction: column;\n    }\n\n  }\n\n  .quiz-customer-form {\n\n    --block-border-radius: 20px;\n    --section-block-background: var(---background-color--content-reversed-3--rgb);\n\n    background: var(---background-color--content-reversed-3);\n    border-radius: var(--block-border-radius);\n\n    display: flex;\n    flex-direction: column;\n    justify-content: stretch;\n    max-width: 490px;\n    width: 100%;\n\n    padding: 30px;\n    \n    form {\n      height: 100%;\n      display: flex;\n      flex-direction: column;\n    }\n\n    .input {\n      \n      margin-top: 20px;\n      margin-bottom: 20px;\n\n      .input__field {\n        padding: 0.25em 0.5em;\n      }\n\n      &:last-child {\n        margin-top: 0;\n        margin-bottom: 0;\n        padding-top: 0;\n      }\n\n      &:first-child {\n        // margin-top: 0;\n      }\n    }\n\n    .input:last-child {\n      // margin-top: auto;\n      // padding-top: 20px;\n    }\n\n    @media (max-width: 1000px) {\n      margin: auto;\n    }\n\n    .input__field {\n      font-size: 20px !important;\n      padding: 0.5em 1em;\n    }\n\n  }\n\n  .quiz-customer-form__step {\n    padding: 1em 0;\n\n    .input--radio {\n      label {\n        font-size: var(---font-size-body-large--desktop);\n      }\n    }\n\n    .kyc-option-radio {\n      margin: 0.5em 0 !important;\n    }\n\n    .input__field {\n      padding-left: 0  !important;\n    }\n  }\n\n  /* ----- Tabs ----- */\n\n  .quiz-step__tabs {\n\n    &:before,\n    &:after {\n\n      display: inline-block;\n      position: absolute;\n      top: 0;\n      z-index: 3;\n\n      content: '';\n      width: 50px;\n      height: 100%;\n\n      pointer-events: none;\n\n    }\n\n    &:before {\n      left: 0;\n      background: linear-gradient(270deg, rgba(var(---background-color--content-reversed-1--rgb), 0), rgba(var(---background-color--content-reversed-1--rgb), 1) 100%);\n    }\n\n    &:after {\n      right: 0;\n      background: linear-gradient(90deg, rgba(var(---background-color--content-reversed-1--rgb), 0), rgba(var(---background-color--content-reversed-1--rgb), 1) 100%);\n    }\n\n\n  }\n\n  .quiz-step__tabs-inner {\n\n    display: flex;\n    gap: 20px;\n\n    scroll-snap-type: x mandatory;\n    scroll-snap-align: center;\n\n    overflow: scroll;\n\n    display: flex;\n    align-items: flex-start;\n    justify-content: center;\n    padding: 10px 50px;\n    gap: 30px;\n\n    width: 100%;\n\n    &::-webkit-scrollbar {\n      display: none;\n    }\n\n    @include respond-to($medium-down) {\n      padding: 0;\n    }\n\n    .quiz-step-tab {\n      display: flex;\n    }\n\n  }\n\n  .quiz-step-tab {\n\n    display: block;\n    scroll-snap-align: center;\n\n    white-space: nowrap;\n\n    pointer-events: all;\n\n    @include respond-to($medium-down) {\n      padding: 0 20px;\n    }\n\n  }\n\n  .quiz-step-logo {\n    max-width: 200px;\n    margin-top: 50px;\n    margin-bottom: 30px;\n\n    @include respond-to($medium-up) {\n      margin-top: 100px;\n      margin-bottom: 50px;\n    }\n\n  }\n\n  .quiz-step-icon {\n\n    width: 150px;\n    height: 150px;\n\n    margin-block-end: 20px;\n\n    @media (max-width: 700px) {\n    // @include respond-to($small-down) {\n      .quiz-step-icon {\n        width: 120px;\n        height: 120px;\n        margin-bottom: -20px;\n      }\n    }\n\n  }\n\n\n\n\n  /* ----- Step Content Elements ----- */\n\n  .quiz-step-title {\n\n    margin-bottom: 0.5em;\n    margin-top: 0;\n\n    strong, span {\n      color: RGB(var(---color--brand-1--rgb));\n    }\n\n  }\n\n  .quiz-step-description {\n\n    margin-top: 30px;\n    margin-bottom: 30px;\n\n  }\n\n  .quiz-step-line {\n\n    display: block;\n    margin: 10px 0;\n    line-height: 1.2;\n\n    font-size: var(---font-size-h4--mobile);\n\n    @include respond-to($small-up) {\n      font-size: var(---font-size-h4--desktop);\n    }\n\n    opacity: 1;\n\n    transition: transform 0.25s ease-in-out,\n    opacity 0.25s ease-in-out;\n\n    select {\n      font-size: 1rem !important;\n    }\n\n    &.quiz-step-line--hidden {\n      opacity: 0;\n      transform: translateY(-10%);\n      pointer-events: none;\n    }\n\n    >* {\n      display: inline-block;\n    }\n\n    .quiz-step-line__hint {\n      display: block;\n    }\n\n  }\n\n  .quiz-step-line__hint {\n    margin-top: 1em;\n    font-size: var(---font-size-body--desktop);\n  }\n\n  .quiz-step-hint {\n\n    margin: 30px auto;\n\n    @include respond-to($small-down) {\n      margin: 0 auto;\n      max-width: 200px;\n    }\n\n  }\n\n  .quiz-step-actions {\n\n    display: flex;\n    flex-wrap: wrap;\n    justify-content: center;\n    gap: 1em;\n\n    width: 100%;\n    padding-top: 20px;\n    padding-bottom: 20px;\n\n    @include respond-to($small-up) {\n      padding-top: 30px;\n    }\n\n    @include respond-to($wide-up) {\n      padding-top: 5px;\n    }\n\n  }\n\n  .quiz-step-actions-account {\n    margin: 10px auto;\n  }\n\n  .quiz-step-form {\n    padding-bottom: var(--container-gutter);\n  }\n\n  /* ----- Inputs ----- */\n\n  .quiz-input {\n\n    display: inline-block;\n    background-color: transparent;\n    border: none;\n\n    min-width: 10px;\n    border-bottom: 2px solid var(---color--secondary);\n    margin: 10px 0;\n\n    font-weight: var(--text-font-bold-weight);\n\n    color: var(---color--highlight);\n    transition: 0.25s border-color;\n\n    &::placeholder {\n      font-weight: var(--text-font-weight);\n      opacity: 1;\n    }\n\n    &:focus,\n    &:hover {\n      outline: none;\n      border-color: var(---color--highlight);\n    }\n\n  }\n\n  select.quiz-input {\n    padding-right: 50px;\n    background-image: url(\"data:image/svg+xml,%3Csvg width='12' height='6' viewBox='0 0 12 6' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M1 0.5L6 5.5L11 0.5' stroke='%23E6E6E6' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E%0A\");\n    background-size: 15px;\n    background-repeat: no-repeat;\n    background-position: calc(100% - 10px);\n  }\n\n  quiz-step-dog-health {\n\n    .quiz-step-hint__final {\n      display: none;\n    }\n\n    .quiz-step-hint__in-progress {\n      display: block;\n    }\n\n    &:last-of-type {\n      .quiz-step-hint__final {\n        display: block;\n      }\n\n      .quiz-step-hint__in-progress {\n        display: none;\n      }\n    }\n\n  }\n\n\n  /* ----- Decoration ----- */\n\n  .quiz-decoration {\n\n    content: \"\";\n    position: absolute;\n    z-index: 0;\n\n    pointer-events: none;\n\n  }\n\n  .quiz-decoration--1 {\n\n    width: 850px;\n    height: 100%;\n    left: 0;\n    top: 0;\n    bottom: 0;\n    z-index: 0;\n\n    transform: scale(1.1) translate(0%);\n\n    svg {\n      transform: translateX(-40%);\n    }\n\n  }\n\n  .quiz-decoration--2 {\n\n    width: 850px;\n    height: 100%;\n    right: 0;\n    top: 0;\n    bottom: 0;\n    z-index: 0;\n\n    transform: scale(1.1) translate(0%);\n\n    svg {\n      transform: translateX(30%) rotate(160deg);\n    }\n\n  }\n\n  @include respond-to($small-down) {\n\n    .quiz-decoration--1 {\n\n      width: 100%;\n      height: 400px;\n      left: 0;\n      top: auto;\n      bottom: 0;\n      z-index: 0;\n\n      transform-origin: 50% 50%;\n\n      transform: scale(1.2) translateX(0%) translateY(30%) rotate(230deg);\n\n      svg {\n        transform: translateX(0);\n      }\n\n    }\n\n    .quiz-decoration--2 {\n      display: none;\n    }\n  }\n\n}", "/* ----- Tiles ----- */\n\n.quiz-tiles {\n\n  .quiz-tiles__container {\n    \n    display: flex;\n    margin: var(--container-gutter) 0;\n    gap: 30px;\n    flex-wrap: wrap;\n    justify-content: center;\n\n    @include respond-to($medium-down) {\n      gap: 15px;\n    }\n\n  }\n\n}\n\n.quiz-tiles__input {\n  @include visually-hidden();\n}\n\n.quiz-tile {\n\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n\n  padding: 20px;\n  border: 2px solid var(---color--brand-3);\n\n  border-radius: 8px;\n  background-color: var(---color--brand-2);\n  transition: 0.25s border-color;\n\n  cursor: pointer;\n\n  &:hover {\n    background: var(---color--primary--dark);\n  }\n\n  &:focus {\n    border-color: var(---color--highlight);\n  }\n\n  &.quiz-tile--selected,\n  &:focus {\n    \n    border-color: var(---color--highlight);\n\n    .quiz-tile__icon--default:not(:only-child) {\n      opacity: 0;\n    }\n\n    .quiz-tile__icon--hover {\n      opacity: 1;\n    }\n\n  }\n\n  .quiz-tile__icon {\n    position: relative;\n  }\n\n  .quiz-tile__icon--hover {\n    position: absolute;\n    left: 0;\n    top: 0;\n    opacity: 0;\n  }\n\n  .quiz-tile__text__hint {\n    display: block;\n  }\n\n  .quiz-tile__text__title {\n    display: block;\n  }\n\n  @include respond-to($small-up) {\n\n    .quiz-tile__text__hint {\n      display: none;\n    }\n\n  }\n\n  @include respond-to($small-down) {\n\n    flex-direction: row;\n    gap: 20px;\n    width: 100%;\n    padding: 10px;\n\n    .quiz-tile__text {\n      text-align: left;\n    }\n\n    .quiz-tile__icon {\n\n      flex: 1 0 150px;\n      max-height: 100px;\n      max-width: 100px;\n      width: 100%;\n      height: 100%;\n\n      img {\n        max-height: 100px;\n        max-width: 100px;\n        width: 100%;\n        height: 100%;\n      }\n    }\n\n  }\n}\n\n.quiz-tile__icon {\n  img {\n    height: 150px;\n    width: 150px;\n  }\n}", ".quiz-substep {\n\n  display: none;\n  visibility: hidden;\n  \n}\n\n.quiz-substep--active {\n  display: block;\n  visibility: visible;\n}\n\n.quiz-substep--visible {\n  display: block;\n  visibility: visible;\n}\n\n.quiz-substep--animating {\n  display: block;\n}\n\n.quiz-substep--entering {\n}\n\n.quiz-substep--leaving {\n}", "quiz-loading-overlay {\n\n  position: fixed;\n  top: 0;\n  left: 0;\n  z-index: 100;\n  width: 100vw;\n  height: 100vh;\n\n  display: flex;\n  align-items: center;\n  justify-content: center;\n\n  background: var(---background-color--content-reversed-1);\n\n}\n\n.quiz-loading-overlay__inner {\n  text-align: center;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n}\n\n.quiz-loading-overlay__image {\n\n  max-width: 180px;\n\n  @include respond-to($medium-up) {\n    max-width: 290px;\n  }\n\n}\n\n.quiz-loading-overlay__text {\n  .heading {\n    margin: 0;\n  }\n}", ".quiz {\n\n  .price {\n    font-weight: 500;\n    letter-spacing: 0;\n  }\n\n  /* ----- Quiz Elements ----- */\n\n  .quiz-page-header {\n    text-align: center;\n    margin-bottom: 20px;\n  }\n\n  .quiz-page-footer {\n    text-align: center;\n    margin-bottom: 20px;\n    margin-top: 20px;\n  }\n\n  .quiz-terms {\n    margin-top: 20px;\n    max-width: 350px;\n    margin: auto;\n    text-align: center;\n  }\n\n  .quiz-sticky-form {\n    --background: var(---background-color--content-reversed-1--rgb);\n    --border-color: var(---color-text--rgb);\n    border-bottom: 0;\n  }\n\n  /* ----- Radio Buttons ----- */\n\n  .input--sub-option {\n    margin: 0;\n    padding-left: 30px;\n    text-align: left;\n    margin: 0 !important;\n    \n    .nice-select, select, input {\n        font-size: var(---font-size-body--desktop) !important;\n    }\n\n    input,\n    textarea,\n    select, .select {\n      padding: 0.5em 3em 0.5em 1.4em;\n      display: block;\n      font-size: 18px;\n      background-color: rgba(var(--section-block-background), 0.8);\n      margin-bottom: 1em;\n      border-radius: 8px;\n      border: 1px solid rgba(var(--text-color), 0.25);\n\n      &:hover {\n        background-color: rgba(var(--section-block-background), 1);\n        border: 1px solid rgba(var(--text-color), 0.5);\n      }\n\n      &:focus {\n        border: 1px solid rgba(var(--text-color), 0.5);\n      }\n      \n    }\n\n    .select,\n    select {\n\n      background-image: var(---icon--chevron);\n      background-position: right 1em top 50%;\n      background-repeat: no-repeat;\n      background-size: 14px;\n\n    }\n\n  }\n\n  /* ----- Radio Buttons ----- */\n\n  .input--radio {\n\n    margin: 0;\n    margin-bottom: 0.5em;\n\n    label {\n\n      font-size: var(---font-size-label-radio--mobile);\n\n      @media (min-width: 1000px) {\n        font-size: var(---font-size-label-radio--desktop);\n      }\n\n    }\n\n    input[type=\"radio\"] {\n\n      background: #fff;\n      position: relative;\n      top: -1px;\n\n      &:after {\n\n        content: '';\n\n        display: inline-block;\n        position: absolute;\n        left: 0;\n        top: 0;\n        right: 0;\n        bottom: 0;\n        margin: auto;\n\n        color: #000;\n        width: 10px;\n        height: 10px;\n        border-radius: 100%;\n        background: currentColor;\n\n        transform: scale(0);\n        opacity: 0.5;\n\n        transition: transform .25s, opacity .25s;\n\n      }\n\n      &:checked,\n      &:hover {\n        &:after {\n          transform: scale(1);\n        }\n      }\n\n      &:checked {\n        &:after {\n          opacity: 1;\n        }\n\n        +label {\n          font-weight: 700;\n        }\n      }\n    }\n\n    .checkbox-container {\n      align-items: center;\n    }\n\n    .checkbox {\n      width: 16px;\n      height: 16px;\n      border-radius: 100%;\n\n      @media (min-width: 1000px) {\n        width: 18px;\n        height: 18px;\n      }\n    }\n\n\n  }\n\n  /* ----- HRs ----- */\n\n  // hr, .hr {\n\n  //   width: 100%;\n  //   margin: 2em auto;\n  //   border-width: 0.5px;\n  //   border-bottom: 0;\n  //   border-style: solid;\n  //   border-color: var(---color-line);\n\n  //   &.hr--light {\n  //     border-color: var(---color-line--light);\n  //   }\n\n  //   &.hr--dark {\n  //     border-color: var(---color-line--dark);\n  //   }\n\n  //   &.hr--clear {\n  //     border-color: transparent;\n  //   }\n\n  //   &.hr--small {\n  //     margin: 1em 0;\n  //   }\n\n  //   &.hr--xsmall {\n  //     margin: 0.5em 0;\n  //   }\n\n  //   &.hr--narrow {\n  //     max-width: 70px;\n  //   }\n\n  // }\n\n  .banner {\n\n    border-radius: 20px;\n    font-weight: var(---font-weight-body--bold);\n\n    .banner__content {\n      a {\n        text-decoration: underline;\n      }\n    }\n\n    &.banner--success {\n      color: var(---color-text--strong);\n      background-color: var(---color--highlight);\n    }\n    \n    &.banner--error {\n      color: var(---color-text--reversed-strong);\n      background-color: var(---color--danger);\n    }\n\n  }\n\n\n}\n", "@keyframes fade-in {\n  0% {\n    opacity: 0;\n  }\n  100% {\n    opacity: 1;\n  }\n}\n\n@keyframes fade-out {\n  0% {\n    opacity: 1;\n  }\n  100% {\n    opacity: 0;\n  }\n}\n\n@keyframes rotate {\n\n  0% {\n    transform: rotate(0deg);\n  }\n  100% {\n    transform: rotate(360deg);\n  }\n\n}\n"]}