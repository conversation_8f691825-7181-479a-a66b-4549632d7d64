{"version": 3, "sources": ["basic-styles/_theme-overrides.scss", "custom.css", "basic-styles/_typography.scss", "utilities/_mixins.scss", "basic-styles/_icons.scss", "basic-styles/__basic-styles.scss", "layout/_split-page.scss", "layout/_body-modifiers.scss", "sections/_theme-customers-account.scss", "sections/_mini-cart.scss", "sections/_custom-dog-info.scss", "sections/_custom-main-product.scss", "sections/_feeding-calculator-standalone.scss", "sections/__sections.scss", "components/_buttons.scss", "components/_modal.scss", "components/_form-elements.scss", "components/_revealing-forms.scss", "components/_expanding-input.scss", "components/_box-line-item.scss", "components/_tile-radios.scss", "components/_tables.scss", "components/_shipping-bar.scss", "components/_vet-sticky-bar.scss", "components/_cart-vet-partner.scss", "components/_split-page-step.scss", "components/_banners.scss", "components/_weight-range.scss", "components/__components.scss", "apps/_awtomic.scss", "apps/_section-store.scss", "utilities/_classes.scss", "custom.min.css", "third-party/_nice-select.scss", "utilities/_animations.scss"], "names": [], "mappings": "AAOI,6CACE,aCoEN,CDjEE,qCACE,0CCmEJ,CDlEI,2CACE,UCoEN,CDhEI,qDACE,mBCkEN,CD/DM,sHACE,mBCiER,CCvFA,uEAwBI,+BAAA,CAEA,aAAA,CALA,sCAAA,CAEA,oCAAA,CADA,sCAAA,CAGA,4CDsEJ,CC9DE,4GAJE,uCAAA,CADA,qDAAA,CApBF,+CDgHF,CCvFE,qCCwVA,uCAAA,CDnVE,sCAAA,CCoVF,qCAAA,CAEA,kCFpQF,CEyQE,qFACE,6BAAA,CAEA,uCAAA,CADA,mBFrQJ,CEyQE,yCACE,wBFtQJ,CE5DM,yCDrCJ,qCAOI,uCAAA,CCoWF,sDAAA,CACA,gDFrQF,CACF,CC5FE,wBC4UA,uCAAA,CDvUE,sCAAA,CCwUF,qCAAA,CAEA,kCAAA,CADA,uCAAA,CAGA,qDAAA,CACA,+CFrOF,CEuOE,wHACE,6BAAA,CAEA,uCAAA,CADA,mBFnOJ,CEuOE,gCACE,wBFpOJ,CE9FM,yCDzBJ,wBAOI,uCAAA,CCwVF,sDAAA,CACA,gDFnOF,CACF,CClHE,mECgUA,uCAAA,CD3TE,sCAAA,CC4TF,qCAAA,CAEA,kCAAA,CADA,uCAAA,CAGA,qDAAA,CACA,+CFnMF,CEqME,mNACE,6BAAA,CAEA,uCAAA,CADA,mBFjMJ,CEqME,+EACE,wBFlMJ,CEhIM,yCDbJ,mEAOI,uCAAA,CC4UF,sDAAA,CACA,gDFjMF,CACF,CCxIE,wBCoTA,uCAAA,CD/SE,sCAAA,CCgTF,qCAAA,CAEA,kCAAA,CADA,uCAAA,CAGA,qDAAA,CACA,+CFjKF,CEmKE,wHACE,6BAAA,CAEA,uCAAA,CADA,mBF/JJ,CEmKE,gCACE,wBFhKJ,CElKM,yCDDJ,wBAOI,uCAAA,CCgUF,sDAAA,CACA,gDF/JF,CACF,CC9JE,wBCwSA,uCAAA,CDnSE,sCAAA,CCoSF,qCAAA,CAEA,kCAAA,CADA,uCAAA,CAGA,qDAAA,CACA,+CF/HF,CEiIE,wHACE,6BAAA,CAEA,uCAAA,CADA,mBF7HJ,CEiIE,gCACE,wBF9HJ,CEpMM,yCDWJ,wBAOI,uCAAA,CCoTF,sDAAA,CACA,gDF7HF,CACF,CCpLE,wBCuTA,oCAAA,CDlTE,sCAAA,CCmTF,kCAAA,CACA,0CAAA,CAEA,kDAAA,CACA,4CFxHF,CE0HE,wHACE,6BAAA,CAEA,oCAAA,CADA,mBFtHJ,CE0HE,gCACE,wBFvHJ,CErOM,yCDuBJ,wBAOI,uCAAA,CCkUF,sDAAA,CACA,gDFtHF,CACF,CCzME,wBC2SA,oCAAA,CDtSE,sCAAA,CCuSF,kCAAA,CACA,0CAAA,CAEA,kDAAA,CACA,4CFvFF,CEyFE,wHACE,6BAAA,CAEA,oCAAA,CADA,mBFrFJ,CEyFE,gCACE,wBFtFJ,CEtQM,yCDmCJ,wBAOI,uCAAA,CCsTF,sDAAA,CACA,gDFrFF,CACF,CC7NE,YACE,kCDsOJ,CCnOE,0BAOE,uCAAA,CACA,oDAAA,CAJA,qCAAA,CACA,uCAAA,CAHA,wDAAA,CACA,wBDyOJ,CEhSM,yCDoDJ,0BAWI,qDDqOJ,CACF,CChOE,oDAQE,uCAAA,CACA,8CAAA,CAJA,qCAAA,CACA,uCAAA,CAHA,wDAAA,CACA,wBDsOJ,CC/NI,8sBASI,eD8OR,CC3OI,0DACE,YD8ON,CE3UM,yCDqEJ,oDA4BI,+CD+OJ,CACF,CC3OE,yBAOE,uCAAA,CACA,oDAAA,CAJA,qCAAA,CACA,uCAAA,CAHA,wDAAA,CACA,wBDiPJ,CE1VM,yCDsGJ,yBAWI,qDD6OJ,CACF,CCtOE,KACE,4CDyOJ,CCtOE,aAIE,wCAAA,CAFA,YDyOJ,CExWM,yCD6HJ,aAMI,yCDyOJ,CACF,CE7WM,yCDgKJ,WAKI,oCAAA,CADA,wCAAA,CAEA,oCD+OJ,CACF,CC1OE,qBAEE,2CD6OJ,CE3ZM,yCD4KJ,qBAII,4CDgPJ,CACF,CC/OI,yBACE,2CDkPN,CEraM,yCDkLF,yBAGI,4CDqPN,CACF,CE3aM,yCD0LJ,uCAII,oCAAA,CAEA,2CAAA,CADA,oCDoPJ,CACF,CC/OE,qBAEE,8CDkPJ,CEzbM,yCDqMJ,qBAII,+CDqPJ,CACF,CCpPI,qDACE,8CDuPN,CEncM,yCD2MF,qDAGI,+CD0PN,CACF,CEzcM,yCDmNJ,sCAKI,oCAAA,CADA,8CAAA,CAEA,oCDwPJ,CACF,CCpPE,oBAEE,8CDuPJ,CEvdM,yCD8NJ,oBAII,+CD0PJ,CACF,CCzPI,oDACE,8CD4PN,CEjeM,yCDoOF,oDAGI,+CD+PN,CACF,CEveM,yCD4OJ,sCAKI,oCAAA,CADA,8CAAA,CAEA,oCD6PJ,CACF,CCjPE,kCAJE,0CD8PJ,CC1PE,mBAEE,oBDwPJ,CCrPE,uBASE,sCDsPJ,CEngBM,yCDoQJ,uBAWI,uCDwPJ,CACF,CC/OI,wCAEE,uCAAA,CAKA,8CAAA,CAJA,0CAAA,CAEA,wDAAA,CADA,+CDoPN,CEjhBM,yCDyRF,wCASI,+CDmPN,CACF,CC7OE,qBAEE,sCD+OJ,CE1hBM,yCDySJ,qBAII,uCDiPJ,CACF,CC3OE,kBAEE,0CD8OJ,CCrOI,mIASE,kBAAA,CADA,eDqON,CGzmBM,yBACE,WAAA,CACA,iBH4mBR,CIxnBA,KACE,sBJ2nBF,CItnBE,oBACE,4BJynBJ,CKhoBA,YASE,+CL4nBF,CE9jBM,0CGvEN,YAKI,YAAA,CACA,6BAAA,CAFA,WAAA,CADA,eLyoBF,CACF,CKloBE,uCAEE,kBAAA,CADA,YLqoBJ,CK/nBA,oBAEE,4CLioBF,CK7nBA,oBAGE,qBAAA,CADA,+CLgoBF,CK7nBE,4CACE,QL+nBJ,CK9nBI,iGAEE,eAAA,CACA,oBLgoBN,CKlnBA,mBACE,WAAA,CACA,mBAAA,CAAA,gBLqnBF,CKlnBA,qBACE,iCLqnBF,CKlnBA,6BAOE,YAAA,CACA,qBAAA,CAJA,WAAA,CAKA,6BAAA,CAJA,WAAA,CAHA,iBLynBF,CE7mBM,0CGdN,6BAYI,6CLmnBF,CACF,CMhrBI,sFACE,YNsrBN,CO1rBE,sCAEE,yDAAA,CACA,yCAAA,CAEA,4BAAA,CACA,uBPisBJ,CO/rBI,2DAEE,qBPgsBN,CO/rBM,iEACE,8BPisBR,CO9rBM,0EACE,qBPgsBR,CO/rBQ,gFACE,qCPisBV,CO5rBI,wDAEE,qCAAA,CAEA,uBAAA,CAGA,cAAA,CACA,mBAAA,CAFA,oBP6rBN,COzrBM,8DACE,YP2rBR,CO/qBI,kDAEE,kBAAA,CADA,YAAA,CAGA,SAAA,CADA,sBPkrBN,CO9qBI,uDACE,yCAAA,CACA,wBAAA,CAKA,cAAA,CAJA,eAAA,CAEA,WAAA,CADA,gCAAA,CAEA,UPirBN,COrqBA,SAEE,oDPuqBF,COrqBE,gCAGE,gCAAA,CADA,ePuqBJ,CO9pBI,4EACE,ePmqBN,COhqBI,gDAME,yCAAA,CALA,gBAAA,CAGA,iBAAA,CAFA,WAAA,CACA,aPoqBN,CO3pBE,sCACE,YAAA,CACA,qBAAA,CACA,QP6pBJ,CO5pBI,0BAJF,sCAKI,kBAAA,CACA,QP+pBJ,CACF,CQlxBA,WAEE,0DAAA,CACA,mEAAA,CACA,qDAAA,CAEA,WRyxBF,CQrxBE,iBAeE,4BAAA,CAPA,QAAA,CAJA,UAAA,CACA,aAAA,CAIA,MAAA,CAPA,mBAAA,CAIA,iBAAA,CAIA,OAAA,CAHA,KAAA,CAMA,gCAAA,CAFA,SRuxBJ,CQ7wBI,sCACE,6DAAA,CACA,mBR+wBN,CQxwBE,2BAEE,eAAA,CAEA,WAAA,CADA,eR0wBJ,CQrwBE,0BAEE,eAAA,CADA,mBRwwBJ,CQpwBE,iCACE,QAAA,CACA,KRswBJ,CQnwBE,6BAIE,wDAAA,CAEA,4CAAA,CAHA,eAAA,CADA,iBRuwBJ,CQjwBI,0CACE,eRmwBN,CQ9vBE,qCACE,kDAAA,CACA,oCRgwBJ,CQxvBE,qCAEE,yCAAA,CADA,eR2vBJ,CQvvBE,wCACE,oCRyvBJ,CQtvBE,gDAGE,kBAAA,CAFA,YAAA,CACA,6BRyvBJ,CQtvBI,qEACE,YRwvBN,CQhvBI,kDAEE,kBAAA,CADA,YRmvBN,CQ/uBI,uCACE,URivBN,CQ9uBI,wCACE,iBRgvBN,CQ7uBI,gDACE,iBR+uBN,CE/xBM,0CM+CF,gDAGI,iBRivBN,CACF,CQ9uBI,gDACE,eRgvBN,CQvuBI,uHACE,QR4uBN,CQvuBI,yCACE,+BAAA,CACA,eRyuBN,CQtuBI,gDACE,+CAAA,CACA,oCRyuBN,CQruBI,iGAHE,wDR2uBN,CQpuBI,gDACE,+CAAA,CACA,oCRsuBN,CQhuBE,wCAIE,oCAAA,CAFA,oCAAA,CAGA,iBAAA,CAFA,iBRmuBJ,CQ/tBI,8CAUE,yDAAA,CATA,UAAA,CACA,aAAA,CAGA,WAAA,CADA,iBAAA,CAGA,KAAA,CACA,2BAAA,CAFA,URmuBN,CQvtBE,+BAME,8CAAA,CADA,iBAAA,CAHA,aAAA,CACA,kBR0tBJ,CQrtBI,iEAGE,kBAAA,CAFA,YAAA,CACA,qBAAA,CAEA,ORutBN,CQptBI,yDAEE,yDAAA,CADA,YRutBN,CE71BM,0CMqIF,yDAKI,YRutBN,CACF,CQptBI,oDAOE,kBAAA,CAFA,YAAA,CACA,qBAAA,CAHA,QAAA,CAKA,sBAAA,CANA,YR0tBN,CE12BM,0CM8IF,oDAWI,kBRqtBN,CACF,CQntBM,0DAEE,uCAAA,CADA,eRstBR,CQltBM,2DAEE,iBAAA,CADA,+BRqtBR,CQ5sBE,8BACE,YAAA,CACA,cAAA,CAEA,QAAA,CADA,aR+sBJ,CQ3sBE,6BAGE,kBAAA,CAIA,8CAAA,CACA,iBAAA,CANA,mBAAA,CAGA,SAAA,CADA,kBAAA,CAOA,wBAAA,CAAA,qBAAA,CAAA,gBRysBJ,CQtsBM,mCACE,iBAAA,CACA,oBRwsBR,CQlsBE,2CACE,qDAAA,CACA,qCRosBJ,CQjsBE,2CACE,yCRmsBJ,CQ/rBE,mCAEE,kBAAA,CADA,YAAA,CAEA,aRisBJ,CQ/rBI,uCAEE,WAAA,CADA,URksBN,CQ7rBE,mCACE,aR+rBJ,CQzrBE,6BAEE,yBAAA,CACA,uBR0rBJ,CQtrBE,oCAIE,8CAAA,CACA,+CAAA,CAEA,mCRwrBJ,CQhrBE,yEAJE,mCAAA,CATA,0CAAA,CAMA,sCAAA,CACA,uCR8rBJ,CQxrBE,qCAUE,eAAA,CANA,4CRorBJ,CQ1qBE,qCACE,YAAA,CACA,6BR4qBJ,CQzqBE,sCAEE,QAAA,CADA,SR4qBJ,CQxqBE,oCAKE,+CAAA,CADA,eAAA,CAFA,UR2qBJ,CQtqBI,8EAEE,wBRwqBN,CQnqBM,oFAGE,cAAA,CADA,iBRsqBR,CQnqBQ,4GACE,eRsqBV,CQnqBQ,0GACE,gBRsqBV,CQhqBE,sCAEE,oDAAA,CAGA,wCAAA,CAEA,eAAA,CAHA,YAAA,CADA,aRoqBJ,CQ9pBI,wCACE,wBRgqBN,CQ3pBE,4CACE,8CAAA,CAEA,eAAA,CADA,gBR8pBJ,CStiCA,kBAEI,sBAAA,CAOA,kBAAA,CAKA,4BAAA,CAVA,YAAA,CACA,qBAAA,CAOA,mCAAA,CANA,QAAA,CAIA,eAAA,CAHA,YT2iCJ,CSliCI,yBACI,eToiCR,CSjiCI,iVAYI,+BTqiCR,CShiCI,oCACI,YTkiCR,CS/hCI,kCAEI,YAAA,CACA,qBAAA,CACA,QTgiCR,CEtgCM,0CO9BF,kCAOQ,YAAA,CACA,kBTiiCV,CACF,CS9hCI,0BACI,yBTgiCR,CS7hCI,0CACI,sBT+hCR,CElhCM,0COvEN,kBA8DQ,YT+hCN,CU7lCF,gBAEI,eAAA,CACA,0FVimCF,CANF,CEvhCM,yCSvEN,qCAII,gBXsmCF,CACF,CWjmCA,UAEE,qBAAA,CAEA,gCXkmCF,CWhmCE,qBACE,eXkmCJ,CW7lCA,oBAEE,iBX+lCF,CW3lCA,0BAUE,YAAA,CAPA,MAAA,CAGA,WAAA,CAJA,iBAAA,CAEA,OAAA,CACA,KAAA,CAGA,0BX6lCF,CE1jCM,yCS3CN,0BAcI,aAAA,CAGA,YAAA,CADA,WX2lCF,CACF,CEjkCM,0CS3CN,0BAwBI,YAAA,CADA,WX0lCF,CACF,CW7kCM,yCACE,iCXglCR,CW7kCM,6CACE,kBX+kCR,CW1kCE,yCACE,cX4kCJ,CWxkCI,kCACE,aAAA,CACA,eX0kCN,CWpkCI,wEACE,aXskCN,CWjkCE,iCAEE,iBAAA,CADA,iBXokCJ,CW3jCE,yBAEE,kBAAA,CADA,YX+jCJ,CW3jCE,mCAEE,sBAAA,CADA,YAAA,CAGA,QAAA,CADA,0BX8jCJ,CW1jCE,yCAIE,gDAAA,CAFA,kDAAA,CACA,iBAAA,CAFA,mBAAA,CAIA,gCX4jCJ,CW3jCI,8FACE,iDX6jCN,CWjjCA,8BAEE,0BAAA,CACA,gCAAA,CAEA,gDAAA,CAGA,gBAAA,CADA,iBXmjCF,CW9iCA,sCASE,oCAAA,CACA,iDAAA,CACA,kDAAA,CAPA,YAAA,CAGA,WAAA,CAFA,sBAAA,CAHA,iBXsjCF,CW3iCE,4CAcE,kFAAA,CAKA,oCAAA,CACA,mBAAA,CAlBA,UAAA,CAEA,aAAA,CAkBA,6CAAA,CAZA,MAAA,CAJA,WAAA,CADA,YAAA,CAGA,iBAAA,CAGA,OAAA,CAFA,KAAA,CAQA,mCAAA,CAIA,4CAAA,CATA,SXgjCJ,CWhiCA,4CAEE,iBAAA,CACA,KAAA,CACA,2BAAA,CACA,SXkiCF,CW9hCA,uCAME,2DAAA,CAJA,4CAAA,CACA,iBAAA,CACA,SXiiCF,CWrhCA,mBAGE,kBAAA,CADA,YAAA,CAGA,cAAA,CACA,QAAA,CAFA,sBX8hCF,CEhrCM,0CS8IN,mBAQI,QX8hCF,CACF,CWxhCA,mBACE,YAAA,CACA,qBAAA,CACA,QX4hCF,CE5rCM,yCS6JN,mBAKI,kBX8hCF,CW7hCE,2CACE,UX+hCJ,CACF,CWzhCA,+BACE,kBX4hCF,CWrhCA,sBAEE,oBAAA,CAGA,0CAAA,CAGA,8CAAA,CAFA,YAAA,CAFA,eX0hCF,CEjtCM,yCSmLN,sBAUI,+CXwhCF,CACF,CWphCA,8BAIE,2CAAA,CAFA,eXuhCF,CE3tCM,yCSkMN,8BAMI,4CXuhCF,CACF,CWjhCA,mBACE,aXqhCF,CWlhCA,2BAKE,iDAAA,CAJA,YAAA,CACA,QAAA,CAEA,4BAAA,CADA,UXuhCF,CWjhCM,iFACE,WXmhCR,CWxgCA,iBAME,0CAAA,CAJA,aAAA,CAGA,iBAAA,CADA,cAAA,CADA,4BX8gCF,CWzgCE,6BACE,YX2gCJ,CWtgCA,wBACE,cAAA,CACA,wBX0gCF,CWjgCA,kDARE,0CX8gCF,CWjgCA,2BAEE,kBAAA,CAOA,sCAAA,CADA,iBAAA,CAPA,mBAAA,CAMA,eAAA,CAFA,WAAA,CAFA,sBAAA,CACA,UXwgCF,CWjgCE,kEAEE,qCXkgCJ,CW//BE,kCACE,WXigCJ,CW5/BA,4BACE,YAAA,CACA,UX+/BF,CW5/BA,2EACE,uBX+/BF,CWz/BM,yBADF,8CAEI,iBX6/BN,CACF,CWr/BA,gBACE,aAAA,CACA,UXy/BF,CWt/BA,oBAEE,iBAAA,CAMA,eAAA,CALA,OAAA,CAIA,eAAA,CAFA,UXy/BF,CWp/BE,sBAGE,iBAAA,CADA,UXs/BJ,CWn/BI,yBALF,sBAMI,WXs/BJ,CACF,CWp/BI,iCAGE,iBAAA,CAFA,eAAA,CACA,UXu/BN,CWn/BI,gCACE,gBXq/BN,CW/+BA,cAEE,iBAAA,CADA,UXm/BF,CWh/BE,gBAKE,iDAAA,CAJA,kBAAA,CAGA,4BXm/BJ,CWh/BI,0BAEE,eAAA,CADA,gBXm/BN,CW1+BA,eACE,+BX8+BF,CW3+BA,gBAEE,YAAA,CAEA,QAAA,CADA,kBAAA,CAFA,iBXi/BF,CW5+BE,kBACE,QX8+BJ,CW1+BA,uBACE,QX6+BF,CY96CE,8BACE,QAAA,CACA,kCZi7CJ,CY96CE,+BACE,oDZg7CJ,CY76CE,iCACE,mDZ+6CJ,CY56CE,6BACE,QAAA,CACA,SZ86CJ,CYl6CE,gCACE,SZo6CJ,CY/3CA,oBACE,eZi4CF,Car8CA,QAGE,kBAAA,CADA,mBAAA,CAaA,0CAAA,CAFA,wCAAA,CARA,QAAA,CADA,sBAAA,CAOA,gBAAA,CAJA,iBAAA,CADA,qBb88CF,Can8CE,2BAEE,2BAAA,CACA,yBAAA,CAAA,wBAAA,CADA,0BAAA,CAEA,iBbo8CJ,CEl5CM,yCWvEN,QA2BI,2Cbk8CF,CACF,Ca/7CE,kBACE,+BAAA,CAGA,kDAAA,CAFA,kBAAA,CACA,Ubk8CJ,Ca97CE,2BACE,ebg8CJ,Ca77CE,6BACE,Qb+7CJ,Ca/6CE,0BACE,iDAAA,CACA,yBb27CJ,Cax7CE,yBACE,gDAAA,CACA,yBb07CJ,Cav7CE,oBAME,sCAAA,CAEA,cAAA,CANA,eAAA,CACA,wBAAA,CACA,yBb07CJ,Cap7CI,8BACE,eAAA,CAEA,kBAAA,CADA,mBbu7CN,Can7CI,oCAEE,cbo7CN,Cal7CM,oFAEE,uCbm7CR,Cah7CM,2CACE,sCAAA,CACA,cAAA,CACA,mBbk7CR,Ca36CE,qBAGE,kBAAA,CADA,mBAAA,CAQA,+CAAA,CACA,0CAAA,CAPA,QAAA,CAEA,WAAA,CACA,cAAA,CACA,eAAA,CAKA,qBby6CJ,Cav6CI,sDAEE,+Bbw6CN,Can6CM,uCAEE,WAAA,CACA,uBAAA,CAFA,Ubu6CR,Ca/5CE,uBAEE,sBAAA,CAGA,kDAAA,CAFA,mCAAA,CAIA,qDb85CJ,Ca55CI,6BACE,wCAAA,CACA,mCb85CN,Caz5CE,wBAEE,+CAAA,CACA,4BAAA,CAEA,qDby5CJ,Cav5CI,8BAEE,wCAAA,CACA,mCbw5CN,Cal5CE,qBAWE,0CAAA,CALA,gBAAA,CAGA,WAAA,CADA,kBbk5CJ,CE1/CM,0CWgGJ,qBAcI,ebg5CJ,CACF,CE//CM,0CWgGJ,qBAoBI,0CAAA,CAFA,WAAA,CACA,gBbk5CJ,CACF,Ca94CE,sBAiBE,gDAAA,CAXA,gBAAA,CACA,iBb64CJ,CE5gDM,0CWwHJ,sBAUI,eb84CJ,CACF,CEjhDM,0CWwHJ,sBAoBI,iDAAA,CANA,Wb+4CJ,CACF,Cc7lDE,OAEE,qDdomDJ,CclmDI,4BAEE,UAAA,CADA,QdqmDN,CcjmDI,sBAEE,gBAAA,CADA,iBdomDN,CchmDI,qBACE,iDdkmDN,Cc/lDI,uBAEE,iBdgmDN,Cc5lDI,sBACE,cd8lDN,CEhjDM,yCY5CA,8BAGI,sBAAA,CADA,Ud+lDR,CACF,CcrlDI,iCAKE,yCAAA,CAFA,uCAAA,CADA,iBdylDN,Cc9kDI,iCACE,gBdilDN,Cc9kDI,oCAIE,MAAA,CAEA,WAAA,CAJA,iBAAA,CAGA,OAAA,CAFA,KAAA,CAKA,0BAAA,CAPA,WdslDN,Cc5kDI,4CACE,0Bd8kDN,Cc3kDI,oCACE,yBd6kDN,Cc1kDI,+BACE,oDd4kDN,CctkDI,gCACE,qBdykDN,CctkDI,yCACE,+BAAA,CACA,edwkDN,CcrkDI,mCACE,kCAAA,CACA,yBdukDN,CcrkDM,yCACE,uBdukDR,Cc9jDI,sCACE,WdqkDN,CcjkDM,kDACE,WdmkDR,Cc/jDI,8CACE,YAAA,CACA,sBAAA,CACA,cdikDN,Cc5jDI,4CAEE,WAAA,CACA,eAAA,CAFA,cdgkDN,Cc3jDI,2CAIE,8CAAA,CAHA,eAAA,CAEA,sCAAA,CADA,mCd+jDN,Cc1jDI,oDACE,YAAA,CACA,iBd4jDN,Cc1jDM,2DACE,cd4jDR,CcxjDI,4BAEE,kBAAA,CADA,Yd2jDN,CEvoDM,0CYgFF,yBAGI,4CAAA,CACA,6CdwjDN,CACF,CeltDE,2BAEE,wCfqtDJ,CgBvtDA,+BAEE,ahB2tDF,CgBttDA,2CAIE,YhBwtDF,CgB1sDA,kQAEE,ahBstDF,CiBnvDA,gBAEE,iBjBqvDF,CiBnvDE,uBAME,QAAA,CACA,WAAA,CAJA,MAAA,CAMA,kBAAA,CACA,SAAA,CARA,iBAAA,CAEA,OAAA,CACA,KAAA,CAGA,UjBsvDJ,CiBhvDM,8GAEE,qCAAA,CADA,YjBmvDR,CiB1uDA,0BAKE,WAAA,CAHA,iBAAA,CACA,kBjB6uDF,CiBzuDE,gCACE,qBjB2uDJ,CiBxuDE,gCACE,8BjB0uDJ,CiBxuDI,sCACE,0BAAA,CACA,ejB0uDN,CiBtuDE,gCACE,8BjBwuDJ,CiBnuDA,yBAEE,cjBquDF,CiBnuDE,mDAGE,gPAAA,CAGA,qCAAA,CADA,2BAAA,CADA,oBAAA,CAFA,kBAAA,CAMA,mBjBmuDJ,CiB5tDE,oCfrCA,kBAAA,CAQA,QAAA,CAHA,WAAA,CAJA,UAAA,CAKA,WAAA,CAFA,cAAA,CADA,aAAA,CAJA,eAAA,CAQA,SAAA,CATA,2BAAA,CAIA,SF6wDF,CiBluDE,qCACE,kCAAA,CAGA,kCAAA,CADA,yDAAA,CADA,yBjBuuDJ,CkB1zDA,eAEE,4BlB4zDF,CkB1zDE,yCACE,sClB4zDJ,CkBvzDA,sBAKE,gDAAA,CACA,iBAAA,CAJA,kBAAA,CACA,sClB2zDF,CkBpzDA,qBAEE,YAAA,CACA,gCAAA,CAEA,sClBqzDF,CkBjzDA,sBACE,cAAA,CAEA,mBAAA,CAAA,gBAAA,CADA,UlBqzDF,CkBjzDA,wBACE,iBlBozDF,CkBjzDA,yBACE,UlBozDF,CkBjzDA,uBAIE,wDAAA,CAHA,YAAA,CACA,6BAAA,CAKA,eAAA,CAFA,sCAAA,CACA,gBlBozDF,CmBj2DI,sCACE,iCAAA,CACA,mCAAA,CACA,wCnBo2DN,CmB/1DA,aACE,YAAA,CAIA,cAAA,CAFA,QAAA,CADA,sBnBo2DF,CmB71DA,YAGE,kBAAA,CAWA,0CAAA,CAGA,iBAAA,CACA,+BAAA,CAVA,cAAA,CANA,YAAA,CAEA,SAAA,CAOA,eAAA,CAEA,YAAA,CAHA,iBAAA,CAYA,yCAAA,CAFA,wBAAA,CAAA,qBAAA,CAAA,gBAAA,CAdA,UnBu2DF,CmBp1DE,kBAEE,oCAAA,CADA,qBnBu1DJ,CE/zDM,yCiBlDN,YAkCI,qBAAA,CACA,YAAA,CAHA,iBAAA,CACA,UnBu1DF,CACF,CmBj1DA,kBAGE,WAAA,CAFA,mBAAA,CACA,UnBq1DF,CmBj1DA,qBACE,YAAA,CACA,qBAAA,CACA,SAAA,CACA,enBo1DF,CEp1DM,yCiBJN,qBAQI,iBnBo1DF,CACF,CmB70DA,4CACE,enBo1DF,CoBt6DE,mBACE,iBpBy6DJ,CoBt6DE,gBAEE,iBAAA,CACA,epBu6DJ,CoBn6DQ,4BACE,iCpBq6DV,CoB/5DI,mBACE,iCpBi6DN,CoB95DI,sCAME,eAAA,CAHA,mBAAA,CACA,iBAAA,CACA,qBpBg6DN,CoB75DM,8DAGE,wCAAA,CAFA,kBAAA,CACA,epBi6DR,CoB75DM,4DACE,mBpBg6DR,CoB15DM,yBAKE,iCAAA,CACA,kCAAA,CALA,sCAAA,CAEA,cAAA,CADA,mCAAA,CAEA,oBpB85DR,CqB/8DA,cAEE,sCAAA,CACA,6BAAA,CACA,oCAAA,CAGA,aAAA,CACA,yBAAA,CAFA,iBrBk9DF,CqB98DE,sCAEE,qCrB+8DJ,CqB78DI,6CAOE,uBAAA,CALA,UAAA,CACA,aAAA,CAGA,WAAA,CAEA,uCAAA,CAJA,iBAAA,CACA,SAAA,CAIA,UrB88DN,CqBz8DE,+CACE,6CrB28DJ,CqBz8DM,6EACE,0CrB28DR,CqBp8DA,oBAEE,gBAAA,CAMA,kBAAA,CAOA,eAAA,CAEA,kBAAA,CACA,iCAAA,CAXA,mBAAA,CAIA,uBAAA,CAFA,sBAAA,CAJA,MAAA,CADA,iBAAA,CAEA,KAAA,CAcA,8BAAA,CAPA,kBAAA,CAHA,sBrB28DF,CqB77DA,oBAGE,oBAAA,CAFA,eAAA,CACA,kBrBi8DF,CsBlgEA,gBAEE,QAAA,CAKA,gCAAA,CAJA,MAAA,CAFA,cAAA,CAGA,OAAA,CAKA,0BAAA,CACA,wBAAA,CALA,StBwgEF,CsB//DE,sBACE,aAAA,CACA,uBtBigEJ,CsB7/DI,kCACE,wCtB+/DN,CE78DM,yCoB9CJ,+BAII,aAAA,CACA,UtB2/DJ,CACF,CsBx/DE,kCACE,YtB0/DJ,CEt9DM,0CoB/BJ,8CAEI,cAAA,CACA,etBu/DJ,CACF,CsBp/DE,uCAGE,QAAA,CAFA,wBAAA,CACA,StBu/DJ,CEj+DM,0CoBlBJ,iDAGI,YAAA,CACA,mBtBo/DJ,CACF,CsBh/DE,mDACE,oBtBk/DJ,CsB7+DE,4CAGE,8CAAA,CACA,kBAAA,CAFA,uCAAA,CADA,iBtBk/DJ,CEh/DM,yCoBHJ,4CAOI,aAAA,CACA,UtBg/DJ,CACF,CsB7+DE,kDACE,YtB++DJ,CsB1+DA,qBAEE,8CAAA,CACA,uCAAA,CAEA,iBAAA,CACA,wBAAA,CASA,kBAAA,CAUA,qCAAA,CACA,2BAAA,CAZA,YAAA,CAQA,mCAAA,CANA,sBAAA,CANA,MAAA,CAUA,WAAA,CADA,YAAA,CAXA,iBAAA,CAGA,OAAA,CAaA,iBAAA,CAfA,KAAA,CAGA,2BAAA,CAgBA,0BAAA,CAVA,sBAAA,CAAA,iBtB++DF,CsBn+DE,2BAEE,oDtBo+DJ,CsBj+DE,uDAUE,qCAAA,CAPA,UAAA,CACA,aAAA,CASA,WAAA,CAPA,iBAAA,CACA,KAAA,CAIA,0BAAA,CACA,uBAAA,CAJA,UtBo+DJ,CsB39DE,4BAGE,gDAAA,CAFA,UAAA,CACA,sBtB89DJ,CsB19DE,2BAGE,iDAAA,CAFA,WAAA,CACA,qBtB69DJ,CsBt9DA,yBAIE,+CAAA,CAFA,YtBy9DF,CE7iEM,yCoBwFJ,oCAEI,StBu9DJ,CsBj9DF,uBAII,8CAAA,CADA,gBtBq9DF,CANF,CsBz8DA,uBACE,YAAA,CACA,qBtBm9DF,CE9jEM,0CoByGN,uBAKI,kBAAA,CACA,QtBo9DF,CACF,CsBj9DA,sBACE,YAAA,CACA,QtBo9DF,CsBj9DA,yBACE,YAAA,CACA,wBtBo9DF,CsBh9DE,kCACE,QtBm9DJ,CsB/8DA,iCAEE,GACE,wCtBi9DF,CsB98DA,IACE,wCtBg9DF,CsB78DA,IACE,wCtB+8DF,CsB58DA,IACE,wCtB88DF,CsB38DA,IACE,wCtB68DF,CsB18DA,GACE,wCtB48DF,CACF,CEvmEM,yCoB+JN,MAEI,kBAAA,CACA,iBAAA,CACA,iBtB08DF,CACF,CE9mEM,yCoB+JN,MAQI,kBAAA,CACA,iBAAA,CACA,iBtB28DF,CACF,CuBxrEA,kBAEE,uBAAA,CACA,cAAA,CAEA,YAAA,CACA,qBAAA,CACA,2BAAA,CAEA,uBvB2rEF,CuBzrEE,2BAEE,WAAA,CADA,mBvB4rEJ,CuBxrEE,0CACE,8CAAA,CACA,iBAAA,CACA,gBAAA,CACA,iBAAA,CACA,wBAAA,CACA,qBAAA,CACA,gBvB0rEJ,CuBprEE,uCACE,mBvBurEJ,CuBnrEA,yBAEE,YAAA,CACA,qBAAA,CACA,QvBqrEF,CuBjrEA,iCACE,YAAA,CAEA,kBAAA,CADA,kBvBqrEF,CE9pEM,0CqBzBN,iCAOI,kBvBorEF,CACF,CEnqEM,0CqBfJ,wCAEI,WvBorEJ,CACF,CuBhrEA,uBAGE,kBAAA,CADA,YAAA,CAEA,sBAAA,CAEA,iBvBirEF,CuB7qEA,kCAEE,iBvB+qEF,CuB3qEA,0BAKE,uCAAA,CACA,wCAAA,CALA,YAAA,CAGA,kBAAA,CADA,yBAAA,CADA,YvBkrEF,CuB1qEE,2CACE,mBvB6qEJ,CuBzqEA,eACE,avB4qEF,CuBnqEA,qBAIE,aAAA,CAFA,eAAA,CACA,SvByqEF,CuBtqEE,iDAGE,8CAAA,CACA,wCAAA,CAHA,eAAA,CACA,YvB0qEJ,CEntEM,0CqBuCJ,iDAOI,YvByqEJ,CACF,CuBpqEE,iDAGE,iDAAA,CADA,kBAAA,CADA,mBvBwqEJ,CuBnqEE,4CAEE,QAAA,CADA,SvBsqEJ,CuBlqEE,iDAGE,kBAAA,CAFA,+BAAA,CACA,iBvBqqEJ,CuBjqEE,+CACE,8CvBmqEJ,CuBhqEE,uDACE,YvBkqEJ,CuB9pEI,8DACE,8CvBgqEN,CuB5pEE,sCACE,uBAAA,CACA,WAAA,CACA,kBvB8pEJ,CuB5pEI,6CACE,YvB8pEN,CuB1pEE,+CACE,YvB4pEJ,CuB1pEI,0BAHF,+CAII,YvB6pEJ,CACF,CuB1pEE,oCACE,YAAA,CACA,sBvB4pEJ,CuBzpEE,uCACE,YAAA,CACA,KvB2pEJ,CwB90EA,gBAEE,YxBg1EF,CwB90EE,yCACE,axBg1EJ,CwB50EI,sCACE,UxB80EN,CyBx1EA,QAEE,wCzB01EF,CyBx1EE,wBAGE,0CzB01EJ,CyBv1EE,8CALE,8CAAA,CACA,4BzBg2EJ,CyB51EE,sBAGE,wCzBy1EJ,C0Bt2EA,cAEE,mBAAA,CACA,iBAAA,CACA,sBAAA,CAEA,aAAA,CACA,iCAAA,CAEA,iB1Bs2EF,C0Bl2EA,qBAOE,2CAAA,CADA,mBAAA,CADA,0BAAA,CADA,aAAA,CAIA,wCAAA,CANA,iB1By2EF,C0B/1EA,qBAME,kBAAA,CAYA,QAAA,CACA,UAAA,CAfA,YAAA,CACA,qBAAA,CAEA,QAAA,CAGA,0BAAA,CARA,mBAAA,CAOA,iBAAA,CAGA,uEACE,CAQF,S1Bw1EF,C0Bh1EA,qBACE,0B1Bm1EF,C0Bn0EA,kCAXE,6BAAA,CAOA,SAAA,CAJA,uHACE,CAHF,4B1Bo1EF,C0Bz0EA,mCAZE,6BAAA,CAOA,SAAA,CAJA,uHACE,CAHF,4B1B21EF,C0B/0EA,uCAbE,6BAAA,CAOA,SAAA,CAJA,uHACE,CAHF,4B1Bk2EF,C0Br1EA,2CAdE,6BAAA,CAOA,SAAA,CAJA,uHACE,CAHF,4B1By2EF,C0B11EA,kCAAoC,S1B81EpC,C0B71EA,mCAAqC,S1Bi2ErC,C0Bh2EA,uCAAyC,S1Bo2EzC,C0Bn2EA,oDAAsD,S1Bu2EtD,C0Br2EA,qBAQE,2CAAA,CACA,mBAAA,CAJA,0BAAA,CAFA,MAAA,CAQA,mBAAA,CATA,iBAAA,CAEA,KAAA,CAEA,2B1B02EF,C0Bj2EA,0BAKE,kGAAA,CAEA,uBAAA,CACA,2BAAA,CAFA,uBAAA,CAJA,6BAAA,CACA,4B1Bu2EF,C0B51EA,2BAOE,iDAAA,CAFA,iBAAA,CAHA,mBAAA,CAMA,wCAAA,CAJA,cAAA,CAMA,iBAAA,CACA,KAAA,CACA,2B1B21EF,C0Bv1EA,sBAEE,YAAA,CACA,6BAAA,CACA,gBAAA,CACA,mBAAA,CACA,wBAAA,CAAA,qBAAA,CAAA,gB1By1EF,C2B79EA,yBAEE,wD3Bg+EF,C2B79EA,cAEE,iCAAA,CADA,S3Bi+EF,C2B79EA,QACE,e3Bg+EF,C2Bv9EE,yCAEE,kBAAA,CADA,YAAA,CAGA,qBAAA,CADA,Q3B29EJ,C2Bp9EA,OAIE,iBAAA,CACA,eAAA,CAEA,+BAAA,CADA,kBAAA,CAHA,eAAA,CADA,U3B29EF,C2Bp9EE,2BACE,sC3Bs9EJ,C2Bn9EE,yBACE,qC3Bq9EJ,C2Bl9EE,2BACE,wB3Bo9EJ,C2Bj9EE,2BACE,Y3Bm9EJ,C2Bh9EE,6BACE,a3Bk9EJ,C2B/8EE,6BAEE,0BAAA,CACA,2BAAA,CAFA,c3Bm9EJ,C2Bv8EA,sBACE,6C3B08EF,C2Bv8EA,sBACE,8B3B08EF,C4BnhFI,2DAEE,kB5BshFN,C4BphFM,0FAGE,QAAA,CAGA,yCAAA,CAJA,kBAAA,CAMA,sC5BkhFR,C4BhhFQ,gGAEE,uBAAA,CADA,e5BmhFV,C4B/gFQ,kIACE,mC5BihFV,C4B7gFM,gGAEE,e5B8gFR,C4B5gFQ,qGACE,8CAAA,CAGA,QAAA,CAFA,eAAA,CACA,Y5B+gFV,C4B1gFM,yFACE,QAAA,CACA,Y5B4gFR,C4B1gFQ,+FACE,c5B4gFV,C4BxgFM,2FAIE,mDAAA,CADA,kBAAA,CADA,kBAAA,CADA,2B5B6gFR,C6B5jFE,6BACE,S7BgkFJ,CE/+EE,OACE,uBFo/EJ,CEj/EE,QACE,sBFo/EJ,CE5+EE,WACE,yBF++EJ,CE5+EE,YACE,0BF++EJ,CE5+EE,aACE,2BF++EJ,CEnhFM,+DAcJ,cACE,uBF0gFF,CEvgFA,eACE,sBFygFF,CEjgFA,kBACE,yBFmgFF,CEhgFA,mBACE,0BFkgFF,CE//EA,oBACE,2BFigFF,C8B5lFE,WACE,a9B8lFJ,CACF,CEziFM,yCAcJ,iBACE,uBF8hFF,CE3hFA,kBACE,sBF6hFF,CErhFA,qBACE,yBFuhFF,CEphFA,sBACE,0BFshFF,CEnhFA,uBACE,2BFqhFF,C8BhnFE,cACE,a9BknFJ,CACF,CE7jFM,yCAcJ,mBACE,uBFkjFF,CE/iFA,oBACE,sBFijFF,CEziFA,uBACE,yBF2iFF,CExiFA,wBACE,0BF0iFF,CEviFA,yBACE,2BFyiFF,C8BpoFE,gBACE,a9BsoFJ,CACF,CEjlFM,iEAcJ,eACE,uBFskFF,CEnkFA,gBACE,sBFqkFF,CE7jFA,mBACE,yBF+jFF,CE5jFA,oBACE,0BF8jFF,CE3jFA,qBACE,2BF6jFF,C8BxpFE,YACE,a9B0pFJ,CACF,CErmFM,0CAcJ,kBACE,uBF0lFF,CEvlFA,mBACE,sBFylFF,CEjlFA,sBACE,yBFmlFF,CEhlFA,uBACE,0BFklFF,CE/kFA,wBACE,2BFilFF,C8B5qFE,eACE,a9B8qFJ,CACF,CEznFM,0CAcJ,oBACE,uBF8mFF,CE3mFA,qBACE,sBF6mFF,CErmFA,wBACE,yBFumFF,CEpmFA,yBACE,0BFsmFF,CEnmFA,0BACE,2BFqmFF,C8BhsFE,iBACE,a9BksFJ,CACF,CE7oFM,iEAcJ,cACE,uBFkoFF,CE/nFA,eACE,sBFioFF,CEznFA,kBACE,yBF2nFF,CExnFA,mBACE,0BF0nFF,CEvnFA,oBACE,2BFynFF,C8BptFE,WACE,a9BstFJ,CACF,CEjqFM,0CAcJ,iBACE,uBFspFF,CEnpFA,kBACE,sBFqpFF,CE7oFA,qBACE,yBF+oFF,CE5oFA,sBACE,0BF8oFF,CE3oFA,uBACE,2BF6oFF,C8BxuFE,cACE,a9B0uFJ,CACF,CErrFM,0CAcJ,mBACE,uBF0qFF,CEvqFA,oBACE,sBFyqFF,CEjqFA,uBACE,yBFmqFF,CEhqFA,wBACE,0BFkqFF,CE/pFA,yBACE,2BFiqFF,C8B5vFE,gBACE,a9B8vFJ,CACF,CEzsFM,iEAcJ,aACE,uBF8rFF,CE3rFA,cACE,sBF6rFF,CErrFA,iBACE,yBFurFF,CEprFA,kBACE,0BFsrFF,CEnrFA,mBACE,2BFqrFF,C8BhxFE,UACE,a9BkxFJ,CACF,CE7tFM,0CAcJ,gBACE,uBFktFF,CE/sFA,iBACE,sBFitFF,CEzsFA,oBACE,yBF2sFF,CExsFA,qBACE,0BF0sFF,CEvsFA,sBACE,2BFysFF,C8BpyFE,aACE,a9BsyFJ,CACF,CEjvFM,0CAcJ,kBACE,uBFsuFF,CEnuFA,mBACE,sBFquFF,CE7tFA,sBACE,yBF+tFF,CE5tFA,uBACE,0BF8tFF,CE3tFA,wBACE,2BF6tFF,C8BxzFE,eACE,a9B0zFJ,CACF,C8BpzFA,WCi0FE,M/BVF,CEzzFE,gBAGE,UAAA,CAFA,UAAA,CACA,aF4zFJ,C8BxzFA,gC5BQE,kBAAA,CAQA,QAAA,CAHA,WAAA,CAJA,UAAA,CAKA,WAAA,CAFA,cAAA,CADA,aAAA,CAJA,eAAA,CAQA,SAAA,CATA,2BAAA,CAIA,SF6zFF,C8Bl0FA,QACE,Y9Bq0FF,C8B9zFA,mBACE,Y9Bq0FF,C8Bl0FA,aACE,sB9Bq0FF,C8Bl0FA,cACE,kB9Bq0FF,C8Bl0FA,WACE,oB9Bq0FF,C8Bl0FA,eACE,wB9Bq0FF,C8Bl0FA,gBACE,oB9Bq0FF,C8Bl0FA,aACE,sB9Bq0FF,C8Bl0FA,QACE,Q9Bq0FF,C8Bl0FA,sCAEE,kC9Bq0FF,C8Bl0FA,sBACE,6B9Bq0FF,C8Bl0FA,eACE,4B9Bq0FF,C8B/zFE,2BACE,iC9Bk0FJ,C8Bn0FE,2BACE,iC9Bs0FJ,C8Bv0FE,6BACE,mC9B00FJ,C8B30FE,4BACE,kC9B80FJ,C8B/0FE,2BACE,iC9Bk1FJ,C8Bn1FE,2BACE,iC9Bs1FJ,C8Bv1FE,0BACE,gC9B01FJ,C8B31FE,wBACE,8B9B81FJ,C8B/1FE,wBACE,8B9Bk2FJ,C8B71FE,gBACE,4B9Bg2FJ,C8Bj2FE,gBACE,4B9Bo2FJ,C8Br2FE,kBACE,8B9Bw2FJ,C8Bz2FE,iBACE,6B9B42FJ,C8B72FE,gBACE,4B9Bg3FJ,C8Bj3FE,gBACE,4B9Bo3FJ,C8Br3FE,eACE,2B9Bw3FJ,C8Bz3FE,aACE,yB9B43FJ,C8B73FE,aACE,yB9Bg4FJ,C8B33FA,wBACE,gC9B83FF,C8B33FA,sBACE,gCAAA,CAAA,6B9B83FF,C8B33FA,yBACE,mCAAA,CAAA,gC9B83FF,C8B33FA,yBACE,mCAAA,CAAA,gC9B83FF,C8B33FA,uBACE,iCAAA,CAAA,8B9B83FF,C8B33FA,wBACE,kCAAA,CAAA,+B9B83FF,C8Bz3FA,oBACE,2B9B43FF,C8Bz3FA,kBACE,yB9B43FF,C8Bz3FA,mBACE,0B9B43FF,CE77FM,yC4BoEN,4BAEI,2B9B43FF,C8Bx3FF,0BAEI,yB9B43FF,C8Bx3FF,2BAEI,0B9B43FF,CAXF,C8B72FA,QACE,kB9B43FF,C8Bv3FA,WACE,kB9B03FF,C8Bv3FA,gBACE,sB9B03FF,C8Bv3FA,kBACE,wB9B03FF,C8Bv3FA,iBACE,uB9B03FF,C8Bv3FA,mBACE,yB9B03FF,C8Bv3FA,YACE,mB9B03FF,C8Bv3FA,iBACE,uB9B03FF,C8Bv3FA,mBACE,yB9B03FF,C8Bv3FA,kBACE,wB9B03FF,C8Bv3FA,oBACE,0B9B03FF,C8Bv3FA,kBACE,2B9B03FF,C8Bv3FA,kBACE,2B9B03FF,C8Bv3FA,kBACE,2B9B03FF,C8Bv3FA,kBACE,2B9B03FF,C8Bv3FA,kBACE,2B9B03FF,C8Bv3FA,mBACE,4B9B03FF,C8Bv3FA,mBACE,4B9B03FF,C8Bv3FA,mBACE,4B9B03FF,C8Bv3FA,mBACE,4B9B03FF,C8Bv3FA,mBACE,4B9B03FF,C8Bv3FA,iBACE,0B9B03FF,C8Bv3FA,iBACE,0B9B03FF,C8Bv3FA,iBACE,0B9B03FF,C8Bv3FA,iBACE,0B9B03FF,C8Bv3FA,iBACE,0B9B03FF,C8Bv3FA,oBACE,6B9B03FF,C8Bv3FA,oBACE,6B9B03FF,C8Bv3FA,oBACE,6B9B03FF,C8Bv3FA,oBACE,6B9B03FF,C8Bv3FA,oBACE,6B9B03FF,C8Bh3FE,sEACE,sB9Bu3FJ,C8Bn3FA,UAEE,eAAA,CACA,oBAAA,CAFA,iB9Bw3FF,CgChqGA,yDAGE,uChCoqGF,CgCjqGA,mBACE,4BAAA,CACA,2BAAA,CACA,UAAA,CACA,aAAA,CACA,WAAA,CAEA,eAAA,CACA,mBAAA,CACA,iBAAA,CACA,UAAA,CACA,OAAA,CAEA,uBAAA,CADA,uBAAA,CAEA,+BAAA,CARA,UhC4qGF,CgCjqGA,wBACE,yBhCoqGF,CgCjqGA,wCACE,SAAA,CACA,mBAAA,CACA,gChCoqGF,CgCjqGA,sBACE,oBAAA,CACA,gCAAA,CACA,mBhCoqGF,CgCjqGA,4BACE,uChCoqGF,CgCjqGA,kBACE,UhCoqGF,CgCjqGA,wCACE,gBAAA,CACA,iBhCoqGF,CgCjqGA,mBACE,WhCoqGF,CgCjqGA,yCACE,SAAA,CACA,OhCoqGF,CgCjqGA,mCAGE,gDAAA,CACA,iBAAA,CACA,uCAAA,CAIA,MAAA,CAPA,cAAA,CAYA,SAAA,CARA,mBAAA,CACA,iBAAA,CACA,QAAA,CAGA,qCAAA,CADA,sBAAA,CAEA,kEAAA,CAXA,UAAA,CAYA,ShCqqGF,CgCjqGA,mBACE,iBAAA,CACA,qBAAA,CAGA,gBAAA,CAFA,eAAA,CAGA,eAAA,CAFA,ShCsqGF,CgClqGE,kCACE,sBhCoqGJ,CgC/pGA,qBACE,cAAA,CACA,eAAA,CACA,eAAA,CACA,YAAA,CACA,iBAAA,CACA,kBAAA,CACA,eAAA,CACA,kBhCkqGF,CgC/pGA,8BACE,ehCkqGF,CgC/pGA,8BACE,4BAAA,CACA,UAAA,CACA,chCkqGF,CgC/pGA,uBACE,ehCkqGF,CgC/pGA,wDACE,YhCkqGF,CgC/pGA,6DACE,ahCkqGF,CgC3pGA,2BAEE,WAAA,CAGA,gBAAA,CADA,eAAA,CADA,gBAAA,CAFA,mBhCkqGF,CgC3pGA,wCAEE,6CAAA,CADA,2CAAA,CAGA,iBAAA,CACA,oBAAA,CAEA,cAAA,CADA,gBAAA,CAEA,iBAAA,CACA,gBAAA,CANA,chCoqGF,CgC3pGA,6CACE,aAAA,CACA,gBAAA,CACA,ShC8pGF,CgC3pGA,qCAKE,yBAAA,CAJA,qBAAA,CAEA,WAAA,CACA,mBAAA,CAFA,UhCiqGF,CgC3pGA,iCAEE,mDAAA,CACA,2CAAA,CACA,iBAAA,CAHA,qBAAA,CAIA,+BAAA,CACA,oBAAA,CAOA,WAAA,CADA,gBAAA,CAHA,iBAAA,CAEA,eAAA,CAGA,mBAAA,CANA,gBAAA,CADA,qBAAA,CAGA,UhCkqGF,CgCtpGE,qB9BrJA,kBAAA,CAQA,QAAA,CAHA,WAAA,CAJA,UAAA,CAKA,WAAA,CAFA,cAAA,CADA,aAAA,CAJA,eAAA,CAQA,SAAA,CATA,2BAAA,CAIA,SFwzGF,CgChqGI,yBACE,sBhCkqGN,CgC9pGE,8BACE,UhCgqGJ,CgC5pGA,aAEE,gCAAA,CAUA,mDAAA,CADA,4CAAA,CAEA,yCAAA,CATA,YAAA,CAGA,WAAA,CAFA,0BAAA,CAIA,yBAAA,CAMA,eAAA,CAPA,UhCiqGF,CgCxpGE,mBACE,kDhC2pGJ,CgCvpGE,sCAHE,2ChC6pGJ,CgCrpGI,6DACE,wBAAA,CACA,ShCupGN,CgCnpGE,sBAEE,eAAA,CACA,sBAAA,CAFA,kBhCupGJ,CgClpGE,mBACE,ehCopGJ,CgCjpGE,qBAGE,gCAAA,CADA,eAAA,CADA,gBhCqpGJ,CgCjpGI,8CACE,+BAAA,CACA,iBAAA,CACA,kDhCmpGN,CgC7oGE,mCACE,wChC+oGJ,CgCxoGI,2CAEE,wBAAA,CADA,iBAAA,CAEA,4BhC0oGN,CgCjoGI,qEAEE,4BAAA,CACA,yBAAA,CAFA,wBhCsoGN,CgCloGM,2EACE,gChCooGR,CgCloGQ,iFACE,sBhCooGV,CgC5nGA,aAEE,0BAAA,CAEA,cAAA,CACA,iBhC6nGF,CgC3nGE,mCAEE,mCAAA,CADA,chC8nGJ,CgC3nGI,yCACE,YhC6nGN,CgC3nGM,iDACE,iBhC6nGR,CgC3nGQ,sDACE,ehC6nGV,CgC1nGQ,uDAQE,yBAAA,CAPA,UAAA,CACA,aAAA,CAKA,WAAA,CAHA,MAAA,CAKA,SAAA,CANA,iBAAA,CAEA,KAAA,CACA,UAAA,CAIA,UhC4nGV,CgCxnGU,6DACE,ShC0nGZ,CiCp8GA,mBACE,GACE,SjCw8GF,CiCt8GA,GACE,SjCw8GF,CACF,CiCr8GA,oBACE,GACE,SjCu8GF,CiCr8GA,GACE,SjCu8GF,CACF,CiCp8GA,kBAEE,GACE,sBjCq8GF,CiCn8GA,GACE,uBjCq8GF,CACF,CiC7zGA,qBACE,GAME,kCAAA,CAJA,kBAAA,CAEA,8BjC67GF,CiCx7GA,IAIE,iCAAA,CAFA,oBjC47GF,CiCv7GA,IAIE,kCAAA,CAFA,oBjC27GF,CiCt7GA,IAIE,iCAAA,CAFA,oBjC07GF,CiCr7GA,IAIE,kCAAA,CAFA,kBjCy7GF,CACF,CiCp7GA,WAEE,yCjCs7GF,CiCn4GA,4BAEE,MAGE,uBjCq6GF,CiCl6GA,gBAKE,0BjCi6GF,CiC95GA,YAIE,yBjC85GF,CiC35GA,IAEE,yBjC65GF,CiC15GA,IAEE,0BjC45GF,CACF,CiCx5GA,kBAEE,oEjC05GF", "file": "custom.min.css", "sourcesContent": ["\n/*  -----------------------------------\n    Links\n    ----------------------------------- */\n\n.link--animated {\n  &.link--animated--spaced {\n    &:after {\n      bottom: -0.25em;\n    }\n  }\n  &.link--animated--bold {\n    font-weight: var(---font-weight-body--bold);\n    &:after {\n      height: 2px;\n    }\n  }\n  &.link--animated--show-underline {\n    &:after {\n      transform: scaleX(1);\n    }\n    &:hover, &:focus {\n      &:after {\n        transform: scaleX(0);\n      }\n    }\n  }\n}\n", "@charset \"UTF-8\";\n/* 1. Variables */ /*\n$site-width: 1600px;\n$container-width: 1200px;\n$container-narrow-width: 800px;\n$container-extra-narrow-width: 600px;\n\n$container-gutter--desktop: 24px;\n$container-gutter--mobile: 24px;\n\n$section-spacer--desktop: 50px;\n$section-spacer--mobile: 25px;\n*/\n/*  ------------------------------\n    Grid Variables\n    ------------------------------ */\n/**\n * Define your breakpoints. The first value is the prefix that shall be used for\n * your classes (e.g. `.small--one-half`), the second value is the media query\n * that the breakpoint fires at.\n */\n/**\n * Define which namespaced breakpoints you would like to generate for each of\n * widths, push and pull. This is handy if you only need pull on, say, desk, or\n * you only need a new width breakpoint at mobile sizes. It allows you to only\n * compile as much CSS as you need. All are turned on by default, but you can\n * add and remove breakpoints at will.\n *\n * Push and pull shall only be used if `$push` and/or `$pull` and `$responsive`\n * have been set to ‘true’.\n */\n/* 2. Mixins */\n/*\n\n  1. Utilities\n  2. Responsive\n  3. UI Elements\n    3.1. Buttons\n\n*/\n/*  ==============================\n    1. Utilities\n    ============================== */\n/*  ==============================\n    2. Responsive\n    ============================== */\n/*================ Responsive Show/Hide Helper ================*/\n/*================ Responsive Text Alignment Helper ================*/\n/*  ==============================\n    3. UI Elements\n    ============================== */\n/*  ------------------------------\n    3.1. Buttons\n    ------------------------------ */\n/* ------------------------------\n   Headings\n   ------------------------------ */\n/* ------------------------------\n   Labels\n   ------------------------------ */\n/* ------------------------------\n   Inputs\n   ------------------------------ */\n/* ------------------------------\n   RTE\n   ------------------------------ */\n/*  ------------------------------\n    3.3. Shopify\n    ------------------------------ */\n/* 3. Fonts  */\n/* 4. Basic Styles */\n/*  -----------------------------------\n    Links\n    ----------------------------------- */\n.link--animated.link--animated--spaced:after {\n  bottom: -0.25em;\n}\n.link--animated.link--animated--bold {\n  font-weight: var(---font-weight-body--bold);\n}\n.link--animated.link--animated--bold:after {\n  height: 2px;\n}\n.link--animated.link--animated--show-underline:after {\n  transform: scaleX(1);\n}\n.link--animated.link--animated--show-underline:hover:after, .link--animated.link--animated--show-underline:focus:after {\n  transform: scaleX(0);\n}\n\nh1, .h1,\nh2, .h2, .jdgm-rev-widg__title, .jdgm-carousel-title,\nh3, .h3,\nh4, .h4 {\n  line-height: var(---line-height-heading--mobile);\n  letter-spacing: var(---letter-spacing-heading--mobile);\n}\n\nh1, .h1,\nh2, .h2, .jdgm-rev-widg__title, .jdgm-carousel-title,\nh3, .h3,\nh4, .h4 {\n  font-family: var(--heading-font-family);\n  font-weight: var(--heading-font-weight);\n  font-style: var(--heading-font-style);\n  color: rgb(var(--heading-color));\n  text-transform: var(--heading-text-transform);\n  display: block;\n  letter-spacing: var(---letter-spacing-heading--mobile);\n  font-weight: var(---font-weight-heading);\n}\n\n.heading--large,\n.rte .heading--large {\n  font-family: var(---font-family-heading);\n  font-style: var(---font-style-heading);\n  font-weight: var(---font-weight-heading);\n  font-variation-settings: \"wght\" 400;\n  letter-spacing: var(---letter-spacing-heading--mobile);\n  line-height: var(---line-height-heading--mobile);\n  font-size: var(---font-size-h0--mobile);\n}\n.heading--large span.heading--alternate,\n.rte .heading--large span.heading--alternate {\n  color: var(---color-heading-1);\n  text-transform: none;\n  font-weight: var(---font-weight-heading);\n}\n.heading--large a,\n.rte .heading--large a {\n  color: var(---color-link);\n}\n@media only screen and (min-width: 741px) {\n  .heading--large,\n  .rte .heading--large {\n    letter-spacing: var(---letter-spacing-heading--desktop);\n    line-height: var(---line-height-heading--desktop);\n  }\n}\n@media only screen and (min-width: 741px) {\n  .heading--large,\n  .rte .heading--large {\n    font-size: var(---font-size-h0--desktop);\n  }\n}\n\nh1, .h1,\n.rte h1, .rte .h1 {\n  font-family: var(---font-family-heading);\n  font-style: var(---font-style-heading);\n  font-weight: var(---font-weight-heading);\n  font-variation-settings: \"wght\" 400;\n  letter-spacing: var(---letter-spacing-heading--mobile);\n  line-height: var(---line-height-heading--mobile);\n  font-size: var(---font-size-h1--mobile);\n}\nh1 span.heading--alternate, .h1 span.heading--alternate,\n.rte h1 span.heading--alternate, .rte .h1 span.heading--alternate {\n  color: var(---color-heading-1);\n  text-transform: none;\n  font-weight: var(---font-weight-heading);\n}\nh1 a, .h1 a,\n.rte h1 a, .rte .h1 a {\n  color: var(---color-link);\n}\n@media only screen and (min-width: 741px) {\n  h1, .h1,\n  .rte h1, .rte .h1 {\n    letter-spacing: var(---letter-spacing-heading--desktop);\n    line-height: var(---line-height-heading--desktop);\n  }\n}\n@media only screen and (min-width: 741px) {\n  h1, .h1,\n  .rte h1, .rte .h1 {\n    font-size: var(---font-size-h1--desktop);\n  }\n}\n\nh2, .h2, .jdgm-rev-widg__title, .jdgm-carousel-title,\n.rte h2, .rte .h2 {\n  font-family: var(---font-family-heading);\n  font-style: var(---font-style-heading);\n  font-weight: var(---font-weight-heading);\n  font-variation-settings: \"wght\" 400;\n  letter-spacing: var(---letter-spacing-heading--mobile);\n  line-height: var(---line-height-heading--mobile);\n  font-size: var(---font-size-h2--mobile);\n}\nh2 span.heading--alternate, .h2 span.heading--alternate, .jdgm-rev-widg__title span.heading--alternate, .jdgm-carousel-title span.heading--alternate,\n.rte h2 span.heading--alternate, .rte .h2 span.heading--alternate {\n  color: var(---color-heading-1);\n  text-transform: none;\n  font-weight: var(---font-weight-heading);\n}\nh2 a, .h2 a, .jdgm-rev-widg__title a, .jdgm-carousel-title a,\n.rte h2 a, .rte .h2 a {\n  color: var(---color-link);\n}\n@media only screen and (min-width: 741px) {\n  h2, .h2, .jdgm-rev-widg__title, .jdgm-carousel-title,\n  .rte h2, .rte .h2 {\n    letter-spacing: var(---letter-spacing-heading--desktop);\n    line-height: var(---line-height-heading--desktop);\n  }\n}\n@media only screen and (min-width: 741px) {\n  h2, .h2, .jdgm-rev-widg__title, .jdgm-carousel-title,\n  .rte h2, .rte .h2 {\n    font-size: var(---font-size-h2--desktop);\n  }\n}\n\nh3, .h3,\n.rte h3, .rte .h3 {\n  font-family: var(---font-family-heading);\n  font-style: var(---font-style-heading);\n  font-weight: var(---font-weight-heading);\n  font-variation-settings: \"wght\" 400;\n  letter-spacing: var(---letter-spacing-heading--mobile);\n  line-height: var(---line-height-heading--mobile);\n  font-size: var(---font-size-h3--mobile);\n}\nh3 span.heading--alternate, .h3 span.heading--alternate,\n.rte h3 span.heading--alternate, .rte .h3 span.heading--alternate {\n  color: var(---color-heading-1);\n  text-transform: none;\n  font-weight: var(---font-weight-heading);\n}\nh3 a, .h3 a,\n.rte h3 a, .rte .h3 a {\n  color: var(---color-link);\n}\n@media only screen and (min-width: 741px) {\n  h3, .h3,\n  .rte h3, .rte .h3 {\n    letter-spacing: var(---letter-spacing-heading--desktop);\n    line-height: var(---line-height-heading--desktop);\n  }\n}\n@media only screen and (min-width: 741px) {\n  h3, .h3,\n  .rte h3, .rte .h3 {\n    font-size: var(---font-size-h3--desktop);\n  }\n}\n\nh4, .h4,\n.rte h4, .rte .h4 {\n  font-family: var(---font-family-heading);\n  font-style: var(---font-style-heading);\n  font-weight: var(---font-weight-heading);\n  font-variation-settings: \"wght\" 400;\n  letter-spacing: var(---letter-spacing-heading--mobile);\n  line-height: var(---line-height-heading--mobile);\n  font-size: var(---font-size-h4--mobile);\n}\nh4 span.heading--alternate, .h4 span.heading--alternate,\n.rte h4 span.heading--alternate, .rte .h4 span.heading--alternate {\n  color: var(---color-heading-1);\n  text-transform: none;\n  font-weight: var(---font-weight-heading);\n}\nh4 a, .h4 a,\n.rte h4 a, .rte .h4 a {\n  color: var(---color-link);\n}\n@media only screen and (min-width: 741px) {\n  h4, .h4,\n  .rte h4, .rte .h4 {\n    letter-spacing: var(---letter-spacing-heading--desktop);\n    line-height: var(---line-height-heading--desktop);\n  }\n}\n@media only screen and (min-width: 741px) {\n  h4, .h4,\n  .rte h4, .rte .h4 {\n    font-size: var(---font-size-h4--desktop);\n  }\n}\n\nh5, .h5,\n.rte h5, .rte .h5 {\n  font-family: var(---font-family-body);\n  font-style: var(---font-style-body);\n  font-weight: var(---font-weight-body--bold);\n  letter-spacing: var(---letter-spacing-body--mobile);\n  line-height: var(---line-height-body--mobile);\n  font-size: var(---font-size-h5--mobile);\n}\nh5 span.heading--alternate, .h5 span.heading--alternate,\n.rte h5 span.heading--alternate, .rte .h5 span.heading--alternate {\n  color: var(---color-heading-2);\n  text-transform: none;\n  font-weight: var(---font-weight-body);\n}\nh5 a, .h5 a,\n.rte h5 a, .rte .h5 a {\n  color: var(---color-link);\n}\n@media only screen and (min-width: 741px) {\n  h5, .h5,\n  .rte h5, .rte .h5 {\n    letter-spacing: var(---letter-spacing-heading--desktop);\n    line-height: var(---line-height-heading--desktop);\n  }\n}\n@media only screen and (min-width: 741px) {\n  h5, .h5,\n  .rte h5, .rte .h5 {\n    font-size: var(---font-size-h5--desktop);\n  }\n}\n\nh6, .h6,\n.rte h6, .rte .h6 {\n  font-family: var(---font-family-body);\n  font-style: var(---font-style-body);\n  font-weight: var(---font-weight-body--bold);\n  letter-spacing: var(---letter-spacing-body--mobile);\n  line-height: var(---line-height-body--mobile);\n  font-size: var(---font-size-h6--mobile);\n}\nh6 span.heading--alternate, .h6 span.heading--alternate,\n.rte h6 span.heading--alternate, .rte .h6 span.heading--alternate {\n  color: var(---color-heading-2);\n  text-transform: none;\n  font-weight: var(---font-weight-body);\n}\nh6 a, .h6 a,\n.rte h6 a, .rte .h6 a {\n  color: var(---color-link);\n}\n@media only screen and (min-width: 741px) {\n  h6, .h6,\n  .rte h6, .rte .h6 {\n    letter-spacing: var(---letter-spacing-heading--desktop);\n    line-height: var(---line-height-heading--desktop);\n  }\n}\n@media only screen and (min-width: 741px) {\n  h6, .h6,\n  .rte h6, .rte .h6 {\n    font-size: var(---font-size-h6--desktop);\n  }\n}\n\n.subheading {\n  color: RGB(var(--subheading-color));\n}\n\n.heading.heading--regular {\n  letter-spacing: var(---letter-spacing-subheading--mobile);\n  text-transform: uppercase;\n  font-style: var(---font-style-heading);\n  font-weight: var(---font-weight-heading);\n  font-family: var(---font-family-heading);\n  font-size: var(---font-size-subheading-large--mobile);\n}\n@media only screen and (min-width: 741px) {\n  .heading.heading--regular {\n    font-size: var(---font-size-subheading-large--desktop);\n  }\n}\n\n.product-sticky-form__title,\n.heading.heading--small {\n  letter-spacing: var(---letter-spacing-subheading--mobile);\n  text-transform: uppercase;\n  font-style: var(---font-style-heading);\n  font-weight: var(---font-weight-heading);\n  font-family: var(---font-family-heading);\n  font-size: var(---font-size-subheading--mobile);\n}\n.product-sticky-form__title + p,\n.product-sticky-form__title + .h1,\n.product-sticky-form__title + h1,\n.product-sticky-form__title + .h2,\n.product-sticky-form__title + .jdgm-rev-widg__title,\n.product-sticky-form__title + .jdgm-carousel-title,\n.product-sticky-form__title + h2,\n.product-sticky-form__title + .h3,\n.product-sticky-form__title + h3,\n.product-sticky-form__title + .h4,\n.product-sticky-form__title + h4,\n.heading.heading--small + p,\n.heading.heading--small + .h1,\n.heading.heading--small + h1,\n.heading.heading--small + .h2,\n.heading.heading--small + .jdgm-rev-widg__title,\n.heading.heading--small + .jdgm-carousel-title,\n.heading.heading--small + h2,\n.heading.heading--small + .h3,\n.heading.heading--small + h3,\n.heading.heading--small + .h4,\n.heading.heading--small + h4 {\n  margin-top: 12px;\n}\n.product-sticky-form__title + hr,\n.heading.heading--small + hr {\n  margin-top: 0;\n}\n@media only screen and (min-width: 741px) {\n  .product-sticky-form__title,\n  .heading.heading--small {\n    font-size: var(---font-size-subheading--desktop);\n  }\n}\n\n.heading.heading--xsmall {\n  letter-spacing: var(---letter-spacing-subheading--mobile);\n  text-transform: uppercase;\n  font-style: var(---font-style-heading);\n  font-weight: var(---font-weight-heading);\n  font-family: var(---font-family-heading);\n  font-size: var(---font-size-subheading-small--mobile);\n}\n@media only screen and (min-width: 741px) {\n  .heading.heading--xsmall {\n    font-size: var(---font-size-subheading-small--desktop);\n  }\n}\n\nbody {\n  line-height: var(---line-height-body--mobile);\n}\n\n.text--small {\n  margin-top: 0;\n  font-size: var(---font-size-body--mobile);\n}\n@media only screen and (min-width: 741px) {\n  .text--small {\n    font-size: var(---font-size-body--desktop);\n  }\n}\n\n.text--xxsmall,\n.tiny {\n  font-size: var(---font-size-body-xs--mobile);\n}\n@media only screen and (min-width: 741px) {\n  .text--xxsmall,\n  .tiny {\n    font-size: var(---font-size-body-xs--desktop);\n  }\n}\n\n.text--xsmall,\n.minor {\n  font-size: var(---font-size-body-small--mobile);\n}\n@media only screen and (min-width: 741px) {\n  .text--xsmall,\n  .minor {\n    font-size: var(---font-size-body-small--desktop);\n  }\n}\n\n.text--large,\n.major {\n  font-size: var(---font-size-body-large--mobile);\n}\n@media only screen and (min-width: 741px) {\n  .text--large,\n  .major {\n    font-size: var(---font-size-body-large--desktop);\n  }\n}\n\n@media only screen and (max-width: 740px) {\n  .p--mobile {\n    font-size: var(---font-size-body--mobile);\n    font-family: var(---font-family-body);\n    font-weight: var(---font-weight-body);\n  }\n}\n\n.text--xxsmall,\n.tiny {\n  font-size: var(---font-size-body-xs--mobile);\n}\n@media only screen and (min-width: 741px) {\n  .text--xxsmall,\n  .tiny {\n    font-size: var(---font-size-body-xs--desktop);\n  }\n}\n.text--xxsmall p,\n.tiny p {\n  font-size: var(---font-size-body-xs--mobile);\n}\n@media only screen and (min-width: 741px) {\n  .text--xxsmall p,\n  .tiny p {\n    font-size: var(---font-size-body-xs--desktop);\n  }\n}\n\n@media only screen and (max-width: 740px) {\n  .text--xxsmall--mobile,\n  .p-tiny--mobile {\n    font-family: var(---font-family-body);\n    font-weight: var(---font-weight-body);\n    font-size: var(---font-size-body-xs--mobile);\n  }\n}\n\n.text--xsmall,\n.minor {\n  font-size: var(---font-size-body-small--mobile);\n}\n@media only screen and (min-width: 741px) {\n  .text--xsmall,\n  .minor {\n    font-size: var(---font-size-body-small--desktop);\n  }\n}\n.text--xsmall p:not(.heading),\n.minor p:not(.heading) {\n  font-size: var(---font-size-body-small--mobile);\n}\n@media only screen and (min-width: 741px) {\n  .text--xsmall p:not(.heading),\n  .minor p:not(.heading) {\n    font-size: var(---font-size-body-small--desktop);\n  }\n}\n\n@media only screen and (max-width: 740px) {\n  .text--small--mobile,\n  .p-minor--mobile {\n    font-size: var(---font-size-body-small--mobile);\n    font-family: var(---font-family-body);\n    font-weight: var(---font-weight-body);\n  }\n}\n\n.text--large,\n.major {\n  font-size: var(---font-size-body-large--mobile);\n}\n@media only screen and (min-width: 741px) {\n  .text--large,\n  .major {\n    font-size: var(---font-size-body-large--desktop);\n  }\n}\n.text--large p:not(.heading),\n.major p:not(.heading) {\n  font-size: var(---font-size-body-large--mobile);\n}\n@media only screen and (min-width: 741px) {\n  .text--large p:not(.heading),\n  .major p:not(.heading) {\n    font-size: var(---font-size-body-large--desktop);\n  }\n}\n\n@media only screen and (max-width: 740px) {\n  .text--large--mobile,\n  .p-major--mobile {\n    font-size: var(---font-size-body-large--mobile);\n    font-family: var(---font-family-body);\n    font-weight: var(---font-weight-body);\n  }\n}\n\nstrong, .strong {\n  font-weight: var(---font-weight-body--bold);\n}\n\n.link.link--strong {\n  font-weight: var(---font-weight-body--bold);\n  text-decoration: none;\n}\n\n.blockquote, blockquote {\n  /*\n  font-size: var(---font-size-h2--mobile);\n  @include respond-to($small-up){\n    font-size: var(---font-size-h2--desktop);\n  }\n  */\n  font-size: var(---font-size-h3--mobile);\n}\n@media only screen and (min-width: 741px) {\n  .blockquote, blockquote {\n    font-size: var(---font-size-h3--desktop);\n  }\n}\n\n/* Product Titles */\n.product-item .product-item-meta__title {\n  font-family: var(---font-family-heading);\n  font-weight: var(---font-weight-body--bold);\n  line-height: var(---line-height-heading--mobile);\n  letter-spacing: var(---letter-spacing-subheading--mobile);\n  font-size: var(---font-size-body-large--mobile);\n}\n@media only screen and (min-width: 741px) {\n  .product-item .product-item-meta__title {\n    font-size: var(---font-size-body-large--desktop);\n  }\n}\n\n.product-meta__title {\n  font-size: var(---font-size-h1--mobile);\n}\n@media only screen and (min-width: 741px) {\n  .product-meta__title {\n    font-size: var(---font-size-h1--desktop);\n  }\n}\n\n/* Header */\n.header__linklist {\n  font-weight: var(---font-weight-body--bold);\n}\n\n.quiz h1, .quiz .h1,\n.quiz h2, .quiz .h2, .quiz .jdgm-rev-widg__title, .quiz .jdgm-carousel-title,\n.quiz h3, .quiz .h3,\n.quiz h4, .quiz .h4 {\n  margin-top: 36px;\n  margin-bottom: 12px;\n}\n\n.quiz .icon.icon--fill * {\n  stroke: none;\n  fill: currentColor;\n}\n\nhtml {\n  scroll-behavior: smooth;\n}\n\n.table--bordered td {\n  border-top: 1px solid #e6e6e6;\n}\n\n/* 5. Layout */\n.split-page {\n  background: RGB(var(--section-block-background));\n}\n@media only screen and (min-width: 1001px) {\n  .split-page {\n    min-height: 80vh;\n    height: 100%;\n    display: grid;\n    grid-template-columns: 3fr 4fr;\n  }\n}\n.split-page .page-header__text-wrapper {\n  margin-top: 0;\n  margin-bottom: 38px;\n}\n\n.split-page__header {\n  padding-top: calc(var(--vertical-breather) * 2);\n}\n\n.split-page__footer {\n  padding-bottom: calc(var(--vertical-breather) * 2);\n  justify-self: flex-end;\n}\n.split-page__footer .form__secondary-action {\n  margin: 0;\n}\n.split-page__footer .form__secondary-action button,\n.split-page__footer .form__secondary-action a {\n  font-weight: bold;\n  text-decoration: none;\n}\n\n.split-page__image {\n  height: 100%;\n  object-fit: cover;\n}\n\n.split-page__content {\n  padding: 0 var(--container-gutter);\n}\n\n.split-page__content-wrapper {\n  position: relative;\n  height: 100%;\n  margin: auto;\n  display: flex;\n  flex-direction: column;\n  justify-content: space-between;\n}\n@media only screen and (min-width: 1001px) {\n  .split-page__content-wrapper {\n    max-height: calc(100vh - var(--header-height));\n  }\n}\n\nhtml.supports-no-cookies .supports-no-cookies {\n  display: none;\n}\nhtml.supports-cookies .supports-cookies {\n  display: none;\n}\n\n/* 6. Sections */\n[class*=template-customers] {\n  /* ----- Link Bar ----- */\n  /* ----- Page Header ----- */\n  /* ----- Link Bar ----- */\n}\n[class*=template-customers] .link-bar {\n  --background: var(---background-color--content-reversed-1);\n  --text-color: var(---color-text--reversed);\n  background: var(--background);\n  color: var(--text-color);\n}\n[class*=template-customers] .link-bar .link-bar__link-item {\n  transition: 0.25s color;\n}\n[class*=template-customers] .link-bar .link-bar__link-item:hover {\n  color: var(---color--highlight);\n}\n[class*=template-customers] .link-bar .link-bar__link-item .text--subdued {\n  transition: 0.25s color;\n}\n[class*=template-customers] .link-bar .link-bar__link-item .text--subdued:hover {\n  color: var(---color--danger) !important;\n}\n[class*=template-customers] .link-bar .text--underlined {\n  --text-color: var(---color--highlight);\n  color: var(--text-color);\n  text-decoration: none;\n  cursor: default;\n  pointer-events: none;\n}\n[class*=template-customers] .link-bar .text--underlined:after {\n  content: none;\n}\n[class*=template-customers] .page-header .heading {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 0.25em;\n}\n[class*=template-customers] .page-header .bubble-count {\n  background-color: var(---color--highlight);\n  color: var(---color-text);\n  font-weight: 700;\n  letter-spacing: -0.075em !important;\n  height: 36px;\n  width: 36px;\n  font-size: 0.5em;\n}\n\n.account {\n  background-color: var(---background-color--content-1);\n}\n.account .account__orders-table {\n  font-weight: 400;\n  font-size: var(---font-fize-body);\n}\n.account .account__orders-table thead th {\n  padding: 0.5rem 0;\n}\n.account .account__orders-table td {\n  padding: 0.5rem 0;\n}\n.account .account__orders-table .reorder-button {\n  letter-spacing: 0;\n  min-width: 0;\n  padding: 0em 1em;\n  line-height: 2.4em;\n  background-color: var(---color--highlight);\n}\n.account .account__order-item-actions {\n  display: flex;\n  flex-direction: column;\n  gap: 0.5em;\n}\n@media (min-width: 1000px) {\n  .account .account__order-item-actions {\n    flex-direction: row;\n    gap: 20px;\n  }\n}\n\n.mini-cart {\n  --root-background: var(---background-color--content-1--rgb);\n  --section-block-background: var(---background-color--content-1--rgb);\n  --background: var(---background-color--content-1--rgb);\n  width: 100vw;\n  /* ----- Loading Overlay ----- */\n  /* ----- Drawer Header ----- */\n  /* ----- Line Items ----- */\n  /* ----- Cart Subscriptions Box ----- */\n  /* ----- Tags ----- */\n  /* ----- Shipping Details ----- */\n}\n.mini-cart:after {\n  pointer-events: none;\n  content: \"\";\n  display: block;\n  position: absolute;\n  top: 0;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  z-index: 1;\n  transition: 0.25s background-color;\n  background: RGBA(255, 255, 255, 0);\n}\n.mini-cart.cart-drawer--loading:after {\n  background: RGBA(var(---background-color--content-1--rgb), 0.75);\n  pointer-events: auto;\n}\n.mini-cart .drawer__header {\n  border-bottom: 0;\n  max-height: none;\n  height: auto;\n}\n.mini-cart .drawer__title {\n  text-transform: none;\n  margin-bottom: 0;\n}\n.mini-cart .drawer__close-button {\n  bottom: 0;\n  top: 0;\n}\n.mini-cart free-shipping-bar {\n  padding: 20px 30px;\n  margin: 0 0 20px 0;\n  background: RGB(var(---background-color--content-2--rgb));\n  border-radius: var(---border-radius--general);\n}\n.mini-cart free-shipping-bar .text--small {\n  margin-bottom: 0;\n}\n.mini-cart .mini-cart__drawer-footer {\n  --root-border-color: var(---color-line--light--rgb);\n  padding: 20px var(--container-gutter);\n}\n.mini-cart .product-item-meta__title {\n  line-height: 1.2;\n  font-size: var(---font-size-body--desktop);\n}\n.mini-cart .product-item-meta__property {\n  font-weight: var(---font-weight-body);\n}\n.mini-cart .product-item-meta__price-and-remove {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n.mini-cart .product-item-meta__price-and-remove .line-item__quantity {\n  margin-top: 0;\n}\n.mini-cart .line-item .line-item__content-wrapper {\n  margin-top: 0;\n  margin-bottom: 35px;\n}\n.mini-cart .line-item .line-item__info {\n  width: 100%;\n}\n.mini-cart .line-item .line-item__image {\n  border-radius: 6px;\n}\n.mini-cart .line-item .line-item__image-wrapper {\n  margin-right: 10px;\n}\n@media only screen and (min-width: 1001px) {\n  .mini-cart .line-item .line-item__image-wrapper {\n    margin-right: 20px;\n  }\n}\n.mini-cart .line-item .line-item__remove-button {\n  font-weight: 400;\n}\n.mini-cart .line-item .product-item-meta__property-list {\n  margin: 0;\n}\n.mini-cart .line-item .product-item-meta__price-list-container {\n  margin: 0;\n}\n.mini-cart .line-item .quantity-selector {\n  --quantity-selector-height: 32px;\n  overflow: hidden;\n}\n.mini-cart .line-item .quantity-selector__input {\n  font-size: var(---font-size-body-small--desktop);\n  font-weight: var(---font-weight-body);\n  background: RGB(var(---background-color--content-1--rgb));\n}\n.mini-cart .line-item .quantity-selector__button {\n  background: RGB(var(---background-color--content-1--rgb));\n}\n.mini-cart .line-item .line-item__remove-button {\n  font-size: var(---font-size-body-small--desktop);\n  font-weight: var(---font-weight-body);\n}\n.mini-cart .mini-cart__drawer-prefooter {\n  padding: 10px var(--container-gutter);\n  text-align: center;\n  font-weight: var(---font-weight-body);\n  position: relative;\n}\n.mini-cart .mini-cart__drawer-prefooter:after {\n  content: \"\";\n  display: block;\n  position: absolute;\n  height: 30px;\n  width: 100%;\n  top: 0;\n  transform: translateY(-100%);\n  background: linear-gradient(to bottom, rgba(255, 255, 255, 0), rgb(255, 255, 255));\n}\n.mini-cart .cart-subscriptions {\n  display: block;\n  margin-bottom: 12px;\n  border-radius: 8px;\n  background: var(---background-color--content-2);\n}\n.mini-cart .cart-subscriptions .cart-subscriptions-form__actions {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 1em;\n}\n.mini-cart .cart-subscriptions .cart-subscriptions__form {\n  padding: 12px;\n  border-top: 1px solid rgba(var(---color--brand-6--rgb), 0.5);\n}\n@media only screen and (min-width: 1001px) {\n  .mini-cart .cart-subscriptions .cart-subscriptions__form {\n    padding: 24px;\n  }\n}\n.mini-cart .cart-subscriptions .subscriptions-input {\n  margin: 1em 0;\n  gap: 10px;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n}\n@media only screen and (min-width: 1001px) {\n  .mini-cart .cart-subscriptions .subscriptions-input {\n    flex-direction: row;\n  }\n}\n.mini-cart .cart-subscriptions .subscriptions-input label {\n  font-weight: 700;\n  font-size: var(---font-size-body--small);\n}\n.mini-cart .cart-subscriptions .subscriptions-input select {\n  padding: 0.25em 2.5em 0.25em 0.75em;\n  border-radius: 8px;\n}\n.mini-cart .product-item-tags {\n  display: flex;\n  flex-wrap: wrap;\n  margin: 0.5em 0;\n  gap: 10px;\n}\n.mini-cart .product-item-tag {\n  display: inline-flex;\n  align-items: center;\n  padding: 0.35em 0.5em;\n  gap: 0.25em;\n  background: var(---background-color--secondary);\n  border-radius: 4px;\n  user-select: none;\n}\n.mini-cart .product-item-tag svg * {\n  fill: currentColor;\n  outline: currentColor;\n}\n.mini-cart .product-item-tag--prescription {\n  background: RGB(var(---background-color--danger--rgb));\n  color: RGB(var(---color--danger--rgb));\n}\n.mini-cart .product-item-tag--subscription {\n  background-color: var(---color--highlight);\n}\n.mini-cart .product-item-tag__icon {\n  display: flex;\n  align-items: center;\n  line-height: 1;\n}\n.mini-cart .product-item-tag__icon svg {\n  width: 20px;\n  height: 20px;\n}\n.mini-cart .product-item-tag__text {\n  line-height: 1;\n}\n.mini-cart .shipping-details {\n  --padding-horizontal: 30px;\n  --padding-vertical: 20px;\n}\n.mini-cart .shipping-details__inner {\n  margin-top: calc(1 * var(--padding-vertical));\n  margin-left: calc(-1 * var(--padding-horizontal));\n  margin-right: calc(-1 * var(--padding-horizontal));\n  padding-top: var(--padding-vertical);\n  padding-left: var(--padding-horizontal);\n  padding-right: var(--padding-horizontal);\n  border-top: 1px solid RGBA(0, 0, 0, 0.2);\n}\n.mini-cart .shipping-details__footer {\n  margin-top: calc(1 * var(--padding-vertical));\n  padding-top: calc(0.5 * var(--padding-vertical));\n  padding-left: var(--padding-horizontal);\n  padding-right: var(--padding-horizontal);\n  border-top: 1px solid RGBA(0, 0, 0, 0.2);\n  line-height: 1.2;\n}\n.mini-cart .shipping-details__header {\n  display: flex;\n  justify-content: space-between;\n}\n.mini-cart .shipping-details__heading {\n  padding: 0;\n  margin: 0;\n}\n.mini-cart .shipping-details__table {\n  width: 100%;\n  text-align: left;\n  font-size: var(---font-size-body-small--desktop);\n}\n.mini-cart .shipping-details__table th,\n.mini-cart .shipping-details__table td {\n  font-size: 0.9em !important;\n}\n.mini-cart .shipping-details__table tr th,\n.mini-cart .shipping-details__table tr td {\n  text-align: center;\n  padding: 0.1em 0;\n}\n.mini-cart .shipping-details__table tr th:first-child,\n.mini-cart .shipping-details__table tr td:first-child {\n  text-align: left;\n}\n.mini-cart .shipping-details__table tr th:last-child,\n.mini-cart .shipping-details__table tr td:last-child {\n  text-align: right;\n}\n.mini-cart .shipping-details__message {\n  background-color: var(---background-color--content-1);\n  padding: 0.75em;\n  margin: 1em 0;\n  border-radius: var(--block-border-radius);\n  line-height: 1.4;\n}\n.mini-cart .shipping-details__message p {\n  font-size: 0.9em !important;\n}\n.mini-cart .cart-vet-partner__selector-form {\n  border-top: 1px solid var(---color-line--light);\n  padding-top: 20px;\n  margin-top: 20px;\n}\n\n.account-dog-info {\n  --text-font-weight: 300;\n  display: flex;\n  flex-direction: column;\n  gap: 30px;\n  padding: 24px;\n  border-radius: 18px;\n  overflow: hidden;\n  font-weight: var(--text-font-weight);\n  color: RGB(var(--text-color));\n}\n.account-dog-info strong {\n  font-weight: 700;\n}\n.account-dog-info h1,\n.account-dog-info h2,\n.account-dog-info h3,\n.account-dog-info h4,\n.account-dog-info h5,\n.account-dog-info h6,\n.account-dog-info .h1,\n.account-dog-info .h2,\n.account-dog-info .jdgm-rev-widg__title,\n.account-dog-info .jdgm-carousel-title,\n.account-dog-info .h3,\n.account-dog-info .h4,\n.account-dog-info .h5,\n.account-dog-info .h6 {\n  color: RGB(var(--heading-color));\n}\n.account-dog-info .account-dog-info {\n  display: flex;\n}\n.account-dog-info .button-wrapper {\n  display: flex;\n  flex-direction: column;\n  gap: 0.5em;\n}\n@media only screen and (min-width: 1001px) {\n  .account-dog-info .button-wrapper {\n    display: flex;\n    flex-direction: row;\n  }\n}\n.account-dog-info .button {\n  line-height: 1.2 !important;\n}\n.account-dog-info .button-wrapper--center {\n  justify-content: center;\n}\n@media only screen and (min-width: 1001px) {\n  .account-dog-info {\n    padding: 48px;\n  }\n}\n\n@media only screen and (min-width: 1001px) {\n  .product__media {\n    position: sticky;\n    top: calc(var(--announcement-bar-height) + var(--header-height) + var(--vertical-breather));\n  }\n}\n\n@media only screen and (min-width: 741px) {\n  .shopify-section--feeding-calculator {\n    margin-top: 150px;\n  }\n}\n\n.fieldset {\n  --form-input-gap: 24px;\n  margin: var(--container-gutter) 0;\n}\n.fieldset:last-child {\n  margin-bottom: 0;\n}\n\n.feeding-calculator {\n  position: relative;\n}\n\n.feeding-calculator__icon {\n  position: absolute;\n  left: 0;\n  right: 0;\n  top: 0;\n  margin: auto;\n  transform: translateY(-65%);\n  display: none;\n}\n@media only screen and (min-width: 741px) {\n  .feeding-calculator__icon {\n    display: block;\n    width: 200px;\n    height: 200px;\n  }\n}\n@media only screen and (min-width: 1201px) {\n  .feeding-calculator__icon {\n    width: 250px;\n    height: 250px;\n  }\n}\n\n.calculator-results-table tr.selected td {\n  background: var(---color--brand-1);\n}\n.calculator-results-table tr.selected .label {\n  visibility: visible;\n}\n.calculator-results-table tr.results-row {\n  cursor: pointer;\n}\n.calculator-results-table th span {\n  display: block;\n  font-size: 0.75em;\n}\n.calculator-results-table td .label, .calculator-results-table th .label {\n  margin: 0 0.5em;\n}\n.calculator-results-table .label {\n  visibility: hidden;\n  padding: 0.4em 0.8em;\n}\n\n.results-row button.link {\n  display: flex;\n  align-items: center;\n}\n.results-row .results-row__details {\n  display: flex;\n  align-items: flex-start;\n  justify-content: flex-start;\n  gap: 0.5em;\n}\n.results-row .results-row__external-link {\n  padding: 0.05em 0.25em;\n  border: 1px solid RGBA(var(---color-line--rgb), 0.25);\n  border-radius: 5px;\n  background-color: RGBA(var(---color-line--rgb), 0);\n  transition: 0.25s background-color;\n}\n.results-row .results-row__external-link:hover, .results-row .results-row__external-link:focus {\n  background-color: RGBA(var(---color-line--rgb), 0.1);\n}\n\n/* ========== Nutrition ========== */\n.feeding-calculator-nutrition {\n  --product-image-size: 120px;\n  --product-image-border-size: 36px;\n  --primary-button-background: var(--product-color);\n  position: relative;\n  padding-top: 30px;\n}\n\n.feeding-calculator-nutrition__header {\n  position: relative;\n  display: flex;\n  justify-content: center;\n  height: 60px;\n  background: RGB(var(--product-color));\n  border-top-left-radius: var(--block-border-radius);\n  border-top-right-radius: var(--block-border-radius);\n}\n.feeding-calculator-nutrition__header:after {\n  content: \"\";\n  display: block;\n  padding: 15px;\n  margin: auto;\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  z-index: 1;\n  --offset: calc(-1 * (var(--product-image-size) - var(--product-image-border-size) * 2));\n  transform: translateY(var(--offset));\n  background: RGB(var(--product-color));\n  border-radius: 120px;\n  width: calc(var(--product-image-size) + 30px);\n  height: calc(var(--product-image-size) + 30px);\n}\n\n.feeding-calculator-nutrition__header-image {\n  position: absolute;\n  top: 0;\n  transform: translateY(-30px);\n  z-index: 2;\n}\n\n.feeding-calculator-nutrition__content {\n  padding-top: calc(var(--vertical-breather) / 2);\n  position: relative;\n  z-index: 1;\n  background: RGB(var(--section-background, var(--background)));\n}\n\n.spaced-content--tight {\n  gap: 20px;\n}\n\n/* ----- Ratings ----- */\n.nutrition-ratings {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  flex-wrap: wrap;\n  gap: 10px;\n}\n@media only screen and (min-width: 1001px) {\n  .nutrition-ratings {\n    gap: 20px;\n  }\n}\n\n/* ----- Nutrition Summary ----- */\n.nutrition-summary {\n  display: flex;\n  flex-direction: column;\n  gap: 20px;\n}\n@media only screen and (min-width: 741px) {\n  .nutrition-summary {\n    flex-direction: row;\n  }\n  .nutrition-summary .nutrition-summary-item {\n    width: 100%;\n  }\n}\n\n.nutrition-summary-item__title {\n  margin-bottom: 0.5em;\n}\n\n/* ========== Analysis Table ========== */\n.nutritional-analysis {\n  --row-spacing: 0.35em;\n  text-align: left;\n  border: 5px solid RGB(var(--heading-color));\n  padding: 20px;\n  font-size: var(---font-size-body-small--mobile);\n}\n@media only screen and (min-width: 741px) {\n  .nutritional-analysis {\n    font-size: var(---font-size-body-small--desktop);\n  }\n}\n\n.nutritional-analysis__footer {\n  margin-top: 20px;\n  font-size: var(---font-size-body-xs--mobile);\n}\n@media only screen and (min-width: 741px) {\n  .nutritional-analysis__footer {\n    font-size: var(---font-size-body-xs--desktop);\n  }\n}\n\n/* ----- Analysis Category ----- */\n.analysis-category {\n  display: block;\n}\n\n.analysis-category__header {\n  display: flex;\n  gap: 10px;\n  width: 100%;\n  padding: var(--row-spacing) 0;\n  border-bottom: 1px solid var(---color-line--light);\n}\n.analysis-category__header[aria-expanded=true] .analysis-category__button:before {\n  content: \"-\";\n}\n\n/* ----- Analysis Header ----- */\n.analysis-header {\n  display: block;\n  padding: var(--row-spacing) 0;\n  margin-top: 1em;\n  margin-bottom: 5px;\n  border-bottom: 1px solid var(---color-line);\n}\n.analysis-header:first-child {\n  margin-top: 0;\n}\n\n.analysis-header__title {\n  font-size: 20px;\n  text-transform: uppercase;\n  font-weight: var(---font-weight-body--bold);\n}\n\n/* ----- Analysis Category ----- */\n.analysis-category__title {\n  font-weight: var(---font-weight-body--bold);\n}\n\n.analysis-category__button {\n  display: inline-flex;\n  align-items: center;\n  justify-content: center;\n  width: 20px;\n  height: 20px;\n  font-weight: bold;\n  border-radius: 4px;\n  background: RGBA(var(--text-color), 0.05);\n}\n.analysis-category__button:focus, .analysis-category__button:hover {\n  background: RGBA(var(--text-color), 0.1);\n}\n.analysis-category__button:before {\n  content: \"+\";\n}\n\n.analysis-category__content {\n  display: none;\n  width: 100%;\n}\n\n.analysis-category__header[aria-expanded=true] + .analysis-category__content {\n  display: table !important;\n}\n\n@media (min-width: 480px) {\n  .analysis-category .analysis-row > *:first-child {\n    padding-left: 30px;\n  }\n}\n\n/* ----- Analysis Table ----- */\n.analysis-table {\n  display: table;\n  width: 100%;\n}\n\n.analysis-table-row {\n  display: table-row;\n  gap: 5px;\n  width: 100%;\n  line-height: 1.4;\n  font-size: 0.95em;\n}\n.analysis-table-row > * {\n  width: 70px;\n  text-align: center;\n}\n@media (min-width: 480px) {\n  .analysis-table-row > * {\n    width: 100px;\n  }\n}\n.analysis-table-row > *:first-child {\n  text-align: left;\n  width: auto;\n  margin-right: auto;\n}\n.analysis-table-row > *:last-child {\n  text-align: right;\n}\n\n.analysis-row {\n  width: 100%;\n  display: table-row;\n}\n.analysis-row > * {\n  display: table-cell;\n  padding-left: 5px;\n  padding-right: 5px;\n  padding: var(--row-spacing) 0;\n  border-bottom: 1px solid var(---color-line--light);\n}\n.analysis-row > *:last-child {\n  text-align: right;\n  padding-right: 0;\n}\n\n/* ========== Classes ========== */\n.product-color {\n  color: RGB(var(--product-color));\n}\n\n.spaced-content {\n  text-align: center;\n  display: grid;\n  grid-auto-flow: row;\n  gap: 32px;\n}\n.spaced-content > * {\n  margin: 0;\n}\n\n.spaced-content--tight {\n  gap: 20px;\n}\n\n.section.section--use-padding {\n  margin: 0;\n  padding: var(--vertical-breather) 0;\n}\n.section.section--half-padding {\n  --vertical-breather: calc(var(--vertical-breather) / 2);\n}\n.section.section--double-spacing {\n  --vertical-breather: var(--vertical-breather-double);\n}\n.section.section--no-padding {\n  margin: 0;\n  padding: 0;\n}\n.section .container--no-padding {\n  padding: 0;\n}\n.container--smaller {\n  max-width: 700px;\n}\n\n/* 7. Page-Specific Styles */\n/* 8. Components */\n.button {\n  display: inline-flex;\n  align-items: center;\n  justify-content: center;\n  gap: 0.5em;\n  transition: color 0.25s;\n  text-align: center;\n  line-height: 1.2;\n  min-height: unset;\n  font-weight: var(--text-font-bold-weight);\n  font-size: var(---font-size-button--mobile);\n}\n.button:not(.button--text) {\n  padding-inline-start: unset;\n  padding-inline-end: unset;\n  padding: 1em 1.5em;\n}\n@media only screen and (min-width: 741px) {\n  .button {\n    font-size: var(---font-size-button--desktop);\n  }\n}\n.button[disabled] {\n  --button-background: 154, 154, 154;\n  cursor: not-allowed;\n  opacity: 0.5;\n  background-position: 100% -100%, 100% 100% !important;\n}\n.button:not(.button--link) {\n  min-width: 200px;\n}\n.button .loader-button__text {\n  gap: 16px;\n}\n.button:not(.button--text) {\n  /*\n  padding-left: 50px;\n  padding-right: 50px;\n\n  @include respond-to($small-down) {\n    padding: 0 30px;\n  }\n  */\n}\n.button.button--highlight {\n  --button-background: var(---color--highlight--rgb);\n  --button-text-color: 0, 0, 0;\n}\n.button.button--tertiary {\n  --button-background: var(---color--tertiary--rgb);\n  --button-text-color: 0, 0, 0;\n}\n.button.button--tab {\n  min-width: unset;\n  padding-left: 0 !important;\n  padding-right: 0 !important;\n  color: RGB(var(---color--brand-4--rgb));\n  cursor: pointer;\n}\n.button.button--tab[disabled] {\n  background: none;\n  pointer-events: none;\n  cursor: not-allowed;\n}\n.button.button--tab:not([disabled]) {\n  cursor: pointer;\n}\n.button.button--tab:not([disabled]):hover, .button.button--tab:not([disabled]):focus {\n  color: RGB(var(---color--tertiary--rgb));\n}\n.button.button--tab:not([disabled]).active {\n  color: RGB(var(---color--brand-1--rgb));\n  cursor: default;\n  pointer-events: none;\n}\n.button.button--text {\n  display: inline-flex;\n  align-items: center;\n  gap: 10px;\n  min-width: 0;\n  padding-left: 0;\n  padding-right: 0;\n  font-size: var(---font-size-body-large--desktop);\n  font-weight: var(---font-weight-body--bold);\n  transition: 0.25s color;\n}\n.button.button--text:focus, .button.button--text:hover {\n  color: var(---color-text--light);\n}\n.button.button--text .button__icon svg {\n  width: 10px;\n  height: 10px;\n  transform: rotate(45deg);\n}\n.button.button--hollow {\n  background: transparent;\n  color: RGB(var(--button-background));\n  box-shadow: 0 0 0 1px RGB(var(--button-background));\n  transition: 0.25s color, 0.25s box-shadow, 0.25s background;\n}\n.button.button--hollow:hover {\n  background: RGB(var(--button-background));\n  color: RGB(var(--button-text-color));\n}\n.button.button--stealth {\n  background: RGB(var(--section-block-background));\n  color: RGB(var(--text-color));\n  transition: 0.25s color, 0.25s box-shadow, 0.25s background;\n}\n.button.button--stealth:hover {\n  background: RGB(var(--button-background));\n  color: RGB(var(--button-text-color));\n}\n.button.button--tiny {\n  padding: 0.25em 0.5em;\n  line-height: 24px;\n  padding-left: 24px;\n  padding-right: 24px;\n  min-width: 0;\n  font-size: var(---font-size-button--mobile);\n}\n@media only screen and (max-width: 1000px) {\n  .button.button--tiny {\n    min-width: unset;\n  }\n}\n@media only screen and (min-width: 1001px) {\n  .button.button--tiny {\n    height: 36px;\n    line-height: 36px;\n    font-size: var(---font-size-button--mobile);\n  }\n}\n.button.button--large {\n  font-size: var(---font-size-button-large--mobile);\n  min-height: unset;\n  padding: 0.75em 2em;\n  font-size: var(---font-size-button-large--mobile);\n}\n@media only screen and (max-width: 1000px) {\n  .button.button--large {\n    min-width: unset;\n  }\n}\n@media only screen and (min-width: 1001px) {\n  .button.button--large {\n    height: 64px;\n  }\n}\n@media only screen and (min-width: 1001px) {\n  .button.button--large {\n    font-size: var(---font-size-button-large--desktop);\n  }\n}\n\n.modal {\n  --background: var(---background-color--content-1--rgb);\n}\n.modal .modal__close-button {\n  top: 26px;\n  right: 26px;\n}\n.modal .modal__header {\n  text-align: center;\n  padding-top: 24px;\n}\n.modal .modal__title {\n  font-size: var(---font-size-button-large--desktop);\n}\n.modal .modal__content {\n  border-radius: 8px;\n}\n.modal .form__actions {\n  margin-top: 2em;\n}\n@media only screen and (max-width: 740px) {\n  .modal .form__actions .button {\n    width: 100%;\n    justify-content: center;\n  }\n}\n\n.modal--login .quiz-modal-footer {\n  text-align: center;\n  padding-bottom: var(--vertical-breather);\n  font-size: var(---font-size-body--desktop);\n}\n\n.modal--register .modal__content {\n  overflow: visible;\n}\n.modal--register .quiz-modal__image {\n  width: 185px;\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  margin: auto;\n  transform: translateY(-60%);\n}\n.modal--register .newsletter-modal__content {\n  padding-top: 90px !important;\n}\n.modal--register .quiz-modal-footer {\n  margin-top: 20px !important;\n}\n.modal--register .button--link {\n  transform: translateY(calc(var(--vertical-breather)));\n}\n\n.recipe-modal .newsletter-modal {\n  flex-direction: column;\n}\n.recipe-modal .newsletter-modal__content {\n  padding: var(--container-gutter);\n  text-align: left;\n}\n.recipe-modal .modal__close-button {\n  color: var(---color-text--reversed);\n  transition: transform 0.25s;\n}\n.recipe-modal .modal__close-button:hover {\n  transform: rotate(90deg);\n}\n\n.modal--upsells {\n  /* ----- Quiz Results Product ----- */\n  /* ----- Layout ----- */\n}\n.modal--upsells .quiz-results-product {\n  width: 300px;\n}\n.modal--upsells .quiz-results-product__header img {\n  margin: auto;\n}\n.modal--upsells .quiz-results-product__footer {\n  display: flex;\n  justify-content: center;\n  padding: 0 20px;\n}\n.modal--upsells .quiz-modal-content__header {\n  padding: 0 40px;\n  margin: auto;\n  max-width: 450px;\n}\n.modal--upsells .newsletter-modal__content {\n  max-width: unset;\n  padding-top: var(--container-gutter);\n  padding-bottom: var(--container-gutter);\n  background: var(---background-color--content-2);\n}\n.modal--upsells .quiz-results-product__footer-price {\n  display: flex;\n  margin-right: auto;\n}\n.modal--upsells .quiz-results-product__footer-price .price {\n  font-size: 16px;\n}\n.modal--upsells .price-list {\n  display: flex;\n  align-items: center;\n}\n@media only screen and (max-width: 1000px) {\n  .modal--upsells .gallery {\n    margin-left: calc(var(--container-gutter) * -1);\n    margin-right: calc(var(--container-gutter) * -1);\n  }\n}\n\n.input select,\n.input input {\n  font-weight: var(--text-font-bold-weight);\n}\n\n/* ----- Form Container ----- */\nrevealing-form,\n.revealing-form {\n  display: block;\n}\n\n/* ----- Inputs ----- */\nrevealing-form-input,\n.revealing-form-input {\n  display: none;\n}\nrevealing-form-input.revealing-form-input--visible,\n.revealing-form-input.revealing-form-input--visible {\n  display: block;\n}\nrevealing-form-input.revealing-form-input--animating,\n.revealing-form-input.revealing-form-input--animating {\n  display: block;\n}\n\n/* ----- Actions ----- */\nrevealing-form-actions,\n.revealing-form__actions {\n  display: block;\n}\n\nexpanding-input {\n  position: relative;\n}\nexpanding-input select {\n  position: absolute;\n  left: 0;\n  right: 0;\n  top: 0;\n  bottom: 0;\n  height: 100%;\n  width: 100%;\n  margin: 0 !important;\n  opacity: 0;\n}\nexpanding-input select:focus + .expanding-input__display, expanding-input select:hover + .expanding-input__display {\n  outline: none;\n  border-color: var(---color--highlight);\n}\n\n.expanding-input__display {\n  padding-left: 0.5em;\n  padding-right: 0.5em;\n  cursor: text;\n}\n.expanding-input__display:after {\n  transition: color 0.25s;\n}\n.expanding-input__display:empty {\n  color: var(---color--secondary);\n}\n.expanding-input__display:empty:after {\n  content: attr(data-default);\n  font-weight: 300;\n}\n.expanding-input__display:focus {\n  color: var(---color--highlight);\n}\n\n.expanding-input--select {\n  cursor: pointer;\n}\n.expanding-input--select .expanding-input__display {\n  padding-right: 50px;\n  background-image: url(\"data:image/svg+xml,%3Csvg width='12' height='6' viewBox='0 0 12 6' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M1 0.5L6 5.5L11 0.5' stroke='%23E6E6E6' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E%0A\");\n  background-size: 15px;\n  background-repeat: no-repeat;\n  background-position: calc(100% - 10px);\n  pointer-events: none;\n}\n\n.expanding-input__input:not(select) {\n  position: absolute !important;\n  overflow: hidden;\n  clip: rect(0 0 0 0);\n  height: 1px;\n  width: 1px;\n  max-width: 1px;\n  max-height: 1px;\n  font-size: 0;\n  margin: -1px;\n  padding: 0;\n  border: 0;\n}\n\nselect.expanding-input__input option {\n  background-color: #e6e6e6 !important;\n  text-align: left !important;\n  font-size: var(---font-size-body-large--desktop) !important;\n  color: var(---color-text) !important;\n}\n\n.box-line-item {\n  --box-line-item-padding: 20px;\n}\n.box-line-item .product-item-tag--frozen {\n  background: RGB(var(--root-background));\n}\n\n.box-line-item__inner {\n  margin-bottom: 20px;\n  padding: var(--box-line-item-padding) 0;\n  background: rgba(var(---color--brand-5--rgb), 0.25);\n  border-radius: 8px;\n}\n\n.box-line-item__body {\n  display: flex;\n  gap: var(--box-line-item-padding);\n  padding: 0 var(--box-line-item-padding);\n}\n\n.box-line-item__image {\n  max-width: 92px;\n  width: 100%;\n  object-fit: cover;\n}\n\n.line-item__image-inner {\n  position: relative;\n}\n\n.box-line-item__contents {\n  width: 100%;\n}\n\n.box-line-item__footer {\n  display: flex;\n  justify-content: space-between;\n  border-top: 1px solid rgba(var(---color--brand-5--rgb), 1);\n  padding: 0 var(--box-line-item-padding);\n  padding-top: 10px;\n  margin-top: 15px;\n}\n\n.tile-radio-input:checked + .tile-radio {\n  background: var(---color--brand-7);\n  border-color: var(---color--brand-7);\n  outline: 2px solid var(---color--brand-2);\n}\n\n.tile-radios {\n  display: flex;\n  justify-content: center;\n  gap: 15px;\n  flex-wrap: wrap;\n}\n\n.tile-radio {\n  display: flex;\n  align-items: center;\n  gap: 0.75em;\n  width: 100%;\n  cursor: pointer;\n  text-align: center;\n  line-height: 1.1;\n  padding: 10px;\n  border: 1px solid var(---color-line--light);\n  border-radius: 8px;\n  box-shadow: 0 0px 0px rgba(0, 0, 0, 0.1);\n  user-select: none;\n  transition: transform 0.25s, box-shadow 0.25s;\n}\n.tile-radio:hover {\n  transform: scale(1.05);\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);\n}\n@media only screen and (min-width: 741px) {\n  .tile-radio {\n    text-align: center;\n    width: auto;\n    flex-direction: column;\n    padding: 20px;\n  }\n}\n\n.tile-radio__icon {\n  pointer-events: none;\n  width: 66px;\n  height: 66px;\n}\n\n.tile-radio__content {\n  display: flex;\n  flex-direction: column;\n  gap: 0.25em;\n  text-align: left;\n}\n@media only screen and (min-width: 741px) {\n  .tile-radio__content {\n    text-align: center;\n  }\n}\n\n.tile-radio__title {\n  line-height: 1.2;\n}\n\n.tile-radio__description {\n  line-height: 1.2;\n}\n\n.table.table--auto {\n  table-layout: auto;\n}\n.table.table--1 {\n  border-radius: 4px;\n  overflow: hidden;\n}\n.table.table--1 tr:hover td {\n  background: var(---color--brand-1);\n}\n.table.table--1 td {\n  background: var(---color--brand-7);\n}\n.table.table--1 td,\n.table.table--1 th {\n  padding: 0.75em 0.25em;\n  text-align: center;\n  vertical-align: middle;\n  line-height: 1.2;\n}\n.table.table--1 td:first-child,\n.table.table--1 th:first-child {\n  padding-left: 0.75em;\n  text-align: left;\n  font-weight: var(--text-font-bold-weight);\n}\n.table.table--1 td:last-child,\n.table.table--1 th:last-child {\n  padding-right: 0.75em;\n}\n.table.table--1 thead th {\n  font-family: var(--heading-font-family);\n  font-weight: var(--body-font-weight);\n  font-size: 0.9em;\n  letter-spacing: 0.05em;\n  background: var(---color--brand-2);\n  color: var(---color-text--reversed);\n}\n\n.shipping-bar {\n  --background--unmet: RGBA(235, 87, 87, .3);\n  --progress-background: #D9D9D9;\n  --loading-bar-background: 255, 255, 255;\n  position: relative;\n  display: block;\n  margin-top: 15px !important;\n}\n.shipping-bar .shipping-bar__progress {\n  background: var(--progress-background);\n}\n.shipping-bar .shipping-bar__progress:before {\n  content: \"\";\n  display: block;\n  position: absolute;\n  width: 2px;\n  height: 100%;\n  background: currentColor;\n  left: calc(var(--frozen-threshold) * 100%);\n  z-index: 10;\n}\n.shipping-bar.shipping-bar--frozen-food--unmet {\n  background: var(--background--unmet) !important;\n}\n.shipping-bar.shipping-bar--frozen-food--unmet .shipping-bar__progress:after {\n  background: var(---color--danger) !important;\n}\n\n.shipping-bar__icon {\n  --icon-size: 44px;\n  position: absolute;\n  left: 0;\n  top: 0;\n  display: inline-flex;\n  align-items: center;\n  justify-content: center;\n  width: var(--icon-size);\n  height: var(--icon-size);\n  vertical-align: top;\n  background: #fff;\n  border-radius: 100%;\n  box-shadow: 0 0 5px rgba(0, 0, 0, 0.1);\n  transform: translate(-50%, -30%);\n}\n\n.shipping-bar__text {\n  line-height: 1.4;\n  margin-bottom: 0.5em;\n  display: inline-block;\n}\n\n.vet-sticky-bar {\n  position: fixed;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  z-index: 9;\n  box-shadow: var(---shadow--modal);\n  transform: translateY(100%);\n  transition: transform 500ms;\n}\n.vet-sticky-bar[open] {\n  display: block;\n  transform: translateY(0);\n}\n.vet-sticky-bar:hover:not([open]) {\n  transform: translateY(var(--tease-tug-1));\n}\n@media only screen and (max-width: 740px) {\n  .vet-sticky-bar .cart-vet-text {\n    display: block;\n    width: 100%;\n  }\n}\n.vet-sticky-bar .cart-vet-partner {\n  display: flex;\n}\n@media only screen and (min-width: 1001px) {\n  .vet-sticky-bar .vet-partner-selector-wrapper {\n    flex: 1 0 250px;\n    max-width: 250px;\n  }\n}\n.vet-sticky-bar .cart-vet-partner__vet {\n  justify-content: flex-end;\n  padding: 0;\n  border: 0;\n}\n@media only screen and (min-width: 1001px) {\n  .vet-sticky-bar .cart-vet-partner__selector-form {\n    display: flex;\n    justify-content: end;\n  }\n}\n.vet-sticky-bar .cart-vet-partner__selector-button {\n  flex: 0 2 max-content;\n}\n.vet-sticky-bar .cart-vet-partner__vet-text {\n  padding: 15px 30px;\n  font-size: var(---font-size-h6--desktop);\n  background: var(---background-color--secondary);\n  border-radius: 10px;\n}\n@media only screen and (max-width: 740px) {\n  .vet-sticky-bar .cart-vet-partner__vet-text {\n    display: block;\n    width: 100%;\n  }\n}\n.vet-sticky-bar .cart-vet-partner__product-notice {\n  display: none;\n}\n\n.vet-sticky-bar__tag {\n  --tab-background: var(---color--highlight--rgb);\n  --tab-color: var(---color--default--rgb);\n  --edge-width: 30px;\n  --edge-corner-radius: 8px;\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  transform: translateY(-100%);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: max-content;\n  padding: 0.5em;\n  margin: auto;\n  font-weight: var(--bold-font-weight);\n  text-align: center;\n  background: RGB(var(--tab-background));\n  color: RGB(var(--tab-color));\n  transition: background 250ms;\n}\n.vet-sticky-bar__tag:hover {\n  --tab-background: var(---color--highlight--dark--rgb);\n}\n.vet-sticky-bar__tag:before, .vet-sticky-bar__tag:after {\n  content: \"\";\n  display: block;\n  position: absolute;\n  top: 0;\n  z-index: -1;\n  background: RGB(var(--tab-background));\n  transition: background 250ms;\n  width: var(--edge-width);\n  height: 100%;\n}\n.vet-sticky-bar__tag:before {\n  left: -10px;\n  transform: skew(-10deg);\n  border-top-left-radius: var(--edge-corner-radius);\n}\n.vet-sticky-bar__tag:after {\n  right: -10px;\n  transform: skew(10deg);\n  border-top-right-radius: var(--edge-corner-radius);\n}\n\n.vet-sticky-bar__wrapper {\n  padding: 20px;\n  background-color: RGB(var(--section-background));\n}\n@media only screen and (max-width: 740px) {\n  .vet-sticky-bar__wrapper .container {\n    padding: 0;\n  }\n}\n\n@media only screen and (max-width: 740px) {\n  .cart-vet-partner__vet {\n    padding-top: 10px;\n    border-top: 1px solid var(---color-line--light);\n  }\n}\n\n.vet-sticky-bar__inner {\n  display: flex;\n  flex-direction: column;\n}\n@media only screen and (min-width: 1001px) {\n  .vet-sticky-bar__inner {\n    flex-direction: row;\n    gap: 20px;\n  }\n}\n\n.vet-sticky-bar__info {\n  display: flex;\n  gap: 10px;\n}\n\n.vet-sticky-bar__actions {\n  display: flex;\n  justify-content: flex-end;\n}\n\n.vet-sticky-bar__text .subheading {\n  margin: 0;\n}\n\n@keyframes vet-sticky-bar--tease {\n  0% {\n    transform: translateY(var(--tease-start));\n  }\n  15% {\n    transform: translateY(var(--tease-tug-1));\n  }\n  30% {\n    transform: translateY(var(--tease-start));\n  }\n  60% {\n    transform: translateY(var(--tease-start));\n  }\n  80% {\n    transform: translateY(var(--tease-tug-2));\n  }\n  100% {\n    transform: translateY(var(--tease-start));\n  }\n}\n@media only screen and (max-width: 740px) {\n  :root {\n    --tease-start: 100%;\n    --tease-tug-1: 95%;\n    --tease-tug-2: 85%;\n  }\n}\n@media only screen and (min-width: 741px) {\n  :root {\n    --tease-start: 100%;\n    --tease-tug-1: 90%;\n    --tease-tug-2: 70%;\n  }\n}\n\n/*  --------------------------------------------------\n    Cart\n    -------------------------------------------------- */\n.cart-vet-partner {\n  --vertical-spacing: 12px;\n  --spacing: 12px;\n  display: flex;\n  flex-direction: column;\n  gap: var(--vertical-spacing);\n  transition: opacity 0.25s;\n}\n.cart-vet-partner[loading] {\n  pointer-events: none;\n  opacity: 0.25;\n}\n.cart-vet-partner .unlisted-vet-container {\n  background: var(---background-color--secondary);\n  border-radius: 4px;\n  padding: 0.5em 1em;\n  text-align: center;\n  -webkit-user-select: none;\n  -moz-user-select: none;\n  user-select: none;\n}\n\n.modal--vet-not-listed .modal__overlay {\n  pointer-events: none;\n}\n\n.cart-vet-partner__inner {\n  display: flex;\n  flex-direction: column;\n  gap: 12px;\n}\n\n.cart-vet-partner__selector-form {\n  display: grid;\n  grid-auto-flow: row;\n  gap: var(--spacing);\n}\n@media only screen and (max-width: 1000px) {\n  .cart-vet-partner__selector-form {\n    grid-auto-flow: row;\n  }\n}\n@media only screen and (min-width: 1001px) {\n  .cart-vet-partner__selector-form select {\n    height: 100%;\n  }\n}\n\n.cart-vet-partner__vet {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  text-align: center;\n}\n\n.cart-vet-partner__product-notice {\n  text-align: center;\n}\n\n.cart-vet-partner__notice {\n  display: flex;\n  padding: 16px;\n  margin-top: var(--spacing);\n  gap: var(--spacing);\n  border: 2px solid var(---color--default);\n  border-radius: var(--block-border-radius);\n}\n\n.cart-vet-partner__notice-text .subheading {\n  margin-bottom: 0.25em;\n}\n\n.cart-vet-text {\n  line-height: 1;\n}\n\n/*  --------------------------------------------------\n    Account Page\n    -------------------------------------------------- */\n.account-vet-partner {\n  max-width: 800px;\n  padding: 0 0;\n  margin: 0 auto;\n}\n.account-vet-partner .account-vet-partner__inner {\n  margin-top: 20px;\n  padding: 20px;\n  background: var(---background-color--secondary);\n  border-radius: var(--block-border-radius);\n}\n@media only screen and (min-width: 1001px) {\n  .account-vet-partner .account-vet-partner__inner {\n    padding: 30px;\n  }\n}\n.account-vet-partner .cart-vet-partner__selector {\n  padding-bottom: 20px;\n  margin-bottom: 20px;\n  border-bottom: 1px solid var(---color-line--light);\n}\n.account-vet-partner .cart-vet-partner__vet {\n  padding: 0;\n  border: 0;\n}\n.account-vet-partner .cart-vet-partner__vet-text {\n  font-size: var(--base-font-size);\n  padding: 15px 30px;\n  border-radius: 10px;\n}\n.account-vet-partner .cart-vet-partner__notice {\n  background: var(---background-color--content-1);\n}\n.account-vet-partner .cart-vet-partner__product-notice {\n  display: none;\n}\n.account-vet-partner .cart-vet-partner__selector-form .select {\n  background: var(---background-color--content-1);\n}\n.account-vet-partner .cart-vet-notice {\n  display: block !important;\n  height: auto;\n  visibility: visible;\n}\n.account-vet-partner .cart-vet-notice button {\n  display: none;\n}\n.account-vet-partner .cart-vet-partner__notice {\n  padding: 20px;\n}\n@media (min-width: 1000px) {\n  .account-vet-partner .cart-vet-partner__notice {\n    padding: 30px;\n  }\n}\n.account-vet-partner .cart-vet-text {\n  display: flex;\n  justify-content: center;\n}\n.account-vet-partner .cart-vet-partner {\n  display: flex;\n  gap: 0;\n}\n\nsplit-page-step {\n  display: none;\n}\nsplit-page-step.split-page-step--visible {\n  display: block;\n}\nsplit-page-step .input--select > select {\n  width: 100%;\n}\n\n.banner {\n  border-radius: var(--block-border-radius);\n}\n.banner.banner--success {\n  --text-color: var(---color-text--reversed--rgb);\n  color: RGB(var(--text-color));\n  background-color: rgb(var(--success-color));\n}\n.banner.banner--error {\n  --text-color: var(---color-text--reversed--rgb);\n  color: RGB(var(--text-color));\n  background-color: rgb(var(--error-color));\n}\n\n.weight-range {\n  --track-height: 14px;\n  --thumb-size: 40px;\n  --thumb-icon-size: 80px;\n  display: block;\n  margin-top: var(--thumb-icon-size);\n  position: relative;\n}\n\n.weight-range__inner {\n  position: relative;\n  line-height: 0;\n  height: var(--track-height);\n  border-radius: 100px;\n  background: RGB(var(---color--brand-2--rgb));\n  outline: 2px solid RGB(var(--text-color));\n}\n\n.weight-range__thumb {\n  pointer-events: none;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 10px;\n  position: absolute;\n  left: var(--range-position);\n  transform: translateX(calc(-1 * var(--thumb-icon-size) / 2)) translateY(50%);\n  bottom: 0;\n  bottom: 50%;\n  z-index: 1;\n}\n\n.weight-range__range {\n  height: var(--track-height);\n}\n\n.weight-range__range::range-thumb {\n  height: var(--thumb-icon-size);\n  width: var(--thumb-icon-size);\n  transform: translateY(calc(-1 * var(--thumb-size) / 2 - -1 * var(--track-height) / 2)) translateX(calc(100 * var(--range-offset)));\n  opacity: 0;\n}\n\n.weight-range__range::slider-thumb {\n  height: var(--thumb-icon-size);\n  width: var(--thumb-icon-size);\n  transform: translateY(calc(-1 * var(--thumb-size) / 2 - -1 * var(--track-height) / 2)) translateX(calc(100 * var(--range-offset)));\n  opacity: 0;\n}\n\n.weight-range__range::-moz-range-thumb {\n  height: var(--thumb-icon-size);\n  width: var(--thumb-icon-size);\n  transform: translateY(calc(-1 * var(--thumb-size) / 2 - -1 * var(--track-height) / 2)) translateX(calc(100 * var(--range-offset)));\n  opacity: 0;\n}\n\n.weight-range__range::-webkit-slider-thumb {\n  height: var(--thumb-icon-size);\n  width: var(--thumb-icon-size);\n  transform: translateY(calc(-1 * var(--thumb-size) / 2 - -1 * var(--track-height) / 2)) translateX(calc(100 * var(--range-offset)));\n  opacity: 0;\n}\n\n.weight-range__range::range-track {\n  opacity: 0;\n}\n\n.weight-range__range::slider-track {\n  opacity: 0;\n}\n\n.weight-range__range::-moz-range-track {\n  opacity: 0;\n}\n\n.weight-range__range::-webkit-slider-runnable-track {\n  opacity: 0;\n}\n\n.weight-range__track {\n  position: absolute;\n  left: 0;\n  top: 0;\n  height: var(--track-height);\n  width: var(--range-position);\n  background: RGB(var(---color--brand-1--rgb));\n  border-radius: 100px;\n  pointer-events: none;\n}\n\n.weight-range__thumb-icon {\n  height: var(--thumb-icon-size);\n  width: var(--thumb-icon-size);\n  background-image: url(https://cdn.shopify.com/s/files/1/1683/1605/files/pup-thumb.png?v=1719171809);\n  background-size: contain;\n  background-position: center;\n  background-repeat: no-repeat;\n}\n\n.weight-range__thumb-value {\n  display: inline-flex;\n  padding: 0em 0.5em;\n  border-radius: 8px;\n  background-color: RGB(var(---color--brand-1--rgb));\n  outline: 2px solid RGB(var(--text-color));\n  position: absolute;\n  top: 0px;\n  transform: translateY(-100%);\n}\n\n.weight-range__labels {\n  display: flex;\n  justify-content: space-between;\n  margin-top: 0.25em;\n  pointer-events: none;\n  user-select: none;\n}\n\n.popover,\n.mobile-toolbar {\n  background: var(---background-color--content-1) !important;\n}\n\n.nav-dropdown {\n  z-index: 1;\n  background: RGB(var(--background));\n}\n\n.button {\n  line-height: 1.2;\n}\n\n.button-wrapper.button-wrapper--vertical {\n  display: flex;\n  align-items: center;\n  gap: 0.5em;\n  flex-direction: column;\n}\n\nhr, .hr {\n  width: 100%;\n  margin: 2em auto;\n  border-width: 0.5px;\n  border-bottom: 0;\n  border-style: solid;\n  border-color: var(---color-line);\n}\nhr.hr--light, .hr.hr--light {\n  border-color: var(---color-line--light);\n}\nhr.hr--dark, .hr.hr--dark {\n  border-color: var(---color-line--dark);\n}\nhr.hr--clear, .hr.hr--clear {\n  border-color: transparent;\n}\nhr.hr--small, .hr.hr--small {\n  margin: 1em 0;\n}\nhr.hr--xsmall, .hr.hr--xsmall {\n  margin: 0.5em 0;\n}\nhr.hr--narrow, .hr.hr--narrow {\n  max-width: 70px;\n  margin-left: auto !important;\n  margin-right: auto !important;\n}\n\n[data-tooltip]:before {\n  font-size: var(---font-size-body-xs) !important;\n}\n\n.account-link-current {\n  color: var(---color--highlight);\n}\n\n/* 9. Apps  */\n.product-form__buy-buttons .awt-style-1 .bundleapp-wrapper {\n  margin: 0 !important;\n}\n.product-form__buy-buttons .awt-style-1 .bundleapp-wrapper .bundleapp-plan-selector-group {\n  border-radius: 12px;\n  border: 0;\n  border: 2px solid RGB(var(--border-color));\n  transition: 0.25s border, 0.25s background;\n}\n.product-form__buy-buttons .awt-style-1 .bundleapp-wrapper .bundleapp-plan-selector-group > label {\n  font-weight: 800;\n  color: var(--text-color);\n}\n.product-form__buy-buttons .awt-style-1 .bundleapp-wrapper .bundleapp-plan-selector-group.bundleapp-plan-selector-group--selected {\n  background: var(---color--highlight);\n}\n.product-form__buy-buttons .awt-style-1 .bundleapp-wrapper .bundleapp-plan-selector-description {\n  line-height: 1.2;\n}\n.product-form__buy-buttons .awt-style-1 .bundleapp-wrapper .bundleapp-plan-selector-description span {\n  background: var(---background-color--content-1);\n  font-weight: 400;\n  padding: 20px;\n  border: 0;\n}\n.product-form__buy-buttons .awt-style-1 .bundleapp-wrapper .bundleapp-plan-selector-plan {\n  margin: 0;\n  padding: 0.5em;\n}\n.product-form__buy-buttons .awt-style-1 .bundleapp-wrapper .bundleapp-plan-selector-plan label {\n  font-size: 0.9em;\n}\n.product-form__buy-buttons .awt-style-1 .bundleapp-wrapper .bundleapp-plan-selector-select {\n  padding: 0.4em 0.8em !important;\n  margin: 0 !important;\n  border-radius: 12px;\n  border: 2px solid RGB(var(--border-color)) !important;\n}\n\n/* ---------- Marquee Text ---------- */\n.section .marquee-horizontal {\n  z-index: 3;\n}\n\n/* 10. Utility Classes */\n/*================ Build Base Grid Classes ================*/\n.shown {\n  display: block !important;\n}\n\n.hidden {\n  display: none !important;\n}\n\n.text-left {\n  text-align: left !important;\n}\n\n.text-right {\n  text-align: right !important;\n}\n\n.text-center {\n  text-align: center !important;\n}\n\n/*================ Build Responsive Grid Classes ================*/\n@media only screen and (min-width: 741px) and (max-width: 999px) {\n  .small--shown {\n    display: block !important;\n  }\n  .small--hidden {\n    display: none !important;\n  }\n  .small--text-left {\n    text-align: left !important;\n  }\n  .small--text-right {\n    text-align: right !important;\n  }\n  .small--text-center {\n    text-align: center !important;\n  }\n  .br--small {\n    display: block;\n  }\n}\n@media only screen and (min-width: 741px) {\n  .small-up--shown {\n    display: block !important;\n  }\n  .small-up--hidden {\n    display: none !important;\n  }\n  .small-up--text-left {\n    text-align: left !important;\n  }\n  .small-up--text-right {\n    text-align: right !important;\n  }\n  .small-up--text-center {\n    text-align: center !important;\n  }\n  .br--small-up {\n    display: block;\n  }\n}\n@media only screen and (max-width: 740px) {\n  .small-down--shown {\n    display: block !important;\n  }\n  .small-down--hidden {\n    display: none !important;\n  }\n  .small-down--text-left {\n    text-align: left !important;\n  }\n  .small-down--text-right {\n    text-align: right !important;\n  }\n  .small-down--text-center {\n    text-align: center !important;\n  }\n  .br--small-down {\n    display: block;\n  }\n}\n@media only screen and (min-width: 1001px) and (max-width: 1199px) {\n  .medium--shown {\n    display: block !important;\n  }\n  .medium--hidden {\n    display: none !important;\n  }\n  .medium--text-left {\n    text-align: left !important;\n  }\n  .medium--text-right {\n    text-align: right !important;\n  }\n  .medium--text-center {\n    text-align: center !important;\n  }\n  .br--medium {\n    display: block;\n  }\n}\n@media only screen and (min-width: 1001px) {\n  .medium-up--shown {\n    display: block !important;\n  }\n  .medium-up--hidden {\n    display: none !important;\n  }\n  .medium-up--text-left {\n    text-align: left !important;\n  }\n  .medium-up--text-right {\n    text-align: right !important;\n  }\n  .medium-up--text-center {\n    text-align: center !important;\n  }\n  .br--medium-up {\n    display: block;\n  }\n}\n@media only screen and (max-width: 1000px) {\n  .medium-down--shown {\n    display: block !important;\n  }\n  .medium-down--hidden {\n    display: none !important;\n  }\n  .medium-down--text-left {\n    text-align: left !important;\n  }\n  .medium-down--text-right {\n    text-align: right !important;\n  }\n  .medium-down--text-center {\n    text-align: center !important;\n  }\n  .br--medium-down {\n    display: block;\n  }\n}\n@media only screen and (min-width: 1201px) and (max-width: 1399px) {\n  .large--shown {\n    display: block !important;\n  }\n  .large--hidden {\n    display: none !important;\n  }\n  .large--text-left {\n    text-align: left !important;\n  }\n  .large--text-right {\n    text-align: right !important;\n  }\n  .large--text-center {\n    text-align: center !important;\n  }\n  .br--large {\n    display: block;\n  }\n}\n@media only screen and (min-width: 1201px) {\n  .large-up--shown {\n    display: block !important;\n  }\n  .large-up--hidden {\n    display: none !important;\n  }\n  .large-up--text-left {\n    text-align: left !important;\n  }\n  .large-up--text-right {\n    text-align: right !important;\n  }\n  .large-up--text-center {\n    text-align: center !important;\n  }\n  .br--large-up {\n    display: block;\n  }\n}\n@media only screen and (max-width: 1200px) {\n  .large-down--shown {\n    display: block !important;\n  }\n  .large-down--hidden {\n    display: none !important;\n  }\n  .large-down--text-left {\n    text-align: left !important;\n  }\n  .large-down--text-right {\n    text-align: right !important;\n  }\n  .large-down--text-center {\n    text-align: center !important;\n  }\n  .br--large-down {\n    display: block;\n  }\n}\n@media only screen and (min-width: 1401px) and (max-width: 1399px) {\n  .wide--shown {\n    display: block !important;\n  }\n  .wide--hidden {\n    display: none !important;\n  }\n  .wide--text-left {\n    text-align: left !important;\n  }\n  .wide--text-right {\n    text-align: right !important;\n  }\n  .wide--text-center {\n    text-align: center !important;\n  }\n  .br--wide {\n    display: block;\n  }\n}\n@media only screen and (min-width: 1401px) {\n  .wide-up--shown {\n    display: block !important;\n  }\n  .wide-up--hidden {\n    display: none !important;\n  }\n  .wide-up--text-left {\n    text-align: left !important;\n  }\n  .wide-up--text-right {\n    text-align: right !important;\n  }\n  .wide-up--text-center {\n    text-align: center !important;\n  }\n  .br--wide-up {\n    display: block;\n  }\n}\n@media only screen and (max-width: 1400px) {\n  .wide-down--shown {\n    display: block !important;\n  }\n  .wide-down--hidden {\n    display: none !important;\n  }\n  .wide-down--text-left {\n    text-align: left !important;\n  }\n  .wide-down--text-right {\n    text-align: right !important;\n  }\n  .wide-down--text-center {\n    text-align: center !important;\n  }\n  .br--wide-down {\n    display: block;\n  }\n}\n.clearfix {\n  *zoom: 1;\n}\n.clearfix::after {\n  content: \"\";\n  display: table;\n  clear: both;\n}\n\n.fallback-text,\n.visually-hidden {\n  position: absolute !important;\n  overflow: hidden;\n  clip: rect(0 0 0 0);\n  height: 1px;\n  width: 1px;\n  max-width: 1px;\n  max-height: 1px;\n  font-size: 0;\n  margin: -1px;\n  padding: 0;\n  border: 0;\n}\n\n.hidden {\n  display: none;\n}\n\n.flex {\n  display: flex;\n}\n\n.inline-flex {\n  display: flex;\n}\n\n.align-start {\n  align-items: flex-start;\n}\n\n.align-center {\n  align-items: center;\n}\n\n.align-end {\n  align-items: flex-end;\n}\n\n.justify-start {\n  justify-items: flex-start;\n}\n\n.justify-center {\n  justify-items: center;\n}\n\n.justify-end {\n  justify-items: flex-end;\n}\n\n.gap-05 {\n  gap: 0.5em;\n}\n\n.uppercase,\n.text-transform--uppercase {\n  text-transform: uppercase !important;\n}\n\n.text-transform--none {\n  text-transform: none !important;\n}\n\n.strikethrough {\n  text-decoration: line-through;\n}\n\n.background-color--default {\n  background: var(---color--default);\n}\n\n.background-color--primary {\n  background: var(---color--primary);\n}\n\n.background-color--secondary {\n  background: var(---color--secondary);\n}\n\n.background-color--tertiary {\n  background: var(---color--tertiary);\n}\n\n.background-color--success {\n  background: var(---color--success);\n}\n\n.background-color--warning {\n  background: var(---color--warning);\n}\n\n.background-color--danger {\n  background: var(---color--danger);\n}\n\n.background-color--info {\n  background: var(---color--info);\n}\n\n.background-color--link {\n  background: var(---color--link);\n}\n\n.color--default {\n  color: var(---color--default);\n}\n\n.color--primary {\n  color: var(---color--primary);\n}\n\n.color--secondary {\n  color: var(---color--secondary);\n}\n\n.color--tertiary {\n  color: var(---color--tertiary);\n}\n\n.color--success {\n  color: var(---color--success);\n}\n\n.color--warning {\n  color: var(---color--warning);\n}\n\n.color--danger {\n  color: var(---color--danger);\n}\n\n.color--info {\n  color: var(---color--info);\n}\n\n.color--link {\n  color: var(---color--link);\n}\n\n.justify-content-center {\n  justify-content: center !important;\n}\n\n.object-position--top {\n  object-position: top !important;\n}\n\n.object-position--bottom {\n  object-position: bottom !important;\n}\n\n.object-position--center {\n  object-position: center !important;\n}\n\n.object-position--left {\n  object-position: left !important;\n}\n\n.object-position--right {\n  object-position: right !important;\n}\n\n.text-align--center {\n  text-align: center !important;\n}\n\n.text-align--left {\n  text-align: left !important;\n}\n\n.text-align--right {\n  text-align: right !important;\n}\n\n@media only screen and (max-width: 740px) {\n  .text-align--center--mobile {\n    text-align: center !important;\n  }\n}\n\n@media only screen and (max-width: 740px) {\n  .text-align--left--mobile {\n    text-align: left !important;\n  }\n}\n\n@media only screen and (max-width: 740px) {\n  .text-align--right--mobile {\n    text-align: right !important;\n  }\n}\n\n.nowrap {\n  white-space: nowrap;\n}\n\n.no-margin {\n  margin: 0 !important;\n}\n\n.no-margin--top {\n  margin-top: 0 !important;\n}\n\n.no-margin--right {\n  margin-right: 0 !important;\n}\n\n.no-margin--left {\n  margin-left: 0 !important;\n}\n\n.no-margin--bottom {\n  margin-bottom: 0 !important;\n}\n\n.no-padding {\n  padding: 0 !important;\n}\n\n.no-padding--top {\n  padding-top: 0 !important;\n}\n\n.no-padding--right {\n  padding-right: 0 !important;\n}\n\n.no-padding--left {\n  padding-left: 0 !important;\n}\n\n.no-padding--bottom {\n  padding-bottom: 0 !important;\n}\n\n.padding-left--10 {\n  padding-left: 10px !important;\n}\n\n.padding-left--20 {\n  padding-left: 20px !important;\n}\n\n.padding-left--30 {\n  padding-left: 30px !important;\n}\n\n.padding-left--40 {\n  padding-left: 40px !important;\n}\n\n.padding-left--50 {\n  padding-left: 50px !important;\n}\n\n.padding-right--10 {\n  padding-right: 10px !important;\n}\n\n.padding-right--20 {\n  padding-right: 20px !important;\n}\n\n.padding-right--30 {\n  padding-right: 30px !important;\n}\n\n.padding-right--40 {\n  padding-right: 40px !important;\n}\n\n.padding-right--50 {\n  padding-right: 50px !important;\n}\n\n.padding-top--10 {\n  padding-top: 10px !important;\n}\n\n.padding-top--20 {\n  padding-top: 20px !important;\n}\n\n.padding-top--30 {\n  padding-top: 30px !important;\n}\n\n.padding-top--40 {\n  padding-top: 40px !important;\n}\n\n.padding-top--50 {\n  padding-top: 50px !important;\n}\n\n.padding-bottom--10 {\n  padding-bottom: 10px !important;\n}\n\n.padding-bottom--20 {\n  padding-bottom: 20px !important;\n}\n\n.padding-bottom--30 {\n  padding-bottom: 30px !important;\n}\n\n.padding-bottom--40 {\n  padding-bottom: 40px !important;\n}\n\n.padding-bottom--50 {\n  padding-bottom: 50px !important;\n}\n\nbody.logged-in .logged-in--hidden {\n  display: none !important;\n}\n\nbody.logged-out .logged-out--hidden {\n  display: none !important;\n}\n\n.fraction {\n  margin-left: 0.25em;\n  font-size: 0.75em;\n  letter-spacing: -0.1em;\n}\n\n/* 11. Third-Party Styles */\n.nice-select:active,\n.nice-select.open,\n.nice-select:focus {\n  border-color: rgba(var(--text-color), 0.5);\n}\n\n.nice-select:after {\n  border-bottom: 2px solid #999;\n  border-right: 2px solid #999;\n  content: \"\";\n  display: block;\n  height: 10px;\n  width: 10px;\n  margin-top: -6px;\n  pointer-events: none;\n  position: absolute;\n  right: 17px;\n  top: 50%;\n  transform-origin: center;\n  transform: rotate(45deg);\n  transition: all 0.15s ease-in-out;\n}\n\n.nice-select.open:after {\n  transform: rotate(-135deg);\n}\n\n.nice-select.open .nice-select-dropdown {\n  opacity: 1;\n  pointer-events: auto;\n  transform: scale(1) translateY(0);\n}\n\n.nice-select.disabled {\n  border-color: #ededed;\n  color: rgba(var(--text-color), 0.5);\n  pointer-events: none;\n}\n\n.nice-select.disabled:after {\n  border-color: rgba(var(--text-color), 0.5);\n}\n\n.nice-select.wide {\n  width: 100%;\n}\n\n.nice-select.wide .nice-select-dropdown {\n  left: 0 !important;\n  right: 0 !important;\n}\n\n.nice-select.right {\n  float: right;\n}\n\n.nice-select.right .nice-select-dropdown {\n  left: auto;\n  right: 0;\n}\n\n.nice-select .nice-select-dropdown {\n  width: 100%;\n  margin-top: 4px;\n  background-color: rgba(var(--section-background));\n  border-radius: 5px;\n  box-shadow: 0 0 0 1px rgba(68, 68, 68, 0.11);\n  pointer-events: none;\n  position: absolute;\n  top: 100%;\n  left: 0;\n  transform-origin: 50% 0;\n  transform: scale(0.75) translateY(19px);\n  transition: all 0.2s cubic-bezier(0.5, 0, 0, 1.25), opacity 0.15s ease-out;\n  z-index: 9;\n  opacity: 0;\n}\n\n.nice-select .list {\n  border-radius: 5px;\n  box-sizing: border-box;\n  overflow: hidden;\n  padding: 0;\n  max-height: 210px;\n  overflow-y: auto;\n}\n.nice-select .list li:first-child {\n  display: none !important;\n}\n\n.nice-select .option {\n  cursor: pointer;\n  font-weight: 400;\n  list-style: none;\n  outline: none;\n  padding-left: 18px;\n  padding-right: 29px;\n  text-align: left;\n  transition: all 0.2s;\n}\n\n.nice-select .option.selected {\n  font-weight: bold;\n}\n\n.nice-select .option.disabled {\n  background-color: rgba(0, 0, 0, 0);\n  color: #999;\n  cursor: default;\n}\n\n.nice-select .optgroup {\n  font-weight: bold;\n}\n\n.no-csspointerevents .nice-select .nice-select-dropdown {\n  display: none;\n}\n\n.no-csspointerevents .nice-select.open .nice-select-dropdown {\n  display: block;\n}\n\n.nice-select .has-multiple {\n  white-space: inherit;\n  height: auto;\n  padding: 7px 12px;\n  min-height: 36px;\n  line-height: 22px;\n}\n\n.nice-select .has-multiple span.current {\n  border: 1px solid rgba(var(--text-color), 0.5);\n  background: rgba(var(--section-background), 0.8);\n  padding: 0 10px;\n  border-radius: 3px;\n  display: inline-block;\n  line-height: 24px;\n  font-size: 14px;\n  margin-bottom: 3px;\n  margin-right: 3px;\n}\n\n.nice-select .has-multiple .multiple-options {\n  display: block;\n  line-height: 24px;\n  padding: 0;\n}\n\n.nice-select .nice-select-search-box {\n  box-sizing: border-box;\n  width: 100%;\n  padding: 5px;\n  pointer-events: none;\n  border-radius: 5px 5px 0 0;\n}\n\n.nice-select .nice-select-search {\n  box-sizing: border-box;\n  background-color: rgba(var(--section-background), 0.8);\n  border: 1px solid rgba(var(--text-color), 0.5);\n  border-radius: 3px;\n  color: rgba(var(--text-color), 1);\n  display: inline-block;\n  vertical-align: middle;\n  padding: 7px 12px;\n  margin: 0 10px 0 0;\n  width: 100%;\n  min-height: 36px;\n  line-height: 22px;\n  height: auto;\n  outline: 0 !important;\n}\n\n/* ------ Custom ------ */\nstyled-select select {\n  position: absolute !important;\n  overflow: hidden;\n  clip: rect(0 0 0 0);\n  height: 1px;\n  width: 1px;\n  max-width: 1px;\n  max-height: 1px;\n  font-size: 0;\n  margin: -1px;\n  padding: 0;\n  border: 0;\n}\nstyled-select > div > select {\n  display: none !important;\n}\nstyled-select div.nice-select {\n  float: none;\n}\n\n.nice-select {\n  --section-background: 255, 255, 255;\n  display: flex;\n  justify-content: flex-start;\n  height: 100%;\n  width: 100%;\n  padding: 0.5em 2em 0.5em 1em;\n  border: 1px solid rgba(var(--text-color), 0.25);\n  background-color: rgba(var(--section-background), 0.8);\n  border-radius: var(--button-border-radius);\n  text-align: left;\n}\n.nice-select:hover {\n  background-color: rgba(var(--section-background), 1);\n  border: 1px solid rgba(var(--text-color), 0.5);\n}\n.nice-select:focus {\n  border: 1px solid rgba(var(--text-color), 0.5);\n}\n.nice-select.nice-select--position-top .nice-select-dropdown {\n  bottom: calc(100% + 10px);\n  top: unset;\n}\n.nice-select .current {\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n.nice-select .list {\n  border-radius: 0;\n}\n.nice-select .option {\n  padding: 0.5em 1em 0.5em 1em;\n  line-height: 1.2;\n  color: rgba(var(--text-color), 0.7);\n}\n.nice-select .option[data-value=\"Not Listed\"] {\n  color: rgba(var(--text-color), 1);\n  font-style: italic;\n  font-weight: var(--text-font-bold-weight) !important;\n}\n.nice-select .nice-select-dropdown {\n  border-radius: var(--block-border-radius);\n}\n.nice-select .nice-select-search-box input {\n  border-radius: 6px;\n  border: 1px solid #e6e6e6;\n  padding: 0.7em calc(1em - 5px) 0.7em calc(1em - 5px);\n}\n\n.cart-vet-partner-select .nice-select-dropdown .option.null.disabled {\n  text-transform: uppercase;\n  color: RGB(var(--text-color));\n  font-weight: 700 !important;\n}\n.cart-vet-partner-select .nice-select-dropdown .option.null.disabled:hover {\n  background: transparent !important;\n}\n.cart-vet-partner-select .nice-select-dropdown .option.null.disabled:hover:after {\n  display: none !important;\n}\n\n.nice-select {\n  --section-background: var();\n  cursor: pointer;\n  position: relative;\n}\n.nice-select .nice-select-dropdown {\n  cursor: default;\n  box-shadow: 0 0 10px rgba(0, 0, 0, 0.25);\n}\n.nice-select .nice-select-dropdown .list {\n  margin-top: 0;\n}\n.nice-select .nice-select-dropdown .list .option {\n  position: relative;\n}\n.nice-select .nice-select-dropdown .list .option.null {\n  font-weight: normal;\n}\n.nice-select .nice-select-dropdown .list .option:after {\n  content: \"\";\n  display: block;\n  position: absolute;\n  left: 0;\n  top: 0;\n  width: 100%;\n  height: 100%;\n  background: rgba(0, 0, 0, 0.1);\n  opacity: 0;\n  z-index: -1;\n}\n.nice-select .nice-select-dropdown .list .option:hover:after {\n  opacity: 1;\n}\n\n/* 12. Animations */\n@keyframes fade-in {\n  0% {\n    opacity: 0;\n  }\n  100% {\n    opacity: 1;\n  }\n}\n@keyframes fade-out {\n  0% {\n    opacity: 1;\n  }\n  100% {\n    opacity: 0;\n  }\n}\n@keyframes rotate {\n  0% {\n    transform: rotate(0deg);\n  }\n  100% {\n    transform: rotate(360deg);\n  }\n}\n/* ----------------------------------------------\n * Generated by Animista on 2025-4-24 16:5:49\n * Licensed under FreeBSD License.\n * See http://animista.net/license for more info. \n * w: http://animista.net, t: @cssanimista\n * ---------------------------------------------- */\n/**\n * ----------------------------------------\n * animation heartbeat\n * ----------------------------------------\n */\n@-webkit-keyframes heartbeat {\n  from {\n    -webkit-transform: scale(1);\n    transform: scale(1);\n    -webkit-transform-origin: center center;\n    transform-origin: center center;\n    -webkit-animation-timing-function: ease-out;\n    animation-timing-function: ease-out;\n  }\n  10% {\n    -webkit-transform: scale(0.91);\n    transform: scale(0.91);\n    -webkit-animation-timing-function: ease-in;\n    animation-timing-function: ease-in;\n  }\n  17% {\n    -webkit-transform: scale(0.98);\n    transform: scale(0.98);\n    -webkit-animation-timing-function: ease-out;\n    animation-timing-function: ease-out;\n  }\n  33% {\n    -webkit-transform: scale(0.87);\n    transform: scale(0.87);\n    -webkit-animation-timing-function: ease-in;\n    animation-timing-function: ease-in;\n  }\n  45% {\n    -webkit-transform: scale(1);\n    transform: scale(1);\n    -webkit-animation-timing-function: ease-out;\n    animation-timing-function: ease-out;\n  }\n}\n@keyframes heartbeat {\n  from {\n    -webkit-transform: scale(1);\n    transform: scale(1);\n    -webkit-transform-origin: center center;\n    transform-origin: center center;\n    -webkit-animation-timing-function: ease-out;\n    animation-timing-function: ease-out;\n  }\n  10% {\n    -webkit-transform: scale(0.91);\n    transform: scale(0.91);\n    -webkit-animation-timing-function: ease-in;\n    animation-timing-function: ease-in;\n  }\n  17% {\n    -webkit-transform: scale(0.98);\n    transform: scale(0.98);\n    -webkit-animation-timing-function: ease-out;\n    animation-timing-function: ease-out;\n  }\n  33% {\n    -webkit-transform: scale(0.87);\n    transform: scale(0.87);\n    -webkit-animation-timing-function: ease-in;\n    animation-timing-function: ease-in;\n  }\n  45% {\n    -webkit-transform: scale(1);\n    transform: scale(1);\n    -webkit-animation-timing-function: ease-out;\n    animation-timing-function: ease-out;\n  }\n}\n/* ----------------------------------------------\n * Generated by Animista on 2025-4-24 16:5:49\n * Licensed under FreeBSD License.\n * See http://animista.net/license for more info. \n * w: http://animista.net, t: @cssanimista\n * ---------------------------------------------- */\n/**\n * ----------------------------------------\n * animation heartbeat\n * ----------------------------------------\n */\n@-webkit-keyframes heartbeat {\n  from {\n    -webkit-transform: scale(1);\n    transform: scale(1);\n    -webkit-transform-origin: center center;\n    transform-origin: center center;\n    -webkit-animation-timing-function: ease-out;\n    animation-timing-function: ease-out;\n  }\n  10% {\n    -webkit-transform: scale(0.91);\n    transform: scale(0.91);\n    -webkit-animation-timing-function: ease-in;\n    animation-timing-function: ease-in;\n  }\n  17% {\n    -webkit-transform: scale(0.98);\n    transform: scale(0.98);\n    -webkit-animation-timing-function: ease-out;\n    animation-timing-function: ease-out;\n  }\n  33% {\n    -webkit-transform: scale(0.87);\n    transform: scale(0.87);\n    -webkit-animation-timing-function: ease-in;\n    animation-timing-function: ease-in;\n  }\n  45% {\n    -webkit-transform: scale(1);\n    transform: scale(1);\n    -webkit-animation-timing-function: ease-out;\n    animation-timing-function: ease-out;\n  }\n}\n@keyframes heartbeat {\n  from {\n    -webkit-transform: scale(1);\n    transform: scale(1);\n    -webkit-transform-origin: center center;\n    transform-origin: center center;\n    -webkit-animation-timing-function: ease-out;\n    animation-timing-function: ease-out;\n  }\n  10% {\n    -webkit-transform: scale(0.91);\n    transform: scale(0.91);\n    -webkit-animation-timing-function: ease-in;\n    animation-timing-function: ease-in;\n  }\n  17% {\n    -webkit-transform: scale(0.98);\n    transform: scale(0.98);\n    -webkit-animation-timing-function: ease-out;\n    animation-timing-function: ease-out;\n  }\n  33% {\n    -webkit-transform: scale(0.87);\n    transform: scale(0.87);\n    -webkit-animation-timing-function: ease-in;\n    animation-timing-function: ease-in;\n  }\n  45% {\n    -webkit-transform: scale(1);\n    transform: scale(1);\n    -webkit-animation-timing-function: ease-out;\n    animation-timing-function: ease-out;\n  }\n}\n.heartbeat {\n  -webkit-animation: heartbeat 1.5s ease-in-out both;\n  animation: heartbeat 1.5s ease-in-out both;\n}\n\n/* ----------------------------------------------\n * Generated by Animista on 2025-4-24 16:9:18\n * Licensed under FreeBSD License.\n * See http://animista.net/license for more info. \n * w: http://animista.net, t: @cssanimista\n * ---------------------------------------------- */\n/**\n * ----------------------------------------\n * animation shake-horizontal\n * ----------------------------------------\n */\n@-webkit-keyframes shake-horizontal {\n  0%, 100% {\n    -webkit-transform: translateX(0);\n    transform: translateX(0);\n  }\n  10%, 30%, 50%, 70% {\n    -webkit-transform: translateX(-10px);\n    transform: translateX(-10px);\n  }\n  20%, 40%, 60% {\n    -webkit-transform: translateX(10px);\n    transform: translateX(10px);\n  }\n  80% {\n    -webkit-transform: translateX(8px);\n    transform: translateX(8px);\n  }\n  90% {\n    -webkit-transform: translateX(-8px);\n    transform: translateX(-8px);\n  }\n}\n@keyframes shake-horizontal {\n  0%, 100% {\n    -webkit-transform: translateX(0);\n    transform: translateX(0);\n  }\n  10%, 30%, 50%, 70% {\n    -webkit-transform: translateX(-5px);\n    transform: translateX(-5px);\n  }\n  20%, 40%, 60% {\n    -webkit-transform: translateX(5px);\n    transform: translateX(5px);\n  }\n  80% {\n    -webkit-transform: translateX(3px);\n    transform: translateX(3px);\n  }\n  90% {\n    -webkit-transform: translateX(-3px);\n    transform: translateX(-3px);\n  }\n}\n.shake-horizontal {\n  -webkit-animation: shake-horizontal 0.8s cubic-bezier(0.455, 0.03, 0.515, 0.955) both;\n  animation: shake-horizontal 0.8s cubic-bezier(0.455, 0.03, 0.515, 0.955) both;\n}", "\nh1, .h1,\nh2, .h2,\nh3, .h3,\nh4, .h4\n// h5, .h5,\n// h6, .h6,\n// .heading--small \n{\n  line-height: var(---line-height-heading--mobile);\n  letter-spacing: var(---letter-spacing-heading--mobile);\n}\n\n\n  h1, .h1,\n  h2, .h2,\n  h3, .h3,\n  h4, .h4,\n  // h5, .h5,\n  // h6, .h6 \n  {\n    \n    font-family: var(--heading-font-family);\n    font-weight: var(--heading-font-weight);\n    font-style: var(--heading-font-style);\n    color: rgb(var(--heading-color));\n    text-transform: var(--heading-text-transform);\n    display: block;\n\n    letter-spacing: var(---letter-spacing-heading--mobile);\n    font-weight: var(---font-weight-heading);\n  }\n\n\n  .heading--large,\n  .rte .heading--large {\n\n    @include heading-style--1();\n\n    font-size: var(---font-size-h0--mobile);\n    @include respond-to($small-up){\n      font-size: var(---font-size-h0--desktop);\n    }\n\n  }\n\n  h1, .h1,\n  .rte h1, .rte .h1 {\n\n    @include heading-style--1();\n\n    font-size: var(---font-size-h1--mobile);\n    @include respond-to($small-up){\n      font-size: var(---font-size-h1--desktop);\n    }\n\n  }\n\n  h2, .h2,\n  .rte h2, .rte .h2 {\n\n    @include heading-style--1();\n\n    font-size: var(---font-size-h2--mobile);\n    @include respond-to($small-up){\n      font-size: var(---font-size-h2--desktop);\n    }\n\n  }\n\n  h3, .h3,\n  .rte h3, .rte .h3 {\n\n    @include heading-style--1();\n\n    font-size: var(---font-size-h3--mobile);\n    @include respond-to($small-up){\n      font-size: var(---font-size-h3--desktop);\n    }\n\n  }\n\n  h4, .h4,\n  .rte h4, .rte .h4 {\n\n    @include heading-style--1();\n\n    font-size: var(---font-size-h4--mobile);\n    @include respond-to($small-up){\n      font-size: var(---font-size-h4--desktop);\n    }\n\n  }\n\n  h5, .h5,\n  .rte h5, .rte .h5 {\n\n    @include heading-style--2();\n\n    font-size: var(---font-size-h5--mobile);\n    @include respond-to($small-up){\n      font-size: var(---font-size-h5--desktop);\n    }\n\n  }\n\n  h6, .h6,\n  .rte h6, .rte .h6 {\n\n    @include heading-style--2();\n\n    font-size: var(---font-size-h6--mobile);\n    @include respond-to($small-up){\n      font-size: var(---font-size-h6--desktop);\n    }\n\n  }\n\n\n  .subheading {\n    color: RGB(var(--subheading-color));\n  }\n\n  .heading.heading--regular {\n\n    letter-spacing: var(---letter-spacing-subheading--mobile);\n    text-transform: uppercase;\n    font-style: var(---font-style-heading);\n    font-weight: var(---font-weight-heading);\n  \n    font-family: var(---font-family-heading);\n    font-size: var(---font-size-subheading-large--mobile);\n    \n    @include respond-to($small-up) {\n      font-size: var(---font-size-subheading-large--desktop);\n    }\n\n\n  }\n\n  .product-sticky-form__title,\n  .heading.heading--small {\n\n    letter-spacing: var(---letter-spacing-subheading--mobile);\n    text-transform: uppercase;\n    font-style: var(---font-style-heading);\n    font-weight: var(---font-weight-heading);\n\n    font-family: var(---font-family-heading);\n    font-size: var(---font-size-subheading--mobile);\n    \n    + p,\n    + .h1,\n    + h1,\n    + .h2,\n    + h2,\n    + .h3,\n    + h3,\n    + .h4,\n    + h4 {\n        margin-top: 12px;\n    }\n\n    + hr {\n      margin-top: 0;\n    }\n\n    @include respond-to($small-up) {\n      font-size: var(---font-size-subheading--desktop);\n    }\n\n  }\n\n  .heading.heading--xsmall {\n\n    letter-spacing: var(---letter-spacing-subheading--mobile);\n    text-transform: uppercase;\n    font-style: var(---font-style-heading);\n    font-weight: var(---font-weight-heading);\n\n    font-family: var(---font-family-heading);\n    font-size: var(---font-size-subheading-small--mobile);\n\n    @include respond-to($small-up) {\n      font-size: var(---font-size-subheading-small--desktop);\n    }\n\n  }\n\n\n  // Text Content\n\n  body {\n    line-height: var(---line-height-body--mobile);\n  }\n\n  .text--small {\n\n    margin-top: 0;\n\n    font-size: var(---font-size-body--mobile);\n    @include respond-to($small-up){\n      font-size: var(---font-size-body--desktop);\n    }\n\n  }\n\n  .text--xxsmall,\n  .tiny {\n    font-size: var(---font-size-body-xs--mobile);\n    @include respond-to($small-up){\n      font-size: var(---font-size-body-xs--desktop);\n    }\n  }\n\n  .text--xsmall,\n  .minor {\n    font-size: var(---font-size-body-small--mobile);\n    @include respond-to($small-up){\n      font-size: var(---font-size-body-small--desktop);\n    }\n  }\n\n  .text--large,\n  .major {\n    font-size: var(---font-size-body-large--mobile);\n    @include respond-to($small-up){\n      font-size: var(---font-size-body-large--desktop);\n    }\n  }\n\n  .p--mobile {\n\n    @include respond-to($small-down){\n\n      font-size: var(---font-size-body--mobile);\n      font-family: var(---font-family-body);\n      font-weight: var(---font-weight-body);\n\n    }\n\n  }\n\n  .text--xxsmall,\n  .tiny {\n    font-size: var(---font-size-body-xs--mobile);\n    @include respond-to($small-up){\n      font-size: var(---font-size-body-xs--desktop);\n    }\n    p {\n      font-size: var(---font-size-body-xs--mobile);\n      @include respond-to($small-up){\n        font-size: var(---font-size-body-xs--desktop);\n      }\n    }\n  }\n\n  .text--xxsmall--mobile,\n  .p-tiny--mobile {\n\n    @include respond-to($small-down){\n      font-family: var(---font-family-body);\n      font-weight: var(---font-weight-body);\n      font-size: var(---font-size-body-xs--mobile);\n    }\n\n  }\n\n  .text--xsmall,\n  .minor {\n    font-size: var(---font-size-body-small--mobile);\n    @include respond-to($small-up){\n      font-size: var(---font-size-body-small--desktop);\n    }\n    p:not(.heading) {\n      font-size: var(---font-size-body-small--mobile);\n      @include respond-to($small-up){\n        font-size: var(---font-size-body-small--desktop);\n      }\n    }\n  }\n\n  .text--small--mobile,\n  .p-minor--mobile {\n\n    @include respond-to($small-down){\n      font-size: var(---font-size-body-small--mobile);\n      font-family: var(---font-family-body);\n      font-weight: var(---font-weight-body);\n    }\n\n  }\n\n  .text--large,\n  .major {\n    font-size: var(---font-size-body-large--mobile);\n    @include respond-to($small-up){\n      font-size: var(---font-size-body-large--desktop);\n    }\n    p:not(.heading) {\n      font-size: var(---font-size-body-large--mobile);\n      @include respond-to($small-up){\n        font-size: var(---font-size-body-large--desktop);\n      }\n    }\n  }\n\n  .text--large--mobile,\n  .p-major--mobile {\n\n    @include respond-to($small-down){\n      font-size: var(---font-size-body-large--mobile);\n      font-family: var(---font-family-body);\n      font-weight: var(---font-weight-body);\n    }\n\n  }\n\n  strong, .strong {\n\n    // --color: var(---color-text-dark);\n\n    font-weight: var(---font-weight-body--bold);\n\n  }\n\n  .link.link--strong {\n    font-weight: var(---font-weight-body--bold);\n    text-decoration: none;\n  }\n\n  .blockquote, blockquote {\n\n    /*\n    font-size: var(---font-size-h2--mobile);\n    @include respond-to($small-up){\n      font-size: var(---font-size-h2--desktop);\n    }\n    */\n\n    font-size: var(---font-size-h3--mobile);\n    @include respond-to($small-up){\n      font-size: var(---font-size-h3--desktop);\n    }\n\n  }\n\n\n  /* Product Titles */\n\n  .product-item {\n\n    .product-item-meta__title {\n\n      font-family: var(---font-family-heading);\n      font-weight: var(---font-weight-body--bold);\n      line-height: var(---line-height-heading--mobile);\n      letter-spacing: var(---letter-spacing-subheading--mobile);\n\n      font-size: var(---font-size-body-large--mobile);\n      @include respond-to($small-up){\n        font-size: var(---font-size-body-large--desktop);\n      }\n  \n    }\n\n  }\n\n  .product-meta__title {\n\n    font-size: var(---font-size-h1--mobile);\n    @include respond-to($small-up){\n      font-size: var(---font-size-h1--desktop);\n    }\n\n  }\n\n  /* Header */\n\n  .header__linklist {\n    // font-family: var(---font-family-heading);\n    font-weight: var(---font-weight-body--bold);\n  }\n\n\n\n\n\n  .quiz {\n\n    h1, .h1,\n    h2, .h2,\n    h3, .h3,\n    h4, .h4,\n    // h5, .h5,\n    // h6, .h6 \n    {\n\n      margin-top: 36px;\n      margin-bottom: 12px;\n\n    }\n\n  }\n\n\n  .jdgm-carousel-title {\n    @extend .h2;\n  }", "/*\n\n  1. Utilities\n  2. Responsive\n  3. UI Elements\n    3.1. Buttons\n\n*/\n\n/*  ==============================\n    1. Utilities\n    ============================== */\n\n@mixin hide-scrollbars() {\n  -ms-overflow-style: none;  /* Internet Explorer 10+ */\n  scrollbar-width: none;  /* Firefox */\n  &::-webkit-scrollbar {\n    display: none;  /* Safari and Chrome */\n  }\n}\n\n@mixin clearfix() {\n  &::after {\n    content: '';\n    display: table;\n    clear: both;\n  }\n\n  // sass-lint:disable\n  *zoom: 1;\n}\n\n@mixin visually-hidden() {\n  // sass-lint:disable no-important\n  position: absolute !important;\n  overflow: hidden;\n  clip: rect(0 0 0 0);\n  height: 1px;\n  width: 1px;\n  max-width: 1px;\n  max-height: 1px;\n  font-size: 0;\n  margin: -1px;\n  padding: 0;\n  border: 0;\n}\n\n@mixin visually-shown($position: inherit) {\n  // sass-lint:disable no-important\n  position: $position !important;\n  overflow: auto;\n  clip: auto;\n  width: auto;\n  height: auto;\n  margin: 0;\n}\n\n/*  ==============================\n    2. Responsive\n    ============================== */\n\n@mixin respond-to($media-query) {\n  $breakpoint-found: false;\n\n  @each $breakpoint in $breakpoints {\n    $name: nth($breakpoint, 1);\n    $declaration: nth($breakpoint, 2);\n\n    @if $media-query == $name and $declaration {\n      $breakpoint-found: true;\n\n      @media only screen and #{$declaration} {\n        @content;\n      }\n    }\n  }\n\n  @if $breakpoint-found == false {\n    @warn 'Breakpoint \"#{$media-query}\" does not exist';\n  }\n}\n\n/*================ Responsive Show/Hide Helper ================*/\n@mixin responsive-display-helper($breakpoint: '') {\n  // sass-lint:disable no-important\n  .#{$breakpoint}shown {\n    display: block !important;\n  }\n\n  .#{$breakpoint}hidden {\n    display: none !important;\n  }\n}\n\n\n/*================ Responsive Text Alignment Helper ================*/\n@mixin responsive-text-align-helper($breakpoint: '') {\n  // sass-lint:disable no-important\n  .#{$breakpoint}text-left {\n    text-align: left !important;\n  }\n\n  .#{$breakpoint}text-right {\n    text-align: right !important;\n  }\n\n  .#{$breakpoint}text-center {\n    text-align: center !important;\n  }\n}\n\n/*  ==============================\n    3. UI Elements\n    ============================== */\n\n/*  ------------------------------\n    3.1. Buttons\n    ------------------------------ */\n\n@mixin button-structure() {\n\n  // --button-text-color: var(---color--secondary--rgb);\n  // --button-text-color--hover: var(---color-text--reversed--rgb);\n  // --button-background: var(---color--secondary--rgb);\n  // --hover-effect-color: var(---color--secondary--rgb);\n\n  overflow: hidden;\n  position: relative;\n  display: inline-flex;\n  gap: 20px;\n  align-items: center;\n  justify-content: space-between;\n\n  margin: 0;\n  width: auto;\n\n  height: auto;\n  min-height: 0;\n  max-height: none;\n\n  border: 0;\n\n  font-family: var(---font-family-heading-alternate);\n  font-style: var(---font-style-heading-alternate);\n  font-weight: var(---font-weight-heading-alternate);\n\n  line-height: 1.2;\n  text-align: center;\n  text-transform: unset;\n\n  letter-spacing: var(---button-letter-spacing);\n  vertical-align: top;\n  // white-space: nowrap;\n\n  border-radius: var(--button-border-radius);\n  // background: transparent;\n\n  transition: all 0.2s ease-in-out;\n  appearance: none;\n\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n  font-smoothing: antialiased;\n\n  cursor: pointer;\n\n  padding: 1.5em 2.2em;\n  \n  margin-top: 0.25em;\n  margin-bottom: 0.25em;\n  margin-right: 0.5em;\n  margin-left: 0;\n\n  font-size: var(---font-size-button--mobile);\n  @include respond-to($small-up) {\n    font-size: var(---font-size-button--desktop);\n  }\n\n  &:not(.button--link) {\n    min-width: var(---button-min-width);\n  }\n\n  &:last-child {\n    margin-right: 0 !important;\n  }\n  &:only-child {\n    margin-bottom: 0 !important;\n  }\n\n  // Sizes\n\n  @include respond-to($medium-down) {\n    padding: 1em 2em;\n  }\n\n  &.button--tiny {\n    padding: 0.8em 1.2em;\n  }\n\n  &.button--small {\n    padding: 1.2em 1.8em;\n  }\n\n  &.button--large {\n    padding: 2em 2.8em;\n  }\n\n  &.button--huge {\n    padding: 1.4em 1.8em;\n    font-size: var(---font-size-button-large--mobile);\n    @include respond-to($medium-up) {\n      font-size: var(---font-size-button-large--mobile);\n    }\n  }\n\n  &.button--full {\n    display: flex;\n    width: 100%;\n    margin-left: 0 !important;\n    margin-right: 0 !important;\n    text-align: center;\n    justify-content: center;\n  }\n\n  &.disabled,\n  &[disabled] {\n\n    opacity: 0.5;\n    cursor: not-allowed;\n\n  }\n\n  // Style\n\n  .loader-button__text {\n    gap: 0.5em;\n  }\n\n  svg {\n    position: relative;\n    top: -1px;\n    max-height: 16px;\n    fill: currentColor;\n    stroke: currentColor;\n  }\n\n  + .button {\n    margin-left: 0.25em;\n  }\n\n}\n\n@mixin button-style($color){\n\n  --button--background: var(---color--#{$color}--rgb);\n  --master-color--light--rgb: var(---color--#{$color}--light--rgb);\n  --master-color--dark--rgb: var(---color--#{$color}--dark--rgb);\n  --master-color-background--rgb: var(---background-color--#{$color}--rgb);\n\n  --button-background: var(--button--background);\n  --button-background--hover: var(--master-color--light--rgb);\n  --button-background--active: var(--master-color--dark--rgb);\n  --button-text-color: var(---color-text--reversed--rgb);\n  --button-text-color--hover: var(---color-text--reversed--rgb);\n\n  &:not(.button--inverted) {\n    --border-color: transparent;\n    --color-text--hover: var(---color-text--reversed--rgb);\n  }\n  \n  &:not([disabled]) {\n  \n    background-image: \n\n    linear-gradient(178deg, \n      rgb(var(--button-background)), \n      rgb(var(--button-background)) 10%, \n      rgba(var(--button-background--hover),1) 10%, \n      rgba(var(--button-background--hover),1) 100%),\n\n      linear-gradient(rgb(var(--button-background)), rgb(var(--button-background)));\n      \n  }\n\n  background-color: RGB(var(--button-background));\n  \n}\n\n@mixin button-style--inverted(){\n\n  background-color: transparent;\n  --button-text-color: var(--button--background);\n\n  box-shadow: 0 0 0 1px RGBA(var(--button--background), 1) inset;\n\n  &:not([disabled]) {\n\n    background-image: \n      linear-gradient(178deg, \n      rgba(var(--button--background), 0), \n      rgba(var(--button--background), 0) 10%, \n      rgba(var(--button--background), 1) 10%, \n      rgba(var(--button--background), 1) 100%),\n      linear-gradient(rgba(var(--button--background), 0), rgba(var(--button--background), 0));\n\n      --button-text-color: var(--button--background);\n\n    background-size: 100% 200%, 100% 100%;\n    background-position: 100% -100%, 100% 100%;\n    background-repeat: no-repeat;\n    transition: background-position 0.3s cubic-bezier(0.215, 0.61, 0.355, 1);\n    transform: translateZ(0); /* Make sure to promote the button on its own layer */\n\n    &:focus,\n    &:focus-within,\n    &:hover {\n\n      --button-text-color: var(---color-text--reversed--rgb);\n\n      background-position: 100% 25%, 100% 100%;\n\n    }\n\n    &:active {\n\n      background-position: 100% 25%, 100% 100%;\n\n    }\n\n  }\n\n}\n\n@mixin button-style--light(){\n\n  --button-background: var(--master-color-background--rgb);\n  --button-text-color: var(--button--background);\n\n  &:not([disabled]) {\n\n    background-image: \n\n    linear-gradient(178deg, \n      rgb(var(--button-background)), \n      rgb(var(--button-background)) 10%, \n      rgba(var(--button-background--hover),0.35) 10%, \n      rgba(var(--button-background--hover),0.35) 100%),\n\n      linear-gradient(rgb(var(--button-background)), rgb(var(--button-background)));\n\n    background-size: 100% 200%, 100% 100%;\n    background-position: 100% -100%, 100% 100%;\n    background-repeat: no-repeat;\n    transition: background-position 0.3s cubic-bezier(0.215, 0.61, 0.355, 1);\n    transform: translateZ(0); /* Make sure to promote the button on its own layer */\n\n    &:active,\n    &:focus,\n    &:focus-within,\n    &:hover {\n\n      // --button-background: var(--button--background);\n      // --button-text-color: var(---color-text--reversed--rgb);\n\n      background-position: 100% 25%,100% 100%;\n\n    }\n\n  }\n\n}\n\n/* ------------------------------\n   Headings\n   ------------------------------ */\n\n@mixin heading-style--1() {\n\n  font-family: var(---font-family-heading);\n  font-style: var(---font-style-heading);\n  font-weight: var(---font-weight-heading);\n  font-variation-settings: \"wght\" 400;\n\n  letter-spacing: var(---letter-spacing-heading--mobile);\n  line-height: var(---line-height-heading--mobile);\n\n  span.heading--alternate {\n    color: var(---color-heading-1);\n    text-transform: none;\n    font-weight: var(---font-weight-heading);\n  }\n\n  a {\n    color: var(---color-link);\n  }\n\n  @include respond-to($small-up) {\n    letter-spacing: var(---letter-spacing-heading--desktop);\n    line-height: var(---line-height-heading--desktop);\n  }\n\n}\n\n@mixin heading-style--2() {\n\n  font-family: var(---font-family-body);\n  font-style: var(---font-style-body);\n  font-weight: var(---font-weight-body--bold);\n\n  letter-spacing: var(---letter-spacing-body--mobile);\n  line-height: var(---line-height-body--mobile);\n\n  span.heading--alternate {\n    color: var(---color-heading-2);\n    text-transform: none;\n    font-weight: var(---font-weight-body);\n  }\n\n  a {\n    color: var(---color-link);\n  }\n\n  @include respond-to($small-up) {\n    letter-spacing: var(---letter-spacing-heading--desktop);\n    line-height: var(---line-height-heading--desktop);\n  }\n\n}\n\n@mixin blockquote-style() {\n\n  font-family: var(---font-family-body);\n  font-style: var(---font-style-body);\n  font-weight: var(---font-weight-body--bold);\n\n  letter-spacing: var(---letter-spacing-body--mobile);\n  line-height: var(---line-height-body--mobile);\n\n  a {\n    color: var(---color-link);\n  }\n\n  @include respond-to($small-up) {\n    letter-spacing: var(---letter-spacing-heading--desktop);\n    line-height: var(---line-height-heading--desktop);\n  }\n\n}\n\n@mixin subheading-style() {\n\n  text-transform: uppercase;\n  letter-spacing: var(---letter-spacing-subheading--mobile);\n  font-style: var(---font-style-subheading);\n  font-variation-settings: 'wght' 400;\n\n  font-family: var(---font-family-subheading);\n\n  a {\n    color: var(---color-link);\n  }\n\n  @include respond-to($small-up) {\n    letter-spacing: var(---letter-spacing-subheading--desktop);\n    line-height: var(---line-height-subheading--desktop);\n  }\n\n}\n\n\n/* ------------------------------\n   Labels\n   ------------------------------ */\n\n@mixin label-structure() {\n\n  // display: block;\n  // width: 100%;\n\n}\n\n@mixin label-style() {\n\n\n\n}\n\n/* ------------------------------\n   Inputs\n   ------------------------------ */\n\n@mixin input-structure() {\n\n  $border-color: var(---color--secondary);\n  $border-color-hover: var(---color--secondary-hover);\n\n  display: block;\n  // margin-bottom: 1em;\n  // width: 100%;\n  padding: 1em 1.4em;\n\n  font-size: var(---font-size-button--mobile);\n\n  border-width: 1px;\n\n  &:not([disabled]){\n    &:focus,\n    &:hover {\n      border-color: $border-color-hover;\n    }\n  }\n\n  @include respond-to($medium-up) {\n    font-size: var(---font-size-button--desktop);\n  }\n\n}\n\n@mixin input-style() {\n\n  --text-color: var(---color-text);\n  --background: var(---background-color--content-2);\n\n  // border-radius: 12px;\n  border: 1px solid var(---color-line);\n  // margin-bottom: 1em;\n\n  &::placeholder {\n    color: var(---color-text--light);\n    opacity: 1;\n  }\n\n  &:not([disabled]){\n    &:focus,\n    &:hover {\n      border-color: var(---color--primary);\n    }\n  }\n\n  &.input--rounded {\n    border-radius: var(---input-border-radius);\n  }\n\n  /*\n\n  // Removing this as Focal already has good base styles.\n\n  background: var(---input-background);\n  border-style: solid;\n  color: var(---input-color);\n\n  outline: none;\n\n  */\n\n}\n\n@mixin input-style--2() {\n\n  border: 0;\n  padding: 0.5em 0.2em;\n\n  border-bottom: 1px solid var(---color--black);\n\n  font-size: var(---font-size-body--mobile);\n  @include respond-to($medium-up) {\n    font-size: var(---font-size-body--desktop);\n  }\n\n}\n\n/* ------------------------------\n   RTE\n   ------------------------------ */\n\n    @mixin link-style() {\n\n      &:after {\n\n        content: '';\n        display: block;\n        height: 2px;\n        width: 100%;\n        background: currentColor;\n        color: var(---color-link);\n        position: absolute;\n        bottom: 0;\n        left: 0;\n        right: 0;\n\n        transition: transform 0.25s;\n        transform-origin: left;\n        transform: scale(0);\n\n      }\n\n      &:hover {\n        color: var(---color-link--hover);\n        &:after {\n          transform: scale(1);\n        }\n      }\n\n    }\n\n/*  ------------------------------\n    3.3. Shopify\n    ------------------------------ */\n\n@mixin when-logged-in {\n\n  body.logged-in & {\n    @content;\n  }\n\n}\n\n@mixin when-logged-out {\n\n  body.logged-out & {\n    @content;\n  }\n\n}\n", ".quiz {\n\n  svg {\n    * {\n      // fill: currentColor;\n    }\n  }\n  \n  .icon {\n    &.icon--fill {\n      * {\n        stroke: none;\n        fill: currentColor;\n      }\n    }\n  }\n  \n}", "html {\n  scroll-behavior: smooth;\n}\n\n.table--bordered {\n\n  td {\n    border-top: 1px solid #e6e6e6;\n  }\n\n}", ".split-page {\n  \n  @include respond-to($medium-up) {\n    min-height: 80vh;\n    height: 100%;\n    display: grid;\n    grid-template-columns: 3fr 4fr;\n  }\n\n  background: RGB(var(--section-block-background));\n\n  .page-header__text-wrapper {\n    margin-top: 0;\n    margin-bottom: 38px;\n  }\n\n}\n\n.split-page__header {\n  \n  padding-top: calc(var(--vertical-breather) * 2);\n  \n}\n\n.split-page__footer {\n  \n  padding-bottom: calc(var(--vertical-breather) * 2);\n  justify-self: flex-end;\n  \n  .form__secondary-action {\n    margin: 0;\n    button,\n    a {\n      font-weight: bold;\n      text-decoration: none;\n    }\n  }\n\n}\n\n.split-page__left {\n\n}\n\n.split-page__right {\n\n}\n\n.split-page__image {\n  height: 100%;\n  object-fit: cover;\n}\n\n.split-page__content {\n  padding: 0 var(--container-gutter);\n}\n\n.split-page__content-wrapper {\n\n  position: relative;\n  \n  height: 100%;\n  margin: auto;\n  \n  display: flex;\n  flex-direction: column;\n  justify-content: space-between;\n\n  @include respond-to($medium-up) {\n    max-height: calc(100vh - var(--header-height));\n  }\n\n}", "html {\n\n  &.supports-no-cookies {\n    .supports-no-cookies {\n      display: none;\n    }\n  }\n\n  &.supports-cookies {\n    .supports-cookies {\n      display: none;\n    }\n  }\n\n}\n\nbody {\n\n  &.logged-out {\n    \n  }\n\n  &.logged-in {\n\n  }\n\n  &.in-theme-customizer {\n\n  }\n\n}\n", "[class*=\"template-customers\"] {\n\n  // --background: var(---background-color--content-1);;\n\n  /* ----- Link Bar ----- */\n\n  .link-bar {\n\n    --background: var(---background-color--content-reversed-1);\n    --text-color: var(---color-text--reversed);\n\n    background: var(--background);\n    color: var(--text-color);\n\n    .link-bar__link-item {\n\n      transition: 0.25s color;\n      &:hover {\n        color: var(---color--highlight);\n      }\n\n      .text--subdued {\n        transition: 0.25s color;\n        &:hover {\n          color: var(---color--danger) !important;\n        }\n      }\n    }\n\n    .text--underlined {\n\n      --text-color: var(---color--highlight);\n\n      color: var(--text-color);\n      \n      text-decoration: none;\n      cursor: default;\n      pointer-events: none;\n\n      &:after {\n        content: none;\n      }\n\n    }\n\n  }\n\n\n  /* ----- Page Header ----- */\n\n  .page-header {\n\n    .heading {\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      gap: 0.25em;\n    }\n\n    .bubble-count {\n      background-color: var(---color--highlight);\n      color: var(---color-text);\n      font-weight: 700;\n      letter-spacing: -0.075em !important;\n      height: 36px;\n      width: 36px;\n      font-size: 0.5em;\n    }\n  }\n\n\n  /* ----- Link Bar ----- */\n\n}\n\n\n\n.account {\n  \n  background-color: var(---background-color--content-1);\n\n  .account__orders-table {\n  \n    font-weight: 400;\n    font-size: var(---font-fize-body);\n\n    thead {\n      th {\n        padding: 0.5rem 0;\n      }\n    }\n\n    td {\n      padding: 0.5rem 0;\n    }\n\n    .reorder-button {\n      letter-spacing: 0;\n      min-width: 0;\n      padding: 0em 1em;\n      line-height: 2.4em;\n\n      background-color: var(---color--highlight);\n\n    }\n\n  }\n\n  .account__order-item-actions {\n    display: flex;\n    flex-direction: column;\n    gap: 0.5em;\n    @media (min-width: 1000px) {\n      flex-direction: row;\n      gap: 20px;\n    }\n  }\n\n}\n\n", ".mini-cart {\n\n  --root-background: var(---background-color--content-1--rgb);\n  --section-block-background: var(---background-color--content-1--rgb);\n  --background: var(---background-color--content-1--rgb);\n\n  width: 100vw;\n\n  /* ----- Loading Overlay ----- */\n\n  &:after {\n  \n    pointer-events: none;\n\n    content: \"\";\n    display: block;\n    position: absolute;\n    top: 0;\n    bottom: 0;\n    left: 0;\n    right: 0;\n    z-index: 1;\n\n    transition: 0.25s background-color;\n\n    background: RGBA(255, 255, 255, 0);\n\n  }\n\n  &.cart-drawer--loading {\n\n    &:after {\n      background: RGBA(var(---background-color--content-1--rgb), 0.75);\n      pointer-events: auto;\n    }\n\n  }\n\n  /* ----- Drawer Header ----- */\n\n  .drawer__header {\n\n    border-bottom: 0;\n    max-height: none;\n    height: auto;\n\n  }\n\n  .drawer__title {\n    text-transform: none;\n    margin-bottom: 0;\n  }\n\n  .drawer__close-button {\n    bottom: 0;\n    top: 0;\n  }\n\n  free-shipping-bar {\n    \n    padding: 20px 30px;\n    margin: 0 0 20px 0;\n    background: RGB(var(---background-color--content-2--rgb));\n\n    border-radius: var(---border-radius--general);\n\n    .text--small {\n      margin-bottom: 0;\n    }\n\n  }\n\n  .mini-cart__drawer-footer {\n    --root-border-color: var(---color-line--light--rgb);\n    padding: 20px var(--container-gutter);\n  }\n\n  // Buttons\n\n\n  // Product Item Meta\n\n  .product-item-meta__title {\n    line-height: 1.2;\n    font-size: var(---font-size-body--desktop);\n  }\n\n  .product-item-meta__property {\n    font-weight: var(---font-weight-body);\n  }\n\n  .product-item-meta__price-and-remove {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n\n    .line-item__quantity {\n      margin-top: 0;\n    }\n  }\n\n  /* ----- Line Items ----- */\n\n  .line-item {\n    \n    .line-item__content-wrapper {\n      margin-top: 0;\n      margin-bottom: 35px;\n    }\n\n    .line-item__info {\n      width: 100%;\n    }\n\n    .line-item__image {\n      border-radius: 6px;\n    }\n\n    .line-item__image-wrapper {\n      margin-right: 10px;\n      @include respond-to($medium-up) {\n        margin-right: 20px;\n      }\n    }\n\n    .line-item__remove-button {\n      font-weight: 400;\n    }\n\n    // Price Display\n\n    .product-item-meta__property-list {\n      margin: 0;\n    }\n\n    .product-item-meta__price-list-container {\n      margin: 0;\n    }\n\n    // Quantity Selector\n\n    .quantity-selector {\n      --quantity-selector-height: 32px;\n      overflow: hidden;\n    }\n\n    .quantity-selector__input {\n      font-size: var(---font-size-body-small--desktop);\n      font-weight: var(---font-weight-body);\n      background: RGB(var(---background-color--content-1--rgb));\n    }\n\n    .quantity-selector__button {\n      background: RGB(var(---background-color--content-1--rgb));\n    }\n\n    .line-item__remove-button {\n      font-size: var(---font-size-body-small--desktop);\n      font-weight: var(---font-weight-body);\n    }\n\n  }\n\n\n  .mini-cart__drawer-prefooter {\n\n    padding: 10px var(--container-gutter);\n    text-align: center;\n    font-weight: var(---font-weight-body);\n    position: relative;\n\n    &:after {\n      content: '';\n      display: block;\n\n      position: absolute;\n      height: 30px;\n      width: 100%;\n      top: 0;\n      transform: translateY(calc(-100%));\n\n      background: linear-gradient(to bottom, rgba(255, 255, 255, 0), rgba(255, 255, 255, 1));\n    }\n\n  }\n\n  \n  /* ----- Cart Subscriptions Box ----- */\n\n  .cart-subscriptions {\n\n    display: block;\n    margin-bottom: 12px;\n    \n    border-radius: 8px;\n    background: var(---background-color--content-2);\n\n    .cart-subscriptions-form__actions {\n      display: flex;\n      flex-direction: column;\n      align-items: center;\n      gap: 1em;\n    }\n\n    .cart-subscriptions__form {\n      padding: 12px;\n      border-top: 1px solid rgba(var(---color--brand-6--rgb), 0.5);\n\n      @include respond-to($medium-up) {\n        padding: 24px;\n      }\n    }\n\n    .subscriptions-input {\n\n      margin: 1em 0;\n      gap: 10px;\n\n      display: flex;\n      flex-direction: column;\n      align-items: center;\n      justify-content: center;\n\n      @include respond-to($medium-up) {\n        flex-direction: row;\n      }\n\n      label {\n        font-weight: 700;\n        font-size: var(---font-size-body--small);\n      }\n\n      select {\n        padding: 0.25em 2.5em 0.25em 0.75em;\n        border-radius: 8px;\n      }\n    }\n\n  }\n\n  /* ----- Tags ----- */\n\n  .product-item-tags {\n    display: flex;\n    flex-wrap: wrap;\n    margin: 0.5em 0;\n    gap: 10px;\n  }\n  \n  .product-item-tag {\n  \n    display: inline-flex;\n    align-items: center;\n    padding: 0.35em 0.5em;\n    gap: 0.25em;\n  \n    background: var(---background-color--secondary);\n    border-radius: 4px;\n  \n    // pointer-events: none;\n    user-select: none;\n\n    svg {\n      * {\n        fill: currentColor;\n        outline: currentColor;\n      }\n    }\n  \n  }\n  \n  .product-item-tag--prescription {\n    background: RGB(var(---background-color--danger--rgb));\n    color: RGB(var(---color--danger--rgb));\n  }\n  \n  .product-item-tag--subscription {\n    background-color: var(---color--highlight);\n    // color: RGB(var(---color--danger--rgb));\n  }\n  \n  .product-item-tag__icon {\n    display: flex;\n    align-items: center;\n    line-height: 1;\n  \n    svg {\n      width: 20px;\n      height: 20px;\n    }\n  }\n  \n  .product-item-tag__text {\n    line-height: 1;\n  }\n\n\n  /* ----- Shipping Details ----- */\n\n  .shipping-details {\n\n    --padding-horizontal: 30px;\n    --padding-vertical: 20px;\n\n  }\n\n  .shipping-details__inner {\n\n    margin-top: calc(1 * var(--padding-vertical));\n\n    margin-left: calc(-1 * var(--padding-horizontal));\n    margin-right: calc(-1 * var(--padding-horizontal));\n\n    padding-top: var(--padding-vertical);\n    padding-left: var(--padding-horizontal);\n    padding-right: var(--padding-horizontal);\n\n    border-top: 1px solid RGBA(0, 0, 0, .2);\n\n  }\n\n  .shipping-details__footer {\n\n    margin-top: calc(1 * var(--padding-vertical));\n\n    padding-top: calc(0.5 * var(--padding-vertical));\n    padding-left: var(--padding-horizontal);\n    padding-right: var(--padding-horizontal);\n\n    border-top: 1px solid RGBA(0, 0, 0, .2);\n\n    line-height: 1.2;\n\n  }\n\n  .shipping-details__header {\n    display: flex;\n    justify-content: space-between;\n  }\n\n  .shipping-details__heading {\n    padding: 0;\n    margin: 0;\n  }\n\n  .shipping-details__table {\n\n    width: 100%;\n\n    text-align: left;\n    font-size: var(---font-size-body-small--desktop);\n\n    th,\n    td {\n      font-size: 0.9em !important;\n    }\n\n    tr {\n\n      th,\n      td {\n        text-align: center;\n        padding: 0.1em 0;\n\n        &:first-child {\n          text-align: left;\n        }\n\n        &:last-child {\n          text-align: right;\n        }\n      }\n    }\n  }\n\n  .shipping-details__message {\n\n    background-color: var(---background-color--content-1);\n    padding: 0.75em;\n    margin: 1em 0;\n    border-radius: var(--block-border-radius);\n\n    line-height: 1.4;\n\n    p {\n      font-size: 0.9em !important;\n    }\n\n  }\n\n  .cart-vet-partner__selector-form {\n    border-top: 1px solid var(---color-line--light);\n    padding-top: 20px;\n    margin-top: 20px;\n  }\n\n}", ".account-dog-info {\n\n    --text-font-weight: 300;\n\n    display: flex;\n    flex-direction: column;\n    gap: 30px;\n    padding: 24px;\n\n    border-radius: 18px;\n    overflow: hidden;\n\n    font-weight: var(--text-font-weight);\n\n    color: RGB(var(--text-color));\n\n    strong {\n        font-weight: 700;\n    }\n\n    h1,\n    h2,\n    h3,\n    h4,\n    h5,\n    h6,\n    .h1,\n    .h2,\n    .h3,\n    .h4,\n    .h5,\n    .h6 {\n        color: RGB(var(--heading-color));\n    }\n\n    .account-dog-info__body {}\n\n    .account-dog-info {\n        display: flex;\n    }\n\n    .button-wrapper {\n\n        display: flex;\n        flex-direction: column;\n        gap: 0.5em;\n\n        @include respond-to($medium-up) {\n            display: flex;\n            flex-direction: row;\n        }\n    }\n\n    .button {\n        line-height: 1.2 !important;\n    }\n\n    .button-wrapper--center {\n        justify-content: center;\n    }\n\n    @include respond-to($medium-up) {\n        padding: 48px;\n    }\n\n}", ".product__media {\n  @include respond-to($medium-up) {\n    position: sticky;\n    top: calc(var(--announcement-bar-height) + var(--header-height) + var(--vertical-breather));\n  }\n}", ".shopify-section--feeding-calculator {\n\n  @include respond-to($small-up) {\n\n    margin-top: 150px;\n\n  }\n\n}\n\n.fieldset {\n\n  --form-input-gap: 24px;\n\n  margin: var(--container-gutter) 0;\n\n  &:last-child {\n    margin-bottom: 0;\n  }\n  \n}\n\n.feeding-calculator {\n\n  position: relative;\n\n}\n\n.feeding-calculator__icon {\n\n  position: absolute;\n  left: 0;\n  right: 0;\n  top: 0;\n  margin: auto;\n\n  transform: translateY(-65%);\n\n  display: none; // Hidden on Mobile\n\n  @include respond-to($small-up) {\n\n    display: block;\n    \n    width: 200px;\n    height: 200px;\n    \n  }\n\n  @include respond-to($large-up) {\n    \n    width: 250px;\n    height: 250px;\n    \n  }\n\n}\n\n\n// Table\n\n.calculator-results-table {\n\n  tr {\n    &.selected {\n      td {\n        background: var(---color--brand-1);\n      }\n  \n      .label {\n        visibility: visible;\n      }\n    }\n  }\n\n  tr.results-row {\n    cursor: pointer;\n  }\n\n  th {\n    span {\n      display: block;\n      font-size: 0.75em;\n    }\n  }\n\n  td, th {\n\n    .label {\n      margin: 0 0.5em;\n    }\n\n  }\n\n  .label {\n    visibility: hidden;\n    padding: 0.4em 0.8em;\n  }\n\n}\n\n\n.results-row {\n\n  button.link {\n    display: flex;\n    align-items: center;\n  }\n\n  .results-row__details {\n    display: flex;\n    align-items: flex-start;\n    justify-content: flex-start;\n    gap: 0.5em;\n  }\n\n  .results-row__external-link {\n    padding: 0.05em 0.25em;\n    border: 1px solid RGBA(var(---color-line--rgb), 0.25);\n    border-radius: 5px;\n    background-color: RGBA(var(---color-line--rgb), 0);\n    transition: 0.25s background-color;\n    &:hover, &:focus {\n      background-color: RGBA(var(---color-line--rgb), 0.1);\n    }\n  }\n}\n\n\n\n\n\n\n/* ========== Nutrition ========== */\n\n.feeding-calculator-nutrition {\n\n  --product-image-size: 120px;\n  --product-image-border-size: 36px;\n\n  --primary-button-background: var(--product-color);\n\n  position: relative;\n  padding-top: 30px;\n\n}\n\n.feeding-calculator-nutrition__header {\n\n  position: relative;\n\n  display: flex;\n  justify-content: center;\n\n  height: 60px;\n\n  background: RGB(var(--product-color));\n  border-top-left-radius: var(--block-border-radius);\n  border-top-right-radius: var(--block-border-radius);\n\n  &:after {\n\n    content: '';\n\n    display: block;\n    padding: 15px;\n    margin: auto;\n\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    z-index: 1;\n\n    --offset: calc(-1 * (var(--product-image-size) - var(--product-image-border-size) * 2));\n\n    // transform: translateY(-45px);\n    transform: translateY(var(--offset));\n\n    background: RGB(var(--product-color));\n    border-radius: 120px;\n    width: calc(var(--product-image-size) + 30px);\n    height: calc(var(--product-image-size) + 30px);\n\n  }\n\n}\n\n.feeding-calculator-nutrition__header-image {\n\n  position: absolute;\n  top: 0;\n  transform: translateY(-30px);\n  z-index: 2;\n\n}\n\n.feeding-calculator-nutrition__content {\n\n  padding-top: calc(var(--vertical-breather) / 2);\n  position: relative;\n  z-index: 1;\n\n  background: RGB(var(--section-background, var(--background)));\n}\n\n.spaced-content--tight {\n  gap: 20px;\n}\n\n\n/* ----- Ratings ----- */\n\n.nutrition-ratings {\n\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  flex-wrap: wrap;\n  gap: 10px;\n  @include respond-to($medium-up) {\n    gap: 20px;\n  }\n  \n}\n\n/* ----- Nutrition Summary ----- */\n\n.nutrition-summary {\n  display: flex;\n  flex-direction: column;\n  gap: 20px;\n  @include respond-to($small-up) {\n    flex-direction: row;\n    .nutrition-summary-item {\n      width: 100%;\n    }\n  }\n}\n\n.nutrition-summary-item {}\n\n.nutrition-summary-item__title {\n  margin-bottom: 0.5em;\n}\n\n\n/* ========== Analysis Table ========== */\n\n\n.nutritional-analysis {\n\n  --row-spacing: 0.35em;\n  \n  text-align: left;\n  border: 5px solid RGB(var(--heading-color));\n  padding: 20px;\n  \n  font-size: var(---font-size-body-small--mobile);\n  @include respond-to($small-up) {\n    font-size: var(---font-size-body-small--desktop);\n  }\n\n}\n\n.nutritional-analysis__footer {\n\n  margin-top: 20px;\n\n  font-size: var(---font-size-body-xs--mobile);\n  @include respond-to($small-up) {\n    font-size: var(---font-size-body-xs--desktop);\n  }\n}\n\n\n/* ----- Analysis Category ----- */\n\n.analysis-category {\n  display: block;\n}\n\n.analysis-category__header {\n  display: flex;\n  gap: 10px;\n  width: 100%;\n  padding: var(--row-spacing) 0;\n  border-bottom: 1px solid var(---color-line--light);\n\n  &[aria-expanded=\"true\"] {\n    .analysis-category__button {\n      &:before {\n        content: \"-\";\n      }\n    }\n  }\n\n\n}\n\n\n/* ----- Analysis Header ----- */\n\n.analysis-header {\n  \n  display: block;\n  padding: var(--row-spacing) 0;\n  margin-top: 1em;\n  margin-bottom: 5px;\n  border-bottom: 1px solid var(---color-line);\n\n  &:first-child {\n    margin-top: 0;\n  }\n  \n}\n\n.analysis-header__title {\n  font-size: 20px;\n  text-transform: uppercase;\n  font-weight: var(---font-weight-body--bold);\n}\n\n\n/* ----- Analysis Category ----- */\n\n.analysis-category__content {}\n\n.analysis-category__title {\n  font-weight: var(---font-weight-body--bold);\n  // font-weight: bold;\n}\n\n.analysis-category__button {\n  display: inline-flex;\n  align-items: center;\n  justify-content: center;\n  width: 20px;\n  height: 20px;\n\n  font-weight: bold;\n  border-radius: 4px;\n  background: RGBA(var(--text-color), 0.05);\n\n  &:focus,\n  &:hover {\n    background: RGBA(var(--text-color), 0.1);\n  }\n\n  &:before {\n    content: \"+\";\n  }\n\n}\n\n.analysis-category__content {\n  display: none;\n  width: 100%;\n}\n\n.analysis-category__header[aria-expanded=\"true\"]+.analysis-category__content {\n  display: table !important;\n}\n\n.analysis-category {\n  .analysis-row {\n    >*:first-child {\n      @media(min-width: 480px) {\n        padding-left: 30px;\n      }\n    }\n  }\n}\n\n\n/* ----- Analysis Table ----- */\n\n.analysis-table {\n  display: table;\n  width: 100%;\n}\n\n.analysis-table-row {\n\n  display: table-row;\n  gap: 5px;\n\n  width: 100%;\n\n  line-height: 1.4;\n  font-size: 0.95em;\n\n  > * {\n\n    width: 70px;\n    text-align: center;\n\n    @media (min-width: 480px) {\n      width: 100px;\n    }\n\n    &:first-child {\n      text-align: left;\n      width: auto;\n      margin-right: auto;\n    }\n\n    &:last-child {\n      text-align: right;\n    }\n  }\n}\n\n\n.analysis-row {\n  width: 100%;\n  display: table-row;\n\n  >* {\n    display: table-cell;\n    padding-left: 5px;\n    padding-right: 5px;\n    padding: var(--row-spacing) 0;\n    border-bottom: 1px solid var(---color-line--light);\n\n    &:last-child {\n      text-align: right;\n      padding-right: 0;\n    }\n  }\n\n}\n\n/* ========== Classes ========== */\n\n.product-color {\n  color: RGB(var(--product-color));\n}\n\n.spaced-content {\n  text-align: center;\n  display: grid;\n  grid-auto-flow: row;\n  gap: 32px;\n\n  >* {\n    margin: 0;\n  }\n}\n\n.spaced-content--tight {\n  gap: 20px;\n}", ".section {\n\n  &.section--use-padding {\n    margin: 0;\n    padding: var(--vertical-breather) 0;\n  }\n\n  &.section--half-padding {\n    --vertical-breather: calc(var(--vertical-breather) / 2);\n  }\n\n  &.section--double-spacing {\n    --vertical-breather: var(--vertical-breather-double);\n  }\n\n  &.section--no-padding {\n    margin: 0;\n    padding: 0;\n  }\n\n  .container {\n\n    // margin-top: 0;\n    // margin-bottom: 0;\n    // padding-top: var(--vertical-breather);\n    // padding-bottom: var(--vertical-breather);\n\n  }\n\n  .container--no-padding {\n    padding: 0;\n  }\n\n  &.section--no-spacing {\n\n    // margin-top: 0;\n    // margin-bottom: 0;\n    // padding-top: 0;\n    // padding-bottom: 0;\n\n  }\n\n  &.section--no-spacing--top {\n\n    // margin-top: 0;\n    // padding-top: 0;\n\n  }\n\n  .subheading {\n    + .heading {}\n  }\n\n  .section__header {\n    &.section__header--wide {\n\n    }\n  }\n\n  .section__footer {\n    &.section__footer-left {\n\n    }\n  }\n\n}\n\n.container--smaller {\n  max-width: 700px;\n}", ".button {\n\n  display: inline-flex;\n  align-items: center;\n  justify-content: center;\n  gap: 0.5em;\n  transition: color 0.25s;\n  text-align: center;\n\n  line-height: 1.2;\n  // min-height: var(--button-height);\n  min-height: unset;\n\n  font-weight: var(--text-font-bold-weight);\n\n  font-size: var(---font-size-button--mobile);\n\n  &:not(.button--text) {\n\n    padding-inline-start: unset;\n    padding-inline-end: unset;\n    padding: 1em 1.5em;\n\n  }\n\n  @include respond-to($small-up) {\n    \n    font-size: var(---font-size-button--desktop);\n\n  }\n\n  &[disabled] {\n    --button-background: 154, 154, 154;\n    cursor: not-allowed;\n    opacity: 0.5;\n    background-position: 100% -100%, 100% 100% !important;\n  }\n\n  &:not(.button--link) {\n    min-width: 200px;\n  }\n\n  .loader-button__text {\n    gap: 16px;\n  }\n\n  &:not(.button--text) {\n\n    /*\n    padding-left: 50px;\n    padding-right: 50px;\n\n    @include respond-to($small-down) {\n      padding: 0 30px;\n    }\n    */\n\n  }\n\n  &.button--highlight {\n    --button-background: var(---color--highlight--rgb);\n    --button-text-color: 0, 0, 0;\n  }\n\n  &.button--tertiary {\n    --button-background: var(---color--tertiary--rgb);\n    --button-text-color: 0, 0, 0;\n  }\n\n  &.button--tab {\n\n    min-width: unset;\n    padding-left: 0 !important;\n    padding-right: 0 !important;\n\n    color: RGB(var(---color--brand-4--rgb));\n\n    cursor: pointer;\n\n    &[disabled] {\n      background: none;\n      pointer-events: none;\n      cursor: not-allowed;\n    }\n\n    &:not([disabled]) {\n\n      cursor: pointer;\n\n      &:hover,\n      &:focus {\n        color: RGB(var(---color--tertiary--rgb));\n      }\n\n      &.active {\n        color: RGB(var(---color--brand-1--rgb));\n        cursor: default;\n        pointer-events: none;\n      }\n\n    }\n\n  }\n\n  &.button--text {\n\n    display: inline-flex;\n    align-items: center;\n    gap: 10px;\n\n    min-width: 0;\n    padding-left: 0;\n    padding-right: 0;\n\n    font-size: var(---font-size-body-large--desktop);\n    font-weight: var(---font-weight-body--bold);\n\n    transition: 0.25s color;\n\n    &:focus,\n    &:hover {\n      color: var(---color-text--light);\n    }\n\n    .button__icon {\n\n      svg {\n        width: 10px;\n        height: 10px;\n        transform: rotate(45deg);\n      }\n    }\n\n  }\n\n  &.button--hollow {\n\n    background: transparent;\n    color: RGB(var(--button-background));\n\n    box-shadow: 0 0 0 1px RGB(var(--button-background));\n\n    transition: 0.25s color, 0.25s box-shadow, 0.25s background;\n\n    &:hover {\n      background: RGB(var(--button-background));\n      color: RGB(var(--button-text-color));\n    }\n\n  }\n\n  &.button--stealth {\n\n    background: RGB(var(--section-block-background));\n    color: RGB(var(--text-color));\n\n    transition: 0.25s color, 0.25s box-shadow, 0.25s background;\n\n    &:hover {\n\n      background: RGB(var(--button-background));\n      color: RGB(var(--button-text-color));\n\n    }\n\n  }\n\n  &.button--tiny {\n\n    // height: 45px;\n\n    padding: 0.25em 0.5em;\n\n    line-height: 24px;\n    padding-left: 24px;\n    padding-right: 24px;\n    min-width: 0;\n\n    font-size: var(---font-size-button--mobile);\n\n    @include respond-to($medium-down) {\n      min-width: unset;\n    }\n\n    @include respond-to($medium-up) {\n      height: 36px;\n      line-height: 36px;\n      font-size: var(---font-size-button--mobile);\n    }\n  }\n\n  &.button--large {\n\n    font-size: var(---font-size-button-large--mobile);\n\n    // min-height: var(--button-height--large);\n    // line-height: 56px;\n    min-height: unset;\n    padding: 0.75em 2em;\n\n    @include respond-to($medium-down) {\n      min-width: unset;\n    }\n\n    @include respond-to($medium-up) {\n      height: 64px;\n    }\n\n    font-size: var(---font-size-button-large--mobile);\n\n    @include respond-to($medium-up) {\n      font-size: var(---font-size-button-large--desktop);\n    }\n\n  }\n\n}", "  .modal {\n\n    --background: var(---background-color--content-1--rgb);\n\n    .modal__close-button {\n      top: 26px;\n      right: 26px;\n    }\n\n    .modal__header {\n      text-align: center;\n      padding-top: 24px;\n    }\n\n    .modal__title {\n      font-size: var(---font-size-button-large--desktop);\n    }\n\n    .modal__content {\n\n      border-radius: 8px;\n\n    }\n\n    .form__actions {\n      margin-top: 2em;\n\n      .button {\n        @include respond-to($small-down) {\n          width: 100%;\n          justify-content: center;\n        }\n      }\n\n    }\n\n  }\n\n  .modal--login {\n\n    .quiz-modal-footer {\n\n      text-align: center;\n      padding-bottom: var(--vertical-breather);\n\n      font-size: var(---font-size-body--desktop);\n\n    }\n\n  }\n\n  .modal--register {\n\n    .modal__content {\n      overflow: visible;\n    }\n\n    .quiz-modal__image {\n      width: 185px;\n      position: absolute;\n      top: 0;\n      left: 0;\n      right: 0;\n      margin: auto;\n\n      transform: translateY(-60%);\n    }\n\n    .newsletter-modal__content {\n      padding-top: 90px !important;\n    }\n\n    .quiz-modal-footer {\n      margin-top: 20px !important;\n    }\n\n    .button--link {\n      transform: translateY(calc(var(--vertical-breather)));\n    }\n\n  }\n\n  .recipe-modal {\n    .newsletter-modal {\n      flex-direction: column;\n    }\n\n    .newsletter-modal__content {\n      padding: var(--container-gutter);\n      text-align: left;\n    }\n\n    .modal__close-button {\n      color: var(---color-text--reversed);\n      transition: transform 0.25s;\n\n      &:hover {\n        transform: rotate(90deg);\n      }\n    }\n  }\n\n  .modal--upsells {\n\n    /* ----- Quiz Results Product ----- */\n\n    .quiz-results-product {\n      width: 300px;\n    }\n\n    .quiz-results-product__header {\n      img {\n        margin: auto;\n      }\n    }\n\n    .quiz-results-product__footer {\n      display: flex;\n      justify-content: center;\n      padding: 0 20px;\n    }\n\n    /* ----- Layout ----- */\n\n    .quiz-modal-content__header {\n      padding: 0 40px;\n      margin: auto;\n      max-width: 450px;\n    }\n\n    .newsletter-modal__content {\n      max-width: unset;\n      padding-top: var(--container-gutter);\n      padding-bottom: var(--container-gutter);\n      background: var(---background-color--content-2);\n    }\n\n    .quiz-results-product__footer-price {\n      display: flex;\n      margin-right: auto;\n\n      .price {\n        font-size: 16px;\n      }\n    }\n\n    .price-list {\n      display: flex;\n      align-items: center;\n    }\n\n    .gallery {\n\n      @include respond-to($medium-down) {\n        margin-left: calc(var(--container-gutter) * -1);\n        margin-right: calc(var(--container-gutter) * -1);\n      }\n\n    }\n\n  }", ".input {\n\n  select,\n  input {\n    font-weight: var(--text-font-bold-weight);\n  }\n}", "/* ----- Form Container ----- */\n\nrevealing-form,\n.revealing-form {\n  display: block;\n}\n\n/* ----- Inputs ----- */\n\nrevealing-form-input,\n.revealing-form-input {\n\n  // display: block;\n  display: none;\n\n  &.revealing-form-input--visible {\n    display: block;\n  }\n\n  &.revealing-form-input--animating {\n    display: block;\n  }\n\n}\n\n/* ----- Actions ----- */\n\nrevealing-form-actions,\n.revealing-form__actions {\n  display: block;\n}", "expanding-input {\n\n  position: relative;\n\n  select {\n    \n    position: absolute;\n    left: 0;\n    right: 0;\n    top: 0;\n    bottom: 0;\n    height: 100%;\n    width: 100%;\n    margin: 0 !important;\n    opacity: 0;\n    \n    &:focus,\n    &:hover {\n      + .expanding-input__display {\n        outline: none;\n        border-color: var(---color--highlight);\n      }\n    }\n\n  }\n\n}\n\n.expanding-input__display {\n\n  padding-left: 0.5em;\n  padding-right: 0.5em;\n\n  cursor: text;\n\n  &:after {\n    transition: color 0.25s;\n  }\n\n  &:empty {\n    color: var(---color--secondary);\n\n    &:after {\n      content: attr(data-default);\n      font-weight: 300;\n    }\n  }\n\n  &:focus {\n    color: var(---color--highlight);\n  }\n\n}\n\n.expanding-input--select {\n\n  cursor: pointer;\n\n  .expanding-input__display {\n\n    padding-right: 50px;\n    background-image: url(\"data:image/svg+xml,%3Csvg width='12' height='6' viewBox='0 0 12 6' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M1 0.5L6 5.5L11 0.5' stroke='%23E6E6E6' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E%0A\");\n    background-size: 15px;\n    background-repeat: no-repeat;\n    background-position: calc(100% - 10px);\n\n    pointer-events: none;\n\n  }\n\n}\n\n.expanding-input__input {\n  &:not(select) {\n    @include visually-hidden();\n  }\n}\n\nselect.expanding-input__input {\n\n  // Windows Select Styling\n  option {\n    background-color: #e6e6e6 !important;\n    text-align: left !important;\n    font-size: var(---font-size-body-large--desktop) !important;\n    color: var(---color-text) !important;\n  }\n\n}", ".box-line-item {\n\n  --box-line-item-padding: 20px;\n\n  .product-item-tag--frozen {\n    background: RGB(var(--root-background));\n  }\n\n}\n\n.box-line-item__inner {\n\n  margin-bottom: 20px;\n  padding: var(--box-line-item-padding) 0;\n\n  background: rgba(var(---color--brand-5--rgb), 0.25);\n  border-radius: 8px;\n\n}\n\n.box-line-item__body {\n\n  display: flex;\n  gap: var(--box-line-item-padding);\n\n  padding: 0 var(--box-line-item-padding);\n\n}\n\n.box-line-item__image {\n  max-width: 92px;\n  width: 100%;\n  object-fit: cover;\n}\n\n.line-item__image-inner {\n  position: relative;\n}\n\n.box-line-item__contents {\n  width: 100%;\n}\n\n.box-line-item__footer {\n  display: flex;\n  justify-content: space-between;\n\n  border-top: 1px solid rgba(var(---color--brand-5--rgb), 1);\n  padding: 0 var(--box-line-item-padding);\n  padding-top: 10px;\n  margin-top: 15px;\n}", ".tile-radio-input {\n  // display: none;\n\n  &:checked {\n    +.tile-radio {\n      background: var(---color--brand-7);\n      border-color: var(---color--brand-7);\n      outline: 2px solid var(---color--brand-2);\n    }\n  }\n}\n\n.tile-radios {\n  display: flex;\n  justify-content: center;\n  gap: 15px;\n  // gap: var(--container-gutter);\n  flex-wrap: wrap;\n\n}\n\n.tile-radio {\n\n  display: flex;\n  align-items: center;\n  gap: 0.75em;\n\n  width: 100%;\n\n  cursor: pointer;\n\n  text-align: center;\n  line-height: 1.1;\n\n  padding: 10px;\n  border: 1px solid var(---color-line--light);\n\n  // border-radius: var(---input-border-radius);\n  border-radius: 8px;\n  box-shadow: 0 0px 0px rgba(0, 0, 0, .1);\n\n  user-select: none;\n\n  transition: transform 0.25s, box-shadow 0.25s;\n\n\n  &:hover {\n    transform: scale(1.05);\n    box-shadow: 0 2px 10px rgba(0,0,0,.1);\n  }\n\n  // @media (min-width: 1000px) {\n  @include respond-to($small-up) {\n    text-align: center;\n    width: auto;\n    flex-direction: column;\n    padding: 20px;\n  }\n\n}\n\n.tile-radio__icon {\n  pointer-events: none;\n  width: 66px;\n  height: 66px;\n}\n\n.tile-radio__content {\n  display: flex;\n  flex-direction: column;\n  gap: 0.25em;\n  text-align: left;\n\n  // @media (min-width: 1000px) {\n  @include respond-to($small-up) {\n    text-align: center;\n  }\n}\n\n.tile-radio__title {\n  line-height: 1.2;\n}\n\n.tile-radio__description {\n  line-height: 1.2;\n}", ".table {\n\n  &.table--auto {\n    table-layout: auto;\n  }\n\n  &.table--1 {\n\n    border-radius: 4px;\n    overflow: hidden;\n\n    tr {\n      &:hover {\n        td {\n          background: var(---color--brand-1);\n        }\n      }\n\n    }\n\n    td {\n      background: var(---color--brand-7);\n    }\n\n    td,\n    th {\n\n      padding: 0.75em 0.25em;\n      text-align: center;\n      vertical-align: middle;\n      line-height: 1.2;\n\n      &:first-child {\n        padding-left: 0.75em;\n        text-align: left;\n        font-weight: var(--text-font-bold-weight);\n      }\n\n      &:last-child {\n        padding-right: 0.75em;\n      }\n\n    }\n\n    thead {\n      th {\n        font-family: var(--heading-font-family);\n        font-weight: var(--body-font-weight);\n        font-size: 0.9em;\n        letter-spacing: 0.05em;\n        background: var(---color--brand-2);\n        color: var(---color-text--reversed);\n      }\n    }\n\n  }\n}", ".shipping-bar {\n\n  --background--unmet: RGBA(235, 87, 87, .3);\n  --progress-background: #D9D9D9;\n  --loading-bar-background: 255, 255, 255;\n\n  position: relative;\n  display: block;\n  margin-top: 15px !important;\n\n  .shipping-bar__progress {\n\n    background: var(--progress-background);\n\n    &:before {\n\n      content: '';\n      display: block;\n      position: absolute;\n      width: 2px;\n      height: 100%;\n      background: currentColor;\n      left: calc(var(--frozen-threshold) * 100%);\n      z-index: 10;\n\n    }\n  }\n\n  &.shipping-bar--frozen-food--unmet {\n    background: var(--background--unmet) !important;\n    .shipping-bar__progress {\n      &:after {\n        background: var(---color--danger) !important;\n      }\n    }\n  }\n\n}\n\n.shipping-bar__icon {\n\n  --icon-size: 44px;\n\n  position: absolute;\n  left: 0;\n  top: 0;\n  display: inline-flex;\n  align-items: center;\n  justify-content: center;\n  width: var(--icon-size);\n  height: var(--icon-size);\n\n  vertical-align: top;\n\n  background: #fff;\n\n  border-radius: 100%;\n  box-shadow: 0 0 5px rgba(0, 0, 0, .1);\n\n  transform: translate(-50%, -30%);\n\n}\n\n.shipping-bar__text {\n  line-height: 1.4;\n  margin-bottom: 0.5em;\n  display: inline-block;\n}", ".vet-sticky-bar {\n  position: fixed;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  z-index: 9;\n\n  box-shadow: var(---shadow--modal);\n\n  transform: translateY(100%);\n  transition: transform 500ms;\n\n  // animation: vet-sticky-bar--tease 2s;\n\n  &[open] {\n    display: block;\n    transform: translateY(0);\n  }\n\n  &:hover {\n    &:not([open]) {\n      transform: translateY(var(--tease-tug-1));\n    }\n  }\n\n  .cart-vet-text {\n    @include respond-to($small-down) {\n      // display: flex;\n      // justify-content: center;\n      display: block;\n      width: 100%;\n    }\n  }\n\n  .cart-vet-partner {\n    display: flex;\n  }\n\n  // Selector Form\n\n  .vet-partner-selector-wrapper {\n    @include respond-to($medium-up) {\n      flex: 1 0 250px;\n      max-width: 250px;\n    }\n  }\n  \n  .cart-vet-partner__vet {\n    justify-content: flex-end;\n    padding: 0;\n    border: 0;\n  }\n\n  .cart-vet-partner__selector-form {\n\n    @include respond-to($medium-up) {\n      display: flex;\n      justify-content: end;\n    }\n\n  }\n\n  .cart-vet-partner__selector-button {\n    flex: 0 2 max-content;\n  }\n\n  // Vet Text\n  \n  .cart-vet-partner__vet-text {\n    padding: 15px 30px;\n    font-size: var(---font-size-h6--desktop);\n    background: var(---background-color--secondary);\n    border-radius: 10px;\n\n    @include respond-to($small-down) {\n      display: block;\n      width: 100%;\n    }\n  }\n\n  .cart-vet-partner__product-notice {\n    display: none;\n  }\n\n}\n\n.vet-sticky-bar__tag {\n\n  --tab-background: var(---color--highlight--rgb);\n  --tab-color: var(---color--default--rgb);\n\n  --edge-width: 30px;\n  --edge-corner-radius: 8px;\n\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  transform: translateY(-100%);\n\n  display: flex;\n  align-items: center;\n  justify-content: center;\n\n  width: max-content;\n  padding: 0.5em;\n  margin: auto;\n\n  font-weight: var(--bold-font-weight);\n\n  text-align: center;\n  background: RGB(var(--tab-background));\n  color: RGB(var(--tab-color));\n\n  transition: background 250ms;\n\n  &:hover {\n    // --tab-color: var(---color--highlight--dark--rgb);\n    --tab-background: var(---color--highlight--dark--rgb);\n  }\n\n  &:before,\n  &:after {\n\n    content: '';\n    display: block;\n\n    position: absolute;\n    top: 0;\n    z-index: -1;\n\n    background: RGB(var(--tab-background));\n    transition: background 250ms;\n    width: var(--edge-width);\n    height: 100%;\n\n  }\n\n  &:before {\n    left: -10px;\n    transform: skew(-10deg);\n    border-top-left-radius: var(--edge-corner-radius);\n  }\n\n  &:after {\n    right: -10px;\n    transform: skew(10deg);\n    border-top-right-radius: var(--edge-corner-radius);\n  }\n\n}\n\n\n.vet-sticky-bar__wrapper {\n\n  padding: 20px;\n\n  background-color: RGB(var(--section-background));\n\n  .container {\n    @include respond-to($small-down) {\n      padding: 0;\n    }\n  }\n\n}\n\n.cart-vet-partner__vet {\n\n  @include respond-to($small-down) {\n    padding-top: 10px;\n    border-top: 1px solid var(---color-line--light);\n  }\n\n}\n\n.vet-sticky-bar__inner {\n  display: flex;\n  flex-direction: column;\n\n  @include respond-to($medium-up) {\n    flex-direction: row;\n    gap: 20px;\n  }\n}\n\n.vet-sticky-bar__info {\n  display: flex;\n  gap: 10px;\n}\n\n.vet-sticky-bar__actions {\n  display: flex;\n  justify-content: flex-end;\n}\n\n.vet-sticky-bar__text {\n  .subheading {\n    margin: 0;\n  }\n}\n\n@keyframes vet-sticky-bar--tease {\n\n  0% {\n    transform: translateY(var(--tease-start));\n  }\n\n  15% {\n    transform: translateY(var(--tease-tug-1));\n  }\n\n  30% {\n    transform: translateY(var(--tease-start));\n  }\n\n  60% {\n    transform: translateY(var(--tease-start));\n  }\n\n  80% {\n    transform: translateY(var(--tease-tug-2));\n  }\n\n  100% {\n    transform: translateY(var(--tease-start));\n  }\n\n}\n\n:root {\n  @include respond-to($small-down) {\n    --tease-start: 100%;\n    --tease-tug-1: 95%;\n    --tease-tug-2: 85%;\n  }\n\n  @include respond-to($small-up) {\n    --tease-start: 100%;\n    --tease-tug-1: 90%;\n    --tease-tug-2: 70%;\n  }\n}", "/*  --------------------------------------------------\n    Cart\n    -------------------------------------------------- */\n\n.cart-vet-partner {\n\n  --vertical-spacing: 12px;\n  --spacing: 12px;\n\n  display: flex;\n  flex-direction: column;\n  gap: var(--vertical-spacing);\n\n  transition: opacity 0.25s;\n\n  &[loading] {\n    pointer-events: none;\n    opacity: 0.25;\n  }\n\n  .unlisted-vet-container {\n    background: var(---background-color--secondary);\n    border-radius: 4px;\n    padding: .5em 1em;\n    text-align: center;\n    -webkit-user-select: none;\n    -moz-user-select: none;\n    user-select: none;\n  }\n\n}\n\n.modal--vet-not-listed {\n  .modal__overlay {\n    pointer-events: none;\n  }\n}\n\n.cart-vet-partner__inner {\n\n  display: flex;\n  flex-direction: column;\n  gap: 12px;\n\n}\n\n.cart-vet-partner__selector-form {\n  display: grid;\n  grid-auto-flow: row;\n  gap: var(--spacing);\n  // grid-auto-flow: column;\n\n  @include respond-to($medium-down) {\n    grid-auto-flow: row;\n  }\n\n  select {\n    @include respond-to($medium-up) {\n      height: 100%;\n    }\n  }\n}\n\n.cart-vet-partner__vet {\n\n  display: flex;\n  align-items: center;\n  justify-content: center;\n\n  text-align: center;\n\n}\n\n.cart-vet-partner__product-notice {\n\n  text-align: center;\n\n}\n\n.cart-vet-partner__notice {\n  display: flex;\n  padding: 16px;\n  margin-top: var(--spacing);\n  gap: var(--spacing);\n  border: 2px solid var(---color--default);\n  border-radius: var(--block-border-radius);\n}\n\n.cart-vet-partner__notice-text {\n  .subheading {\n    margin-bottom: 0.25em;\n  }\n}\n\n.cart-vet-text {\n  line-height: 1;\n}\n\n\n\n/*  --------------------------------------------------\n    Account Page\n    -------------------------------------------------- */\n\n.account-vet-partner {\n\n  max-width: 800px;\n  padding: 0 0;\n  margin: 0 auto;\n\n  .account-vet-partner__inner {\n    margin-top: 20px;\n    padding: 20px;\n    background: var(---background-color--secondary);\n    border-radius: var(--block-border-radius);\n\n    @include respond-to($medium-up) {\n      padding: 30px;\n    }\n  }\n\n  // Overrides\n\n  .cart-vet-partner__selector {\n    padding-bottom: 20px;\n    margin-bottom: 20px;\n    border-bottom: 1px solid var(---color-line--light);\n  }\n\n  .cart-vet-partner__vet {\n    padding: 0;\n    border: 0;\n  }\n\n  .cart-vet-partner__vet-text {\n    font-size: var(--base-font-size);\n    padding: 15px 30px;\n    border-radius: 10px;\n  }\n\n  .cart-vet-partner__notice {\n    background: var(---background-color--content-1);\n  }\n\n  .cart-vet-partner__product-notice {\n    display: none;\n  }\n\n  .cart-vet-partner__selector-form {\n    .select {\n      background: var(---background-color--content-1);\n    }\n  }\n\n  .cart-vet-notice {\n    display: block !important;\n    height: auto;\n    visibility: visible;\n\n    button {\n      display: none;\n    }\n  }\n\n  .cart-vet-partner__notice {\n    padding: 20px;\n\n    @media (min-width: 1000px) {\n      padding: 30px;\n    }\n  }\n\n  .cart-vet-text {\n    display: flex;\n    justify-content: center;\n  }\n\n  .cart-vet-partner {\n    display: flex;\n    gap: 0;\n  }\n\n}", "split-page-step {\n\n  display: none;\n\n  &.split-page-step--visible {\n    display: block;\n  }\n\n  .input--select {\n    > select {\n      width: 100%;\n    }\n  }\n\n}", ".banner {\n\n  border-radius: var(--block-border-radius);\n\n  &.banner--success {\n    --text-color: var(---color-text--reversed--rgb);\n    color: RGB(var(--text-color));\n    background-color: rgb(var(--success-color));\n  }\n\n  &.banner--error {\n    --text-color: var(---color-text--reversed--rgb);\n    color: RGB(var(--text-color));\n    background-color: rgb(var(--error-color));\n  }\n\n}", ".weight-range {\n\n  --track-height: 14px;\n  --thumb-size: 40px;\n  --thumb-icon-size: 80px;\n\n  display: block;\n  margin-top: var(--thumb-icon-size);\n\n  position: relative;\n\n}\n\n.weight-range__inner {\n\n  position: relative; //\n\n  line-height: 0;\n  height: var(--track-height);\n  border-radius: 100px;\n  background: RGB(var(---color--brand-2--rgb));\n  outline: 2px solid RGB(var(--text-color));\n\n}\n\n.weight-range__thumb {\n\n  pointer-events: none;\n\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 10px;\n\n  position: absolute;\n  left: var(--range-position);\n\n  transform:\n    translateX(calc(-1 * var(--thumb-icon-size) / 2)) translateY(50%);\n\n\n  // transform: translateX(calc(-1 * var(--thumb-icon-size) / 2 + calc(var(--offset) / 2)));\n\n  bottom: 0;\n  bottom: 50%;\n  //  bottom: calc(var());\n  z-index: 1;\n\n  //   display: none;\n\n}\n\n\n\n.weight-range__range {\n  height: var(--track-height);\n}\n\n@mixin thumb-styling() {\n\n  height: var(--thumb-icon-size);\n  width: var(--thumb-icon-size);\n\n  transform:\n    translateY(calc(calc(-1 * var(--thumb-size) / 2) - calc(-1 * var(--track-height) / 2)))\n    translateX(calc(100 * var(--range-offset)));\n\n  opacity: 0;\n\n}\n\n.weight-range__range::range-thumb { @include thumb-styling; };\n.weight-range__range::slider-thumb { @include thumb-styling; };\n.weight-range__range::-moz-range-thumb { @include thumb-styling; };\n.weight-range__range::-webkit-slider-thumb { @include thumb-styling; };\n\n.weight-range__range::range-track { opacity: 0; }\n.weight-range__range::slider-track { opacity: 0; }\n.weight-range__range::-moz-range-track { opacity: 0; }\n.weight-range__range::-webkit-slider-runnable-track { opacity: 0; }\n\n.weight-range__track {\n\n  position: absolute;\n  left: 0;\n  top: 0;\n  height: var(--track-height);\n  width: var(--range-position);\n\n  background: RGB(var(---color--brand-1--rgb));\n  border-radius: 100px;\n\n  pointer-events: none;\n\n}\n\n.weight-range__thumb-icon {\n\n  height: var(--thumb-icon-size);\n  width: var(--thumb-icon-size);\n\n  background-image: url(https://cdn.shopify.com/s/files/1/1683/1605/files/pup-thumb.png?v=1719171809);\n  background-size: contain;\n  background-position: center;\n  background-repeat: no-repeat;\n\n  //  transform: translateX(-50%);\n\n}\n\n.weight-range__thumb-value {\n\n  display: inline-flex;\n  // display: none;\n  padding: 0em 0.5em;\n  border-radius: 8px;\n\n  background-color: RGB(var(---color--brand-1--rgb));\n  outline: 2px solid RGB(var(--text-color));\n\n  position: absolute;\n  top: 0px;\n  transform: translateY(-100%);\n\n}\n\n.weight-range__labels {\n\n  display: flex;\n  justify-content: space-between;\n  margin-top: 0.25em;\n  pointer-events: none;\n  user-select: none;\n\n}", ".popover,\n.mobile-toolbar {\n  background: var(---background-color--content-1) !important;\n}\n\n.nav-dropdown {\n  z-index: 1;\n  background: RGB(var(--background));\n}\n\n.button {\n  line-height: 1.2;\n}\n\n.drawer {\n}\n\n.button-wrapper {\n\n  \n  &.button-wrapper--vertical {\n    display: flex;\n    align-items: center;\n    gap: 0.5em;\n    flex-direction: column;\n  }\n\n}\n\n\nhr, .hr {\n\n  width: 100%;\n  margin: 2em auto;\n  border-width: 0.5px;\n  border-bottom: 0;\n  border-style: solid;\n  border-color: var(---color-line);\n\n  &.hr--light {\n    border-color: var(---color-line--light);\n  }\n\n  &.hr--dark {\n    border-color: var(---color-line--dark);\n  }\n\n  &.hr--clear {\n    border-color: transparent;\n  }\n\n  &.hr--small {\n    margin: 1em 0;\n  }\n\n  &.hr--xsmall {\n    margin: 0.5em 0;\n  }\n\n  &.hr--narrow {\n    max-width: 70px;\n    margin-left: auto !important;\n    margin-right: auto !important;\n  }\n\n}\n\n\n[data-tooltip] {\n  // position: relative;\n}\n\n[data-tooltip]:before {\n  font-size: var(---font-size-body-xs) !important;\n}\n\n.account-link-current {\n  color: var(---color--highlight);\n}", ".product-form__buy-buttons {\n\n  .awt-style-1 {\n\n    .bundleapp-wrapper {\n  \n      margin: 0 !important;\n  \n      .bundleapp-plan-selector-group {\n  \n        border-radius: 12px;\n        border: 0;\n  \n        // outline: 2px solid var(---color--primary);\n        border: 2px solid RGB(var(--border-color));\n  \n        transition: 0.25s border, 0.25s background;\n  \n        >label {\n          font-weight: 800;\n          color: var(--text-color);\n        }\n  \n        &.bundleapp-plan-selector-group--selected {\n          background: var(---color--highlight);\n        }\n      }\n  \n      .bundleapp-plan-selector-description {\n  \n        line-height: 1.2;\n  \n        span {\n          background: var(---background-color--content-1);\n          font-weight: 400;\n          padding: 20px;\n          border: 0;\n        }\n      }\n  \n      .bundleapp-plan-selector-plan {\n        margin: 0;\n        padding: 0.5em;\n  \n        label {\n          font-size: 0.9em;\n        }\n      }\n  \n      .bundleapp-plan-selector-select {\n        padding: 0.4em 0.8em !important;\n        margin: 0 !important;\n        border-radius: 12px;\n        border: 2px solid RGB(var(--border-color)) !important;\n      }\n  \n    }\n    \n  }\n\n\n}", "/* ---------- <PERSON><PERSON><PERSON> Text ---------- */\n\n.section {\n  .marquee-horizontal {\n    z-index: 3;\n  }\n}\n", "// Breakpoint Helpers\n\n/*================ Build Base Grid Classes ================*/\n\n@include responsive-display-helper();\n@include responsive-text-align-helper();\n\n/*================ Build Responsive Grid Classes ================*/\n\n@each $breakpoint in $breakpoint-has-widths {\n\n  @include respond-to($breakpoint) {\n\n    @include responsive-display-helper('#{$breakpoint}--');\n    @include responsive-text-align-helper('#{$breakpoint}--');\n\n    .br--#{$breakpoint} {\n      display: block;\n    }\n\n  }\n\n}\n\n.clearfix {\n  @include clearfix();\n}\n\n.fallback-text,\n.visually-hidden {\n  @include visually-hidden();\n}\n\n.hidden {\n  display: none;\n}\n\n.flex {\n  display: flex;\n}\n\n.inline-flex {\n  display: flex;\n}\n\n.align-start {\n  align-items: flex-start;\n}\n\n.align-center {\n  align-items: center;\n}\n\n.align-end {\n  align-items: flex-end;\n}\n\n.justify-start {\n  justify-items: flex-start;\n}\n\n.justify-center {\n  justify-items: center;\n}\n\n.justify-end {\n  justify-items: flex-end;\n}\n\n.gap-05 {\n  gap: 0.5em;\n}\n\n.uppercase,\n.text-transform--uppercase {\n  text-transform: uppercase !important;\n}\n\n.text-transform--none {\n  text-transform: none !important;\n}\n\n.strikethrough {\n  text-decoration: line-through;\n}\n\n// Colors\n\n@each $color in $semiotics {\n  .background-color--#{$color} {\n    background: var(---color--#{$color});\n  }\n}\n\n@each $color in $semiotics {\n  .color--#{$color} {\n    color: var(---color--#{$color});\n  }\n}\n\n\n.justify-content-center {\n  justify-content: center !important;\n}\n\n.object-position--top {\n  object-position: top !important;\n}\n\n.object-position--bottom {\n  object-position: bottom !important;\n}\n\n.object-position--center {\n  object-position: center !important;\n}\n\n.object-position--left {\n  object-position: left !important;\n}\n\n.object-position--right {\n  object-position: right !important;\n}\n\n// Text\n\n.text-align--center {\n  text-align: center !important;\n}\n\n.text-align--left {\n  text-align: left !important;\n}\n\n.text-align--right {\n  text-align: right !important;\n}\n\n.text-align--center--mobile {\n  @include respond-to($small-down) {\n    text-align: center !important;\n  }\n}\n\n.text-align--left--mobile {\n  @include respond-to($small-down) {\n    text-align: left !important;\n  }\n}\n\n.text-align--right--mobile {\n  @include respond-to($small-down) {\n    text-align: right !important;\n  }\n}\n\n.nowrap {\n  white-space: nowrap;\n}\n\n// Layout\n\n.no-margin {\n  margin: 0 !important;\n}\n\n.no-margin--top {\n  margin-top: 0 !important;\n}\n\n.no-margin--right {\n  margin-right: 0 !important;\n}\n\n.no-margin--left {\n  margin-left: 0 !important;\n}\n\n.no-margin--bottom {\n  margin-bottom: 0 !important;\n}\n\n.no-padding {\n  padding: 0 !important;\n}\n\n.no-padding--top {\n  padding-top: 0 !important;\n}\n\n.no-padding--right {\n  padding-right: 0 !important;\n}\n\n.no-padding--left {\n  padding-left: 0 !important;\n}\n\n.no-padding--bottom {\n  padding-bottom: 0 !important;\n}\n\n.padding-left--10 {\n  padding-left: 10px !important;\n}\n\n.padding-left--20 {\n  padding-left: 20px !important;\n}\n\n.padding-left--30 {\n  padding-left: 30px !important;\n}\n\n.padding-left--40 {\n  padding-left: 40px !important;\n}\n\n.padding-left--50 {\n  padding-left: 50px !important;\n}\n\n.padding-right--10 {\n  padding-right: 10px !important;\n}\n\n.padding-right--20 {\n  padding-right: 20px !important;\n}\n\n.padding-right--30 {\n  padding-right: 30px !important;\n}\n\n.padding-right--40 {\n  padding-right: 40px !important;\n}\n\n.padding-right--50 {\n  padding-right: 50px !important;\n}\n\n.padding-top--10 {\n  padding-top: 10px !important;\n}\n\n.padding-top--20 {\n  padding-top: 20px !important;\n}\n\n.padding-top--30 {\n  padding-top: 30px !important;\n}\n\n.padding-top--40 {\n  padding-top: 40px !important;\n}\n\n.padding-top--50 {\n  padding-top: 50px !important;\n}\n\n.padding-bottom--10 {\n  padding-bottom: 10px !important;\n}\n\n.padding-bottom--20 {\n  padding-bottom: 20px !important;\n}\n\n.padding-bottom--30 {\n  padding-bottom: 30px !important;\n}\n\n.padding-bottom--40 {\n  padding-bottom: 40px !important;\n}\n\n.padding-bottom--50 {\n  padding-bottom: 50px !important;\n}\n\nbody.logged-in {\n  .logged-in--hidden {\n    display: none !important;\n  }\n}\n\nbody.logged-out {\n  .logged-out--hidden {\n    display: none !important;\n  }\n}\n\n.fraction {\n  margin-left: 0.25em;\n  font-size: 0.75em;\n  letter-spacing: -0.1em;\n}", "@charset \"UTF-8\";\n/* 1. Variables */ /*\n$site-width: 1600px;\n$container-width: 1200px;\n$container-narrow-width: 800px;\n$container-extra-narrow-width: 600px;\n\n$container-gutter--desktop: 24px;\n$container-gutter--mobile: 24px;\n\n$section-spacer--desktop: 50px;\n$section-spacer--mobile: 25px;\n*/\n/*  ------------------------------\n    Grid Variables\n    ------------------------------ */\n/**\n * Define your breakpoints. The first value is the prefix that shall be used for\n * your classes (e.g. `.small--one-half`), the second value is the media query\n * that the breakpoint fires at.\n */\n/**\n * Define which namespaced breakpoints you would like to generate for each of\n * widths, push and pull. This is handy if you only need pull on, say, desk, or\n * you only need a new width breakpoint at mobile sizes. It allows you to only\n * compile as much CSS as you need. All are turned on by default, but you can\n * add and remove breakpoints at will.\n *\n * Push and pull shall only be used if `$push` and/or `$pull` and `$responsive`\n * have been set to ‘true’.\n */\n/* 2. Mixins */\n/*\n\n  1. Utilities\n  2. Responsive\n  3. UI Elements\n    3.1. Buttons\n\n*/\n/*  ==============================\n    1. Utilities\n    ============================== */\n/*  ==============================\n    2. Responsive\n    ============================== */\n/*================ Responsive Show/Hide Helper ================*/\n/*================ Responsive Text Alignment Helper ================*/\n/*  ==============================\n    3. UI Elements\n    ============================== */\n/*  ------------------------------\n    3.1. Buttons\n    ------------------------------ */\n/* ------------------------------\n   Headings\n   ------------------------------ */\n/* ------------------------------\n   Labels\n   ------------------------------ */\n/* ------------------------------\n   Inputs\n   ------------------------------ */\n/* ------------------------------\n   RTE\n   ------------------------------ */\n/*  ------------------------------\n    3.3. Shopify\n    ------------------------------ */\n/* 3. Fonts  */\n/* 4. Basic Styles */\n/*  -----------------------------------\n    Links\n    ----------------------------------- */\n.link--animated.link--animated--spaced:after {\n  bottom: -0.25em;\n}\n.link--animated.link--animated--bold {\n  font-weight: var(---font-weight-body--bold);\n}\n.link--animated.link--animated--bold:after {\n  height: 2px;\n}\n.link--animated.link--animated--show-underline:after {\n  transform: scaleX(1);\n}\n.link--animated.link--animated--show-underline:hover:after, .link--animated.link--animated--show-underline:focus:after {\n  transform: scaleX(0);\n}\n\nh1, .h1,\nh2, .h2, .jdgm-rev-widg__title, .jdgm-carousel-title,\nh3, .h3,\nh4, .h4 {\n  line-height: var(---line-height-heading--mobile);\n  letter-spacing: var(---letter-spacing-heading--mobile);\n}\n\nh1, .h1,\nh2, .h2, .jdgm-rev-widg__title, .jdgm-carousel-title,\nh3, .h3,\nh4, .h4 {\n  font-family: var(--heading-font-family);\n  font-weight: var(--heading-font-weight);\n  font-style: var(--heading-font-style);\n  color: rgb(var(--heading-color));\n  text-transform: var(--heading-text-transform);\n  display: block;\n  letter-spacing: var(---letter-spacing-heading--mobile);\n  font-weight: var(---font-weight-heading);\n}\n\n.heading--large,\n.rte .heading--large {\n  font-family: var(---font-family-heading);\n  font-style: var(---font-style-heading);\n  font-weight: var(---font-weight-heading);\n  font-variation-settings: \"wght\" 400;\n  letter-spacing: var(---letter-spacing-heading--mobile);\n  line-height: var(---line-height-heading--mobile);\n  font-size: var(---font-size-h0--mobile);\n}\n.heading--large span.heading--alternate,\n.rte .heading--large span.heading--alternate {\n  color: var(---color-heading-1);\n  text-transform: none;\n  font-weight: var(---font-weight-heading);\n}\n.heading--large a,\n.rte .heading--large a {\n  color: var(---color-link);\n}\n@media only screen and (min-width: 741px) {\n  .heading--large,\n  .rte .heading--large {\n    letter-spacing: var(---letter-spacing-heading--desktop);\n    line-height: var(---line-height-heading--desktop);\n  }\n}\n@media only screen and (min-width: 741px) {\n  .heading--large,\n  .rte .heading--large {\n    font-size: var(---font-size-h0--desktop);\n  }\n}\n\nh1, .h1,\n.rte h1, .rte .h1 {\n  font-family: var(---font-family-heading);\n  font-style: var(---font-style-heading);\n  font-weight: var(---font-weight-heading);\n  font-variation-settings: \"wght\" 400;\n  letter-spacing: var(---letter-spacing-heading--mobile);\n  line-height: var(---line-height-heading--mobile);\n  font-size: var(---font-size-h1--mobile);\n}\nh1 span.heading--alternate, .h1 span.heading--alternate,\n.rte h1 span.heading--alternate, .rte .h1 span.heading--alternate {\n  color: var(---color-heading-1);\n  text-transform: none;\n  font-weight: var(---font-weight-heading);\n}\nh1 a, .h1 a,\n.rte h1 a, .rte .h1 a {\n  color: var(---color-link);\n}\n@media only screen and (min-width: 741px) {\n  h1, .h1,\n  .rte h1, .rte .h1 {\n    letter-spacing: var(---letter-spacing-heading--desktop);\n    line-height: var(---line-height-heading--desktop);\n  }\n}\n@media only screen and (min-width: 741px) {\n  h1, .h1,\n  .rte h1, .rte .h1 {\n    font-size: var(---font-size-h1--desktop);\n  }\n}\n\nh2, .h2, .jdgm-rev-widg__title, .jdgm-carousel-title,\n.rte h2, .rte .h2 {\n  font-family: var(---font-family-heading);\n  font-style: var(---font-style-heading);\n  font-weight: var(---font-weight-heading);\n  font-variation-settings: \"wght\" 400;\n  letter-spacing: var(---letter-spacing-heading--mobile);\n  line-height: var(---line-height-heading--mobile);\n  font-size: var(---font-size-h2--mobile);\n}\nh2 span.heading--alternate, .h2 span.heading--alternate, .jdgm-rev-widg__title span.heading--alternate, .jdgm-carousel-title span.heading--alternate,\n.rte h2 span.heading--alternate, .rte .h2 span.heading--alternate {\n  color: var(---color-heading-1);\n  text-transform: none;\n  font-weight: var(---font-weight-heading);\n}\nh2 a, .h2 a, .jdgm-rev-widg__title a, .jdgm-carousel-title a,\n.rte h2 a, .rte .h2 a {\n  color: var(---color-link);\n}\n@media only screen and (min-width: 741px) {\n  h2, .h2, .jdgm-rev-widg__title, .jdgm-carousel-title,\n  .rte h2, .rte .h2 {\n    letter-spacing: var(---letter-spacing-heading--desktop);\n    line-height: var(---line-height-heading--desktop);\n  }\n}\n@media only screen and (min-width: 741px) {\n  h2, .h2, .jdgm-rev-widg__title, .jdgm-carousel-title,\n  .rte h2, .rte .h2 {\n    font-size: var(---font-size-h2--desktop);\n  }\n}\n\nh3, .h3,\n.rte h3, .rte .h3 {\n  font-family: var(---font-family-heading);\n  font-style: var(---font-style-heading);\n  font-weight: var(---font-weight-heading);\n  font-variation-settings: \"wght\" 400;\n  letter-spacing: var(---letter-spacing-heading--mobile);\n  line-height: var(---line-height-heading--mobile);\n  font-size: var(---font-size-h3--mobile);\n}\nh3 span.heading--alternate, .h3 span.heading--alternate,\n.rte h3 span.heading--alternate, .rte .h3 span.heading--alternate {\n  color: var(---color-heading-1);\n  text-transform: none;\n  font-weight: var(---font-weight-heading);\n}\nh3 a, .h3 a,\n.rte h3 a, .rte .h3 a {\n  color: var(---color-link);\n}\n@media only screen and (min-width: 741px) {\n  h3, .h3,\n  .rte h3, .rte .h3 {\n    letter-spacing: var(---letter-spacing-heading--desktop);\n    line-height: var(---line-height-heading--desktop);\n  }\n}\n@media only screen and (min-width: 741px) {\n  h3, .h3,\n  .rte h3, .rte .h3 {\n    font-size: var(---font-size-h3--desktop);\n  }\n}\n\nh4, .h4,\n.rte h4, .rte .h4 {\n  font-family: var(---font-family-heading);\n  font-style: var(---font-style-heading);\n  font-weight: var(---font-weight-heading);\n  font-variation-settings: \"wght\" 400;\n  letter-spacing: var(---letter-spacing-heading--mobile);\n  line-height: var(---line-height-heading--mobile);\n  font-size: var(---font-size-h4--mobile);\n}\nh4 span.heading--alternate, .h4 span.heading--alternate,\n.rte h4 span.heading--alternate, .rte .h4 span.heading--alternate {\n  color: var(---color-heading-1);\n  text-transform: none;\n  font-weight: var(---font-weight-heading);\n}\nh4 a, .h4 a,\n.rte h4 a, .rte .h4 a {\n  color: var(---color-link);\n}\n@media only screen and (min-width: 741px) {\n  h4, .h4,\n  .rte h4, .rte .h4 {\n    letter-spacing: var(---letter-spacing-heading--desktop);\n    line-height: var(---line-height-heading--desktop);\n  }\n}\n@media only screen and (min-width: 741px) {\n  h4, .h4,\n  .rte h4, .rte .h4 {\n    font-size: var(---font-size-h4--desktop);\n  }\n}\n\nh5, .h5,\n.rte h5, .rte .h5 {\n  font-family: var(---font-family-body);\n  font-style: var(---font-style-body);\n  font-weight: var(---font-weight-body--bold);\n  letter-spacing: var(---letter-spacing-body--mobile);\n  line-height: var(---line-height-body--mobile);\n  font-size: var(---font-size-h5--mobile);\n}\nh5 span.heading--alternate, .h5 span.heading--alternate,\n.rte h5 span.heading--alternate, .rte .h5 span.heading--alternate {\n  color: var(---color-heading-2);\n  text-transform: none;\n  font-weight: var(---font-weight-body);\n}\nh5 a, .h5 a,\n.rte h5 a, .rte .h5 a {\n  color: var(---color-link);\n}\n@media only screen and (min-width: 741px) {\n  h5, .h5,\n  .rte h5, .rte .h5 {\n    letter-spacing: var(---letter-spacing-heading--desktop);\n    line-height: var(---line-height-heading--desktop);\n  }\n}\n@media only screen and (min-width: 741px) {\n  h5, .h5,\n  .rte h5, .rte .h5 {\n    font-size: var(---font-size-h5--desktop);\n  }\n}\n\nh6, .h6,\n.rte h6, .rte .h6 {\n  font-family: var(---font-family-body);\n  font-style: var(---font-style-body);\n  font-weight: var(---font-weight-body--bold);\n  letter-spacing: var(---letter-spacing-body--mobile);\n  line-height: var(---line-height-body--mobile);\n  font-size: var(---font-size-h6--mobile);\n}\nh6 span.heading--alternate, .h6 span.heading--alternate,\n.rte h6 span.heading--alternate, .rte .h6 span.heading--alternate {\n  color: var(---color-heading-2);\n  text-transform: none;\n  font-weight: var(---font-weight-body);\n}\nh6 a, .h6 a,\n.rte h6 a, .rte .h6 a {\n  color: var(---color-link);\n}\n@media only screen and (min-width: 741px) {\n  h6, .h6,\n  .rte h6, .rte .h6 {\n    letter-spacing: var(---letter-spacing-heading--desktop);\n    line-height: var(---line-height-heading--desktop);\n  }\n}\n@media only screen and (min-width: 741px) {\n  h6, .h6,\n  .rte h6, .rte .h6 {\n    font-size: var(---font-size-h6--desktop);\n  }\n}\n\n.subheading {\n  color: RGB(var(--subheading-color));\n}\n\n.heading.heading--regular {\n  letter-spacing: var(---letter-spacing-subheading--mobile);\n  text-transform: uppercase;\n  font-style: var(---font-style-heading);\n  font-weight: var(---font-weight-heading);\n  font-family: var(---font-family-heading);\n  font-size: var(---font-size-subheading-large--mobile);\n}\n@media only screen and (min-width: 741px) {\n  .heading.heading--regular {\n    font-size: var(---font-size-subheading-large--desktop);\n  }\n}\n\n.product-sticky-form__title,\n.heading.heading--small {\n  letter-spacing: var(---letter-spacing-subheading--mobile);\n  text-transform: uppercase;\n  font-style: var(---font-style-heading);\n  font-weight: var(---font-weight-heading);\n  font-family: var(---font-family-heading);\n  font-size: var(---font-size-subheading--mobile);\n}\n.product-sticky-form__title + p,\n.product-sticky-form__title + .h1,\n.product-sticky-form__title + h1,\n.product-sticky-form__title + .h2,\n.product-sticky-form__title + .jdgm-rev-widg__title,\n.product-sticky-form__title + .jdgm-carousel-title,\n.product-sticky-form__title + h2,\n.product-sticky-form__title + .h3,\n.product-sticky-form__title + h3,\n.product-sticky-form__title + .h4,\n.product-sticky-form__title + h4,\n.heading.heading--small + p,\n.heading.heading--small + .h1,\n.heading.heading--small + h1,\n.heading.heading--small + .h2,\n.heading.heading--small + .jdgm-rev-widg__title,\n.heading.heading--small + .jdgm-carousel-title,\n.heading.heading--small + h2,\n.heading.heading--small + .h3,\n.heading.heading--small + h3,\n.heading.heading--small + .h4,\n.heading.heading--small + h4 {\n  margin-top: 12px;\n}\n.product-sticky-form__title + hr,\n.heading.heading--small + hr {\n  margin-top: 0;\n}\n@media only screen and (min-width: 741px) {\n  .product-sticky-form__title,\n  .heading.heading--small {\n    font-size: var(---font-size-subheading--desktop);\n  }\n}\n\n.heading.heading--xsmall {\n  letter-spacing: var(---letter-spacing-subheading--mobile);\n  text-transform: uppercase;\n  font-style: var(---font-style-heading);\n  font-weight: var(---font-weight-heading);\n  font-family: var(---font-family-heading);\n  font-size: var(---font-size-subheading-small--mobile);\n}\n@media only screen and (min-width: 741px) {\n  .heading.heading--xsmall {\n    font-size: var(---font-size-subheading-small--desktop);\n  }\n}\n\nbody {\n  line-height: var(---line-height-body--mobile);\n}\n\n.text--small {\n  margin-top: 0;\n  font-size: var(---font-size-body--mobile);\n}\n@media only screen and (min-width: 741px) {\n  .text--small {\n    font-size: var(---font-size-body--desktop);\n  }\n}\n\n.text--xxsmall,\n.tiny {\n  font-size: var(---font-size-body-xs--mobile);\n}\n@media only screen and (min-width: 741px) {\n  .text--xxsmall,\n  .tiny {\n    font-size: var(---font-size-body-xs--desktop);\n  }\n}\n\n.text--xsmall,\n.minor {\n  font-size: var(---font-size-body-small--mobile);\n}\n@media only screen and (min-width: 741px) {\n  .text--xsmall,\n  .minor {\n    font-size: var(---font-size-body-small--desktop);\n  }\n}\n\n.text--large,\n.major {\n  font-size: var(---font-size-body-large--mobile);\n}\n@media only screen and (min-width: 741px) {\n  .text--large,\n  .major {\n    font-size: var(---font-size-body-large--desktop);\n  }\n}\n\n@media only screen and (max-width: 740px) {\n  .p--mobile {\n    font-size: var(---font-size-body--mobile);\n    font-family: var(---font-family-body);\n    font-weight: var(---font-weight-body);\n  }\n}\n\n.text--xxsmall,\n.tiny {\n  font-size: var(---font-size-body-xs--mobile);\n}\n@media only screen and (min-width: 741px) {\n  .text--xxsmall,\n  .tiny {\n    font-size: var(---font-size-body-xs--desktop);\n  }\n}\n.text--xxsmall p,\n.tiny p {\n  font-size: var(---font-size-body-xs--mobile);\n}\n@media only screen and (min-width: 741px) {\n  .text--xxsmall p,\n  .tiny p {\n    font-size: var(---font-size-body-xs--desktop);\n  }\n}\n\n@media only screen and (max-width: 740px) {\n  .text--xxsmall--mobile,\n  .p-tiny--mobile {\n    font-family: var(---font-family-body);\n    font-weight: var(---font-weight-body);\n    font-size: var(---font-size-body-xs--mobile);\n  }\n}\n\n.text--xsmall,\n.minor {\n  font-size: var(---font-size-body-small--mobile);\n}\n@media only screen and (min-width: 741px) {\n  .text--xsmall,\n  .minor {\n    font-size: var(---font-size-body-small--desktop);\n  }\n}\n.text--xsmall p:not(.heading),\n.minor p:not(.heading) {\n  font-size: var(---font-size-body-small--mobile);\n}\n@media only screen and (min-width: 741px) {\n  .text--xsmall p:not(.heading),\n  .minor p:not(.heading) {\n    font-size: var(---font-size-body-small--desktop);\n  }\n}\n\n@media only screen and (max-width: 740px) {\n  .text--small--mobile,\n  .p-minor--mobile {\n    font-size: var(---font-size-body-small--mobile);\n    font-family: var(---font-family-body);\n    font-weight: var(---font-weight-body);\n  }\n}\n\n.text--large,\n.major {\n  font-size: var(---font-size-body-large--mobile);\n}\n@media only screen and (min-width: 741px) {\n  .text--large,\n  .major {\n    font-size: var(---font-size-body-large--desktop);\n  }\n}\n.text--large p:not(.heading),\n.major p:not(.heading) {\n  font-size: var(---font-size-body-large--mobile);\n}\n@media only screen and (min-width: 741px) {\n  .text--large p:not(.heading),\n  .major p:not(.heading) {\n    font-size: var(---font-size-body-large--desktop);\n  }\n}\n\n@media only screen and (max-width: 740px) {\n  .text--large--mobile,\n  .p-major--mobile {\n    font-size: var(---font-size-body-large--mobile);\n    font-family: var(---font-family-body);\n    font-weight: var(---font-weight-body);\n  }\n}\n\nstrong, .strong {\n  font-weight: var(---font-weight-body--bold);\n}\n\n.link.link--strong {\n  font-weight: var(---font-weight-body--bold);\n  text-decoration: none;\n}\n\n.blockquote, blockquote {\n  /*\n  font-size: var(---font-size-h2--mobile);\n  @include respond-to($small-up){\n    font-size: var(---font-size-h2--desktop);\n  }\n  */\n  font-size: var(---font-size-h3--mobile);\n}\n@media only screen and (min-width: 741px) {\n  .blockquote, blockquote {\n    font-size: var(---font-size-h3--desktop);\n  }\n}\n\n/* Product Titles */\n.product-item .product-item-meta__title {\n  font-family: var(---font-family-heading);\n  font-weight: var(---font-weight-body--bold);\n  line-height: var(---line-height-heading--mobile);\n  letter-spacing: var(---letter-spacing-subheading--mobile);\n  font-size: var(---font-size-body-large--mobile);\n}\n@media only screen and (min-width: 741px) {\n  .product-item .product-item-meta__title {\n    font-size: var(---font-size-body-large--desktop);\n  }\n}\n\n.product-meta__title {\n  font-size: var(---font-size-h1--mobile);\n}\n@media only screen and (min-width: 741px) {\n  .product-meta__title {\n    font-size: var(---font-size-h1--desktop);\n  }\n}\n\n/* Header */\n.header__linklist {\n  font-weight: var(---font-weight-body--bold);\n}\n\n.quiz h1, .quiz .h1,\n.quiz h2, .quiz .h2, .quiz .jdgm-rev-widg__title, .quiz .jdgm-carousel-title,\n.quiz h3, .quiz .h3,\n.quiz h4, .quiz .h4 {\n  margin-top: 36px;\n  margin-bottom: 12px;\n}\n\n.quiz .icon.icon--fill * {\n  stroke: none;\n  fill: currentColor;\n}\n\nhtml {\n  scroll-behavior: smooth;\n}\n\n.table--bordered td {\n  border-top: 1px solid #e6e6e6;\n}\n\n/* 5. Layout */\n.split-page {\n  background: RGB(var(--section-block-background));\n}\n@media only screen and (min-width: 1001px) {\n  .split-page {\n    min-height: 80vh;\n    height: 100%;\n    display: grid;\n    grid-template-columns: 3fr 4fr;\n  }\n}\n.split-page .page-header__text-wrapper {\n  margin-top: 0;\n  margin-bottom: 38px;\n}\n\n.split-page__header {\n  padding-top: calc(var(--vertical-breather) * 2);\n}\n\n.split-page__footer {\n  padding-bottom: calc(var(--vertical-breather) * 2);\n  justify-self: flex-end;\n}\n.split-page__footer .form__secondary-action {\n  margin: 0;\n}\n.split-page__footer .form__secondary-action button,\n.split-page__footer .form__secondary-action a {\n  font-weight: bold;\n  text-decoration: none;\n}\n\n.split-page__image {\n  height: 100%;\n  -o-object-fit: cover;\n     object-fit: cover;\n}\n\n.split-page__content {\n  padding: 0 var(--container-gutter);\n}\n\n.split-page__content-wrapper {\n  position: relative;\n  height: 100%;\n  margin: auto;\n  display: flex;\n  flex-direction: column;\n  justify-content: space-between;\n}\n@media only screen and (min-width: 1001px) {\n  .split-page__content-wrapper {\n    max-height: calc(100vh - var(--header-height));\n  }\n}\n\nhtml.supports-no-cookies .supports-no-cookies {\n  display: none;\n}\nhtml.supports-cookies .supports-cookies {\n  display: none;\n}\n\n/* 6. Sections */\n[class*=template-customers] {\n  /* ----- Link Bar ----- */\n  /* ----- Page Header ----- */\n  /* ----- Link Bar ----- */\n}\n[class*=template-customers] .link-bar {\n  --background: var(---background-color--content-reversed-1);\n  --text-color: var(---color-text--reversed);\n  background: var(--background);\n  color: var(--text-color);\n}\n[class*=template-customers] .link-bar .link-bar__link-item {\n  transition: 0.25s color;\n}\n[class*=template-customers] .link-bar .link-bar__link-item:hover {\n  color: var(---color--highlight);\n}\n[class*=template-customers] .link-bar .link-bar__link-item .text--subdued {\n  transition: 0.25s color;\n}\n[class*=template-customers] .link-bar .link-bar__link-item .text--subdued:hover {\n  color: var(---color--danger) !important;\n}\n[class*=template-customers] .link-bar .text--underlined {\n  --text-color: var(---color--highlight);\n  color: var(--text-color);\n  text-decoration: none;\n  cursor: default;\n  pointer-events: none;\n}\n[class*=template-customers] .link-bar .text--underlined:after {\n  content: none;\n}\n[class*=template-customers] .page-header .heading {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 0.25em;\n}\n[class*=template-customers] .page-header .bubble-count {\n  background-color: var(---color--highlight);\n  color: var(---color-text);\n  font-weight: 700;\n  letter-spacing: -0.075em !important;\n  height: 36px;\n  width: 36px;\n  font-size: 0.5em;\n}\n\n.account {\n  background-color: var(---background-color--content-1);\n}\n.account .account__orders-table {\n  font-weight: 400;\n  font-size: var(---font-fize-body);\n}\n.account .account__orders-table thead th {\n  padding: 0.5rem 0;\n}\n.account .account__orders-table td {\n  padding: 0.5rem 0;\n}\n.account .account__orders-table .reorder-button {\n  letter-spacing: 0;\n  min-width: 0;\n  padding: 0em 1em;\n  line-height: 2.4em;\n  background-color: var(---color--highlight);\n}\n.account .account__order-item-actions {\n  display: flex;\n  flex-direction: column;\n  gap: 0.5em;\n}\n@media (min-width: 1000px) {\n  .account .account__order-item-actions {\n    flex-direction: row;\n    gap: 20px;\n  }\n}\n\n.mini-cart {\n  --root-background: var(---background-color--content-1--rgb);\n  --section-block-background: var(---background-color--content-1--rgb);\n  --background: var(---background-color--content-1--rgb);\n  width: 100vw;\n  /* ----- Loading Overlay ----- */\n  /* ----- Drawer Header ----- */\n  /* ----- Line Items ----- */\n  /* ----- Cart Subscriptions Box ----- */\n  /* ----- Tags ----- */\n  /* ----- Shipping Details ----- */\n}\n.mini-cart:after {\n  pointer-events: none;\n  content: \"\";\n  display: block;\n  position: absolute;\n  top: 0;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  z-index: 1;\n  transition: 0.25s background-color;\n  background: RGBA(255, 255, 255, 0);\n}\n.mini-cart.cart-drawer--loading:after {\n  background: RGBA(var(---background-color--content-1--rgb), 0.75);\n  pointer-events: auto;\n}\n.mini-cart .drawer__header {\n  border-bottom: 0;\n  max-height: none;\n  height: auto;\n}\n.mini-cart .drawer__title {\n  text-transform: none;\n  margin-bottom: 0;\n}\n.mini-cart .drawer__close-button {\n  bottom: 0;\n  top: 0;\n}\n.mini-cart free-shipping-bar {\n  padding: 20px 30px;\n  margin: 0 0 20px 0;\n  background: RGB(var(---background-color--content-2--rgb));\n  border-radius: var(---border-radius--general);\n}\n.mini-cart free-shipping-bar .text--small {\n  margin-bottom: 0;\n}\n.mini-cart .mini-cart__drawer-footer {\n  --root-border-color: var(---color-line--light--rgb);\n  padding: 20px var(--container-gutter);\n}\n.mini-cart .product-item-meta__title {\n  line-height: 1.2;\n  font-size: var(---font-size-body--desktop);\n}\n.mini-cart .product-item-meta__property {\n  font-weight: var(---font-weight-body);\n}\n.mini-cart .product-item-meta__price-and-remove {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n.mini-cart .product-item-meta__price-and-remove .line-item__quantity {\n  margin-top: 0;\n}\n.mini-cart .line-item .line-item__content-wrapper {\n  margin-top: 0;\n  margin-bottom: 35px;\n}\n.mini-cart .line-item .line-item__info {\n  width: 100%;\n}\n.mini-cart .line-item .line-item__image {\n  border-radius: 6px;\n}\n.mini-cart .line-item .line-item__image-wrapper {\n  margin-right: 10px;\n}\n@media only screen and (min-width: 1001px) {\n  .mini-cart .line-item .line-item__image-wrapper {\n    margin-right: 20px;\n  }\n}\n.mini-cart .line-item .line-item__remove-button {\n  font-weight: 400;\n}\n.mini-cart .line-item .product-item-meta__property-list {\n  margin: 0;\n}\n.mini-cart .line-item .product-item-meta__price-list-container {\n  margin: 0;\n}\n.mini-cart .line-item .quantity-selector {\n  --quantity-selector-height: 32px;\n  overflow: hidden;\n}\n.mini-cart .line-item .quantity-selector__input {\n  font-size: var(---font-size-body-small--desktop);\n  font-weight: var(---font-weight-body);\n  background: RGB(var(---background-color--content-1--rgb));\n}\n.mini-cart .line-item .quantity-selector__button {\n  background: RGB(var(---background-color--content-1--rgb));\n}\n.mini-cart .line-item .line-item__remove-button {\n  font-size: var(---font-size-body-small--desktop);\n  font-weight: var(---font-weight-body);\n}\n.mini-cart .mini-cart__drawer-prefooter {\n  padding: 10px var(--container-gutter);\n  text-align: center;\n  font-weight: var(---font-weight-body);\n  position: relative;\n}\n.mini-cart .mini-cart__drawer-prefooter:after {\n  content: \"\";\n  display: block;\n  position: absolute;\n  height: 30px;\n  width: 100%;\n  top: 0;\n  transform: translateY(-100%);\n  background: linear-gradient(to bottom, rgba(255, 255, 255, 0), rgb(255, 255, 255));\n}\n.mini-cart .cart-subscriptions {\n  display: block;\n  margin-bottom: 12px;\n  border-radius: 8px;\n  background: var(---background-color--content-2);\n}\n.mini-cart .cart-subscriptions .cart-subscriptions-form__actions {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 1em;\n}\n.mini-cart .cart-subscriptions .cart-subscriptions__form {\n  padding: 12px;\n  border-top: 1px solid rgba(var(---color--brand-6--rgb), 0.5);\n}\n@media only screen and (min-width: 1001px) {\n  .mini-cart .cart-subscriptions .cart-subscriptions__form {\n    padding: 24px;\n  }\n}\n.mini-cart .cart-subscriptions .subscriptions-input {\n  margin: 1em 0;\n  gap: 10px;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n}\n@media only screen and (min-width: 1001px) {\n  .mini-cart .cart-subscriptions .subscriptions-input {\n    flex-direction: row;\n  }\n}\n.mini-cart .cart-subscriptions .subscriptions-input label {\n  font-weight: 700;\n  font-size: var(---font-size-body--small);\n}\n.mini-cart .cart-subscriptions .subscriptions-input select {\n  padding: 0.25em 2.5em 0.25em 0.75em;\n  border-radius: 8px;\n}\n.mini-cart .product-item-tags {\n  display: flex;\n  flex-wrap: wrap;\n  margin: 0.5em 0;\n  gap: 10px;\n}\n.mini-cart .product-item-tag {\n  display: inline-flex;\n  align-items: center;\n  padding: 0.35em 0.5em;\n  gap: 0.25em;\n  background: var(---background-color--secondary);\n  border-radius: 4px;\n  -webkit-user-select: none;\n     -moz-user-select: none;\n          user-select: none;\n}\n.mini-cart .product-item-tag svg * {\n  fill: currentColor;\n  outline: currentColor;\n}\n.mini-cart .product-item-tag--prescription {\n  background: RGB(var(---background-color--danger--rgb));\n  color: RGB(var(---color--danger--rgb));\n}\n.mini-cart .product-item-tag--subscription {\n  background-color: var(---color--highlight);\n}\n.mini-cart .product-item-tag__icon {\n  display: flex;\n  align-items: center;\n  line-height: 1;\n}\n.mini-cart .product-item-tag__icon svg {\n  width: 20px;\n  height: 20px;\n}\n.mini-cart .product-item-tag__text {\n  line-height: 1;\n}\n.mini-cart .shipping-details {\n  --padding-horizontal: 30px;\n  --padding-vertical: 20px;\n}\n.mini-cart .shipping-details__inner {\n  margin-top: calc(1 * var(--padding-vertical));\n  margin-left: calc(-1 * var(--padding-horizontal));\n  margin-right: calc(-1 * var(--padding-horizontal));\n  padding-top: var(--padding-vertical);\n  padding-left: var(--padding-horizontal);\n  padding-right: var(--padding-horizontal);\n  border-top: 1px solid RGBA(0, 0, 0, 0.2);\n}\n.mini-cart .shipping-details__footer {\n  margin-top: calc(1 * var(--padding-vertical));\n  padding-top: calc(0.5 * var(--padding-vertical));\n  padding-left: var(--padding-horizontal);\n  padding-right: var(--padding-horizontal);\n  border-top: 1px solid RGBA(0, 0, 0, 0.2);\n  line-height: 1.2;\n}\n.mini-cart .shipping-details__header {\n  display: flex;\n  justify-content: space-between;\n}\n.mini-cart .shipping-details__heading {\n  padding: 0;\n  margin: 0;\n}\n.mini-cart .shipping-details__table {\n  width: 100%;\n  text-align: left;\n  font-size: var(---font-size-body-small--desktop);\n}\n.mini-cart .shipping-details__table th,\n.mini-cart .shipping-details__table td {\n  font-size: 0.9em !important;\n}\n.mini-cart .shipping-details__table tr th,\n.mini-cart .shipping-details__table tr td {\n  text-align: center;\n  padding: 0.1em 0;\n}\n.mini-cart .shipping-details__table tr th:first-child,\n.mini-cart .shipping-details__table tr td:first-child {\n  text-align: left;\n}\n.mini-cart .shipping-details__table tr th:last-child,\n.mini-cart .shipping-details__table tr td:last-child {\n  text-align: right;\n}\n.mini-cart .shipping-details__message {\n  background-color: var(---background-color--content-1);\n  padding: 0.75em;\n  margin: 1em 0;\n  border-radius: var(--block-border-radius);\n  line-height: 1.4;\n}\n.mini-cart .shipping-details__message p {\n  font-size: 0.9em !important;\n}\n.mini-cart .cart-vet-partner__selector-form {\n  border-top: 1px solid var(---color-line--light);\n  padding-top: 20px;\n  margin-top: 20px;\n}\n\n.account-dog-info {\n  --text-font-weight: 300;\n  display: flex;\n  flex-direction: column;\n  gap: 30px;\n  padding: 24px;\n  border-radius: 18px;\n  overflow: hidden;\n  font-weight: var(--text-font-weight);\n  color: RGB(var(--text-color));\n}\n.account-dog-info strong {\n  font-weight: 700;\n}\n.account-dog-info h1,\n.account-dog-info h2,\n.account-dog-info h3,\n.account-dog-info h4,\n.account-dog-info h5,\n.account-dog-info h6,\n.account-dog-info .h1,\n.account-dog-info .h2,\n.account-dog-info .jdgm-rev-widg__title,\n.account-dog-info .jdgm-carousel-title,\n.account-dog-info .h3,\n.account-dog-info .h4,\n.account-dog-info .h5,\n.account-dog-info .h6 {\n  color: RGB(var(--heading-color));\n}\n.account-dog-info .account-dog-info {\n  display: flex;\n}\n.account-dog-info .button-wrapper {\n  display: flex;\n  flex-direction: column;\n  gap: 0.5em;\n}\n@media only screen and (min-width: 1001px) {\n  .account-dog-info .button-wrapper {\n    display: flex;\n    flex-direction: row;\n  }\n}\n.account-dog-info .button {\n  line-height: 1.2 !important;\n}\n.account-dog-info .button-wrapper--center {\n  justify-content: center;\n}\n@media only screen and (min-width: 1001px) {\n  .account-dog-info {\n    padding: 48px;\n  }\n}\n\n@media only screen and (min-width: 1001px) {\n  .product__media {\n    position: sticky;\n    top: calc(var(--announcement-bar-height) + var(--header-height) + var(--vertical-breather));\n  }\n}\n\n@media only screen and (min-width: 741px) {\n  .shopify-section--feeding-calculator {\n    margin-top: 150px;\n  }\n}\n\n.fieldset {\n  --form-input-gap: 24px;\n  margin: var(--container-gutter) 0;\n}\n.fieldset:last-child {\n  margin-bottom: 0;\n}\n\n.feeding-calculator {\n  position: relative;\n}\n\n.feeding-calculator__icon {\n  position: absolute;\n  left: 0;\n  right: 0;\n  top: 0;\n  margin: auto;\n  transform: translateY(-65%);\n  display: none;\n}\n@media only screen and (min-width: 741px) {\n  .feeding-calculator__icon {\n    display: block;\n    width: 200px;\n    height: 200px;\n  }\n}\n@media only screen and (min-width: 1201px) {\n  .feeding-calculator__icon {\n    width: 250px;\n    height: 250px;\n  }\n}\n\n.calculator-results-table tr.selected td {\n  background: var(---color--brand-1);\n}\n.calculator-results-table tr.selected .label {\n  visibility: visible;\n}\n.calculator-results-table tr.results-row {\n  cursor: pointer;\n}\n.calculator-results-table th span {\n  display: block;\n  font-size: 0.75em;\n}\n.calculator-results-table td .label, .calculator-results-table th .label {\n  margin: 0 0.5em;\n}\n.calculator-results-table .label {\n  visibility: hidden;\n  padding: 0.4em 0.8em;\n}\n\n.results-row button.link {\n  display: flex;\n  align-items: center;\n}\n.results-row .results-row__details {\n  display: flex;\n  align-items: flex-start;\n  justify-content: flex-start;\n  gap: 0.5em;\n}\n.results-row .results-row__external-link {\n  padding: 0.05em 0.25em;\n  border: 1px solid RGBA(var(---color-line--rgb), 0.25);\n  border-radius: 5px;\n  background-color: RGBA(var(---color-line--rgb), 0);\n  transition: 0.25s background-color;\n}\n.results-row .results-row__external-link:hover, .results-row .results-row__external-link:focus {\n  background-color: RGBA(var(---color-line--rgb), 0.1);\n}\n\n/* ========== Nutrition ========== */\n.feeding-calculator-nutrition {\n  --product-image-size: 120px;\n  --product-image-border-size: 36px;\n  --primary-button-background: var(--product-color);\n  position: relative;\n  padding-top: 30px;\n}\n\n.feeding-calculator-nutrition__header {\n  position: relative;\n  display: flex;\n  justify-content: center;\n  height: 60px;\n  background: RGB(var(--product-color));\n  border-top-left-radius: var(--block-border-radius);\n  border-top-right-radius: var(--block-border-radius);\n}\n.feeding-calculator-nutrition__header:after {\n  content: \"\";\n  display: block;\n  padding: 15px;\n  margin: auto;\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  z-index: 1;\n  --offset: calc(-1 * (var(--product-image-size) - var(--product-image-border-size) * 2));\n  transform: translateY(var(--offset));\n  background: RGB(var(--product-color));\n  border-radius: 120px;\n  width: calc(var(--product-image-size) + 30px);\n  height: calc(var(--product-image-size) + 30px);\n}\n\n.feeding-calculator-nutrition__header-image {\n  position: absolute;\n  top: 0;\n  transform: translateY(-30px);\n  z-index: 2;\n}\n\n.feeding-calculator-nutrition__content {\n  padding-top: calc(var(--vertical-breather) / 2);\n  position: relative;\n  z-index: 1;\n  background: RGB(var(--section-background, var(--background)));\n}\n\n.spaced-content--tight {\n  gap: 20px;\n}\n\n/* ----- Ratings ----- */\n.nutrition-ratings {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  flex-wrap: wrap;\n  gap: 10px;\n}\n@media only screen and (min-width: 1001px) {\n  .nutrition-ratings {\n    gap: 20px;\n  }\n}\n\n/* ----- Nutrition Summary ----- */\n.nutrition-summary {\n  display: flex;\n  flex-direction: column;\n  gap: 20px;\n}\n@media only screen and (min-width: 741px) {\n  .nutrition-summary {\n    flex-direction: row;\n  }\n  .nutrition-summary .nutrition-summary-item {\n    width: 100%;\n  }\n}\n\n.nutrition-summary-item__title {\n  margin-bottom: 0.5em;\n}\n\n/* ========== Analysis Table ========== */\n.nutritional-analysis {\n  --row-spacing: 0.35em;\n  text-align: left;\n  border: 5px solid RGB(var(--heading-color));\n  padding: 20px;\n  font-size: var(---font-size-body-small--mobile);\n}\n@media only screen and (min-width: 741px) {\n  .nutritional-analysis {\n    font-size: var(---font-size-body-small--desktop);\n  }\n}\n\n.nutritional-analysis__footer {\n  margin-top: 20px;\n  font-size: var(---font-size-body-xs--mobile);\n}\n@media only screen and (min-width: 741px) {\n  .nutritional-analysis__footer {\n    font-size: var(---font-size-body-xs--desktop);\n  }\n}\n\n/* ----- Analysis Category ----- */\n.analysis-category {\n  display: block;\n}\n\n.analysis-category__header {\n  display: flex;\n  gap: 10px;\n  width: 100%;\n  padding: var(--row-spacing) 0;\n  border-bottom: 1px solid var(---color-line--light);\n}\n.analysis-category__header[aria-expanded=true] .analysis-category__button:before {\n  content: \"-\";\n}\n\n/* ----- Analysis Header ----- */\n.analysis-header {\n  display: block;\n  padding: var(--row-spacing) 0;\n  margin-top: 1em;\n  margin-bottom: 5px;\n  border-bottom: 1px solid var(---color-line);\n}\n.analysis-header:first-child {\n  margin-top: 0;\n}\n\n.analysis-header__title {\n  font-size: 20px;\n  text-transform: uppercase;\n  font-weight: var(---font-weight-body--bold);\n}\n\n/* ----- Analysis Category ----- */\n.analysis-category__title {\n  font-weight: var(---font-weight-body--bold);\n}\n\n.analysis-category__button {\n  display: inline-flex;\n  align-items: center;\n  justify-content: center;\n  width: 20px;\n  height: 20px;\n  font-weight: bold;\n  border-radius: 4px;\n  background: RGBA(var(--text-color), 0.05);\n}\n.analysis-category__button:focus, .analysis-category__button:hover {\n  background: RGBA(var(--text-color), 0.1);\n}\n.analysis-category__button:before {\n  content: \"+\";\n}\n\n.analysis-category__content {\n  display: none;\n  width: 100%;\n}\n\n.analysis-category__header[aria-expanded=true] + .analysis-category__content {\n  display: table !important;\n}\n\n@media (min-width: 480px) {\n  .analysis-category .analysis-row > *:first-child {\n    padding-left: 30px;\n  }\n}\n\n/* ----- Analysis Table ----- */\n.analysis-table {\n  display: table;\n  width: 100%;\n}\n\n.analysis-table-row {\n  display: table-row;\n  gap: 5px;\n  width: 100%;\n  line-height: 1.4;\n  font-size: 0.95em;\n}\n.analysis-table-row > * {\n  width: 70px;\n  text-align: center;\n}\n@media (min-width: 480px) {\n  .analysis-table-row > * {\n    width: 100px;\n  }\n}\n.analysis-table-row > *:first-child {\n  text-align: left;\n  width: auto;\n  margin-right: auto;\n}\n.analysis-table-row > *:last-child {\n  text-align: right;\n}\n\n.analysis-row {\n  width: 100%;\n  display: table-row;\n}\n.analysis-row > * {\n  display: table-cell;\n  padding-left: 5px;\n  padding-right: 5px;\n  padding: var(--row-spacing) 0;\n  border-bottom: 1px solid var(---color-line--light);\n}\n.analysis-row > *:last-child {\n  text-align: right;\n  padding-right: 0;\n}\n\n/* ========== Classes ========== */\n.product-color {\n  color: RGB(var(--product-color));\n}\n\n.spaced-content {\n  text-align: center;\n  display: grid;\n  grid-auto-flow: row;\n  gap: 32px;\n}\n.spaced-content > * {\n  margin: 0;\n}\n\n.spaced-content--tight {\n  gap: 20px;\n}\n\n.section.section--use-padding {\n  margin: 0;\n  padding: var(--vertical-breather) 0;\n}\n.section.section--half-padding {\n  --vertical-breather: calc(var(--vertical-breather) / 2);\n}\n.section.section--double-spacing {\n  --vertical-breather: var(--vertical-breather-double);\n}\n.section.section--no-padding {\n  margin: 0;\n  padding: 0;\n}\n.section .container--no-padding {\n  padding: 0;\n}\n.container--smaller {\n  max-width: 700px;\n}\n\n/* 7. Page-Specific Styles */\n/* 8. Components */\n.button {\n  display: inline-flex;\n  align-items: center;\n  justify-content: center;\n  gap: 0.5em;\n  transition: color 0.25s;\n  text-align: center;\n  line-height: 1.2;\n  min-height: unset;\n  font-weight: var(--text-font-bold-weight);\n  font-size: var(---font-size-button--mobile);\n}\n.button:not(.button--text) {\n  -webkit-padding-start: unset;\n          padding-inline-start: unset;\n  -webkit-padding-end: unset;\n          padding-inline-end: unset;\n  padding: 1em 1.5em;\n}\n@media only screen and (min-width: 741px) {\n  .button {\n    font-size: var(---font-size-button--desktop);\n  }\n}\n.button[disabled] {\n  --button-background: 154, 154, 154;\n  cursor: not-allowed;\n  opacity: 0.5;\n  background-position: 100% -100%, 100% 100% !important;\n}\n.button:not(.button--link) {\n  min-width: 200px;\n}\n.button .loader-button__text {\n  gap: 16px;\n}\n.button:not(.button--text) {\n  /*\n  padding-left: 50px;\n  padding-right: 50px;\n\n  @include respond-to($small-down) {\n    padding: 0 30px;\n  }\n  */\n}\n.button.button--highlight {\n  --button-background: var(---color--highlight--rgb);\n  --button-text-color: 0, 0, 0;\n}\n.button.button--tertiary {\n  --button-background: var(---color--tertiary--rgb);\n  --button-text-color: 0, 0, 0;\n}\n.button.button--tab {\n  min-width: unset;\n  padding-left: 0 !important;\n  padding-right: 0 !important;\n  color: RGB(var(---color--brand-4--rgb));\n  cursor: pointer;\n}\n.button.button--tab[disabled] {\n  background: none;\n  pointer-events: none;\n  cursor: not-allowed;\n}\n.button.button--tab:not([disabled]) {\n  cursor: pointer;\n}\n.button.button--tab:not([disabled]):hover, .button.button--tab:not([disabled]):focus {\n  color: RGB(var(---color--tertiary--rgb));\n}\n.button.button--tab:not([disabled]).active {\n  color: RGB(var(---color--brand-1--rgb));\n  cursor: default;\n  pointer-events: none;\n}\n.button.button--text {\n  display: inline-flex;\n  align-items: center;\n  gap: 10px;\n  min-width: 0;\n  padding-left: 0;\n  padding-right: 0;\n  font-size: var(---font-size-body-large--desktop);\n  font-weight: var(---font-weight-body--bold);\n  transition: 0.25s color;\n}\n.button.button--text:focus, .button.button--text:hover {\n  color: var(---color-text--light);\n}\n.button.button--text .button__icon svg {\n  width: 10px;\n  height: 10px;\n  transform: rotate(45deg);\n}\n.button.button--hollow {\n  background: transparent;\n  color: RGB(var(--button-background));\n  box-shadow: 0 0 0 1px RGB(var(--button-background));\n  transition: 0.25s color, 0.25s box-shadow, 0.25s background;\n}\n.button.button--hollow:hover {\n  background: RGB(var(--button-background));\n  color: RGB(var(--button-text-color));\n}\n.button.button--stealth {\n  background: RGB(var(--section-block-background));\n  color: RGB(var(--text-color));\n  transition: 0.25s color, 0.25s box-shadow, 0.25s background;\n}\n.button.button--stealth:hover {\n  background: RGB(var(--button-background));\n  color: RGB(var(--button-text-color));\n}\n.button.button--tiny {\n  padding: 0.25em 0.5em;\n  line-height: 24px;\n  padding-left: 24px;\n  padding-right: 24px;\n  min-width: 0;\n  font-size: var(---font-size-button--mobile);\n}\n@media only screen and (max-width: 1000px) {\n  .button.button--tiny {\n    min-width: unset;\n  }\n}\n@media only screen and (min-width: 1001px) {\n  .button.button--tiny {\n    height: 36px;\n    line-height: 36px;\n    font-size: var(---font-size-button--mobile);\n  }\n}\n.button.button--large {\n  font-size: var(---font-size-button-large--mobile);\n  min-height: unset;\n  padding: 0.75em 2em;\n  font-size: var(---font-size-button-large--mobile);\n}\n@media only screen and (max-width: 1000px) {\n  .button.button--large {\n    min-width: unset;\n  }\n}\n@media only screen and (min-width: 1001px) {\n  .button.button--large {\n    height: 64px;\n  }\n}\n@media only screen and (min-width: 1001px) {\n  .button.button--large {\n    font-size: var(---font-size-button-large--desktop);\n  }\n}\n\n.modal {\n  --background: var(---background-color--content-1--rgb);\n}\n.modal .modal__close-button {\n  top: 26px;\n  right: 26px;\n}\n.modal .modal__header {\n  text-align: center;\n  padding-top: 24px;\n}\n.modal .modal__title {\n  font-size: var(---font-size-button-large--desktop);\n}\n.modal .modal__content {\n  border-radius: 8px;\n}\n.modal .form__actions {\n  margin-top: 2em;\n}\n@media only screen and (max-width: 740px) {\n  .modal .form__actions .button {\n    width: 100%;\n    justify-content: center;\n  }\n}\n\n.modal--login .quiz-modal-footer {\n  text-align: center;\n  padding-bottom: var(--vertical-breather);\n  font-size: var(---font-size-body--desktop);\n}\n\n.modal--register .modal__content {\n  overflow: visible;\n}\n.modal--register .quiz-modal__image {\n  width: 185px;\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  margin: auto;\n  transform: translateY(-60%);\n}\n.modal--register .newsletter-modal__content {\n  padding-top: 90px !important;\n}\n.modal--register .quiz-modal-footer {\n  margin-top: 20px !important;\n}\n.modal--register .button--link {\n  transform: translateY(calc(var(--vertical-breather)));\n}\n\n.recipe-modal .newsletter-modal {\n  flex-direction: column;\n}\n.recipe-modal .newsletter-modal__content {\n  padding: var(--container-gutter);\n  text-align: left;\n}\n.recipe-modal .modal__close-button {\n  color: var(---color-text--reversed);\n  transition: transform 0.25s;\n}\n.recipe-modal .modal__close-button:hover {\n  transform: rotate(90deg);\n}\n\n.modal--upsells {\n  /* ----- Quiz Results Product ----- */\n  /* ----- Layout ----- */\n}\n.modal--upsells .quiz-results-product {\n  width: 300px;\n}\n.modal--upsells .quiz-results-product__header img {\n  margin: auto;\n}\n.modal--upsells .quiz-results-product__footer {\n  display: flex;\n  justify-content: center;\n  padding: 0 20px;\n}\n.modal--upsells .quiz-modal-content__header {\n  padding: 0 40px;\n  margin: auto;\n  max-width: 450px;\n}\n.modal--upsells .newsletter-modal__content {\n  max-width: unset;\n  padding-top: var(--container-gutter);\n  padding-bottom: var(--container-gutter);\n  background: var(---background-color--content-2);\n}\n.modal--upsells .quiz-results-product__footer-price {\n  display: flex;\n  margin-right: auto;\n}\n.modal--upsells .quiz-results-product__footer-price .price {\n  font-size: 16px;\n}\n.modal--upsells .price-list {\n  display: flex;\n  align-items: center;\n}\n@media only screen and (max-width: 1000px) {\n  .modal--upsells .gallery {\n    margin-left: calc(var(--container-gutter) * -1);\n    margin-right: calc(var(--container-gutter) * -1);\n  }\n}\n\n.input select,\n.input input {\n  font-weight: var(--text-font-bold-weight);\n}\n\n/* ----- Form Container ----- */\nrevealing-form,\n.revealing-form {\n  display: block;\n}\n\n/* ----- Inputs ----- */\nrevealing-form-input,\n.revealing-form-input {\n  display: none;\n}\nrevealing-form-input.revealing-form-input--visible,\n.revealing-form-input.revealing-form-input--visible {\n  display: block;\n}\nrevealing-form-input.revealing-form-input--animating,\n.revealing-form-input.revealing-form-input--animating {\n  display: block;\n}\n\n/* ----- Actions ----- */\nrevealing-form-actions,\n.revealing-form__actions {\n  display: block;\n}\n\nexpanding-input {\n  position: relative;\n}\nexpanding-input select {\n  position: absolute;\n  left: 0;\n  right: 0;\n  top: 0;\n  bottom: 0;\n  height: 100%;\n  width: 100%;\n  margin: 0 !important;\n  opacity: 0;\n}\nexpanding-input select:focus + .expanding-input__display, expanding-input select:hover + .expanding-input__display {\n  outline: none;\n  border-color: var(---color--highlight);\n}\n\n.expanding-input__display {\n  padding-left: 0.5em;\n  padding-right: 0.5em;\n  cursor: text;\n}\n.expanding-input__display:after {\n  transition: color 0.25s;\n}\n.expanding-input__display:empty {\n  color: var(---color--secondary);\n}\n.expanding-input__display:empty:after {\n  content: attr(data-default);\n  font-weight: 300;\n}\n.expanding-input__display:focus {\n  color: var(---color--highlight);\n}\n\n.expanding-input--select {\n  cursor: pointer;\n}\n.expanding-input--select .expanding-input__display {\n  padding-right: 50px;\n  background-image: url(\"data:image/svg+xml,%3Csvg width='12' height='6' viewBox='0 0 12 6' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M1 0.5L6 5.5L11 0.5' stroke='%23E6E6E6' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E%0A\");\n  background-size: 15px;\n  background-repeat: no-repeat;\n  background-position: calc(100% - 10px);\n  pointer-events: none;\n}\n\n.expanding-input__input:not(select) {\n  position: absolute !important;\n  overflow: hidden;\n  clip: rect(0 0 0 0);\n  height: 1px;\n  width: 1px;\n  max-width: 1px;\n  max-height: 1px;\n  font-size: 0;\n  margin: -1px;\n  padding: 0;\n  border: 0;\n}\n\nselect.expanding-input__input option {\n  background-color: #e6e6e6 !important;\n  text-align: left !important;\n  font-size: var(---font-size-body-large--desktop) !important;\n  color: var(---color-text) !important;\n}\n\n.box-line-item {\n  --box-line-item-padding: 20px;\n}\n.box-line-item .product-item-tag--frozen {\n  background: RGB(var(--root-background));\n}\n\n.box-line-item__inner {\n  margin-bottom: 20px;\n  padding: var(--box-line-item-padding) 0;\n  background: rgba(var(---color--brand-5--rgb), 0.25);\n  border-radius: 8px;\n}\n\n.box-line-item__body {\n  display: flex;\n  gap: var(--box-line-item-padding);\n  padding: 0 var(--box-line-item-padding);\n}\n\n.box-line-item__image {\n  max-width: 92px;\n  width: 100%;\n  -o-object-fit: cover;\n     object-fit: cover;\n}\n\n.line-item__image-inner {\n  position: relative;\n}\n\n.box-line-item__contents {\n  width: 100%;\n}\n\n.box-line-item__footer {\n  display: flex;\n  justify-content: space-between;\n  border-top: 1px solid rgba(var(---color--brand-5--rgb), 1);\n  padding: 0 var(--box-line-item-padding);\n  padding-top: 10px;\n  margin-top: 15px;\n}\n\n.tile-radio-input:checked + .tile-radio {\n  background: var(---color--brand-7);\n  border-color: var(---color--brand-7);\n  outline: 2px solid var(---color--brand-2);\n}\n\n.tile-radios {\n  display: flex;\n  justify-content: center;\n  gap: 15px;\n  flex-wrap: wrap;\n}\n\n.tile-radio {\n  display: flex;\n  align-items: center;\n  gap: 0.75em;\n  width: 100%;\n  cursor: pointer;\n  text-align: center;\n  line-height: 1.1;\n  padding: 10px;\n  border: 1px solid var(---color-line--light);\n  border-radius: 8px;\n  box-shadow: 0 0px 0px rgba(0, 0, 0, 0.1);\n  -webkit-user-select: none;\n     -moz-user-select: none;\n          user-select: none;\n  transition: transform 0.25s, box-shadow 0.25s;\n}\n.tile-radio:hover {\n  transform: scale(1.05);\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);\n}\n@media only screen and (min-width: 741px) {\n  .tile-radio {\n    text-align: center;\n    width: auto;\n    flex-direction: column;\n    padding: 20px;\n  }\n}\n\n.tile-radio__icon {\n  pointer-events: none;\n  width: 66px;\n  height: 66px;\n}\n\n.tile-radio__content {\n  display: flex;\n  flex-direction: column;\n  gap: 0.25em;\n  text-align: left;\n}\n@media only screen and (min-width: 741px) {\n  .tile-radio__content {\n    text-align: center;\n  }\n}\n\n.tile-radio__title {\n  line-height: 1.2;\n}\n\n.tile-radio__description {\n  line-height: 1.2;\n}\n\n.table.table--auto {\n  table-layout: auto;\n}\n.table.table--1 {\n  border-radius: 4px;\n  overflow: hidden;\n}\n.table.table--1 tr:hover td {\n  background: var(---color--brand-1);\n}\n.table.table--1 td {\n  background: var(---color--brand-7);\n}\n.table.table--1 td,\n.table.table--1 th {\n  padding: 0.75em 0.25em;\n  text-align: center;\n  vertical-align: middle;\n  line-height: 1.2;\n}\n.table.table--1 td:first-child,\n.table.table--1 th:first-child {\n  padding-left: 0.75em;\n  text-align: left;\n  font-weight: var(--text-font-bold-weight);\n}\n.table.table--1 td:last-child,\n.table.table--1 th:last-child {\n  padding-right: 0.75em;\n}\n.table.table--1 thead th {\n  font-family: var(--heading-font-family);\n  font-weight: var(--body-font-weight);\n  font-size: 0.9em;\n  letter-spacing: 0.05em;\n  background: var(---color--brand-2);\n  color: var(---color-text--reversed);\n}\n\n.shipping-bar {\n  --background--unmet: RGBA(235, 87, 87, .3);\n  --progress-background: #D9D9D9;\n  --loading-bar-background: 255, 255, 255;\n  position: relative;\n  display: block;\n  margin-top: 15px !important;\n}\n.shipping-bar .shipping-bar__progress {\n  background: var(--progress-background);\n}\n.shipping-bar .shipping-bar__progress:before {\n  content: \"\";\n  display: block;\n  position: absolute;\n  width: 2px;\n  height: 100%;\n  background: currentColor;\n  left: calc(var(--frozen-threshold) * 100%);\n  z-index: 10;\n}\n.shipping-bar.shipping-bar--frozen-food--unmet {\n  background: var(--background--unmet) !important;\n}\n.shipping-bar.shipping-bar--frozen-food--unmet .shipping-bar__progress:after {\n  background: var(---color--danger) !important;\n}\n\n.shipping-bar__icon {\n  --icon-size: 44px;\n  position: absolute;\n  left: 0;\n  top: 0;\n  display: inline-flex;\n  align-items: center;\n  justify-content: center;\n  width: var(--icon-size);\n  height: var(--icon-size);\n  vertical-align: top;\n  background: #fff;\n  border-radius: 100%;\n  box-shadow: 0 0 5px rgba(0, 0, 0, 0.1);\n  transform: translate(-50%, -30%);\n}\n\n.shipping-bar__text {\n  line-height: 1.4;\n  margin-bottom: 0.5em;\n  display: inline-block;\n}\n\n.vet-sticky-bar {\n  position: fixed;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  z-index: 9;\n  box-shadow: var(---shadow--modal);\n  transform: translateY(100%);\n  transition: transform 500ms;\n}\n.vet-sticky-bar[open] {\n  display: block;\n  transform: translateY(0);\n}\n.vet-sticky-bar:hover:not([open]) {\n  transform: translateY(var(--tease-tug-1));\n}\n@media only screen and (max-width: 740px) {\n  .vet-sticky-bar .cart-vet-text {\n    display: block;\n    width: 100%;\n  }\n}\n.vet-sticky-bar .cart-vet-partner {\n  display: flex;\n}\n@media only screen and (min-width: 1001px) {\n  .vet-sticky-bar .vet-partner-selector-wrapper {\n    flex: 1 0 250px;\n    max-width: 250px;\n  }\n}\n.vet-sticky-bar .cart-vet-partner__vet {\n  justify-content: flex-end;\n  padding: 0;\n  border: 0;\n}\n@media only screen and (min-width: 1001px) {\n  .vet-sticky-bar .cart-vet-partner__selector-form {\n    display: flex;\n    justify-content: end;\n  }\n}\n.vet-sticky-bar .cart-vet-partner__selector-button {\n  flex: 0 2 max-content;\n}\n.vet-sticky-bar .cart-vet-partner__vet-text {\n  padding: 15px 30px;\n  font-size: var(---font-size-h6--desktop);\n  background: var(---background-color--secondary);\n  border-radius: 10px;\n}\n@media only screen and (max-width: 740px) {\n  .vet-sticky-bar .cart-vet-partner__vet-text {\n    display: block;\n    width: 100%;\n  }\n}\n.vet-sticky-bar .cart-vet-partner__product-notice {\n  display: none;\n}\n\n.vet-sticky-bar__tag {\n  --tab-background: var(---color--highlight--rgb);\n  --tab-color: var(---color--default--rgb);\n  --edge-width: 30px;\n  --edge-corner-radius: 8px;\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  transform: translateY(-100%);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: -moz-max-content;\n  width: max-content;\n  padding: 0.5em;\n  margin: auto;\n  font-weight: var(--bold-font-weight);\n  text-align: center;\n  background: RGB(var(--tab-background));\n  color: RGB(var(--tab-color));\n  transition: background 250ms;\n}\n.vet-sticky-bar__tag:hover {\n  --tab-background: var(---color--highlight--dark--rgb);\n}\n.vet-sticky-bar__tag:before, .vet-sticky-bar__tag:after {\n  content: \"\";\n  display: block;\n  position: absolute;\n  top: 0;\n  z-index: -1;\n  background: RGB(var(--tab-background));\n  transition: background 250ms;\n  width: var(--edge-width);\n  height: 100%;\n}\n.vet-sticky-bar__tag:before {\n  left: -10px;\n  transform: skew(-10deg);\n  border-top-left-radius: var(--edge-corner-radius);\n}\n.vet-sticky-bar__tag:after {\n  right: -10px;\n  transform: skew(10deg);\n  border-top-right-radius: var(--edge-corner-radius);\n}\n\n.vet-sticky-bar__wrapper {\n  padding: 20px;\n  background-color: RGB(var(--section-background));\n}\n@media only screen and (max-width: 740px) {\n  .vet-sticky-bar__wrapper .container {\n    padding: 0;\n  }\n}\n\n@media only screen and (max-width: 740px) {\n  .cart-vet-partner__vet {\n    padding-top: 10px;\n    border-top: 1px solid var(---color-line--light);\n  }\n}\n\n.vet-sticky-bar__inner {\n  display: flex;\n  flex-direction: column;\n}\n@media only screen and (min-width: 1001px) {\n  .vet-sticky-bar__inner {\n    flex-direction: row;\n    gap: 20px;\n  }\n}\n\n.vet-sticky-bar__info {\n  display: flex;\n  gap: 10px;\n}\n\n.vet-sticky-bar__actions {\n  display: flex;\n  justify-content: flex-end;\n}\n\n.vet-sticky-bar__text .subheading {\n  margin: 0;\n}\n\n@keyframes vet-sticky-bar--tease {\n  0% {\n    transform: translateY(var(--tease-start));\n  }\n  15% {\n    transform: translateY(var(--tease-tug-1));\n  }\n  30% {\n    transform: translateY(var(--tease-start));\n  }\n  60% {\n    transform: translateY(var(--tease-start));\n  }\n  80% {\n    transform: translateY(var(--tease-tug-2));\n  }\n  100% {\n    transform: translateY(var(--tease-start));\n  }\n}\n@media only screen and (max-width: 740px) {\n  :root {\n    --tease-start: 100%;\n    --tease-tug-1: 95%;\n    --tease-tug-2: 85%;\n  }\n}\n@media only screen and (min-width: 741px) {\n  :root {\n    --tease-start: 100%;\n    --tease-tug-1: 90%;\n    --tease-tug-2: 70%;\n  }\n}\n\n/*  --------------------------------------------------\n    Cart\n    -------------------------------------------------- */\n.cart-vet-partner {\n  --vertical-spacing: 12px;\n  --spacing: 12px;\n  display: flex;\n  flex-direction: column;\n  gap: var(--vertical-spacing);\n  transition: opacity 0.25s;\n}\n.cart-vet-partner[loading] {\n  pointer-events: none;\n  opacity: 0.25;\n}\n.cart-vet-partner .unlisted-vet-container {\n  background: var(---background-color--secondary);\n  border-radius: 4px;\n  padding: 0.5em 1em;\n  text-align: center;\n  -webkit-user-select: none;\n  -moz-user-select: none;\n  user-select: none;\n}\n\n.modal--vet-not-listed .modal__overlay {\n  pointer-events: none;\n}\n\n.cart-vet-partner__inner {\n  display: flex;\n  flex-direction: column;\n  gap: 12px;\n}\n\n.cart-vet-partner__selector-form {\n  display: grid;\n  grid-auto-flow: row;\n  gap: var(--spacing);\n}\n@media only screen and (max-width: 1000px) {\n  .cart-vet-partner__selector-form {\n    grid-auto-flow: row;\n  }\n}\n@media only screen and (min-width: 1001px) {\n  .cart-vet-partner__selector-form select {\n    height: 100%;\n  }\n}\n\n.cart-vet-partner__vet {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  text-align: center;\n}\n\n.cart-vet-partner__product-notice {\n  text-align: center;\n}\n\n.cart-vet-partner__notice {\n  display: flex;\n  padding: 16px;\n  margin-top: var(--spacing);\n  gap: var(--spacing);\n  border: 2px solid var(---color--default);\n  border-radius: var(--block-border-radius);\n}\n\n.cart-vet-partner__notice-text .subheading {\n  margin-bottom: 0.25em;\n}\n\n.cart-vet-text {\n  line-height: 1;\n}\n\n/*  --------------------------------------------------\n    Account Page\n    -------------------------------------------------- */\n.account-vet-partner {\n  max-width: 800px;\n  padding: 0 0;\n  margin: 0 auto;\n}\n.account-vet-partner .account-vet-partner__inner {\n  margin-top: 20px;\n  padding: 20px;\n  background: var(---background-color--secondary);\n  border-radius: var(--block-border-radius);\n}\n@media only screen and (min-width: 1001px) {\n  .account-vet-partner .account-vet-partner__inner {\n    padding: 30px;\n  }\n}\n.account-vet-partner .cart-vet-partner__selector {\n  padding-bottom: 20px;\n  margin-bottom: 20px;\n  border-bottom: 1px solid var(---color-line--light);\n}\n.account-vet-partner .cart-vet-partner__vet {\n  padding: 0;\n  border: 0;\n}\n.account-vet-partner .cart-vet-partner__vet-text {\n  font-size: var(--base-font-size);\n  padding: 15px 30px;\n  border-radius: 10px;\n}\n.account-vet-partner .cart-vet-partner__notice {\n  background: var(---background-color--content-1);\n}\n.account-vet-partner .cart-vet-partner__product-notice {\n  display: none;\n}\n.account-vet-partner .cart-vet-partner__selector-form .select {\n  background: var(---background-color--content-1);\n}\n.account-vet-partner .cart-vet-notice {\n  display: block !important;\n  height: auto;\n  visibility: visible;\n}\n.account-vet-partner .cart-vet-notice button {\n  display: none;\n}\n.account-vet-partner .cart-vet-partner__notice {\n  padding: 20px;\n}\n@media (min-width: 1000px) {\n  .account-vet-partner .cart-vet-partner__notice {\n    padding: 30px;\n  }\n}\n.account-vet-partner .cart-vet-text {\n  display: flex;\n  justify-content: center;\n}\n.account-vet-partner .cart-vet-partner {\n  display: flex;\n  gap: 0;\n}\n\nsplit-page-step {\n  display: none;\n}\nsplit-page-step.split-page-step--visible {\n  display: block;\n}\nsplit-page-step .input--select > select {\n  width: 100%;\n}\n\n.banner {\n  border-radius: var(--block-border-radius);\n}\n.banner.banner--success {\n  --text-color: var(---color-text--reversed--rgb);\n  color: RGB(var(--text-color));\n  background-color: rgb(var(--success-color));\n}\n.banner.banner--error {\n  --text-color: var(---color-text--reversed--rgb);\n  color: RGB(var(--text-color));\n  background-color: rgb(var(--error-color));\n}\n\n.weight-range {\n  --track-height: 14px;\n  --thumb-size: 40px;\n  --thumb-icon-size: 80px;\n  display: block;\n  margin-top: var(--thumb-icon-size);\n  position: relative;\n}\n\n.weight-range__inner {\n  position: relative;\n  line-height: 0;\n  height: var(--track-height);\n  border-radius: 100px;\n  background: RGB(var(---color--brand-2--rgb));\n  outline: 2px solid RGB(var(--text-color));\n}\n\n.weight-range__thumb {\n  pointer-events: none;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 10px;\n  position: absolute;\n  left: var(--range-position);\n  transform: translateX(calc(-1 * var(--thumb-icon-size) / 2)) translateY(50%);\n  bottom: 0;\n  bottom: 50%;\n  z-index: 1;\n}\n\n.weight-range__range {\n  height: var(--track-height);\n}\n\n.weight-range__range::range-thumb {\n  height: var(--thumb-icon-size);\n  width: var(--thumb-icon-size);\n  transform: translateY(calc(-1 * var(--thumb-size) / 2 - -1 * var(--track-height) / 2)) translateX(calc(100 * var(--range-offset)));\n  opacity: 0;\n}\n\n.weight-range__range::slider-thumb {\n  height: var(--thumb-icon-size);\n  width: var(--thumb-icon-size);\n  transform: translateY(calc(-1 * var(--thumb-size) / 2 - -1 * var(--track-height) / 2)) translateX(calc(100 * var(--range-offset)));\n  opacity: 0;\n}\n\n.weight-range__range::-moz-range-thumb {\n  height: var(--thumb-icon-size);\n  width: var(--thumb-icon-size);\n  transform: translateY(calc(-1 * var(--thumb-size) / 2 - -1 * var(--track-height) / 2)) translateX(calc(100 * var(--range-offset)));\n  opacity: 0;\n}\n\n.weight-range__range::-webkit-slider-thumb {\n  height: var(--thumb-icon-size);\n  width: var(--thumb-icon-size);\n  transform: translateY(calc(-1 * var(--thumb-size) / 2 - -1 * var(--track-height) / 2)) translateX(calc(100 * var(--range-offset)));\n  opacity: 0;\n}\n\n.weight-range__range::range-track {\n  opacity: 0;\n}\n\n.weight-range__range::slider-track {\n  opacity: 0;\n}\n\n.weight-range__range::-moz-range-track {\n  opacity: 0;\n}\n\n.weight-range__range::-webkit-slider-runnable-track {\n  opacity: 0;\n}\n\n.weight-range__track {\n  position: absolute;\n  left: 0;\n  top: 0;\n  height: var(--track-height);\n  width: var(--range-position);\n  background: RGB(var(---color--brand-1--rgb));\n  border-radius: 100px;\n  pointer-events: none;\n}\n\n.weight-range__thumb-icon {\n  height: var(--thumb-icon-size);\n  width: var(--thumb-icon-size);\n  background-image: url(https://cdn.shopify.com/s/files/1/1683/1605/files/pup-thumb.png?v=1719171809);\n  background-size: contain;\n  background-position: center;\n  background-repeat: no-repeat;\n}\n\n.weight-range__thumb-value {\n  display: inline-flex;\n  padding: 0em 0.5em;\n  border-radius: 8px;\n  background-color: RGB(var(---color--brand-1--rgb));\n  outline: 2px solid RGB(var(--text-color));\n  position: absolute;\n  top: 0px;\n  transform: translateY(-100%);\n}\n\n.weight-range__labels {\n  display: flex;\n  justify-content: space-between;\n  margin-top: 0.25em;\n  pointer-events: none;\n  -webkit-user-select: none;\n     -moz-user-select: none;\n          user-select: none;\n}\n\n.popover,\n.mobile-toolbar {\n  background: var(---background-color--content-1) !important;\n}\n\n.nav-dropdown {\n  z-index: 1;\n  background: RGB(var(--background));\n}\n\n.button {\n  line-height: 1.2;\n}\n\n.button-wrapper.button-wrapper--vertical {\n  display: flex;\n  align-items: center;\n  gap: 0.5em;\n  flex-direction: column;\n}\n\nhr, .hr {\n  width: 100%;\n  margin: 2em auto;\n  border-width: 0.5px;\n  border-bottom: 0;\n  border-style: solid;\n  border-color: var(---color-line);\n}\nhr.hr--light, .hr.hr--light {\n  border-color: var(---color-line--light);\n}\nhr.hr--dark, .hr.hr--dark {\n  border-color: var(---color-line--dark);\n}\nhr.hr--clear, .hr.hr--clear {\n  border-color: transparent;\n}\nhr.hr--small, .hr.hr--small {\n  margin: 1em 0;\n}\nhr.hr--xsmall, .hr.hr--xsmall {\n  margin: 0.5em 0;\n}\nhr.hr--narrow, .hr.hr--narrow {\n  max-width: 70px;\n  margin-left: auto !important;\n  margin-right: auto !important;\n}\n\n[data-tooltip]:before {\n  font-size: var(---font-size-body-xs) !important;\n}\n\n.account-link-current {\n  color: var(---color--highlight);\n}\n\n/* 9. Apps  */\n.product-form__buy-buttons .awt-style-1 .bundleapp-wrapper {\n  margin: 0 !important;\n}\n.product-form__buy-buttons .awt-style-1 .bundleapp-wrapper .bundleapp-plan-selector-group {\n  border-radius: 12px;\n  border: 0;\n  border: 2px solid RGB(var(--border-color));\n  transition: 0.25s border, 0.25s background;\n}\n.product-form__buy-buttons .awt-style-1 .bundleapp-wrapper .bundleapp-plan-selector-group > label {\n  font-weight: 800;\n  color: var(--text-color);\n}\n.product-form__buy-buttons .awt-style-1 .bundleapp-wrapper .bundleapp-plan-selector-group.bundleapp-plan-selector-group--selected {\n  background: var(---color--highlight);\n}\n.product-form__buy-buttons .awt-style-1 .bundleapp-wrapper .bundleapp-plan-selector-description {\n  line-height: 1.2;\n}\n.product-form__buy-buttons .awt-style-1 .bundleapp-wrapper .bundleapp-plan-selector-description span {\n  background: var(---background-color--content-1);\n  font-weight: 400;\n  padding: 20px;\n  border: 0;\n}\n.product-form__buy-buttons .awt-style-1 .bundleapp-wrapper .bundleapp-plan-selector-plan {\n  margin: 0;\n  padding: 0.5em;\n}\n.product-form__buy-buttons .awt-style-1 .bundleapp-wrapper .bundleapp-plan-selector-plan label {\n  font-size: 0.9em;\n}\n.product-form__buy-buttons .awt-style-1 .bundleapp-wrapper .bundleapp-plan-selector-select {\n  padding: 0.4em 0.8em !important;\n  margin: 0 !important;\n  border-radius: 12px;\n  border: 2px solid RGB(var(--border-color)) !important;\n}\n\n/* ---------- Marquee Text ---------- */\n.section .marquee-horizontal {\n  z-index: 3;\n}\n\n/* 10. Utility Classes */\n/*================ Build Base Grid Classes ================*/\n.shown {\n  display: block !important;\n}\n\n.hidden {\n  display: none !important;\n}\n\n.text-left {\n  text-align: left !important;\n}\n\n.text-right {\n  text-align: right !important;\n}\n\n.text-center {\n  text-align: center !important;\n}\n\n/*================ Build Responsive Grid Classes ================*/\n@media only screen and (min-width: 741px) and (max-width: 999px) {\n  .small--shown {\n    display: block !important;\n  }\n  .small--hidden {\n    display: none !important;\n  }\n  .small--text-left {\n    text-align: left !important;\n  }\n  .small--text-right {\n    text-align: right !important;\n  }\n  .small--text-center {\n    text-align: center !important;\n  }\n  .br--small {\n    display: block;\n  }\n}\n@media only screen and (min-width: 741px) {\n  .small-up--shown {\n    display: block !important;\n  }\n  .small-up--hidden {\n    display: none !important;\n  }\n  .small-up--text-left {\n    text-align: left !important;\n  }\n  .small-up--text-right {\n    text-align: right !important;\n  }\n  .small-up--text-center {\n    text-align: center !important;\n  }\n  .br--small-up {\n    display: block;\n  }\n}\n@media only screen and (max-width: 740px) {\n  .small-down--shown {\n    display: block !important;\n  }\n  .small-down--hidden {\n    display: none !important;\n  }\n  .small-down--text-left {\n    text-align: left !important;\n  }\n  .small-down--text-right {\n    text-align: right !important;\n  }\n  .small-down--text-center {\n    text-align: center !important;\n  }\n  .br--small-down {\n    display: block;\n  }\n}\n@media only screen and (min-width: 1001px) and (max-width: 1199px) {\n  .medium--shown {\n    display: block !important;\n  }\n  .medium--hidden {\n    display: none !important;\n  }\n  .medium--text-left {\n    text-align: left !important;\n  }\n  .medium--text-right {\n    text-align: right !important;\n  }\n  .medium--text-center {\n    text-align: center !important;\n  }\n  .br--medium {\n    display: block;\n  }\n}\n@media only screen and (min-width: 1001px) {\n  .medium-up--shown {\n    display: block !important;\n  }\n  .medium-up--hidden {\n    display: none !important;\n  }\n  .medium-up--text-left {\n    text-align: left !important;\n  }\n  .medium-up--text-right {\n    text-align: right !important;\n  }\n  .medium-up--text-center {\n    text-align: center !important;\n  }\n  .br--medium-up {\n    display: block;\n  }\n}\n@media only screen and (max-width: 1000px) {\n  .medium-down--shown {\n    display: block !important;\n  }\n  .medium-down--hidden {\n    display: none !important;\n  }\n  .medium-down--text-left {\n    text-align: left !important;\n  }\n  .medium-down--text-right {\n    text-align: right !important;\n  }\n  .medium-down--text-center {\n    text-align: center !important;\n  }\n  .br--medium-down {\n    display: block;\n  }\n}\n@media only screen and (min-width: 1201px) and (max-width: 1399px) {\n  .large--shown {\n    display: block !important;\n  }\n  .large--hidden {\n    display: none !important;\n  }\n  .large--text-left {\n    text-align: left !important;\n  }\n  .large--text-right {\n    text-align: right !important;\n  }\n  .large--text-center {\n    text-align: center !important;\n  }\n  .br--large {\n    display: block;\n  }\n}\n@media only screen and (min-width: 1201px) {\n  .large-up--shown {\n    display: block !important;\n  }\n  .large-up--hidden {\n    display: none !important;\n  }\n  .large-up--text-left {\n    text-align: left !important;\n  }\n  .large-up--text-right {\n    text-align: right !important;\n  }\n  .large-up--text-center {\n    text-align: center !important;\n  }\n  .br--large-up {\n    display: block;\n  }\n}\n@media only screen and (max-width: 1200px) {\n  .large-down--shown {\n    display: block !important;\n  }\n  .large-down--hidden {\n    display: none !important;\n  }\n  .large-down--text-left {\n    text-align: left !important;\n  }\n  .large-down--text-right {\n    text-align: right !important;\n  }\n  .large-down--text-center {\n    text-align: center !important;\n  }\n  .br--large-down {\n    display: block;\n  }\n}\n@media only screen and (min-width: 1401px) and (max-width: 1399px) {\n  .wide--shown {\n    display: block !important;\n  }\n  .wide--hidden {\n    display: none !important;\n  }\n  .wide--text-left {\n    text-align: left !important;\n  }\n  .wide--text-right {\n    text-align: right !important;\n  }\n  .wide--text-center {\n    text-align: center !important;\n  }\n  .br--wide {\n    display: block;\n  }\n}\n@media only screen and (min-width: 1401px) {\n  .wide-up--shown {\n    display: block !important;\n  }\n  .wide-up--hidden {\n    display: none !important;\n  }\n  .wide-up--text-left {\n    text-align: left !important;\n  }\n  .wide-up--text-right {\n    text-align: right !important;\n  }\n  .wide-up--text-center {\n    text-align: center !important;\n  }\n  .br--wide-up {\n    display: block;\n  }\n}\n@media only screen and (max-width: 1400px) {\n  .wide-down--shown {\n    display: block !important;\n  }\n  .wide-down--hidden {\n    display: none !important;\n  }\n  .wide-down--text-left {\n    text-align: left !important;\n  }\n  .wide-down--text-right {\n    text-align: right !important;\n  }\n  .wide-down--text-center {\n    text-align: center !important;\n  }\n  .br--wide-down {\n    display: block;\n  }\n}\n.clearfix {\n  *zoom: 1;\n}\n.clearfix::after {\n  content: \"\";\n  display: table;\n  clear: both;\n}\n\n.fallback-text,\n.visually-hidden {\n  position: absolute !important;\n  overflow: hidden;\n  clip: rect(0 0 0 0);\n  height: 1px;\n  width: 1px;\n  max-width: 1px;\n  max-height: 1px;\n  font-size: 0;\n  margin: -1px;\n  padding: 0;\n  border: 0;\n}\n\n.hidden {\n  display: none;\n}\n\n.flex {\n  display: flex;\n}\n\n.inline-flex {\n  display: flex;\n}\n\n.align-start {\n  align-items: flex-start;\n}\n\n.align-center {\n  align-items: center;\n}\n\n.align-end {\n  align-items: flex-end;\n}\n\n.justify-start {\n  justify-items: flex-start;\n}\n\n.justify-center {\n  justify-items: center;\n}\n\n.justify-end {\n  justify-items: flex-end;\n}\n\n.gap-05 {\n  gap: 0.5em;\n}\n\n.uppercase,\n.text-transform--uppercase {\n  text-transform: uppercase !important;\n}\n\n.text-transform--none {\n  text-transform: none !important;\n}\n\n.strikethrough {\n  text-decoration: line-through;\n}\n\n.background-color--default {\n  background: var(---color--default);\n}\n\n.background-color--primary {\n  background: var(---color--primary);\n}\n\n.background-color--secondary {\n  background: var(---color--secondary);\n}\n\n.background-color--tertiary {\n  background: var(---color--tertiary);\n}\n\n.background-color--success {\n  background: var(---color--success);\n}\n\n.background-color--warning {\n  background: var(---color--warning);\n}\n\n.background-color--danger {\n  background: var(---color--danger);\n}\n\n.background-color--info {\n  background: var(---color--info);\n}\n\n.background-color--link {\n  background: var(---color--link);\n}\n\n.color--default {\n  color: var(---color--default);\n}\n\n.color--primary {\n  color: var(---color--primary);\n}\n\n.color--secondary {\n  color: var(---color--secondary);\n}\n\n.color--tertiary {\n  color: var(---color--tertiary);\n}\n\n.color--success {\n  color: var(---color--success);\n}\n\n.color--warning {\n  color: var(---color--warning);\n}\n\n.color--danger {\n  color: var(---color--danger);\n}\n\n.color--info {\n  color: var(---color--info);\n}\n\n.color--link {\n  color: var(---color--link);\n}\n\n.justify-content-center {\n  justify-content: center !important;\n}\n\n.object-position--top {\n  -o-object-position: top !important;\n     object-position: top !important;\n}\n\n.object-position--bottom {\n  -o-object-position: bottom !important;\n     object-position: bottom !important;\n}\n\n.object-position--center {\n  -o-object-position: center !important;\n     object-position: center !important;\n}\n\n.object-position--left {\n  -o-object-position: left !important;\n     object-position: left !important;\n}\n\n.object-position--right {\n  -o-object-position: right !important;\n     object-position: right !important;\n}\n\n.text-align--center {\n  text-align: center !important;\n}\n\n.text-align--left {\n  text-align: left !important;\n}\n\n.text-align--right {\n  text-align: right !important;\n}\n\n@media only screen and (max-width: 740px) {\n  .text-align--center--mobile {\n    text-align: center !important;\n  }\n}\n\n@media only screen and (max-width: 740px) {\n  .text-align--left--mobile {\n    text-align: left !important;\n  }\n}\n\n@media only screen and (max-width: 740px) {\n  .text-align--right--mobile {\n    text-align: right !important;\n  }\n}\n\n.nowrap {\n  white-space: nowrap;\n}\n\n.no-margin {\n  margin: 0 !important;\n}\n\n.no-margin--top {\n  margin-top: 0 !important;\n}\n\n.no-margin--right {\n  margin-right: 0 !important;\n}\n\n.no-margin--left {\n  margin-left: 0 !important;\n}\n\n.no-margin--bottom {\n  margin-bottom: 0 !important;\n}\n\n.no-padding {\n  padding: 0 !important;\n}\n\n.no-padding--top {\n  padding-top: 0 !important;\n}\n\n.no-padding--right {\n  padding-right: 0 !important;\n}\n\n.no-padding--left {\n  padding-left: 0 !important;\n}\n\n.no-padding--bottom {\n  padding-bottom: 0 !important;\n}\n\n.padding-left--10 {\n  padding-left: 10px !important;\n}\n\n.padding-left--20 {\n  padding-left: 20px !important;\n}\n\n.padding-left--30 {\n  padding-left: 30px !important;\n}\n\n.padding-left--40 {\n  padding-left: 40px !important;\n}\n\n.padding-left--50 {\n  padding-left: 50px !important;\n}\n\n.padding-right--10 {\n  padding-right: 10px !important;\n}\n\n.padding-right--20 {\n  padding-right: 20px !important;\n}\n\n.padding-right--30 {\n  padding-right: 30px !important;\n}\n\n.padding-right--40 {\n  padding-right: 40px !important;\n}\n\n.padding-right--50 {\n  padding-right: 50px !important;\n}\n\n.padding-top--10 {\n  padding-top: 10px !important;\n}\n\n.padding-top--20 {\n  padding-top: 20px !important;\n}\n\n.padding-top--30 {\n  padding-top: 30px !important;\n}\n\n.padding-top--40 {\n  padding-top: 40px !important;\n}\n\n.padding-top--50 {\n  padding-top: 50px !important;\n}\n\n.padding-bottom--10 {\n  padding-bottom: 10px !important;\n}\n\n.padding-bottom--20 {\n  padding-bottom: 20px !important;\n}\n\n.padding-bottom--30 {\n  padding-bottom: 30px !important;\n}\n\n.padding-bottom--40 {\n  padding-bottom: 40px !important;\n}\n\n.padding-bottom--50 {\n  padding-bottom: 50px !important;\n}\n\nbody.logged-in .logged-in--hidden {\n  display: none !important;\n}\n\nbody.logged-out .logged-out--hidden {\n  display: none !important;\n}\n\n.fraction {\n  margin-left: 0.25em;\n  font-size: 0.75em;\n  letter-spacing: -0.1em;\n}\n\n/* 11. Third-Party Styles */\n.nice-select:active,\n.nice-select.open,\n.nice-select:focus {\n  border-color: rgba(var(--text-color), 0.5);\n}\n\n.nice-select:after {\n  border-bottom: 2px solid #999;\n  border-right: 2px solid #999;\n  content: \"\";\n  display: block;\n  height: 10px;\n  width: 10px;\n  margin-top: -6px;\n  pointer-events: none;\n  position: absolute;\n  right: 17px;\n  top: 50%;\n  transform-origin: center;\n  transform: rotate(45deg);\n  transition: all 0.15s ease-in-out;\n}\n\n.nice-select.open:after {\n  transform: rotate(-135deg);\n}\n\n.nice-select.open .nice-select-dropdown {\n  opacity: 1;\n  pointer-events: auto;\n  transform: scale(1) translateY(0);\n}\n\n.nice-select.disabled {\n  border-color: #ededed;\n  color: rgba(var(--text-color), 0.5);\n  pointer-events: none;\n}\n\n.nice-select.disabled:after {\n  border-color: rgba(var(--text-color), 0.5);\n}\n\n.nice-select.wide {\n  width: 100%;\n}\n\n.nice-select.wide .nice-select-dropdown {\n  left: 0 !important;\n  right: 0 !important;\n}\n\n.nice-select.right {\n  float: right;\n}\n\n.nice-select.right .nice-select-dropdown {\n  left: auto;\n  right: 0;\n}\n\n.nice-select .nice-select-dropdown {\n  width: 100%;\n  margin-top: 4px;\n  background-color: rgba(var(--section-background));\n  border-radius: 5px;\n  box-shadow: 0 0 0 1px rgba(68, 68, 68, 0.11);\n  pointer-events: none;\n  position: absolute;\n  top: 100%;\n  left: 0;\n  transform-origin: 50% 0;\n  transform: scale(0.75) translateY(19px);\n  transition: all 0.2s cubic-bezier(0.5, 0, 0, 1.25), opacity 0.15s ease-out;\n  z-index: 9;\n  opacity: 0;\n}\n\n.nice-select .list {\n  border-radius: 5px;\n  box-sizing: border-box;\n  overflow: hidden;\n  padding: 0;\n  max-height: 210px;\n  overflow-y: auto;\n}\n.nice-select .list li:first-child {\n  display: none !important;\n}\n\n.nice-select .option {\n  cursor: pointer;\n  font-weight: 400;\n  list-style: none;\n  outline: none;\n  padding-left: 18px;\n  padding-right: 29px;\n  text-align: left;\n  transition: all 0.2s;\n}\n\n.nice-select .option.selected {\n  font-weight: bold;\n}\n\n.nice-select .option.disabled {\n  background-color: rgba(0, 0, 0, 0);\n  color: #999;\n  cursor: default;\n}\n\n.nice-select .optgroup {\n  font-weight: bold;\n}\n\n.no-csspointerevents .nice-select .nice-select-dropdown {\n  display: none;\n}\n\n.no-csspointerevents .nice-select.open .nice-select-dropdown {\n  display: block;\n}\n\n.nice-select .has-multiple {\n  white-space: inherit;\n  height: auto;\n  padding: 7px 12px;\n  min-height: 36px;\n  line-height: 22px;\n}\n\n.nice-select .has-multiple span.current {\n  border: 1px solid rgba(var(--text-color), 0.5);\n  background: rgba(var(--section-background), 0.8);\n  padding: 0 10px;\n  border-radius: 3px;\n  display: inline-block;\n  line-height: 24px;\n  font-size: 14px;\n  margin-bottom: 3px;\n  margin-right: 3px;\n}\n\n.nice-select .has-multiple .multiple-options {\n  display: block;\n  line-height: 24px;\n  padding: 0;\n}\n\n.nice-select .nice-select-search-box {\n  box-sizing: border-box;\n  width: 100%;\n  padding: 5px;\n  pointer-events: none;\n  border-radius: 5px 5px 0 0;\n}\n\n.nice-select .nice-select-search {\n  box-sizing: border-box;\n  background-color: rgba(var(--section-background), 0.8);\n  border: 1px solid rgba(var(--text-color), 0.5);\n  border-radius: 3px;\n  color: rgba(var(--text-color), 1);\n  display: inline-block;\n  vertical-align: middle;\n  padding: 7px 12px;\n  margin: 0 10px 0 0;\n  width: 100%;\n  min-height: 36px;\n  line-height: 22px;\n  height: auto;\n  outline: 0 !important;\n}\n\n/* ------ Custom ------ */\nstyled-select select {\n  position: absolute !important;\n  overflow: hidden;\n  clip: rect(0 0 0 0);\n  height: 1px;\n  width: 1px;\n  max-width: 1px;\n  max-height: 1px;\n  font-size: 0;\n  margin: -1px;\n  padding: 0;\n  border: 0;\n}\nstyled-select > div > select {\n  display: none !important;\n}\nstyled-select div.nice-select {\n  float: none;\n}\n\n.nice-select {\n  --section-background: 255, 255, 255;\n  display: flex;\n  justify-content: flex-start;\n  height: 100%;\n  width: 100%;\n  padding: 0.5em 2em 0.5em 1em;\n  border: 1px solid rgba(var(--text-color), 0.25);\n  background-color: rgba(var(--section-background), 0.8);\n  border-radius: var(--button-border-radius);\n  text-align: left;\n}\n.nice-select:hover {\n  background-color: rgba(var(--section-background), 1);\n  border: 1px solid rgba(var(--text-color), 0.5);\n}\n.nice-select:focus {\n  border: 1px solid rgba(var(--text-color), 0.5);\n}\n.nice-select.nice-select--position-top .nice-select-dropdown {\n  bottom: calc(100% + 10px);\n  top: unset;\n}\n.nice-select .current {\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n.nice-select .list {\n  border-radius: 0;\n}\n.nice-select .option {\n  padding: 0.5em 1em 0.5em 1em;\n  line-height: 1.2;\n  color: rgba(var(--text-color), 0.7);\n}\n.nice-select .option[data-value=\"Not Listed\"] {\n  color: rgba(var(--text-color), 1);\n  font-style: italic;\n  font-weight: var(--text-font-bold-weight) !important;\n}\n.nice-select .nice-select-dropdown {\n  border-radius: var(--block-border-radius);\n}\n.nice-select .nice-select-search-box input {\n  border-radius: 6px;\n  border: 1px solid #e6e6e6;\n  padding: 0.7em calc(1em - 5px) 0.7em calc(1em - 5px);\n}\n\n.cart-vet-partner-select .nice-select-dropdown .option.null.disabled {\n  text-transform: uppercase;\n  color: RGB(var(--text-color));\n  font-weight: 700 !important;\n}\n.cart-vet-partner-select .nice-select-dropdown .option.null.disabled:hover {\n  background: transparent !important;\n}\n.cart-vet-partner-select .nice-select-dropdown .option.null.disabled:hover:after {\n  display: none !important;\n}\n\n.nice-select {\n  --section-background: var();\n  cursor: pointer;\n  position: relative;\n}\n.nice-select .nice-select-dropdown {\n  cursor: default;\n  box-shadow: 0 0 10px rgba(0, 0, 0, 0.25);\n}\n.nice-select .nice-select-dropdown .list {\n  margin-top: 0;\n}\n.nice-select .nice-select-dropdown .list .option {\n  position: relative;\n}\n.nice-select .nice-select-dropdown .list .option.null {\n  font-weight: normal;\n}\n.nice-select .nice-select-dropdown .list .option:after {\n  content: \"\";\n  display: block;\n  position: absolute;\n  left: 0;\n  top: 0;\n  width: 100%;\n  height: 100%;\n  background: rgba(0, 0, 0, 0.1);\n  opacity: 0;\n  z-index: -1;\n}\n.nice-select .nice-select-dropdown .list .option:hover:after {\n  opacity: 1;\n}\n\n/* 12. Animations */\n@keyframes fade-in {\n  0% {\n    opacity: 0;\n  }\n  100% {\n    opacity: 1;\n  }\n}\n@keyframes fade-out {\n  0% {\n    opacity: 1;\n  }\n  100% {\n    opacity: 0;\n  }\n}\n@keyframes rotate {\n  0% {\n    transform: rotate(0deg);\n  }\n  100% {\n    transform: rotate(360deg);\n  }\n}\n/* ----------------------------------------------\n * Generated by Animista on 2025-4-24 16:5:49\n * Licensed under FreeBSD License.\n * See http://animista.net/license for more info. \n * w: http://animista.net, t: @cssanimista\n * ---------------------------------------------- */\n/**\n * ----------------------------------------\n * animation heartbeat\n * ----------------------------------------\n */\n@keyframes heartbeat {\n  from {\n    transform: scale(1);\n    transform-origin: center center;\n    animation-timing-function: ease-out;\n  }\n  10% {\n    transform: scale(0.91);\n    animation-timing-function: ease-in;\n  }\n  17% {\n    transform: scale(0.98);\n    animation-timing-function: ease-out;\n  }\n  33% {\n    transform: scale(0.87);\n    animation-timing-function: ease-in;\n  }\n  45% {\n    transform: scale(1);\n    animation-timing-function: ease-out;\n  }\n}\n/* ----------------------------------------------\n * Generated by Animista on 2025-4-24 16:5:49\n * Licensed under FreeBSD License.\n * See http://animista.net/license for more info. \n * w: http://animista.net, t: @cssanimista\n * ---------------------------------------------- */\n/**\n * ----------------------------------------\n * animation heartbeat\n * ----------------------------------------\n */\n@keyframes heartbeat {\n  from {\n    transform: scale(1);\n    transform-origin: center center;\n    animation-timing-function: ease-out;\n  }\n  10% {\n    transform: scale(0.91);\n    animation-timing-function: ease-in;\n  }\n  17% {\n    transform: scale(0.98);\n    animation-timing-function: ease-out;\n  }\n  33% {\n    transform: scale(0.87);\n    animation-timing-function: ease-in;\n  }\n  45% {\n    transform: scale(1);\n    animation-timing-function: ease-out;\n  }\n}\n.heartbeat {\n  animation: heartbeat 1.5s ease-in-out both;\n}\n\n/* ----------------------------------------------\n * Generated by Animista on 2025-4-24 16:9:18\n * Licensed under FreeBSD License.\n * See http://animista.net/license for more info. \n * w: http://animista.net, t: @cssanimista\n * ---------------------------------------------- */\n/**\n * ----------------------------------------\n * animation shake-horizontal\n * ----------------------------------------\n */\n@keyframes shake-horizontal {\n  0%, 100% {\n    transform: translateX(0);\n  }\n  10%, 30%, 50%, 70% {\n    transform: translateX(-5px);\n  }\n  20%, 40%, 60% {\n    transform: translateX(5px);\n  }\n  80% {\n    transform: translateX(3px);\n  }\n  90% {\n    transform: translateX(-3px);\n  }\n}\n.shake-horizontal {\n  animation: shake-horizontal 0.8s cubic-bezier(0.455, 0.03, 0.515, 0.955) both;\n}", ".nice-select:active,\n.nice-select.open,\n.nice-select:focus {\n  border-color: rgba(var(--text-color), 0.5);\n}\n\n.nice-select:after {\n  border-bottom: 2px solid #999;\n  border-right: 2px solid #999;\n  content: \"\";\n  display: block;\n  height: 10px;\n  width: 10px;\n  margin-top: -6px;\n  pointer-events: none;\n  position: absolute;\n  right: 17px;\n  top: 50%;\n  transform-origin: center;\n  transform: rotate(45deg);\n  transition: all .15s ease-in-out;\n}\n\n.nice-select.open:after {\n  transform: rotate(-135deg)\n}\n\n.nice-select.open .nice-select-dropdown {\n  opacity: 1;\n  pointer-events: auto;\n  transform: scale(1) translateY(0)\n}\n\n.nice-select.disabled {\n  border-color: #ededed;\n  color: rgba(var(--text-color), 0.5);\n  pointer-events: none\n}\n\n.nice-select.disabled:after {\n  border-color: rgba(var(--text-color), 0.5);\n}\n\n.nice-select.wide {\n  width: 100%\n}\n\n.nice-select.wide .nice-select-dropdown {\n  left: 0 !important;\n  right: 0 !important\n}\n\n.nice-select.right {\n  float: right\n}\n\n.nice-select.right .nice-select-dropdown {\n  left: auto;\n  right: 0\n}\n\n.nice-select .nice-select-dropdown {\n  width: 100%;\n  margin-top: 4px;\n  background-color: rgba(var(--section-background));\n  border-radius: 5px;\n  box-shadow: 0 0 0 1px rgba(68, 68, 68, .11);\n  pointer-events: none;\n  position: absolute;\n  top: 100%;\n  left: 0;\n  transform-origin: 50% 0;\n  transform: scale(0.75) translateY(19px);\n  transition: all .2s cubic-bezier(0.5, 0, 0, 1.25), opacity .15s ease-out;\n  z-index: 9;\n  opacity: 0\n}\n\n.nice-select .list {\n  border-radius: 5px;\n  box-sizing: border-box;\n  overflow: hidden;\n  padding: 0;\n  max-height: 210px;\n  overflow-y: auto;\n\n  li:first-child {\n    display: none !important;  \n  }\n\n}\n\n.nice-select .option {\n  cursor: pointer;\n  font-weight: 400;\n  list-style: none;\n  outline: none;\n  padding-left: 18px;\n  padding-right: 29px;\n  text-align: left;\n  transition: all .2s\n}\n\n.nice-select .option.selected {\n  font-weight: bold\n}\n\n.nice-select .option.disabled {\n  background-color: rgba(0, 0, 0, 0);\n  color: #999;\n  cursor: default\n}\n\n.nice-select .optgroup {\n  font-weight: bold\n}\n\n.no-csspointerevents .nice-select .nice-select-dropdown {\n  display: none\n}\n\n.no-csspointerevents .nice-select.open .nice-select-dropdown {\n  display: block\n}\n\n.nice-select .list::-webkit-scrollbar {\n  // width: 0\n}\n\n.nice-select .has-multiple {\n  white-space: inherit;\n  height: auto;\n  padding: 7px 12px;\n  min-height: 36px;\n  line-height: 22px\n}\n\n.nice-select .has-multiple span.current {\n  border: 1px solid rgba(var(--text-color), 0.5);\n  background: rgba(var(--section-background), 0.8);\n  padding: 0 10px;\n  border-radius: 3px;\n  display: inline-block;\n  line-height: 24px;\n  font-size: 14px;\n  margin-bottom: 3px;\n  margin-right: 3px\n}\n\n.nice-select .has-multiple .multiple-options {\n  display: block;\n  line-height: 24px;\n  padding: 0\n}\n\n.nice-select .nice-select-search-box {\n  box-sizing: border-box;\n  width: 100%;\n  padding: 5px;\n  pointer-events: none;\n  border-radius: 5px 5px 0 0\n}\n\n.nice-select .nice-select-search {\n  box-sizing: border-box;\n  background-color: rgba(var(--section-background), 0.8);\n  border: 1px solid rgba(var(--text-color), 0.5);\n  border-radius: 3px;\n  color: rgba(var(--text-color), 1);\n  display: inline-block;\n  vertical-align: middle;\n  padding: 7px 12px;\n  margin: 0 10px 0 0;\n  width: 100%;\n  min-height: 36px;\n  line-height: 22px;\n  height: auto;\n  outline: 0 !important;\n}\n\n\n/* ------ Custom ------ */\n\n\nstyled-select {\n  select {\n    @include visually-hidden;\n  }\n\n  >div {\n    >select {\n      display: none !important;\n    }\n  }\n\n  div.nice-select {\n    float: none;\n  }\n}\n\n.nice-select {\n\n  --section-background: 255, 255, 255;\n\n  display: flex;\n  justify-content: flex-start;\n\n  height: 100%;\n  width: 100%;\n  padding: .5em 2em .5em 1em;\n\n  border: 1px solid rgba(var(--text-color), 0.25);\n  background-color: rgba(var(--section-background), 0.8);\n  border-radius: var(--button-border-radius);\n\n  text-align: left;\n\n  &:hover {\n    background-color: rgba(var(--section-background), 1);\n    border: 1px solid rgba(var(--text-color), 0.5);\n  }\n\n  &:focus {\n    border: 1px solid rgba(var(--text-color), 0.5);\n  }\n\n  &.nice-select--position-top {\n    .nice-select-dropdown {\n      bottom: calc(100% + 10px);\n      top: unset;\n    }\n  }\n\n  .current {\n    white-space: nowrap;\n    overflow: hidden;\n    text-overflow: ellipsis;\n  }\n\n  .list {\n    border-radius: 0;\n  }\n\n  .option {\n    padding: 0.5em 1em 0.5em 1em;\n    line-height: 1.2;\n    color: rgba(var(--text-color), .7);\n\n    &[data-value=\"Not Listed\"] {\n      color: rgba(var(--text-color), 1);\n      font-style: italic;\n      font-weight: var(--text-font-bold-weight) !important;\n      // margin: 5px;\n      // background: rgba(var(--text-color), .1);\n    }\n  }\n\n  .nice-select-dropdown {\n    border-radius: var(--block-border-radius);\n  }\n\n  .nice-select-search {}\n\n\n  .nice-select-search-box {\n    input {\n      border-radius: 6px;\n      border: 1px solid #e6e6e6;\n      padding: .7em calc(1em - 5px) .7em calc(1em - 5px);\n    }\n  }\n\n}\n\n.cart-vet-partner-select {\n  // For state option group headings\n  .nice-select-dropdown {\n    .option.null.disabled {\n      text-transform: uppercase;\n      color: RGB(var(--text-color));\n      font-weight: 700 !important;\n\n      &:hover {\n        background: transparent !important;\n\n        &:after {\n          display: none !important;\n        }\n      }\n    }\n  }\n}\n\n\n.nice-select {\n\n  --section-background: var();\n\n  cursor: pointer;\n  position: relative;\n  \n  .nice-select-dropdown {\n    cursor: default;\n    box-shadow: 0 0 10px rgba(0, 0, 0, .25);\n\n    .list {\n      margin-top: 0;\n\n      .option {\n        position: relative;\n\n        &.null {\n          font-weight: normal;\n        }\n\n        &:after {\n          content: \"\";\n          display: block;\n          position: absolute;\n          left: 0;\n          top: 0;\n          width: 100%;\n          height: 100%;\n          background: rgba(0, 0, 0, 0.1);\n          opacity: 0;\n          z-index: -1;\n        }\n\n        &:hover {\n          &:after {\n            opacity: 1;\n          }\n        }\n      }\n    }\n  }\n}\n\n\n\n", "@keyframes fade-in {\n  0% {\n    opacity: 0;\n  }\n  100% {\n    opacity: 1;\n  }\n}\n\n@keyframes fade-out {\n  0% {\n    opacity: 1;\n  }\n  100% {\n    opacity: 0;\n  }\n}\n\n@keyframes rotate {\n\n  0% {\n    transform: rotate(0deg);\n  }\n  100% {\n    transform: rotate(360deg);\n  }\n\n}\n\n/* ----------------------------------------------\n * Generated by Animista on 2025-4-24 16:5:49\n * Licensed under FreeBSD License.\n * See http://animista.net/license for more info. \n * w: http://animista.net, t: @cssanimista\n * ---------------------------------------------- */\n\n/**\n * ----------------------------------------\n * animation heartbeat\n * ----------------------------------------\n */\n@-webkit-keyframes heartbeat {\n  from {\n    -webkit-transform: scale(1);\n            transform: scale(1);\n    -webkit-transform-origin: center center;\n            transform-origin: center center;\n    -webkit-animation-timing-function: ease-out;\n            animation-timing-function: ease-out;\n  }\n  10% {\n    -webkit-transform: scale(0.91);\n            transform: scale(0.91);\n    -webkit-animation-timing-function: ease-in;\n            animation-timing-function: ease-in;\n  }\n  17% {\n    -webkit-transform: scale(0.98);\n            transform: scale(0.98);\n    -webkit-animation-timing-function: ease-out;\n            animation-timing-function: ease-out;\n  }\n  33% {\n    -webkit-transform: scale(0.87);\n            transform: scale(0.87);\n    -webkit-animation-timing-function: ease-in;\n            animation-timing-function: ease-in;\n  }\n  45% {\n    -webkit-transform: scale(1);\n            transform: scale(1);\n    -webkit-animation-timing-function: ease-out;\n            animation-timing-function: ease-out;\n  }\n}\n@keyframes heartbeat {\n  from {\n    -webkit-transform: scale(1);\n            transform: scale(1);\n    -webkit-transform-origin: center center;\n            transform-origin: center center;\n    -webkit-animation-timing-function: ease-out;\n            animation-timing-function: ease-out;\n  }\n  10% {\n    -webkit-transform: scale(0.91);\n            transform: scale(0.91);\n    -webkit-animation-timing-function: ease-in;\n            animation-timing-function: ease-in;\n  }\n  17% {\n    -webkit-transform: scale(0.98);\n            transform: scale(0.98);\n    -webkit-animation-timing-function: ease-out;\n            animation-timing-function: ease-out;\n  }\n  33% {\n    -webkit-transform: scale(0.87);\n            transform: scale(0.87);\n    -webkit-animation-timing-function: ease-in;\n            animation-timing-function: ease-in;\n  }\n  45% {\n    -webkit-transform: scale(1);\n            transform: scale(1);\n    -webkit-animation-timing-function: ease-out;\n            animation-timing-function: ease-out;\n  }\n}\n\n/* ----------------------------------------------\n * Generated by Animista on 2025-4-24 16:5:49\n * Licensed under FreeBSD License.\n * See http://animista.net/license for more info. \n * w: http://animista.net, t: @cssanimista\n * ---------------------------------------------- */\n\n/**\n * ----------------------------------------\n * animation heartbeat\n * ----------------------------------------\n */\n@-webkit-keyframes heartbeat {\n  from {\n    -webkit-transform: scale(1);\n    transform: scale(1);\n    -webkit-transform-origin: center center;\n    transform-origin: center center;\n    -webkit-animation-timing-function: ease-out;\n    animation-timing-function: ease-out;\n  }\n\n  10% {\n    -webkit-transform: scale(0.91);\n    transform: scale(0.91);\n    -webkit-animation-timing-function: ease-in;\n    animation-timing-function: ease-in;\n  }\n\n  17% {\n    -webkit-transform: scale(0.98);\n    transform: scale(0.98);\n    -webkit-animation-timing-function: ease-out;\n    animation-timing-function: ease-out;\n  }\n\n  33% {\n    -webkit-transform: scale(0.87);\n    transform: scale(0.87);\n    -webkit-animation-timing-function: ease-in;\n    animation-timing-function: ease-in;\n  }\n\n  45% {\n    -webkit-transform: scale(1);\n    transform: scale(1);\n    -webkit-animation-timing-function: ease-out;\n    animation-timing-function: ease-out;\n  }\n}\n\n@keyframes heartbeat {\n  from {\n    -webkit-transform: scale(1);\n    transform: scale(1);\n    -webkit-transform-origin: center center;\n    transform-origin: center center;\n    -webkit-animation-timing-function: ease-out;\n    animation-timing-function: ease-out;\n  }\n\n  10% {\n    -webkit-transform: scale(0.91);\n    transform: scale(0.91);\n    -webkit-animation-timing-function: ease-in;\n    animation-timing-function: ease-in;\n  }\n\n  17% {\n    -webkit-transform: scale(0.98);\n    transform: scale(0.98);\n    -webkit-animation-timing-function: ease-out;\n    animation-timing-function: ease-out;\n  }\n\n  33% {\n    -webkit-transform: scale(0.87);\n    transform: scale(0.87);\n    -webkit-animation-timing-function: ease-in;\n    animation-timing-function: ease-in;\n  }\n\n  45% {\n    -webkit-transform: scale(1);\n    transform: scale(1);\n    -webkit-animation-timing-function: ease-out;\n    animation-timing-function: ease-out;\n  }\n}\n\n.heartbeat {\n  -webkit-animation: heartbeat 1.5s ease-in-out both;\n  animation: heartbeat 1.5s ease-in-out both;\n}\n\n\n\n/* ----------------------------------------------\n * Generated by Animista on 2025-4-24 16:9:18\n * Licensed under FreeBSD License.\n * See http://animista.net/license for more info. \n * w: http://animista.net, t: @cssanimista\n * ---------------------------------------------- */\n\n/**\n * ----------------------------------------\n * animation shake-horizontal\n * ----------------------------------------\n */\n@-webkit-keyframes shake-horizontal {\n\n  0%,\n  100% {\n    -webkit-transform: translateX(0);\n    transform: translateX(0);\n  }\n\n  10%,\n  30%,\n  50%,\n  70% {\n    -webkit-transform: translateX(-10px);\n    transform: translateX(-10px);\n  }\n\n  20%,\n  40%,\n  60% {\n    -webkit-transform: translateX(10px);\n    transform: translateX(10px);\n  }\n\n  80% {\n    -webkit-transform: translateX(8px);\n    transform: translateX(8px);\n  }\n\n  90% {\n    -webkit-transform: translateX(-8px);\n    transform: translateX(-8px);\n  }\n}\n\n@keyframes shake-horizontal {\n\n  0%,\n  100% {\n    -webkit-transform: translateX(0);\n    transform: translateX(0);\n  }\n\n  10%,\n  30%,\n  50%,\n  70% {\n    -webkit-transform: translateX(-5px);\n    transform: translateX(-5px);\n  }\n\n  20%,\n  40%,\n  60% {\n    -webkit-transform: translateX(5px);\n    transform: translateX(5px);\n  }\n\n  80% {\n    -webkit-transform: translateX(3px);\n    transform: translateX(3px);\n  }\n\n  90% {\n    -webkit-transform: translateX(-3px);\n    transform: translateX(-3px);\n  }\n}\n\n\n.shake-horizontal {\n  -webkit-animation: shake-horizontal 0.8s cubic-bezier(0.455, 0.030, 0.515, 0.955) both;\n  animation: shake-horizontal 0.8s cubic-bezier(0.455, 0.030, 0.515, 0.955) both;\n}"]}