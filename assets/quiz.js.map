{"version": 3, "sources": ["quiz.js"], "names": ["__defProp", "Object", "defineProperty", "__defNormalProp", "obj", "key", "value", "enumerable", "configurable", "writable", "__publicField", "Delegate", "root", "this", "listenerMap", "handle", "prototype", "bind", "_removedListeners", "matchesTag", "tagName", "element", "toLowerCase", "matchesRoot", "selector", "rootElement", "window", "document", "documentElement", "matchesId", "id", "let", "eventType", "hasOwnProperty", "removeEventListener", "addEventListener", "captureForType", "indexOf", "on", "handler", "useCapture", "matcher", "matcher<PERSON><PERSON><PERSON>", "TypeError", "test", "slice", "Element", "matches", "push", "off", "i", "listener", "listenerList", "singleEventType", "length", "splice", "event", "l", "type", "target", "eventIgnore", "nodeType", "parentNode", "correspondingUseElement", "eventPhase", "currentTarget", "concat", "toFire", "hasAttribute", "call", "parentElement", "HTMLDocument", "ret", "fire", "apply", "preventDefault", "destroy", "main_default", "InputBindingManager", "constructor", "delegateElement", "body", "_onValueChanged", "boundElement", "getElementById", "getAttribute", "options", "selectedIndex", "innerHTML", "triggerEvent", "name", "data", "dispatchEvent", "CustomEvent", "bubbles", "detail", "triggerNonBubblingEvent", "formatMoney", "cents", "format", "replace", "placeholderRegex", "formatString", "themeVariables", "settings", "moneyFormat", "defaultTo", "value2", "defaultValue", "formatWithDelimiters", "number", "precision", "thousands", "decimal", "isNaN", "parts", "toFixed", "split", "match", "CustomHTMLElement", "HTMLElement", "super", "_hasSectionReloaded", "Shopify", "designMode", "rootDelegate", "parentSection", "closest", "load", "_rootDelegate", "delegate", "_delegate", "showLoadingBar", "hideLoadingBar", "untilVisible", "intersectionObserverOptions", "rootMargin", "threshold", "onBecameVisible", "classList", "add", "style", "opacity", "Promise", "IntersectionObserver", "intersectionObserver", "isIntersecting", "disconnect", "requestAnimationFrame", "resolve", "observe", "disconnectedCallback", "_a", "candidateSelectors", "candidate<PERSON><PERSON><PERSON>", "join", "msMatchesSelector", "webkitMatchesSelector", "getCandidates", "el", "<PERSON><PERSON><PERSON><PERSON>", "filter", "candidates", "Array", "querySelectorAll", "unshift", "isContentEditable", "node", "contentEditable", "getTabindex", "tabindexAttr", "parseInt", "nodeName", "tabIndex", "sortOrderedTabbables", "a", "b", "documentOrder", "isInput", "isHiddenInput", "isDetailsWithSummary", "children", "some", "child", "getCheckedRadio", "nodes", "form", "checked", "isTabbableRadio", "queryRadios", "radioScope", "ownerDocument", "CSS", "escape", "radioSet", "err", "console", "error", "message", "isRadio", "isNonTabbableRadio", "isHidden", "displayCheck", "getComputedStyle", "visibility", "nodeUnderDetails", "width", "_node$getBoundingClie", "getBoundingClientRect", "height", "display", "isDisabledFromFieldset", "disabled", "item", "contains", "isNodeMatchingSelectorFocusable", "isNodeMatchingSelectorTabbable", "tabbable", "regularTabbables", "orderedTabbables", "for<PERSON>ach", "candidate", "candidateTabindex", "sort", "map", "focusableCandidateSelector", "isFocusable", "Error", "ownKeys", "object", "enumerableOnly", "symbols", "keys", "getOwnPropertySymbols", "sym", "getOwnPropertyDescriptor", "_objectSpread2", "arguments", "source", "_defineProperty", "getOwnPropertyDescriptors", "defineProperties", "activeFocusTraps", "trapQueue", "activateTrap", "trap", "activeTrap", "pause", "trapIndex", "deactivateTrap", "unpause", "isSelectableInput", "select", "isEscapeEvent", "e", "keyCode", "isTabEvent", "delay", "fn", "setTimeout", "findIndex", "arr", "idx", "every", "valueOrHandler", "_len", "params", "_key", "getActualTarget", "shadowRoot", "<PERSON><PERSON><PERSON>", "createFocusTrap", "elements", "userOptions", "getOption", "configOverrideOptions", "optionName", "configOptionName", "config", "getNodeForOption", "optionValue", "_len2", "_key2", "doc", "querySelector", "updateTabbableNodes", "state", "tabbableGroups", "containers", "container", "tabbableNodes", "firstTabbableNode", "lastTabbableNode", "group", "getReturnFocusNode", "previousActiveElement", "addListeners", "active", "delayInitialFocusTimer", "delayInitialFocus", "tryFocus", "getInitialFocusNode", "checkFocusIn", "checkPointerDown", "capture", "passive", "checkClick", "<PERSON><PERSON><PERSON>", "removeListeners", "returnFocusOnDeactivate", "escapeDeactivates", "nodeFocusedBeforeActivation", "mostRecentlyFocusedNode", "paused", "containersContain", "firstTabbableGroup", "activeElement", "tryFocus2", "focus", "preventScroll", "clickOutsideDeactivates", "deactivate", "returnFocus", "allowOutsideClick", "targetContained", "Document", "stopImmediatePropagation", "checkTab", "lastOfGroupIndex", "_destinationGroupIndex", "destinationNode", "containerIndex", "_ref", "shift<PERSON>ey", "startOfGroupIndex", "_ref2", "destinationGroupIndex", "_ref3", "activate", "activateOptions", "onActivate", "onPostActivate", "checkCanFocusTrap", "finishActivation", "then", "deactivateOptions", "onPostDeactivate", "checkCanReturnFocus", "finishDeactivation", "clearTimeout", "onDeactivate", "updateContainerElements", "containerElements", "elementsAsArray", "Boolean", "CustomAnimation", "effect", "_effect", "_playState", "_finished", "finished", "animationEffects", "CustomKeyframeEffect", "cancel", "animationEffect", "finish", "play", "keyframes", "_animation", "Animation", "KeyframeEffect", "animate", "onfinish", "startTime", "GroupEffect", "childrenEffects", "_childrenEffects", "flatMap", "ParallelEffect", "promises", "all", "SequenceEffect", "async", "reject", "await", "exception", "RevealingForm", "connectedCallback", "inputs", "from", "actions", "submit", "_<PERSON><PERSON><PERSON><PERSON><PERSON>", "updateSubmit", "valid", "Templates", "quizResultsProduct", "product", "html", "quizVariables", "results", "results_product", "index", "product_id", "product_title", "title", "product_description", "shortDescription", "product_color", "productColor", "product_url", "url", "product_image_url", "quizResultImage", "reference", "image", "originalSrc", "images", "edges", "variant", "variants", "variant_first_id", "variant_first_price", "price", "Number", "amount", "variant_first_compare_price", "compareAtPrice", "variant_first_calories", "calories", "variant_last_id", "replaceAll", "quizResultsModal", "results_product_modal", "descriptionHtml", "onlineStoreUrl", "variant_id", "variant_price", "variant_compare_price", "quizStepTab", "attributes", "classes", "quizStepLineText", "text", "expandingInput", "placeholder", "RevealingFormInput", "revealingForm", "input", "inputOnInput", "prevInput", "hide", "nextInput", "isLastInput", "visible", "animating", "show", "nextInputsFull", "animation", "transform", "duration", "easing", "remove", "sibling", "nextElement<PERSON><PERSON>ling", "previousElementSibling", "SplitPageStep", "nextButtons", "prevButtons", "button", "gotoNextStep", "gotoPrevStep", "nextStep", "prevStep", "QuizSteps", "observedAttributes", "dogNames", "buttonBuildQuiz", "substepContainer", "_dogComplete", "next", "prev", "back", "skip<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_onClickUpdateDog", "_onChangeDogSex", "_onQuizNextStep", "_updateProgressBar", "_onQuizNextStepEnd", "_onQuizPrevStep", "_onQuizPrevStepEnd", "subNavigation", "_onQuizSubstepStart", "_onQuizSubstepArrive", "_onClickSubstepTab", "customer", "_onGuestEmailFormSubmit", "_onGuestEmailFormSkip", "_submitQuiz", "_buildQuiz", "_setupNavigation", "hideSubNavigation", "_updateNavigation", "_storeDogNames", "_buildSubteps", "_buildSubNavigation", "showSubNavigation", "createElement", "<PERSON><PERSON><PERSON>", "dogNamePossessive", "getPossessive", "dogNumber", "temporaryElement", "dogHTML", "steps", "dog", "dataNumber", "termsName", "termsNamePossessive", "innerText", "dataset", "append<PERSON><PERSON><PERSON>", "substep", "dogSex", "termsNeutered", "termsSexPossessive", "_storeDogData", "location", "href", "locations", "post_data", "_sanitiseDogData", "_getDogData", "dataString", "JSON", "stringify", "localStorage", "setItem", "dogs", "dogData", "age", "stripNewlines", "sex", "neutered", "breed", "weight", "ideal_weight", "has_health_issue", "prescription_diet", "weight_profile", "activity_level", "ageType", "age_in_months", "customerEmail", "email", "d", "navigation", "navigationHeight", "clientHeight", "undefined", "subnavigationHeight", "setProperty", "backButton", "<PERSON><PERSON><PERSON><PERSON>", "progressBar", "totalMainSteps", "currentMainStepNumber", "s", "totalDogSteps", "currentDogStepNumber", "start", "startingProgressSegment", "mainStepProgress", "dogStepProgress", "progress", "subNavigationTabs", "insertAdjacentHTML", "quizSteps", "destinationStep", "gotoSubstep", "currentStep", "animatingStep", "getStepType", "stepType", "scrollToTop", "scrollTo", "gotoNextSubstep", "gotoPrevSubstep", "prevSubstep", "matchMedia", "breakpoints", "phone", "animation1", "animation2", "currentSubstep", "nextSubstep", "currentTab", "nextTab", "enable", "_startTransition", "animatingSubstep", "destinationSubstep", "ExpandingInput", "inputDisplay", "line", "onInputNumberValidate", "onInputNumber", "onInputSelectChange", "onInput", "displayValue", "String", "fromCharCode", "which", "max", "min", "QuizCustomerRegisterForm", "log", "registerFormContainer", "registerForm", "kycFormContainer", "kycForm", "registerButton", "continueButton", "showRegisterForm", "onRegisterFormSubmit", "onKYCFormSubmit", "realRegisterForm", "showKYCForm", "animateOut", "QuizCustomerForms", "loginForm", "loginButton", "showLoginForm", "QuizStep", "QuizStepLine", "_on<PERSON><PERSON>d", "_onInvalid", "nextLine", "_showNextLine", "validate", "inputElement", "nextButton", "prevLine", "QuizTile", "tiles", "onClick", "selected", "reset", "updateHint", "hint", "QuizTiles", "tile", "resetHint", "default", "QuizStepTab", "removeAttribute", "disable", "QuizProgressBar", "QuizResults", "getItem", "quiz", "_addEventListeners", "_buildQuizResults", "buttonText", "prevDog", "currentDog", "buttonNext", "buttonCheckout", "stickyForm", "calculator", "backButtonLabel", "currentDogNumber", "nextDog", "gotoDog", "destinationDog", "_updateDogProgress", "parse", "defaultDogData", "tab", "tabActive", "dogIndex", "_buildQuizResultsDogs", "updateDog", "updateSummary", "updateProduct", "loadingOverlay", "temp", "dog_condition_not_listed", "dogsWithData", "allSettled", "fetchDogProductRecommendations", "dogDataFinal", "assign", "quizResults", "products", "<PERSON><PERSON><PERSON><PERSON>", "productsContainer", "modalContainer", "dogResultsHTML", "modalsHTML", "productJson", "product_available", "totalInventory", "productHTML", "collectionID", "collectionMessage", "ageTag", "weightTag", "collection_tag", "collections", "hpu", "hpr", "hpo", "hau", "har", "hao", "hsu", "hsr", "hso", "allergies", "calcium_oxolate", "cancer", "grain_intolerance", "heart", "joint", "kidney", "kidney_pancreatitis", "liver", "pancreatitis", "gi", "urate_stones", "query", "response", "fetch", "API", "STOREFRONT", "CONFIG", "URL", "crossDomain", "method", "headers", "X-Shopify-Storefront-Access-Token", "ACCESS_TOKEN", "Content-Type", "json", "responseProducts", "collection", "totalStepsNumber", "currentStepNumber", "onClickCheckout", "<PERSON><PERSON><PERSON><PERSON>", "popup<PERSON><PERSON><PERSON>", "open", "proceedToCheckout", "_endTransition", "onClickCheckoutUpsells", "modal", "selectedProducts", "items", "dogBoxesID", "uuid", "variant16oz", "variantId", "variant64oz", "variantIdLast", "quizData", "transitionCalories", "calculateTransitionCalories", "productCalories", "baseQuantity", "calculatePacketQuantity", "quantity1", "quantity2", "Math", "floor", "quantity", "properties", "_dog", "_boxID", "Starter Box", "item2", "Start<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item3", "routes", "cartAddUrl", "X-Requested-With", "cartUrl", "response2", "<PERSON><PERSON><PERSON><PERSON>", "cart", "openMiniCart", "cartType", "getAllSelectedProducts", "getDogSelectedProducts", "totalPriceAll", "totalPriceDog", "updatePrice", "QuizResultsStickyForm", "dogNameElement", "dogNameElement2", "quizResultsC<PERSON>r", "totalPrice", "totalSelectedProductsPrice", "boxPriceElement", "totalPriceFormatted", "calculateTotalPrice", "activeDogStep", "quizResultsDogs", "QuizResultsDogs", "QuizResultsDog", "selectedProductsNumber", "selectedProductsLimit", "productsList", "storage", "_onProductChange", "_checkLimit", "QuizLoadingOverlay", "QuizResultsProducts", "listItems", "scrollBarElement", "listWrapperElement", "previous", "reverse", "find", "isOnLeftHalfPartOfScreen", "isOnRightHalfPartOfScreen", "boundingRect", "scrollBy", "behavior", "left", "innerWidth", "QuizResultsProduct", "_onClickSelect", "buttonLabel", "components", "add_to_cart", "variantData", "getPrice", "direction", "right", "QuizFeedingCalculator", "labelsDogName", "labelDogWeight", "labelDogAgeNumber", "labelDogAgeType", "labelDogActivity", "labelDogCondition", "labelDogPronoun", "valueDogName", "valueDogWeight", "valueDogCondition", "condition", "valueDogAgeNumber", "ageInMonths", "age<PERSON><PERSON>ber", "valueDogAgeType", "valueDogActivity", "activity", "valueDogPronoun", "dogProducts", "daysInBox", "boxes", "days_in_box", "weeksInBox", "totalCaloriesPerDay", "calculateKCalPerDay", "caloriesPeriod1", "caloriesPeriod2", "caloriesPeriod3", "caloriesPeriod4", "caloriesPerOunce", "caloriesPerCup", "cup_calories", "ouncesPeriod1", "round", "ouncesPeriod2", "ouncesPeriod3", "ouncesPeriod4", "cupsPeriod1", "cupsPeriod2", "cupsPeriod3", "cupsPeriod4", "quantity16Oz", "totalOunces", "totalCups", "totalOuncesPerDay", "totalCupsPerDay", "labelProductName", "labelTotalOunces", "labelTotalCups", "labelPeriod1Ounces", "labelPeriod1Cups", "labelPeriod2Ounces", "labelPeriod2Cups", "labelPeriod3Ounces", "labelPeriod3Cups", "labelPeriod4Ounces", "labelPeriod4Cups", "valueProductName", "valueTotalOunces", "fractionQuarters", "valueTotalCups", "valuePeriod1Ounces", "valuePeriod1Cups", "valuePeriod2Ounces", "valuePeriod2Cups", "valuePeriod3Ounces", "valuePeriod3Cups", "valuePeriod4Ounces", "valuePeriod4Cups", "QuizSubNavigation", "QuizSubNavigationTabs", "allTabs", "label", "QuizStepDogNumber", "customElements", "define", "dogNamesContainer", "dogNumberInput", "_onNumberChanged", "_updateLine", "QuizStepDogSubstep", "QuizStepDogDetails", "QuizStepDogGeneral", "QuizStepDogActivity", "QuizStepDogWeightProfile", "QuizStepDogWeightDetails", "QuizStepDogHealth", "StyledSelect", "searchable", "niceselect", "NiceSelect", "update", "InvisibleInputBindingManager", "_onChanged", "FullNameBindingManager", "boundElementFirstName", "boundElementLastName", "splitName", "firstName", "lastName", "setAttribute", "inputsWithSubOptions", "hideAllSubOptions", "subOptionContainerId", "subOptionContainer", "subOptionInputs", "subOptionInput", "Event", "inputSubOptions", "visibleNoteInput", "hiddenNoteInput", "newValue", "trim", "revealValue", "revealInput"], "mappings": "AAAA,IAAAA,UAAAC,OAAAC,eACAC,gBAAA,CAAAC,EAAAC,EAAAC,IAAAD,KAAAD,EAAAJ,UAAAI,EAAAC,EAAA,CAAAE,WAAA,CAAA,EAAAC,aAAA,CAAA,EAAAC,SAAA,CAAA,EAAAH,MAAAA,CAAA,CAAA,EAAAF,EAAAC,GAAAC,EACAI,cAAA,CAAAN,EAAAC,EAAAC,KACAH,gBAAAC,EAAA,UAAA,OAAAC,EAAAA,EAAA,GAAAA,EAAAC,CAAA,EACAA,GAIA,SAAAK,SAAAC,GACAC,KAAAC,YAAA,CAAA,GAAA,IACAF,GACAC,KAAAD,KAAAA,CAAA,EAEAC,KAAAE,OAAAJ,SAAAK,UAAAD,OAAAE,KAAAJ,IAAA,EACAA,KAAAK,kBAAA,EACA,CAkNA,SAAAC,WAAAC,EAAAC,GACA,OAAAD,EAAAE,YAAA,IAAAD,EAAAD,QAAAE,YAAA,CACA,CACA,SAAAC,YAAAC,EAAAH,GACA,OAAAR,KAAAY,cAAAC,OACAL,IAAAM,UAAAN,IAAAM,SAAAC,iBAAAP,IAAAK,OAEAb,KAAAY,cAAAJ,CACA,CACA,SAAAQ,UAAAC,EAAAT,GACA,OAAAS,IAAAT,EAAAS,EACA,CA5NAnB,SAAAK,UAAAJ,KAAA,SAAAA,GACA,IAAAE,EAAAD,KAAAC,YACAiB,IAAAC,EACA,GAAAnB,KAAAY,YAAA,CACA,IAAAO,KAAAlB,EAAA,GACAA,EAAA,GAAAmB,eAAAD,CAAA,GACAnB,KAAAY,YAAAS,oBAAAF,EAAAnB,KAAAE,OAAA,CAAA,CAAA,EAGA,IAAAiB,KAAAlB,EAAA,GACAA,EAAA,GAAAmB,eAAAD,CAAA,GACAnB,KAAAY,YAAAS,oBAAAF,EAAAnB,KAAAE,OAAA,CAAA,CAAA,CAGA,CACA,GAAAH,GAAAA,EAAAuB,iBAAA,CAOA,IAAAH,KADAnB,KAAAY,YAAAb,EACAE,EAAA,GACAA,EAAA,GAAAmB,eAAAD,CAAA,GACAnB,KAAAY,YAAAU,iBAAAH,EAAAnB,KAAAE,OAAA,CAAA,CAAA,EAGA,IAAAiB,KAAAlB,EAAA,GACAA,EAAA,GAAAmB,eAAAD,CAAA,GACAnB,KAAAY,YAAAU,iBAAAH,EAAAnB,KAAAE,OAAA,CAAA,CAAA,CATA,MAJAF,KAAAY,aACA,OAAAZ,KAAAY,YAeA,OAAAZ,IACA,EACAF,SAAAK,UAAAoB,eAAA,SAAAJ,GACA,MAAA,CAAA,IAAA,CAAA,OAAA,QAAA,QAAA,OAAA,SAAA,UAAAK,QAAAL,CAAA,CACA,EACArB,SAAAK,UAAAsB,GAAA,SAAAN,EAAAR,EAAAe,EAAAC,GACAT,IAAAnB,EACAE,EACAiB,IAAAU,EACAC,EACA,GAAA,CAAAV,EACA,MAAA,IAAAW,UAAA,uBAAAX,CAAA,EAUA,GARA,YAAA,OAAAR,IACAgB,EAAAD,EACAA,EAAAf,EACAA,EAAA,MAEA,KAAA,IAAAgB,IACAA,EAAA3B,KAAAuB,eAAAJ,CAAA,GAEA,YAAA,OAAAO,EACA,MAAA,IAAAI,UAAA,oCAAA,EA6BA,OA3BA/B,EAAAC,KAAAY,aACAX,EAAAD,KAAAC,YAAA0B,EAAA,EAAA,IACAR,KACApB,GACAA,EAAAuB,iBAAAH,EAAAnB,KAAAE,OAAAyB,CAAA,EAEA1B,EAAAkB,GAAA,IAOAS,EALAjB,EAGA,YAAAoB,KAAApB,CAAA,GACAkB,EAAAlB,EACAL,YACA,mBAAAyB,KAAApB,CAAA,GACAkB,EAAAlB,EAAAqB,MAAA,CAAA,EACAhB,YAEAa,EAAAlB,EACAsB,QAAA9B,UAAA+B,UAVAL,EAAA,KACAnB,YAAAN,KAAAJ,IAAA,GAWAC,EAAAkB,GAAAgB,KAAA,CACAxB,SAAAA,EACAe,QAAAA,EACAE,QAAAA,EACAC,aAAAA,CACA,CAAA,EACA7B,IACA,EACAF,SAAAK,UAAAiC,IAAA,SAAAjB,EAAAR,EAAAe,EAAAC,GACAT,IAAAmB,EACAnB,IAAAoB,EACArC,EACAsC,EACArB,IAAAsB,EAMA,GALA,YAAA,OAAA7B,IACAgB,EAAAD,EACAA,EAAAf,EACAA,EAAA,MAEA,KAAA,IAAAgB,EACA3B,KAAAoC,IAAAjB,EAAAR,EAAAe,EAAA,CAAA,CAAA,EACA1B,KAAAoC,IAAAjB,EAAAR,EAAAe,EAAA,CAAA,CAAA,OAIA,GADAzB,EAAAD,KAAAC,YAAA0B,EAAA,EAAA,GACAR,GASA,IADAoB,EAAAtC,EAAAkB,KACAoB,EAAAE,OAAA,CAGA,IAAAJ,EAAAE,EAAAE,OAAA,EAAA,GAAAJ,EAAAA,CAAA,GACAC,EAAAC,EAAAF,GACA1B,GAAAA,IAAA2B,EAAA3B,UAAAe,GAAAA,IAAAY,EAAAZ,UACA1B,KAAAK,kBAAA8B,KAAAG,CAAA,EACAC,EAAAG,OAAAL,EAAA,CAAA,GAGAE,EAAAE,SACA,OAAAxC,EAAAkB,GACAnB,KAAAY,aACAZ,KAAAY,YAAAS,oBAAAF,EAAAnB,KAAAE,OAAAyB,CAAA,EAXA,CAAA,MAVA,IAAAa,KAAAvC,EACAA,EAAAmB,eAAAoB,CAAA,GACAxC,KAAAoC,IAAAI,EAAA7B,EAAAe,CAAA,EAsBA,OAAA1B,IACA,EACAF,SAAAK,UAAAD,OAAA,SAAAyC,GACAzB,IAAAmB,EACAO,EACA,IACA7C,EAEAuC,EAHAO,EAAAF,EAAAE,KAKA3B,IAAAqB,EAAA,GACAO,EACA,IAAAC,EAAA,uBACA,GAAA,CAAA,IAAAJ,EAAAI,GAAA,CAYA,QAPAD,EADA,KADAA,EAAAH,EAAAG,QACAE,SACAF,EAAAG,WAEAH,GAAAI,0BACAJ,EAAAA,EAAAI,yBAEAnD,EAAAC,KAAAY,YACA+B,EAAAQ,aAAAR,EAAAG,SAAAH,EAAAS,cAAA,EAAA,IAEA,KAAA,EACAb,EAAAvC,KAAAC,YAAA,GAAA4C,GACA,MACA,KAAA,EACA7C,KAAAC,YAAA,IAAAD,KAAAC,YAAA,GAAA4C,KACAN,EAAAA,EAAAc,OAAArD,KAAAC,YAAA,GAAA4C,EAAA,GAEA7C,KAAAC,YAAA,IAAAD,KAAAC,YAAA,GAAA4C,KACAN,EAAAA,EAAAc,OAAArD,KAAAC,YAAA,GAAA4C,EAAA,GAEA,MACA,KAAA,EACAN,EAAAvC,KAAAC,YAAA,GAAA4C,EAEA,CACA3B,IAAAoC,EAAA,GAEA,IADAV,EAAAL,EAAAE,OACAK,GAAAF,GAAA,CACA,IAAAP,EAAA,EAAAA,EAAAO,IACAN,EAAAC,EAAAF,IADAA,CAAA,GAKAS,EAAAvC,SAAA,CAAA,EAAA,CAAA,SAAA,QAAA,SAAA,YAAAiB,QAAAsB,EAAAvC,QAAAE,YAAA,CAAA,GAAAqC,EAAAS,aAAA,UAAA,EACAD,EAAA,GACAhB,EAAAV,QAAA4B,KAAAV,EAAAR,EAAAT,aAAAiB,CAAA,GACAQ,EAAAnB,KAAA,CAAAQ,EAAAG,EAAAR,EAAA,EAGA,GAAAQ,IAAA/C,EACA,MAIA,GAFA6C,EAAAL,EAAAE,QACAK,EAAAA,EAAAW,eAAAX,EAAAG,sBACAS,aACA,KAEA,CACAxC,IAAAyC,EACA,IAAAtB,EAAA,EAAAA,EAAAiB,EAAAb,OAAAJ,CAAA,GACA,GAAArC,EAAA,CAAA,EAAAA,KAAAK,kBAAAmB,QAAA8B,EAAAjB,GAAA,EAAA,IAIA,CAAA,IADArC,KAAA4D,KAAAC,MAAA7D,KAAAsD,EAAAjB,EAAA,EACA,CACAiB,EAAAjB,GAAA,GAAAU,GAAA,CAAA,EACAO,EAAAjB,GAAA,GAAAyB,eAAA,EACAH,EAAA,CAAA,EACA,KACA,CAEA,OAAAA,CA9DA,CA+DA,EACA7D,SAAAK,UAAAyD,KAAA,SAAAjB,EAAAG,EAAAR,GACA,OAAAA,EAAAZ,QAAA8B,KAAAV,EAAAH,EAAAG,CAAA,CACA,EAaAhD,SAAAK,UAAA4D,QAAA,WACA/D,KAAAoC,IAAA,EACApC,KAAAD,KAAA,CACA,EACA,IAAAiE,aAAAlE,SAGAmE,0BACAC,cACAlE,KAAAmE,gBAAA,IAAAH,aAAAlD,SAAAsD,IAAA,EACApE,KAAAmE,gBAAA1C,GAAA,SAAA,oBAAAzB,KAAAqE,gBAAAjE,KAAAJ,IAAA,CAAA,CACA,CACAqE,gBAAA1B,EAAAG,GACA,IAAAwB,EAAAxD,SAAAyD,eAAAzB,EAAA0B,aAAA,iBAAA,CAAA,EACAF,IACA,WAAAxB,EAAAvC,UACAuC,EAAAA,EAAA2B,QAAA3B,EAAA4B,gBAEAJ,EAAAK,UAAA7B,EAAAS,aAAA,OAAA,EAAAT,EAAA0B,aAAA,OAAA,EAAA1B,EAAArD,MAEA,CACA,EAGA,SAAAmF,aAAApE,EAAAqE,EAAAC,EAAA,IACAtE,EAAAuE,cAAA,IAAAC,YAAAH,EAAA,CACAI,QAAA,CAAA,EACAC,OAAAJ,CACA,CAAA,CAAA,CACA,CACA,SAAAK,wBAAA3E,EAAAqE,EAAAC,EAAA,IACAtE,EAAAuE,cAAA,IAAAC,YAAAH,EAAA,CACAI,QAAA,CAAA,EACAC,OAAAJ,CACA,CAAA,CAAA,CACA,CAGA,SAAAM,YAAAC,EAAAC,EAAA,IACA,UAAA,OAAAD,IACAA,EAAAA,EAAAE,QAAA,IAAA,EAAA,GAEA,IAAAC,EAAA,sBAAAC,EAAAH,GAAAzE,OAAA6E,eAAAC,SAAAC,YACA,SAAAC,EAAAC,EAAAC,GACA,OAAA,MAAAD,GAAAA,GAAAA,EAAAC,EAAAD,CACA,CACA,SAAAE,EAAAC,EAAAC,EAAAC,EAAAC,GAIA,OAHAF,EAAAL,EAAAK,EAAA,CAAA,EACAC,EAAAN,EAAAM,EAAA,GAAA,EACAC,EAAAP,EAAAO,EAAA,GAAA,EACAC,MAAAJ,CAAA,GAAA,MAAAA,EACA,GAGAK,GADAL,GAAAA,EAAA,KAAAM,QAAAL,CAAA,GACAM,MAAA,GAAA,GAAA,GAAAjB,QAAA,2BAAA,KAAAY,CAAA,GAAAG,EAAA,GAAAF,EAAAE,EAAA,GAAA,GAEA,CACApF,IAAAzB,EAAA,GACA,OAAAgG,EAAAgB,MAAAjB,CAAA,EAAA,IACA,IAAA,SACA/F,EAAAuG,EAAAX,EAAA,CAAA,EACA,MACA,IAAA,qBACA5F,EAAAuG,EAAAX,EAAA,CAAA,EACA,MACA,IAAA,8BACA5F,EAAAuG,EAAAX,EAAA,EAAA,IAAA,GAAA,EACA,MACA,IAAA,8BACA5F,EAAAuG,EAAAX,EAAA,EAAA,IAAA,GAAA,EACA,MACA,IAAA,mCACA5F,EAAAuG,EAAAX,EAAA,EAAA,IAAA,GAAA,EACA,MACA,IAAA,0CACA5F,EAAAuG,EAAAX,EAAA,EAAA,IAAA,GAAA,EACA,MACA,IAAA,0CACA5F,EAAAuG,EAAAX,EAAA,EAAA,GAAA,EACA,MACA,IAAA,+CACA5F,EAAAuG,EAAAX,EAAA,EAAA,GAAA,CAEA,CACA,OAAAI,EAAAjE,QAAA,sBAAA,EACAiE,EAAAF,QAAAC,EAAA/F,CAAA,CAIA,CAGA,IAAAiH,gCAAAC,YACAzC,cACA0C,MAAA,EACA5G,KAAA6G,oBAAA,CAAA,EACAC,QAAAC,YACA/G,KAAAgH,aAAAvF,GAAA,yBAAA,IACA,IAAAwF,EAAAjH,KAAAkH,QAAA,kBAAA,EACAvE,EAAAG,SAAAmE,GAAAtE,EAAAuC,OAAAiC,OACAnH,KAAA6G,oBAAA,CAAA,EAEA,CAAA,CAEA,CACAG,mBACA,OAAAhH,KAAAoH,cAAApH,KAAAoH,eAAA,IAAApD,aAAAlD,SAAAC,eAAA,CACA,CACAsG,eACA,OAAArH,KAAAsH,UAAAtH,KAAAsH,WAAA,IAAAtD,aAAAhE,IAAA,CACA,CACAuH,iBACA3C,aAAA9D,SAAAC,gBAAA,qBAAA,CACA,CACAyG,iBACA5C,aAAA9D,SAAAC,gBAAA,mBAAA,CACA,CACA0G,aAAAC,EAAA,CAAAC,WAAA,WAAAC,UAAA,CAAA,GACA,MAAAC,EAAA,KACA7H,KAAA8H,UAAAC,IAAA,gBAAA,EACA/H,KAAAgI,MAAAC,QAAA,GACA,EACA,OAAA,IAAAC,QAAA,IACArH,OAAAsH,sBACAnI,KAAAoI,qBAAA,IAAAD,qBAAA,IACAxF,EAAA,GAAA0F,iBACArI,KAAAoI,qBAAAE,WAAA,EACAC,sBAAA,KACAC,EAAA,EACAX,EAAA,CACA,CAAA,EAEA,EAAAH,CAAA,EACA1H,KAAAoI,qBAAAK,QAAAzI,IAAA,IAEAwI,EAAA,EACAX,EAAA,EAEA,CAAA,CACA,CACAa,uBACA,IAAAC,EACA3I,KAAAqH,SAAAtD,QAAA,EACA/D,KAAAgH,aAAAjD,QAAA,EACA,OAAA4E,EAAA3I,KAAAoI,uBAAAO,EAAAL,WAAA,EACA,OAAAtI,KAAAsH,UACA,OAAAtH,KAAAoH,aACA,CACA,EAGAwB,mBAAA,CAAA,QAAA,SAAA,WAAA,UAAA,SAAA,aAAA,kBAAA,kBAAA,mDAAA,gCAAA,WACAC,kBAAAD,mBAAAE,KAAA,GAAA,EACA5G,QAAA,aAAA,OAAAD,QAAA,aACAA,QAAA9B,UAAA+B,SAAAD,QAAA9B,UAAA4I,mBAAA9G,QAAA9B,UAAA6I,sBACAC,cAAA,SAAAC,EAAAC,EAAAC,GACA,IAAAC,EAAAC,MAAAnJ,UAAA6B,MAAA6B,MAAAqF,EAAAK,iBAAAV,iBAAA,CAAA,EAKA,OAJAM,GAAAjH,QAAAsB,KAAA0F,EAAAL,iBAAA,GACAQ,EAAAG,QAAAN,CAAA,EAEAG,EAAAA,EAAAD,OAAAA,CAAA,CAEA,EACAK,kBAAA,SAAAC,GACA,MAAA,SAAAA,EAAAC,eACA,EACAC,YAAA,SAAAF,GACA,IAAAG,EAAAC,SAAAJ,EAAAlF,aAAA,UAAA,EAAA,EAAA,EACA,OAAA6B,MAAAwD,CAAA,EAGAJ,CAAAA,kBAAAC,CAAA,IAGA,UAAAA,EAAAK,UAAA,UAAAL,EAAAK,UAAA,YAAAL,EAAAK,UAAA,OAAAL,EAAAlF,aAAA,UAAA,GAGAkF,EAAAM,SAFA,EANAH,CASA,EACAI,qBAAA,SAAAC,EAAAC,GACA,OAAAD,EAAAF,WAAAG,EAAAH,SAAAE,EAAAE,cAAAD,EAAAC,cAAAF,EAAAF,SAAAG,EAAAH,QACA,EACAK,QAAA,SAAAX,GACA,MAAA,UAAAA,EAAAnJ,OACA,EACA+J,cAAA,SAAAZ,GACA,OAAAW,QAAAX,CAAA,GAAA,WAAAA,EAAA7G,IACA,EACA0H,qBAAA,SAAAb,GAIA,MAHA,YAAAA,EAAAnJ,SAAA+I,MAAAnJ,UAAA6B,MAAA6B,MAAA6F,EAAAc,QAAA,EAAAC,KAAA,SAAAC,GACA,MAAA,YAAAA,EAAAnK,OACA,CAAA,CAEA,EACAoK,gBAAA,SAAAC,EAAAC,GACA,IAAA,IAAAxI,EAAA,EAAAA,EAAAuI,EAAAnI,OAAAJ,CAAA,GACA,GAAAuI,EAAAvI,GAAAyI,SAAAF,EAAAvI,GAAAwI,OAAAA,EACA,OAAAD,EAAAvI,EAGA,EACA0I,gBAAA,SAAArB,GACA,GAAA,CAAAA,EAAA7E,KACA,MAAA,CAAA,EAGA,SAAAmG,EAAAnG,GACA,OAAAoG,EAAA1B,iBAAA,6BAAA1E,EAAA,IAAA,CACA,CAHA,IAAAoG,EAAAvB,EAAAmB,MAAAnB,EAAAwB,cAKA,GAAA,aAAA,OAAArK,QAAA,KAAA,IAAAA,OAAAsK,KAAA,YAAA,OAAAtK,OAAAsK,IAAAC,OACAC,EAAAL,EAAAnK,OAAAsK,IAAAC,OAAA1B,EAAA7E,IAAA,CAAA,OAEA,IACAwG,EAAAL,EAAAtB,EAAA7E,IAAA,CAIA,CAHA,MAAAyG,GAEA,OADAC,QAAAC,MAAA,2IAAAF,EAAAG,OAAA,EACA,CAAA,CACA,CAEA,IAAAX,EAAAH,gBAAAU,EAAA3B,EAAAmB,IAAA,EACA,MAAA,CAAAC,GAAAA,IAAApB,CACA,EACAgC,QAAA,SAAAhC,GACA,OAAAW,QAAAX,CAAA,GAAA,UAAAA,EAAA7G,IACA,EACA8I,mBAAA,SAAAjC,GACA,OAAAgC,QAAAhC,CAAA,GAAA,CAAAqB,gBAAArB,CAAA,CACA,EACAkC,SAAA,SAAAlC,EAAAmC,GACA,GAAA,WAAAC,iBAAApC,CAAA,EAAAqC,WACA,MAAA,CAAA,EAEA,IACAC,EADA9J,QAAAsB,KAAAkG,EAAA,+BAAA,EACAA,EAAAjG,cAAAiG,EACA,GAAAxH,QAAAsB,KAAAwI,EAAA,uBAAA,EACA,MAAA,CAAA,EAEA,GAAAH,GAAA,SAAAA,GAOA,GAAA,kBAAAA,EAEA,OADAI,GAAAC,EAAAxC,EAAAyC,sBAAA,GAAAF,MAAAG,EAAAF,EAAAE,OACA,IAAAH,GAAA,IAAAG,CACA,MATA,KAAA1C,GAAA,CACA,GAAA,SAAAoC,iBAAApC,CAAA,EAAA2C,QACA,MAAA,CAAA,EAEA3C,EAAAA,EAAAjG,aACA,CAKA,MAAA,CAAA,CACA,EACA6I,uBAAA,SAAA5C,GACA,GAAAW,QAAAX,CAAA,GAAA,WAAAA,EAAAnJ,SAAA,aAAAmJ,EAAAnJ,SAAA,WAAAmJ,EAAAnJ,QAEA,IADA,IAAA0C,EAAAyG,EAAAjG,cACAR,GAAA,CACA,GAAA,aAAAA,EAAA1C,SAAA0C,EAAAsJ,SAAA,CACA,IAAA,IAAAlK,EAAA,EAAAA,EAAAY,EAAAuH,SAAA/H,OAAAJ,CAAA,GAAA,CACA,IAAAqI,EAAAzH,EAAAuH,SAAAgC,KAAAnK,CAAA,EACA,GAAA,WAAAqI,EAAAnK,QACA,MAAAmK,CAAAA,EAAA+B,SAAA/C,CAAA,CAKA,CACA,MAAA,CAAA,CACA,CACAzG,EAAAA,EAAAQ,aACA,CAEA,MAAA,CAAA,CACA,EACAiJ,gCAAA,SAAAjI,EAAAiF,GACA,MAAAA,EAAAA,EAAA6C,UAAAjC,cAAAZ,CAAA,GAAAkC,SAAAlC,EAAAjF,EAAAoH,YAAA,GAAAtB,qBAAAb,CAAA,GAAA4C,uBAAA5C,CAAA,EAIA,EACAiD,+BAAA,SAAAlI,EAAAiF,GACA,MAAA,EAAA,CAAAgD,gCAAAjI,EAAAiF,CAAA,GAAAiC,mBAAAjC,CAAA,GAAAE,YAAAF,CAAA,EAAA,EAIA,EACAkD,SAAA,SAAA1D,EAAAzE,GAEA,IAAAoI,EAAA,GACAC,EAAA,GAiBA,OAhBA7D,cAAAC,GAHAzE,EAAAA,GAAA,IAGA0E,iBAAAwD,+BAAAvM,KAAA,KAAAqE,CAAA,CAAA,EACAsI,QAAA,SAAAC,EAAA3K,GACA,IAAA4K,EAAArD,YAAAoD,CAAA,EACA,IAAAC,EACAJ,EAAA1K,KAAA6K,CAAA,EAEAF,EAAA3K,KAAA,CACAiI,cAAA/H,EACA2H,SAAAiD,EACAvD,KAAAsD,CACA,CAAA,CAEA,CAAA,EACAF,EAAAI,KAAAjD,oBAAA,EAAAkD,IAAA,SAAAjD,GACA,OAAAA,EAAAR,IACA,CAAA,EAAArG,OAAAwJ,CAAA,CAEA,EACAO,2BAAAxE,mBAAAvF,OAAA,QAAA,EAAAyF,KAAA,GAAA,EACAuE,YAAA,SAAA3D,EAAAjF,GAEA,GADAA,EAAAA,GAAA,GACAiF,EAGA,MAAA,CAAA,IAAAxH,QAAAsB,KAAAkG,EAAA0D,0BAAA,GAGAV,gCAAAjI,EAAAiF,CAAA,EALA,MAAA,IAAA4D,MAAA,kBAAA,CAMA,EAGA,SAAAC,QAAAC,EAAAC,GACA,IAEAC,EAFAC,EAAAvO,OAAAuO,KAAAH,CAAA,EAUA,OATApO,OAAAwO,wBACAF,EAAAtO,OAAAwO,sBAAAJ,CAAA,EACAC,IACAC,EAAAA,EAAAtE,OAAA,SAAAyE,GACA,OAAAzO,OAAA0O,yBAAAN,EAAAK,CAAA,EAAAnO,UACA,CAAA,GAEAiO,EAAAxL,KAAA0B,MAAA8J,EAAAD,CAAA,GAEAC,CACA,CACA,SAAAI,eAAAjL,GACA,IAAA,IAAAT,EAAA,EAAAA,EAAA2L,UAAAvL,OAAAJ,CAAA,GAAA,CACA,IAAA4L,EAAA,MAAAD,UAAA3L,GAAA2L,UAAA3L,GAAA,GACAA,EAAA,EACAkL,QAAAnO,OAAA6O,CAAA,EAAA,CAAA,CAAA,EAAAlB,QAAA,SAAAvN,GACA0O,gBAAApL,EAAAtD,EAAAyO,EAAAzO,EAAA,CACA,CAAA,EACAJ,OAAA+O,0BACA/O,OAAAgP,iBAAAtL,EAAA1D,OAAA+O,0BAAAF,CAAA,CAAA,EAEAV,QAAAnO,OAAA6O,CAAA,CAAA,EAAAlB,QAAA,SAAAvN,GACAJ,OAAAC,eAAAyD,EAAAtD,EAAAJ,OAAA0O,yBAAAG,EAAAzO,CAAA,CAAA,CACA,CAAA,CAEA,CACA,OAAAsD,CACA,CACA,SAAAoL,gBAAA3O,EAAAC,EAAAC,GAWA,OAVAD,KAAAD,EACAH,OAAAC,eAAAE,EAAAC,EAAA,CACAC,MAAAA,EACAC,WAAA,CAAA,EACAC,aAAA,CAAA,EACAC,SAAA,CAAA,CACA,CAAA,EAEAL,EAAAC,GAAAC,EAEAF,CACA,CACA,IAAA8O,iBAAA,WACA,IAAAC,EAAA,GACA,MAAA,CACAC,aAAA,SAAAC,GACA,EAAAF,EAAA7L,SACAgM,EAAAH,EAAAA,EAAA7L,OAAA,MACA+L,GACAC,EAAAC,MAAA,EAHA,IAMAC,EAAAL,EAAA9M,QAAAgN,CAAA,EACA,CAAA,IAAAG,GAGAL,EAAA5L,OAAAiM,EAAA,CAAA,EACAL,EAAAnM,KAAAqM,CAAA,CAEA,EACAI,eAAA,SAAAJ,GACAG,EAAAL,EAAA9M,QAAAgN,CAAA,EACA,CAAA,IAAAG,GACAL,EAAA5L,OAAAiM,EAAA,CAAA,EAEA,EAAAL,EAAA7L,QACA6L,EAAAA,EAAA7L,OAAA,GAAAoM,QAAA,CAEA,CACA,CACA,EAAA,EACAC,kBAAA,SAAApF,GACA,OAAAA,EAAAnJ,SAAA,UAAAmJ,EAAAnJ,QAAAE,YAAA,GAAA,YAAA,OAAAiJ,EAAAqF,MACA,EACAC,cAAA,SAAAC,GACA,MAAA,WAAAA,EAAAzP,KAAA,QAAAyP,EAAAzP,KAAA,KAAAyP,EAAAC,OACA,EACAC,WAAA,SAAAF,GACA,MAAA,QAAAA,EAAAzP,KAAA,IAAAyP,EAAAC,OACA,EACAE,MAAA,SAAAC,GACA,OAAAC,WAAAD,EAAA,CAAA,CACA,EACAE,UAAA,SAAAC,EAAAH,GACA,IAAAI,EAAA,CAAA,EAQA,OAPAD,EAAAE,MAAA,SAAAjQ,EAAA4C,GACA,MAAAgN,CAAAA,EAAA5P,CAAA,IACAgQ,EAAApN,EACA,CAAA,EAGA,CAAA,EACAoN,CACA,EACAE,eAAA,SAAAlQ,GACA,IAAA,IAAAmQ,EAAA5B,UAAAvL,OAAAoN,EAAA,IAAAvG,MAAA,EAAAsG,EAAAA,EAAA,EAAA,CAAA,EAAAE,EAAA,EAAAA,EAAAF,EAAAE,CAAA,GACAD,EAAAC,EAAA,GAAA9B,UAAA8B,GAEA,MAAA,YAAA,OAAArQ,EAAAA,EAAAoE,MAAA,KAAA,EAAAgM,CAAA,EAAApQ,CACA,EACAsQ,gBAAA,SAAApN,GACA,OAAAA,EAAAG,OAAAkN,YAAA,YAAA,OAAArN,EAAAsN,aAAAtN,EAAAsN,aAAA,EAAA,GAAAtN,EAAAG,MACA,EACAoN,gBAAA,SAAAC,EAAAC,GAiBA,SAAAC,EAAAC,EAAAC,EAAAC,GACA,OAAAF,GAAA,KAAA,IAAAA,EAAAC,GAAAD,EAAAC,GAAAE,EAAAD,GAAAD,EACA,CAMA,SAAAG,EAAAH,GACA,IAAAI,EAAAF,EAAAF,GACA,GAAA,YAAA,OAAAI,EAAA,CACA,IAAA,IAAAC,EAAA5C,UAAAvL,OAAAoN,EAAA,IAAAvG,MAAA,EAAAsH,EAAAA,EAAA,EAAA,CAAA,EAAAC,EAAA,EAAAA,EAAAD,EAAAC,CAAA,GACAhB,EAAAgB,EAAA,GAAA7C,UAAA6C,GAEAF,EAAAA,EAAA9M,MAAA,KAAA,EAAAgM,CAAA,CACA,CACA,GAAA,CAAAc,EAAA,CACA,GAAA,KAAA,IAAAA,GAAA,CAAA,IAAAA,EACA,OAAAA,EAEA,MAAA,IAAArD,MAAA,IAAAjK,OAAAkN,EAAA,8DAAA,CAAA,CACA,CACA,IAAA7G,EAAAiH,EACA,GAAA,UAAA,OAAAA,IACAjH,EAAAoH,EAAAC,cAAAJ,CAAA,GAKA,OAAAjH,EAHA,MAAA,IAAA4D,MAAA,IAAAjK,OAAAkN,EAAA,uCAAA,CAAA,CAIA,CAoBA,SAAAS,IAcA,GAbAC,EAAAC,eAAAD,EAAAE,WAAAhE,IAAA,SAAAiE,GACA,IAAAC,EAAAzE,SAAAwE,CAAA,EACA,GAAA,EAAAC,EAAA5O,OACA,MAAA,CACA2O,UAAAA,EACAE,kBAAAD,EAAA,GACAE,iBAAAF,EAAAA,EAAA5O,OAAA,EACA,CAGA,CAAA,EAAA2G,OAAA,SAAAoI,GACA,MAAA,CAAA,CAAAA,CACA,CAAA,EACAP,EAAAC,eAAAzO,QAAA,GAAA,CAAAiO,EAAA,eAAA,EACA,MAAA,IAAApD,MAAA,qGAAA,CAEA,CAoBA,SAAAmE,EAAAC,GACA,IAAAhI,EAAAgH,EAAA,iBAAAgB,CAAA,EACA,OAAAhI,GAAA,CAAA,IAAAA,GAAAgI,CACA,CAwGA,SAAAC,IACA,GAAAV,EAAAW,OAGAvD,iBAAAE,aAAAC,CAAA,EACAyC,EAAAY,uBAAApB,EAAAqB,kBAAA1C,MAAA,WACA2C,EAAAC,EAAA,CAAA,CACA,CAAA,EAAAD,EAAAC,EAAA,CAAA,EACAlB,EAAAxP,iBAAA,UAAA2Q,EAAA,CAAA,CAAA,EACAnB,EAAAxP,iBAAA,YAAA4Q,EAAA,CACAC,QAAA,CAAA,EACAC,QAAA,CAAA,CACA,CAAA,EACAtB,EAAAxP,iBAAA,aAAA4Q,EAAA,CACAC,QAAA,CAAA,EACAC,QAAA,CAAA,CACA,CAAA,EACAtB,EAAAxP,iBAAA,QAAA+Q,EAAA,CACAF,QAAA,CAAA,EACAC,QAAA,CAAA,CACA,CAAA,EACAtB,EAAAxP,iBAAA,UAAAgR,EAAA,CACAH,QAAA,CAAA,EACAC,QAAA,CAAA,CACA,CAAA,CAEA,CACA,SAAAG,IACA,GAAAtB,EAAAW,OAGAd,EAAAzP,oBAAA,UAAA4Q,EAAA,CAAA,CAAA,EACAnB,EAAAzP,oBAAA,YAAA6Q,EAAA,CAAA,CAAA,EACApB,EAAAzP,oBAAA,aAAA6Q,EAAA,CAAA,CAAA,EACApB,EAAAzP,oBAAA,QAAAgR,EAAA,CAAA,CAAA,EACAvB,EAAAzP,oBAAA,UAAAiR,EAAA,CAAA,CAAA,CAEA,CAvPA,IAAAxB,GAAAV,MAAAA,EAAA,KAAA,EAAAA,EAAAtP,WAAAA,SACA2P,EAAA1C,eAAA,CACAyE,wBAAA,CAAA,EACAC,kBAAA,CAAA,EACAX,kBAAA,CAAA,CACA,EAAA1B,CAAA,EACAa,EAAA,CACAE,WAAA,GACAD,eAAA,GACAwB,4BAAA,KACAC,wBAAA,KACAf,OAAA,CAAA,EACAgB,OAAA,CAAA,EACAf,uBAAA,KAAA,CACA,EAKAgB,EAAA,SAAArS,GACA,MAAA,EAAAA,CAAAA,GAAAyQ,CAAAA,EAAAE,WAAA1G,KAAA,SAAA2G,GACA,OAAAA,EAAA3E,SAAAjM,CAAA,CACA,CAAA,EACA,EAwBAwR,EAAA,WACA,IAQAc,EARApJ,EAAAgH,EAAA,cAAA,EACA,GAAA,CAAA,IAAAhH,EACA,MAAA,CAAA,EAWA,GAPAA,EAFA,KAAA,IAAAA,EACAmJ,EAAA/B,EAAAiC,aAAA,EACAjC,EAAAiC,eAEAD,EAAA7B,EAAAC,eAAA,KACA4B,EAAAxB,mBACAZ,EAAA,eAAA,EAGAhH,EAGA,OAAAA,EAFA,MAAA,IAAA4D,MAAA,8DAAA,CAGA,EAmBAyE,EAAA,SAAAiB,EAAAtJ,GACA,CAAA,IAAAA,GAGAA,IAAAoH,EAAAiC,gBAGArJ,GAAAA,EAAAuJ,OAIAvJ,EAAAuJ,MAAA,CACAC,cAAA,CAAA,CAAAzC,EAAAyC,aACA,CAAA,EACAjC,EAAA0B,wBAAAjJ,EACAoF,kBAAApF,CAAA,GACAA,EAAAqF,OAAA,GARAiE,EAAAhB,EAAA,CAAA,EAUA,EAKAE,EAAA,SAAAjD,GACA,IAAAnM,EAAAiN,gBAAAd,CAAA,EACA4D,EAAA/P,CAAA,IAGA6M,eAAAc,EAAA0C,wBAAAlE,CAAA,EACAT,EAAA4E,WAAA,CACAC,YAAA5C,EAAA+B,yBAAA,CAAAnF,YAAAvK,CAAA,CACA,CAAA,EAGA6M,eAAAc,EAAA6C,kBAAArE,CAAA,GAGAA,EAAAnL,eAAA,EACA,EACAmO,EAAA,SAAAhD,GACA,IAAAnM,EAAAiN,gBAAAd,CAAA,EACAsE,EAAAV,EAAA/P,CAAA,EACAyQ,GAAAzQ,aAAA0Q,SACAD,IACAtC,EAAA0B,wBAAA7P,IAGAmM,EAAAwE,yBAAA,EACA1B,EAAAd,EAAA0B,yBAAAX,EAAA,CAAA,EAEA,EACA0B,EAAA,SAAAzE,GACA,IA4BA0E,EAQAC,EApCA9Q,EAAAiN,gBAAAd,CAAA,EAEA4E,GADA7C,EAAA,EACA,MACA,EAAAC,EAAAC,eAAAzO,QACAqR,EAAAvE,UAAA0B,EAAAC,eAAA,SAAA6C,GAEA,OADAA,EAAA3C,UACA3E,SAAA3J,CAAA,CACA,CAAA,GACA,EAEA+Q,EADA5E,EAAA+E,SACA/C,EAAAC,eAAAD,EAAAC,eAAAzO,OAAA,GAAA8O,iBAEAN,EAAAC,eAAA,GAAAI,kBAEArC,EAAA+E,SAQA,IAFAC,GALAA,EAAA1E,UAAA0B,EAAAC,eAAA,SAAAgD,GACA5C,EAAA4C,EAAA5C,kBACA,OAAAxO,IAAAwO,CACA,CAAA,GACA,GAAAL,EAAAC,eAAA4C,GAAA1C,YAAAtO,EACAgR,EAEAG,KACAE,EAAA,IAAAF,EAAAhD,EAAAC,eAAAzO,OAAA,EAAAwR,EAAA,EAEAJ,EADA5C,EAAAC,eAAAiD,GACA5C,kBAUA,IAFAoC,GALAA,EAAApE,UAAA0B,EAAAC,eAAA,SAAAkD,GACA7C,EAAA6C,EAAA7C,iBACA,OAAAzO,IAAAyO,CACA,CAAA,GACA,GAAAN,EAAAC,eAAA4C,GAAA1C,YAAAtO,EACAgR,EAEAH,KACAC,EAAAD,IAAA1C,EAAAC,eAAAzO,OAAA,EAAA,EAAAkR,EAAA,EAEAE,EADA5C,EAAAC,eAAA0C,GACAtC,mBAIAuC,EAAAnD,EAAA,eAAA,EAEAmD,IACA5E,EAAAnL,eAAA,EACAiO,EAAA8B,CAAA,EAEA,EACAvB,EAAA,SAAArD,GACAD,cAAAC,CAAA,GAAA,CAAA,IAAAU,eAAAc,EAAAgC,kBAAAxD,CAAA,GACAA,EAAAnL,eAAA,EACA0K,EAAA4E,WAAA,GAGAjE,WAAAF,CAAA,GACAyE,EAAAzE,CAAA,CAGA,EACAoD,EAAA,SAAApD,GACA,IAGAnM,EAHA6M,eAAAc,EAAA0C,wBAAAlE,CAAA,IAGAnM,EAAAiN,gBAAAd,CAAA,EACA4D,EAAA/P,CAAA,IAGA6M,eAAAc,EAAA6C,kBAAArE,CAAA,IAGAA,EAAAnL,eAAA,EACAmL,EAAAwE,yBAAA,EACA,EAuCAjF,EAAA,CACA6F,SAAA,SAAAC,GACA,IAGAC,EACAC,EACAC,EAwBA,OA7BAxD,EAAAW,SAGA2C,EAAAlE,EAAAiE,EAAA,YAAA,EACAE,EAAAnE,EAAAiE,EAAA,gBAAA,GACAG,EAAApE,EAAAiE,EAAA,mBAAA,IAEAtD,EAAA,EAEAC,EAAAW,OAAA,CAAA,EACAX,EAAA2B,OAAA,CAAA,EACA3B,EAAAyB,4BAAA5B,EAAAiC,cACAwB,GACAA,EAAA,EAEAG,EAAA,WACAD,GACAzD,EAAA,EAEAW,EAAA,EACA6C,GACAA,EAAA,CAEA,EACAC,EACAA,EAAAxD,EAAAE,WAAA9N,OAAA,CAAA,EAAAsR,KAAAD,EAAAA,CAAA,EAGAA,EAAA,GACA1U,IACA,EACAoT,WAAA,SAAAwB,GACA,IAUAC,EACAC,EAIAzB,EACA0B,EAeA,OA/BA9D,EAAAW,SAGAoD,aAAA/D,EAAAY,sBAAA,EACAZ,EAAAY,uBAAA,KAAA,EACAU,EAAA,EACAtB,EAAAW,OAAA,CAAA,EACAX,EAAA2B,OAAA,CAAA,EACAvE,iBAAAO,eAAAJ,CAAA,EACAyG,EAAA5E,EAAAuE,EAAA,cAAA,EACAC,EAAAxE,EAAAuE,EAAA,kBAAA,EACAE,EAAAzE,EAAAuE,EAAA,qBAAA,EACAK,GACAA,EAAA,EAGAF,EAAA,WACA3F,MAAA,WACAiE,GACAtB,EAAAN,EAAAR,EAAAyB,2BAAA,CAAA,EAEAmC,GACAA,EAAA,CAEA,CAAA,CACA,GAVAxB,EAAAhD,EAAAuE,EAAA,cAAA,yBAAA,IAWAE,EACAA,EAAArD,EAAAR,EAAAyB,2BAAA,CAAA,EAAAiC,KAAAI,EAAAA,CAAA,EAGAA,EAAA,GACA/U,IACA,EACA0O,MAAA,WAMA,MALAuC,CAAAA,EAAA2B,QAAA3B,EAAAW,SAGAX,EAAA2B,OAAA,CAAA,EACAL,EAAA,GACAvS,IACA,EACA6O,QAAA,WAOA,OANAoC,EAAA2B,QAAA3B,EAAAW,SAGAX,EAAA2B,OAAA,CAAA,EACA5B,EAAA,EACAW,EAAA,GACA3R,IACA,EACAkV,wBAAA,SAAAC,GACAC,EAAA,GAAA/R,OAAA8R,CAAA,EAAA/L,OAAAiM,OAAA,EAOA,OANApE,EAAAE,WAAAiE,EAAAjI,IAAA,SAAA3M,GACA,MAAA,UAAA,OAAAA,EAAAsQ,EAAAC,cAAAvQ,CAAA,EAAAA,CACA,CAAA,EACAyQ,EAAAW,QACAZ,EAAA,EAEAhR,IACA,CACA,EAEA,OADAwO,EAAA0G,wBAAA/E,CAAA,EACA3B,CACA,EAGA8G,sBACApR,YAAAqR,GACAvV,KAAAwV,QAAAD,EACAvV,KAAAyV,WAAA,OACAzV,KAAA0V,UAAAxN,QAAAM,QAAA,CACA,CACAmN,eACA,OAAA3V,KAAA0V,SACA,CACAE,uBACA,OAAA5V,KAAAwV,mBAAAK,qBAAA,CAAA7V,KAAAwV,SAAAxV,KAAAwV,QAAAI,gBACA,CACAE,SACA9V,KAAA4V,iBAAA7I,QAAA,GAAAgJ,EAAAD,OAAA,CAAA,CACA,CACAE,SACAhW,KAAA4V,iBAAA7I,QAAA,GAAAgJ,EAAAC,OAAA,CAAA,CACA,CACAC,OACAjW,KAAAyV,WAAA,UACAzV,KAAAwV,QAAAS,KAAA,EACAjW,KAAA0V,UAAA1V,KAAAwV,QAAAG,SACA3V,KAAA0V,UAAAf,KAAA,KACA3U,KAAAyV,WAAA,UACA,EAAA,IACAzV,KAAAyV,WAAA,MACA,CAAA,CACA,CACA,EACAI,2BACA3R,YAAApB,EAAAoT,EAAAzR,EAAA,IACA3B,IAGA,cAAAjC,OACAb,KAAAmW,WAAA,IAAAC,UAAA,IAAAC,eAAAvT,EAAAoT,EAAAzR,CAAA,CAAA,GAEAA,EAAA,KAAA,WACAzE,KAAAmW,WAAArT,EAAAwT,QAAAJ,EAAAzR,CAAA,EACAzE,KAAAmW,WAAAzH,MAAA,GAEA1O,KAAAmW,WAAA7U,iBAAA,SAAA,KACAwB,EAAAkF,MAAAC,QAAAiO,EAAA9U,eAAA,SAAA,EAAA8U,EAAA,QAAAA,EAAA,QAAAzT,OAAA,GAAA,KACAK,EAAAkF,MAAA+D,WAAAmK,EAAA9U,eAAA,YAAA,EAAA8U,EAAA,WAAAA,EAAA,WAAAzT,OAAA,GAAA,IACA,CAAA,EACA,CACAkT,eACA,OAAA3V,KAAAmW,WAGAnW,KAAAmW,WAAAR,UAAA,IAAAzN,QAAA,GAAAlI,KAAAmW,WAAAI,SAAA/N,CAAA,EAFAN,QAAAM,QAAA,CAGA,CACAyN,OACAjW,KAAAmW,aACAnW,KAAAmW,WAAAK,UAAA,KACAxW,KAAAmW,WAAAF,KAAA,EAEA,CACAH,SACA9V,KAAAmW,YACAnW,KAAAmW,WAAAL,OAAA,CAEA,CACAE,SACAhW,KAAAmW,YACAnW,KAAAmW,WAAAH,OAAA,CAEA,CACA,EACAS,kBACAvS,YAAAwS,GACA1W,KAAA2W,iBAAAD,EACA1W,KAAA0V,UAAAxN,QAAAM,QAAA,CACA,CACAmN,eACA,OAAA3V,KAAA0V,SACA,CACAE,uBACA,OAAA5V,KAAA2W,iBAAAC,QAAA,GACArB,aAAAM,qBAAAN,EAAAA,EAAAK,gBACA,CACA,CACA,EACAiB,6BAAAJ,YACAR,OACA,IAAAa,EAAA,GACA,IAAA,MAAAvB,KAAAvV,KAAA2W,iBACApB,EAAAU,KAAA,EACAa,EAAA3U,KAAAoT,EAAAI,QAAA,EAEA3V,KAAA0V,UAAAxN,QAAA6O,IAAAD,CAAA,CACA,CACA,EACAE,6BAAAP,YACAR,OACAjW,KAAA0V,UAAA,IAAAxN,QAAA+O,MAAAzO,EAAA0O,KACA,IACA,IAAA,MAAA3B,KAAAvV,KAAA2W,iBACApB,EAAAU,KAAA,EACAkB,MAAA5B,EAAAI,SAEAnN,EAAA,CAGA,CAFA,MAAA4O,GACAF,EAAA,CACA,CACA,CAAA,CACA,CACA,EAEAG,4BAAA3Q,kBAEAxC,cACA0C,MAAA,CACA,CAEA0Q,oBAEAtX,KAAAuX,OAAAjO,MAAAkO,KAAAxX,KAAAuJ,iBAAA,sBAAA,CAAA,EACAvJ,KAAAyX,QAAAzX,KAAA+Q,cAAA,wBAAA,EACA/Q,KAAA0X,OAAA1X,KAAAyX,QAAA1G,cAAA,QAAA,EAEA/Q,KAAA2X,gBAAA,EAEA3X,KAAA4X,aAAA,CAEA,CAEAA,eACA,GAAA5X,KAAA6X,MACA7X,KAAA0X,OAAAnL,SAAA,CAAA,EAGAvM,KAAA0X,OAAAnL,SAAA,CAAA,CAEA,CAEAsL,YAIA,IAAA3W,IAAAmB,EAAA,EAAAA,EAAArC,KAAAuX,OAAA9U,OAAAJ,CAAA,GAEA,GAAA,GADArC,KAAAuX,OAAAlV,GACAwV,MACA,MAAA,CAAA,EAIA,MAAA,CAAA,CAEA,CAEAF,mBAIA,EAEAG,UAAA,CAEAC,mBAAAtT,GAEA,GAAAA,GAAAA,EAAAuT,QAAA,CAIA9W,IAAA+W,EAAApX,OAAAqX,cAAAC,QAAAC,gBAEA,IAAAC,EAAA5T,EAAA4T,MAEAL,EAAAvT,EAAAuT,QACAM,EAAAN,EAAA/W,GAAAsE,QAAA,yBAAA,EAAA,EACAgT,EAAAP,EAAAQ,MACAC,EAAAT,EAAAU,iBAAAV,EAAAU,iBAAAjZ,MAAA,GACAkZ,EAAAX,EAAAY,aAAAZ,EAAAY,aAAAnZ,MAAA,2BACAoZ,EAAAb,EAAAc,IACAC,EAAAf,EAAAgB,gBAAAhB,EAAAgB,gBAAAC,UAAAC,MAAAC,YAAAnB,EAAAoB,OAAAC,MAAA,GAAA3P,KAAAoP,IAEAQ,EAAAtB,EAAAuB,SAAAF,MAAA,GAAA3P,KACA8P,EAAAF,EAAArY,GAAAsE,QAAA,gCAAA,EAAA,EACAkU,EAAAH,EAAAI,MAAA,IAAAC,OAAAL,EAAAI,MAAAE,MAAA,EAAA,GACAC,EAAAP,EAAAQ,eAAA,IAAAH,OAAAL,EAAAQ,eAAAF,MAAA,EAAA,GACAG,EAAAT,EAAAU,SAAAva,MAAAka,OAAAL,EAAAU,SAAAva,KAAA,EAAA,GAGAwa,EADAjC,EAAAuB,SAAAF,MAAArB,EAAAuB,SAAAF,MAAA5W,OAAA,GAAAiH,KACAzI,GAAAsE,QAAA,gCAAA,EAAA,EAqBA,OAFA0S,GAFAA,GADAA,GADAA,GADAA,GAFAA,GADAA,GADAA,GADAA,GADAA,GADAA,GAFAA,EAAAA,EAAAiC,WAAA,SAAA7B,CAAA,GAEA6B,WAAA,cAAA5B,CAAA,GACA4B,WAAA,6BAAAzB,CAAA,GACAyB,WAAA,iBAAA3B,CAAA,GACA2B,WAAA,iBAAAvB,CAAA,GACAuB,WAAA,eAAArB,CAAA,GACAqB,WAAA,qBAAAnB,CAAA,GAEAmB,WAAA,oBAAAV,CAAA,GACAU,WAAA,iBAAAT,CAAA,GACAS,WAAA,yBAAAL,CAAA,GACAK,WAAA,8BAAAH,CAAA,GAEAG,WAAA,mBAAAD,CAAA,CAxCA,CA4CA,EAEAE,iBAAA1V,GAEA,GAAAA,GAAAA,EAAAuT,QAAA,CAIA9W,IAAA+W,EAAApX,OAAAqX,cAAAC,QAAAiC,sBAEA,IAAApC,EAAAvT,EAAAuT,QACAM,EAAAN,EAAA/W,GAAAsE,QAAA,yBAAA,EAAA,EACAgT,EAAAP,EAAAQ,MACAG,EAAAX,EAAAY,aAAAZ,EAAAY,aAAAnZ,MAAA,GACAgZ,EAAAT,EAAAqC,gBAAA7Y,QAAA,sBAAA,EAAAwW,EAAAqC,gBAAA7T,MAAA,sBAAA,EAAA,GAAAwR,EAAAqC,gBACAxB,EAAAb,EAAAsC,eACAvB,EAAAf,EAAAoB,OAAAC,MAAA,GAAA3P,KAAAoP,IAEAQ,EAAAtB,EAAAuB,SAAAF,MAAA,GAAA3P,KACA6Q,EAAAjB,EAAArY,GAAAsE,QAAA,gCAAA,EAAA,EACAiV,EAAAlB,EAAAI,MAAA,IAAAC,OAAAL,EAAAI,MAAAE,MAAA,EAAA,GACAa,EAAAnB,EAAAQ,eAAA,IAAAH,OAAAL,EAAAQ,eAAAF,MAAA,EAAA,GAEAN,EAAArY,GAAAsE,QAAA,gCAAA,EAAA,EAaA,OAFA0S,GADAA,GADAA,GAFAA,GADAA,GADAA,GADAA,GADAA,GADAA,EAAAA,EAAAiC,WAAA,cAAA5B,CAAA,GACA4B,WAAA,uBAAAzB,CAAA,GACAyB,WAAA,iBAAA3B,CAAA,GACA2B,WAAA,iBAAAvB,CAAA,GACAuB,WAAA,eAAArB,CAAA,GACAqB,WAAA,qBAAAnB,CAAA,GAEAmB,WAAA,cAAAK,CAAA,GACAL,WAAA,iBAAAM,CAAA,GACAN,WAAA,yBAAAO,CAAA,CA5BA,CAgCA,EAEAC,YAAA,SAAAjW,GAEA,IAKAkW,EALA,GAAAlW,EAaA,OATAA,EAAAI,KACA8V,EAAAlW,EAAAkW,YAAA,6DACAlW,EAAAmW,SAAA,kBAGAnW,EAAAI,SAAA8V;YACAlW,EAAAI;yBAKA,EAEAgW,iBAAA,SAAApW,GAEA,GAAAA,EAIA,4CAAAA,EAAAqW,aAEA,EAEAC,eAAA,SAAAtW,GAEA,GAAAA,EAAA,CAMAvD,IAAAD,EAAA,kBACA4D,EAAA,kBACAmW,EAAA,kBAyBA;;+HAZAA,EAXAvW,IAEAA,EAAAxD,KACAA,EAAAwD,EAAAxD,IAGAwD,EAAAI,OACAA,EAAAJ,EAAAI,MAGAJ,EAAAuW,aACAvW,EAAAuW,YAOAA;oEACAnW,UAAA5D;;OA3BA,CAiCA,CAEA,EAEAga,iCAAAvU,kBAEAxC,cACA0C,MAAA,CACA,CAEA0Q,oBAEAtX,KAAAkb,cAAAlb,KAAAkH,QAAA,gBAAA,EACAlH,KAAA2X,gBAAA,CAEA,CACAA,kBAEA3X,KAAAmb,MAAA7Z,iBAAA,QAAAtB,KAAAob,aAAAhb,KAAAJ,IAAA,CAAA,CAEA,CACAob,aAAAzY,GAEA3C,KAAAkb,cAAAtD,aAAA,EAEA,IAAA5X,KAAAmb,MAAA1b,OACAO,KAAAqb,WACA,IAAArb,KAAAqb,UAAAF,MAAA1b,OACAO,KAAAsb,KAAA,EACAtb,KAAAqb,UAAAF,MAAAlI,MAAA,GAMAjT,KAAAub,WACA,GAAAvb,KAAAub,UAAA1D,QAKA,GAAA7X,KAAA6X,OAAA,GAAA7X,KAAAwb,YACAxb,KAAAub,WACA,GAAAvb,KAAAub,UAAAE,SACA,GAAAzb,KAAAub,UAAAG,WACA1b,KAAAub,UAAAI,KAAA,EAMA3b,KAAAub,WACA,GAAAvb,KAAA4b,gBACA5b,KAAAub,UAAAD,KAAA,EAKA,CACAA,aAEAtb,KAAA8H,UAAAC,IAAA,iCAAA,EAEA,IAAA8T,EAAA,IAAAvG,gBAAA,IAAAO,qBAAA7V,KACA,CACA+L,WAAA,CAAA,UAAA,UACA+P,UAAA,CAAA,gBAAA,oBACA7T,QAAA,CAAA,IAAA,IACA,EACA,CACA8T,SAAA,IACAC,OAAA,gCACA,CACA,CAAA,EAEAH,EAAA5F,KAAA,EAEAkB,MAAA0E,EAAAlG,SAEA3V,KAAA8H,UAAAmU,OAAA,+BAAA,EACAjc,KAAA8H,UAAAmU,OAAA,iCAAA,CAEA,CACAN,aAEA3b,KAAA8H,UAAAC,IAAA,iCAAA,EAEA,IAAA8T,EAAA,IAAAvG,gBAAA,IAAAO,qBAAA7V,KACA,CACA+L,WAAA,CAAA,SAAA,WACA+P,UAAA,CAAA,mBAAA,iBACA7T,QAAA,CAAA,IAAA,IACA,EACA,CACA8T,SAAA,IACAC,OAAA,gCACA,CACA,CAAA,EAEAH,EAAA5F,KAAA,EAEAkB,MAAA0E,EAAAlG,SAEA3V,KAAA8H,UAAAC,IAAA,+BAAA,EACA/H,KAAA8H,UAAAmU,OAAA,iCAAA,CAEA,CACApE,YACA,MAAA,IAAA7X,KAAAmb,MAAA1b,KAIA,CACAgc,cACA,OAAAzb,KAAA8H,UAAA2E,SAAA,+BAAA,CACA,CACAiP,gBACA,OAAA1b,KAAA8H,UAAA2E,SAAA,iCAAA,CACA,CACA0O,YACA,OAAAnb,KAAA+Q,cAAA,OAAA,CACA,CACAyK,kBACA,OAAA,KAAA,IAAAxb,KAAAub,SAMA,CACAK,qBACA1a,IAAAgb,EAAAlc,KAAAmc,mBACA,KAAAD,GAAA,CACA,GAAAA,EAAAha,QAAA,sBAAA,GACA,IAAAga,EAAAf,MAAA1b,MACA,MAAA,CAAA,EAGAyc,EAAAA,EAAAC,kBACA,CACA,MAAA,CAAA,CACA,CACAZ,gBACAra,IAAAgb,EAAAlc,KAAAmc,mBACA,KAAAD,GAAA,CACA,GAAAA,EAAAha,QAAA,sBAAA,EAAA,OAAAga,EACAA,EAAAA,EAAAC,kBACA,CACA,CACAd,gBACAna,IAAAgb,EAAAlc,KAAAoc,uBACA,KAAAF,GAAA,CACA,GAAAA,EAAAha,QAAA,sBAAA,EAAA,OAAAga,EACAA,EAAAA,EAAAE,sBACA,CACA,CACA,EAEAC,4BAAA3V,kBAEAxC,cACA0C,MAAA,CACA,CAEA0Q,oBAEAtX,KAAAsc,YAAAtc,KAAAuJ,iBAAA,wBAAA,EACAvJ,KAAAuc,YAAAvc,KAAAuJ,iBAAA,wBAAA,EAEAvJ,KAAAsc,YAAAvP,QAAA,IACAyP,EAAAlb,iBAAA,QAAAtB,KAAAyc,aAAArc,KAAAJ,IAAA,CAAA,CACA,CAAA,EAEAA,KAAAuc,YAAAxP,QAAA,IACAyP,EAAAlb,iBAAA,QAAAtB,KAAA0c,aAAAtc,KAAAJ,IAAA,CAAA,CACA,CAAA,CAEA,CAEAyc,qBAEA,IASAZ,EATA7b,KAAA2c,WAIA3c,KAAA8H,UAAAC,IAAA,4BAAA,EACA/H,KAAA2c,SAAA7U,UAAAC,IAAA,4BAAA,EAEAnD,aAAA5E,KAAA2c,SAAA,iCAAA,GAEAd,EAAA,IAAAvG,gBAAA,IAAAO,qBAAA7V,KACA,CACA+L,WAAA,CAAA,UAAA,UACA+P,UAAA,CAAA,iBAAA,oBACA7T,QAAA,CAAA,IAAA,IACA,EACA,CACA8T,SAAA,IACAC,OAAA,gCACA,CACA,CAAA,GAEA/F,KAAA,EAEAkB,MAAA0E,EAAAlG,SACA3V,KAAA8H,UAAAmU,OAAA,0BAAA,EACAjc,KAAA8H,UAAAmU,OAAA,4BAAA,EAEA,IAAA3G,gBAAA,IAAAO,qBAAA7V,KAAA2c,SACA,CACA5Q,WAAA,CAAA,SAAA,WACA+P,UAAA,CAAA,kBAAA,kBACA7T,QAAA,CAAA,IAAA,IACA,EACA,CACA8T,SAAA,IACAC,OAAA,gCACA,CACA,CAAA,EAEA/F,KAAA,EAEArR,aAAA5E,KAAA2c,SAAA,kCAAA,EAEA3c,KAAA2c,SAAA7U,UAAAC,IAAA,0BAAA,EACA/H,KAAA2c,SAAA7U,UAAAmU,OAAA,4BAAA,EAEA,CAEAS,qBAEA,IASAb,EATA7b,KAAA4c,WAIAhY,aAAA5E,KAAA4c,SAAA,iCAAA,EAEA5c,KAAA8H,UAAAC,IAAA,4BAAA,EACA/H,KAAA4c,SAAA9U,UAAAC,IAAA,4BAAA,GAEA8T,EAAA,IAAAvG,gBAAA,IAAAO,qBAAA7V,KACA,CACA+L,WAAA,CAAA,UAAA,UACA+P,UAAA,CAAA,iBAAA,oBACA7T,QAAA,CAAA,IAAA,IACA,EACA,CACA8T,SAAA,IACAC,OAAA,gCACA,CACA,CAAA,GAEA/F,KAAA,EAEAkB,MAAA0E,EAAAlG,SACA3V,KAAA8H,UAAAmU,OAAA,0BAAA,EACAjc,KAAA8H,UAAAmU,OAAA,4BAAA,EAEA,IAAA3G,gBAAA,IAAAO,qBAAA7V,KAAA4c,SACA,CACA7Q,WAAA,CAAA,SAAA,WACA+P,UAAA,CAAA,kBAAA,kBACA7T,QAAA,CAAA,IAAA,IACA,EACA,CACA8T,SAAA,IACAC,OAAA,gCACA,CACA,CAAA,EAEA/F,KAAA,EAEAjW,KAAA4c,SAAA9U,UAAAC,IAAA,0BAAA,EACA/H,KAAA4c,SAAA9U,UAAAmU,OAAA,4BAAA,EAEA,CAEAU,eACAzb,IAAAgb,EAAAlc,KAAAmc,mBACA,KAAAD,GAAA,CACA,GAAAA,EAAAha,QAAA,iBAAA,EAAA,OAAAga,EACAA,EAAAA,EAAAC,kBACA,CACA,CAEAS,eACA1b,IAAAgb,EAAAlc,KAAAoc,uBACA,KAAAF,GAAA,CACA,GAAAA,EAAAha,QAAA,iBAAA,EAAA,OAAAga,EACAA,EAAAA,EAAAE,sBACA,CACA,CAEA,EAEAS,wBAAAnW,kBACAoW,gCACA,MAAA,CAAA,OACA,CACA5Y,cACA0C,MAAA,EACAE,QAAAC,UAIA,CACAuQ,oBAGAtX,KAAA+c,SAAA,GAEA/c,KAAAgd,gBAAAhd,KAAA+Q,cAAA,kCAAA,EAEA/Q,KAAAid,iBAAAjd,KAAA+Q,cAAA,2BAAA,EAEA/Q,KAAAqH,SAAA5F,GAAA,QAAA,kCAAAzB,KAAAkd,aAAA9c,KAAAJ,IAAA,CAAA,EACAA,KAAAqH,SAAA5F,GAAA,QAAA,0BAAAzB,KAAAmd,KAAA/c,KAAAJ,IAAA,CAAA,EACAA,KAAAqH,SAAA5F,GAAA,QAAA,0BAAAzB,KAAAod,KAAAhd,KAAAJ,IAAA,CAAA,EACAA,KAAAqH,SAAA5F,GAAA,QAAA,0BAAAzB,KAAAqd,KAAAjd,KAAAJ,IAAA,CAAA,EACAA,KAAAqH,SAAA5F,GAAA,QAAA,0BAAAzB,KAAAsd,cAAAld,KAAAJ,IAAA,CAAA,EAEAA,KAAAqH,SAAA5F,GAAA,QAAA,gCAAAzB,KAAAud,kBAAAnd,KAAAJ,IAAA,CAAA,EACAA,KAAAqH,SAAA5F,GAAA,SAAA,iBAAAzB,KAAAwd,gBAAApd,KAAAJ,IAAA,CAAA,EAGAA,KAAAsB,iBAAA,kCAAAtB,KAAAyd,gBAAArd,KAAAJ,IAAA,CAAA,EACAA,KAAAsB,iBAAA,0CAAAtB,KAAA0d,mBAAAtd,KAAAJ,IAAA,CAAA,EACAA,KAAAsB,iBAAA,gCAAAtB,KAAA2d,mBAAAvd,KAAAJ,IAAA,CAAA,EAEAA,KAAAsB,iBAAA,kCAAAtB,KAAA4d,gBAAAxd,KAAAJ,IAAA,CAAA,EACAA,KAAAsB,iBAAA,0CAAAtB,KAAA0d,mBAAAtd,KAAAJ,IAAA,CAAA,EACAA,KAAAsB,iBAAA,gCAAAtB,KAAA6d,mBAAAzd,KAAAJ,IAAA,CAAA,EAGAA,KAAA8d,gBAEA9d,KAAA8d,cAAAxc,iBAAA,iCAAAtB,KAAA+d,oBAAA3d,KAAAJ,IAAA,CAAA,EACAA,KAAA8d,cAAAxc,iBAAA,kCAAAtB,KAAAge,qBAAA5d,KAAAJ,IAAA,CAAA,EACAA,KAAAqH,SAAA5F,GAAA,QAAA,gBAAAzB,KAAAie,mBAAA7d,KAAAJ,IAAA,CAAA,EAGAA,KAAA8d,cAAAxc,iBAAA,wCAAAtB,KAAA0d,mBAAAtd,KAAAJ,IAAA,CAAA,GAGAa,OAAAqX,cAAAgG,WAQAle,KAAA+Q,cAAA,qBAAA,GACA/Q,KAAA+Q,cAAA,qBAAA,EAAAzP,iBAAA,SAAAtB,KAAAme,wBAAA/d,KAAAJ,IAAA,CAAA,EAGAA,KAAA+Q,cAAA,gCAAA,GACA/Q,KAAA+Q,cAAA,gCAAA,EAAAzP,iBAAA,QAAAtB,KAAAoe,sBAAAhe,KAAAJ,IAAA,CAAA,GAKAA,KAAAsB,iBAAA,8BAAAtB,KAAAqe,YAAAje,KAAAJ,IAAA,CAAA,EAEAA,KAAAgd,iBACAhd,KAAAgd,gBAAA1b,iBAAA,QAAAtB,KAAAse,WAAAle,KAAAJ,IAAA,CAAA,EAGAA,KAAAue,iBAAA,CAEA,CAEAH,sBAAAzb,GAEAA,EAAAmB,eAAA,EAEAc,aAAA5E,KAAA,6BAAA,CAEA,CAEAme,wBAAAxb,GAEAA,EAAAmB,eAAA,EAEAc,aAAA5E,KAAA,6BAAA,CAEA,CAEA4d,gBAAAjb,GACA3C,KAAAwe,kBAAA,CACA,CAEAf,gBAAA9a,IAGAkb,mBAAAlb,GACA3C,KAAAwe,kBAAA,EACAxe,KAAAye,kBAAA,CACA,CAEAd,mBAAAhb,GACA3C,KAAAye,kBAAA,CACA,CAIAH,WAAA3b,GAEA3C,KAAA0e,eAAA,EAEA1e,KAAA2e,cAAA,EACA3e,KAAA4e,oBAAA,EAEA5e,KAAA6e,kBAAA,CAEA,CAEAH,iBAEA,IACA3B,EADA/c,KAAA+Q,cAAA,YAAA,EACAxH,iBAAA,OAAA,EAEAvJ,KAAA+c,SAAA,GAEAA,EAAAhQ,QAAAkC,IACAjP,KAAA+c,SAAA5a,KAAA8M,EAAAxP,KAAA,CACA,CAAA,CAEA,CAEAkf,gBAIAzd,IAAA+W,EAAA,GAEA/W,IAAAkQ,EAAAtQ,SAAAge,cAAA,uBAAA,EACA1N,EAAAtJ,UAAAC,IAAA,kBAAA,EAEA,IAAA7G,IAAAmB,EAAA,EAAAA,EAAArC,KAAA+c,SAAAta,OAAAJ,CAAA,GAAA,CAEA,MAAA0c,EAAA/e,KAAA+c,SAAA1a,GACA2c,EAAAC,cAAAjf,KAAA+c,SAAA1a,EAAA,EACA6c,EAAA7c,EAAA,EAEAnB,IAAAie,EAAAre,SAAAge,cAAA,KAAA,EACAM,EAAAve,OAAAqX,cAAAmH,MAAAC,IAAApF,WAAA,UAAAgF,CAAA,EAGAK,GAFAJ,EAAAxa,UAAAya,EAEAD,EAAA5V,iBAAA,YAAA,GACAiW,EAAAL,EAAA5V,iBAAA,iBAAA,EACAkW,EAAAN,EAAA5V,iBAAA,4BAAA,EAEAiW,EAAAzS,QAAAkC,IACAA,EAAAyQ,UAAAX,CACA,CAAA,EAEAQ,EAAAxS,QAAAkC,IACAA,EAAA0Q,QAAAL,IAAAJ,CACA,CAAA,EAEAO,EAAA1S,QAAAkC,IACAA,EAAA0Q,QAAAL,IAAAN,CACA,CAAA,EAEA/G,GAAAkH,EAAAxa,SAEA,CAIAyM,EAAAzM,UAAAsT,EAEAjY,KAAAid,iBAAAtY,UAAA,GACA3E,KAAAid,iBAAA2C,YAAAxO,CAAA,EAGApR,KAAAid,iBAAAlM,cAAA,qBAAA,EAAAjJ,UAAAC,IAAA,sBAAA,CAEA,CAEAyV,gBAAA7a,GAEA,IAAAwY,EAAAxY,EAAAG,OAAAoE,QAAA,gBAAA,EACA+V,EAAA9B,EAAAjU,QAAA,2BAAA,EACA2Y,EAAA1E,EAAAjU,QAAA,qBAAA,EACAgY,EAAAW,EAAAF,QAAAL,IACA,MAAAQ,EAAAD,EAAA9O,cAAA,gBAAA,EAAAtR,MACAwd,EAAA1T,kDAAA2V,KAAA,EAEAnS,QAAA8S,IAEAA,EAAAtW,iBAAA,sBAAA,EAEAwD,QAAAkC,IACAA,EAAAyQ,UAAA,SAAAI,EAAA,WAAA,QACA,CAAA,CAEA,CAAA,CAEA,CAEAvC,kBAAA5a,GAEA,IACAkd,EADAld,EAAAG,OAAAoE,QAAA,+BAAA,EACAA,QAAA,qBAAA,EAEAgY,EAAAW,EAAAF,QAAAL,IACA,MAAAQ,EAAAD,EAAA9O,cAAA,gBAAA,EAAAtR,MAEAO,KAAAuJ,kDAAA2V,KAAA,EAEAnS,QAAA8S,IAEA,IAAAE,EAAAF,EAAAtW,iBAAA,sBAAA,EACAyW,EAAAH,EAAAtW,iBAAA,4BAAA,EACAsW,EAAAtW,iBAAA,wBAAA,EAEAwD,QAAAkC,IACAA,EAAAyQ,UAAA,SAAAI,EAAA,KAAA,KACA,CAAA,EAEAC,EAAAhT,QAAAkC,IACAA,EAAAyQ,UAAA,SAAAI,EAAA,WAAA,QACA,CAAA,EAEAE,EAAAjT,QAAAkC,IACAA,EAAAyQ,UAAA,SAAAI,EAAA,MAAA,KACA,CAAA,CAEA,CAAA,CAEA,CAIAzB,YAAA1b,GAEA3C,KAAAigB,cAAA,EAEAC,SAAAC,KAAAtf,OAAAqX,cAAAkI,UAAAC,SAGA,CAEAJ,gBAEA,IAAAnb,EAAA9E,KAAAsgB,iBAAAtgB,KAAAugB,YAAA,CAAA,EACAC,EAAAC,KAAAC,UAAA5b,CAAA,EAEA6b,aAAAC,QAAA,UAAAJ,CAAA,CAEA,CAEAD,cAEArf,IAAA4D,EAAA,CACA+b,KAAA,EACA,EAEA,IAAA3f,IAAAmB,EAAA,EAAAA,EAAArC,KAAA+c,SAAAta,OAAAJ,CAAA,GAAA,CAEA,IAAA6c,EAAA7c,EAAA,EAEAye,EAAA,GAgBAC,GAdAD,EAAAjc,KAAAmc,cAAAlgB,SAAAiQ,iDAAAmO,qBAAA,EAAAQ,SAAA,EACAoB,EAAAG,IAAAD,cAAAlgB,SAAAiQ,4BAAAmO,SAAA,EAAAzf,KAAA,EAEAqhB,EAAAI,SAAA,OAAApgB,SAAAiQ,4BAAAmO,cAAA,EAAAzf,MACAqhB,EAAAK,MAAAH,cAAAlgB,SAAAiQ,4BAAAmO,WAAA,EAAAzf,KAAA,EAEAqhB,EAAAM,OAAAzH,OAAA7Y,SAAAiQ,4BAAAmO,YAAA,EAAAzf,KAAA,EACAqhB,EAAAO,aAAA1H,OAAA7Y,SAAAiQ,4BAAAmO,kBAAA,EAAAzf,KAAA,EACAqhB,EAAAQ,iBAAA,QAAAxgB,SAAAiQ,4BAAAmO,sBAAA,EAAAzf,MACAqhB,EAAAS,kBAAAP,cAAAlgB,SAAAiQ,4BAAAmO,uBAAA,EAAAzf,KAAA,EACAqhB,EAAAU,eAAAR,cAAAlgB,SAAAiQ,4BAAAmO,oBAAA,EAAAzf,KAAA,EACAqhB,EAAAW,eAAAT,cAAAlgB,SAAAiQ,4BAAAmO,oBAAA,EAAAzf,KAAA,EAGAqB,SAAAiQ,4BAAAmO,SAAA,EAAAzf,OACAiiB,EAAA5gB,SAAAiQ,4BAAAmO,cAAA,EAAAzf,MAEA,QAAAiiB,EACAZ,EAAAa,cAAA,GAAAZ,EAGA,SAAAW,IACAZ,EAAAa,cAAAhI,OAAAoH,CAAA,GAGAjc,EAAA+b,KAAA1e,KAAA2e,CAAA,CAEA,CAEA5f,IAAA0gB,EAgBA,OAdA9gB,SAAAiQ,cAAA,gBAAA,EACA6Q,EAAA9gB,SAAAiQ,cAAA,gBAAA,EAAAtR,MAGAoB,OAAAqX,cAAAgG,WACA0D,EAAA/gB,OAAAqX,cAAAgG,SAAA2D,OAGAD,IACA9c,EAAAoZ,SAAA,CACA2D,MAAAD,CACA,GAGA9c,CAEA,CAEAwb,iBAAAxb,GAKA,GAAAA,EAAA,CAIA,IAAAgc,EAAAhc,EAEA,IAAA5D,IAAA4gB,EAAA,EAAAA,EAAAhB,EAAAD,KAAApe,OAAAqf,CAAA,GAAA,CACA,IAAAxC,EAAAwB,EAAAD,KAAAiB,GACAxC,EAAAza,KAAA,KAAAya,EAAAza,KAAA,OAAAya,EAAAza,KACAya,EAAA2B,IAAA,KAAA3B,EAAA2B,IAAA,OAAA3B,EAAA2B,IACA3B,EAAA4B,SAAA,KAAA5B,EAAA4B,SAAA,OAAA5B,EAAA4B,SACA5B,EAAA6B,MAAA,KAAA7B,EAAA6B,MAAA,OAAA7B,EAAA6B,MACA7B,EAAA8B,OAAA,KAAA9B,EAAA8B,OAAA,IAAA9B,EAAA8B,OACA9B,EAAA+B,aAAA,KAAA/B,EAAA+B,aAAA,IAAA/B,EAAA+B,aACA/B,EAAAgC,iBAAA,KAAAhC,EAAAgC,iBAAA,OAAAhC,EAAAgC,iBACAhC,EAAAiC,kBAAA,KAAAjC,EAAAiC,kBAAA,OAAAjC,EAAAiC,kBACAjC,EAAAkC,eAAA,KAAAlC,EAAAkC,eAAA,OAAAlC,EAAAkC,eACAlC,EAAAmC,eAAA,KAAAnC,EAAAmC,eAAA,OAAAnC,EAAAmC,eACAnC,EAAAqC,cAAA,KAAArC,EAAAqC,cAAA,OAAArC,EAAAqC,aACA,CAIA,OAAAb,CArBA,CAuBA,CAIAiB,iBAEA,OAAA/hB,KAAA+Q,cAAA,kBAAA,CAEA,CAEAwN,mBAEA,IAAAyD,EAAAhiB,KAAA+hB,WAAA/hB,KAAA+hB,WAAAE,aAAAC,KAAAA,EACAC,EAAAniB,KAAA8d,cAAA9d,KAAA8d,cAAAmE,aAAAC,KAAAA,EAEAF,GACAlhB,SAAAC,gBAAAiH,MAAAoa,YAAA,2BAAAJ,EAAA,IAAA,EAGAG,GACArhB,SAAAC,gBAAAiH,MAAAoa,YAAA,+BAAAD,EAAA,IAAA,EAGAniB,KAAAye,kBAAA,CAEA,CAEAA,oBAEA,IAAA4D,EAAAriB,KAAA+Q,cAAA,yBAAA,EACAsR,EAAAtR,cAAA,+BAAA,EAEA,GAAA/Q,KAAAsiB,WACAD,EAAAva,UAAAC,IAAA,gCAAA,EAGAsa,EAAAva,UAAAmU,OAAA,gCAAA,CAGA,CAIAsG,kBACA,OAAAzhB,SAAAiQ,cAAA,mBAAA,CACA,CAEAyR,qBACA,OAAAxiB,KAAAuJ,iBAAA,YAAA,EAAA9G,MACA,CAEAggB,4BACA,IAAApD,EAAArf,KAAAuJ,iBAAA,YAAA,EACA,IAAArI,IAAAwhB,EAAA,EAAAA,EAAArD,EAAA5c,OAAAigB,CAAA,GAEA,GAAA,GADArD,EAAAqD,GACA5a,UAAA2E,SAAA,qBAAA,EACA,OAAAiW,EAGA,OAAA,CACA,CAEAC,oBACA,OAAA3iB,KAAAuJ,iBAAA,eAAA,EAAA9G,MACA,CAEAmgB,2BACA,IAAAvD,EAAArf,KAAAuJ,iBAAA,eAAA,EACA,IAAArI,IAAAwhB,EAAA,EAAAA,EAAArD,EAAA5c,OAAAigB,CAAA,GAEA,GAAA,GADArD,EAAAqD,GACA5a,UAAA2E,SAAA,sBAAA,EACA,OAAAiW,EAAA,CAGA,CAEAhF,qBAEA,IAEAmF,EAAA7iB,KAAAuiB,YAAA5C,QAAAmD,wBAAAnJ,OAAA3Z,KAAAuiB,YAAA5C,QAAAmD,uBAAA,EAFA,EAEA,GAEAC,EAAA/iB,KAAAyiB,sBAAAziB,KAAAwiB,eAAA,GACAQ,EAAA,GAAAhjB,KAAAyiB,sBAAAziB,KAAA4iB,qBAAA5iB,KAAA2iB,cAAA,GAAA,EAIA3iB,KAAAuiB,YAAAU,SAFAJ,GAAAG,EAAAD,GAPA,CASA,CAEA,CAIAjF,oBACA,OAAAhd,SAAAiQ,cAAA,qBAAA,CACA,CACAmS,wBACA,OAAApiB,SAAAiQ,cAAA,qBAAA,CACA,CAEA6N,sBAEA5e,KAAAkjB,kBAAAve,UAAA,GAEA,IAAAzD,IAAAmB,EAAA,EAAAA,EAAArC,KAAA+c,SAAAta,OAAAJ,CAAA,GAAA,CAEA,IAAAwC,EAAA7E,KAAA+c,SAAA1a,GACAuP,EAAA,IAAAvP,EAAA,SAAA,GAGArC,KAAAkjB,kBAAAC,mBAAA,YAAArL,UAAA4C,YAAA,CACA7V,KAAAA,EACA8V,wBAAAtY,EAAA,OAJA,UAAAuP,EAAA,GAAA,YAKAgJ,QAAAhJ,CACA,CAAA,CAAA,CAEA,CAEA5R,KAAA6e,kBAAA,CAEA,CAEAA,oBACA7e,KAAA8d,cAAAhW,UAAAmU,OAAA,6BAAA,CACA,CACAuC,oBACAxe,KAAA8d,cAAAhW,UAAAC,IAAA,6BAAA,CACA,CAEAkW,mBAAAtb,GAEA,IAAAygB,EAAAtiB,SAAAiQ,cAAA,YAAA,EAEAuO,EADA3c,EAAAG,OAAAoE,QAAA,eAAA,EACAyY,QAAAL,IAEA+D,EAAAviB,SAAAiQ,iDAAAuO,KAAA,EAKA8D,EAAAE,YAAAD,CAAA,CAEA,CAKAE,kBACA,OAAAvjB,KAAA+Q,cAAA,oBAAA,CACA,CACAuR,iBACA,IAAAjD,EAAArf,KAAAuJ,iBAAA,YAAA,EACA,IAAArI,IAAAwhB,EAAA,EAAAA,EAAArD,EAAA5c,OAAAigB,CAAA,GAEA,GAAA,GADArD,EAAAqD,GACA5a,UAAA2E,SAAA,mBAAA,EACA,OAAAiW,EAAA,CAGA,CACA/F,eACAzb,IAAAgb,EAAAlc,KAAAujB,YAAApH,mBACA,KAAAD,GAAA,CACA,GAAAA,EAAAha,QAAA,YAAA,EAAA,OAAAga,EACAA,EAAAA,EAAAC,kBACA,CACA,CACAS,eACA1b,IAAAgb,EAAAlc,KAAAujB,YAAAnH,uBACA,KAAAF,GAAA,CACA,GAAAA,EAAAha,QAAA,YAAA,EAAA,OAAAga,EACAA,EAAAA,EAAAE,sBACA,CACA,CACAoH,oBACA,OAAAxjB,KAAA+Q,cAAA,uBAAA,CACA,CAEA0S,YAAAjH,GAEAtb,IAAAwiB,EASA,OANAA,EADAlH,EAAAtV,QAAA,uBAAA,EACA,UAGA,MAKA,CAEAoW,gBACA4C,SAAAC,KAAAtf,OAAAqX,cAAAkI,UAAAjI,OACA,CAEAwL,cACA9iB,OAAA+iB,SAAA,EAAA,CAAA,CACA,CAEAzG,KAAAxa,GAEA,IAAA6Z,EAAA7Z,EAAAG,OACA4gB,EAAA1jB,KAAAyjB,YAAAjH,CAAA,EAEA,QAAAkH,EACA1jB,KAAAyc,aAAA9Z,CAAA,EAEA,WAAA+gB,GACA1jB,KAAA6jB,gBAAAlhB,CAAA,EAGA3C,KAAA2jB,YAAA,CAEA,CAEAvG,KAAAza,GAEA,IAAA6Z,EAAA7Z,EAAAG,OACA4gB,EAAA1jB,KAAAyjB,YAAAjH,CAAA,EAEA,QAAAkH,EACA1jB,KAAA0c,aAAA/Z,CAAA,EAGA,WAAA+gB,GACA1jB,KAAA8jB,gBAAAnhB,CAAA,EAGA3C,KAAA2jB,YAAA,CAEA,CAEAtG,KAAA1a,GAIA,IAAA6Z,EAAA7Z,EAAAG,OACA9C,KAAAyjB,YAAAjH,CAAA,EAEAxc,KAAAujB,YAAAxS,cAAA,eAAA,GAEA/Q,KAAA+jB,YACA/jB,KAAA8jB,gBAAAnhB,CAAA,EAOA3C,KAAA0c,aAAA/Z,CAAA,EAGAiC,aAAA5E,KAAA,sBAAA,CAEA,CAEAyc,mBAAA9Z,GAEAA,EAAAS,cAAA,IACAuZ,EAAA3c,KAAA2c,SACA4G,EAAAvjB,KAAAujB,YACAC,EAAAxjB,KAAAwjB,cAEA7G,GAAA6G,CAAAA,IAIA5e,aAAA5E,KAAA,iCAAA,EAEAujB,EAAAzb,UAAAC,IAAA,sBAAA,EACAwb,EAAAzb,UAAAC,IAAA,oBAAA,EAEA4U,EAAA7U,UAAAC,IAAA,sBAAA,EACA4U,EAAA7U,UAAAC,IAAA,qBAAA,EAEAnD,aAAA5E,KAAA,yCAAA,EAEAa,OAAAmjB,WAAAnjB,OAAA6E,eAAAue,YAAAC,KAAA,EAAAhiB,UAIAiiB,EAAA,IAAA7O,gBAAA,IAAAO,qBAAA0N,EACA,CACAxX,WAAA,CAAA,UAAA,UACA+P,UAAA,CAAA,iBAAA,qBACA7T,QAAA,CAAA,IAAA,IACA,EACA,CACA8T,SAAA,IACAC,OAAA,gCACA,CACA,CAAA,GAEA/F,KAAA,EACAkB,MAAAgN,EAAAxO,SAEA4N,EAAAzb,UAAAmU,OAAA,mBAAA,EACAsH,EAAAzb,UAAAmU,OAAA,sBAAA,EACAsH,EAAAzb,UAAAmU,OAAA,oBAAA,GAEAmI,EAAA,IAAA9O,gBAAA,IAAAO,qBAAA8G,EACA,CACA5Q,WAAA,CAAA,SAAA,WACA+P,UAAA,CAAA,mBAAA,iBACA7T,QAAA,CAAA,IAAA,IACA,EACA,CACA8T,SAAA,IACAC,OAAA,gCACA,CACA,CAAA,GAEA/F,KAAA,EACAkB,MAAAiN,EAAAzO,SAEA/Q,aAAA5E,KAAA,kCAAA,KAaA6b,EAAA,IAAAvG,gBAAA,IAAAuB,eAAA,CACA,IAAAhB,qBAAA0N,EACA,CACAxX,WAAA,CAAA,UAAA,UACA+P,UAAA,CAAA,iBAAA,qBACA7T,QAAA,CAAA,IAAA,IACA,EACA,CACA8T,SAAA,IACAC,OAAA,gCACA,CACA,EACA,IAAAnG,qBAAA8G,EACA,CACA5Q,WAAA,CAAA,SAAA,WACA+P,UAAA,CAAA,mBAAA,iBACA7T,QAAA,CAAA,IAAA,IACA,EACA,CACA8T,SAAA,IACAC,OAAA,gCACA,CACA,EACA,CAAA,GAEA/F,KAAA,EAEAkB,MAAA0E,EAAAlG,SAEA/Q,aAAA5E,KAAA,kCAAA,EAEAujB,EAAAzb,UAAAmU,OAAA,mBAAA,EACAsH,EAAAzb,UAAAmU,OAAA,sBAAA,EACAsH,EAAAzb,UAAAmU,OAAA,oBAAA,GAEAU,EAAA7U,UAAAC,IAAA,mBAAA,EACA4U,EAAA7U,UAAAmU,OAAA,sBAAA,EACAU,EAAA7U,UAAAmU,OAAA,qBAAA,EAEArX,aAAA5E,KAAA,+BAAA,EAIA,CACA0c,mBAAA/Z,GAEAA,EAAAS,cAAA,IACAwZ,EAAA5c,KAAA4c,SACA2G,EAAAvjB,KAAAujB,YACAC,EAAAxjB,KAAAwjB,cAEA5G,GAAA4G,CAAAA,IAIA5e,aAAA5E,KAAA,iCAAA,EAEAujB,EAAAzb,UAAAC,IAAA,sBAAA,EACAwb,EAAAzb,UAAAC,IAAA,oBAAA,EAEA6U,EAAA9U,UAAAC,IAAA,sBAAA,EACA6U,EAAA9U,UAAAC,IAAA,qBAAA,EAEAnD,aAAA5E,KAAA,yCAAA,EAEAa,OAAAmjB,WAAAnjB,OAAA6E,eAAAue,YAAAC,KAAA,EAAAhiB,UAIAiiB,EAAA,IAAA7O,gBAAA,IAAAO,qBAAA0N,EACA,CACAxX,WAAA,CAAA,UAAA,UACA+P,UAAA,CAAA,gBAAA,oBACA7T,QAAA,CAAA,IAAA,IACA,EACA,CACA8T,SAAA,IACAC,OAAA,gCACA,CACA,CAAA,GAEA/F,KAAA,EACAkB,MAAAgN,EAAAxO,SAEA4N,EAAAzb,UAAAmU,OAAA,mBAAA,EACAsH,EAAAzb,UAAAmU,OAAA,sBAAA,EACAsH,EAAAzb,UAAAmU,OAAA,oBAAA,GAEAmI,EAAA,IAAA9O,gBAAA,IAAAO,qBAAA+G,EACA,CACA7Q,WAAA,CAAA,SAAA,WACA+P,UAAA,CAAA,oBAAA,iBACA7T,QAAA,CAAA,IAAA,IACA,EACA,CACA8T,SAAA,IACAC,OAAA,gCACA,CACA,CAAA,GAEA/F,KAAA,EACAkB,MAAAiN,EAAAzO,SAEA/Q,aAAA5E,KAAA,kCAAA,KAaA6b,EAAA,IAAAvG,gBAAA,IAAAuB,eAAA,CACA,IAAAhB,qBAAA0N,EACA,CACAxX,WAAA,CAAA,UAAA,UACA+P,UAAA,CAAA,gBAAA,oBACA7T,QAAA,CAAA,IAAA,IACA,EACA,CACA8T,SAAA,IACAC,OAAA,gCACA,CACA,EACA,IAAAnG,qBAAA+G,EACA,CACA7Q,WAAA,CAAA,SAAA,WACA+P,UAAA,CAAA,oBAAA,iBACA7T,QAAA,CAAA,IAAA,IACA,EACA,CACA8T,SAAA,IACAC,OAAA,gCACA,CACA,EACA,CAAA,GAEA/F,KAAA,EAEAkB,MAAA0E,EAAAlG,SAEA/Q,aAAA5E,KAAA,kCAAA,EAEAujB,EAAAzb,UAAAmU,OAAA,mBAAA,EACAsH,EAAAzb,UAAAmU,OAAA,sBAAA,EACAsH,EAAAzb,UAAAmU,OAAA,oBAAA,GAEAW,EAAA9U,UAAAC,IAAA,mBAAA,EACA6U,EAAA9U,UAAAmU,OAAA,sBAAA,EACAW,EAAA9U,UAAAmU,OAAA,qBAAA,EAEArX,aAAA5E,KAAA,+BAAA,EAIA,CAIAqkB,qBACA,OAAArkB,KAAA+Q,cAAA,uBAAA,CACA,CACAuT,kBACApjB,IAAAgb,EAAAlc,KAAAqkB,eAAAlI,mBACA,KAAAD,GAAA,CACA,GAAAA,EAAAha,QAAA,eAAA,EAAA,OAAAga,EACAA,EAAAA,EAAAC,kBACA,CACA,CACA4H,kBACA7iB,IAAAgb,EAAAlc,KAAAqkB,eAAAjI,uBACA,KAAAF,GAAA,CACA,GAAAA,EAAAha,QAAA,eAAA,EAAA,OAAAga,EACAA,EAAAA,EAAAE,sBACA,CACA,CAEAyH,gBAAAlhB,GAEA3C,KAAAskB,aACAtkB,KAAAsjB,YAAAtjB,KAAAskB,WAAA,EACA1f,aAAA5E,KAAAkjB,kBAAA,8BAAA,GAIAljB,KAAA2c,SACA3c,KAAAyc,aAAA9Z,CAAA,EAGAiC,aAAA5E,KAAA,6BAAA,CAGA,CAEA8jB,kBACA9jB,KAAAsjB,YAAAtjB,KAAA+jB,WAAA,EACAnf,aAAA5E,KAAAkjB,kBAAA,8BAAA,CACA,CAEAhG,aAAAva,GAEA,IAAA6Z,EAAA7Z,EAAAG,OAAAoE,QAAA,QAAA,EAEAmd,EAAArkB,KAAAqkB,eACAC,EAAAD,EAAAlI,mBACA+C,EAAAmF,EAAA1E,QAAAL,IAEAiF,EAAAvkB,KAAA8d,cAAA/M,yCAAAmO,KAAA,EACAsF,EAAAD,EAAApI,mBAEAoI,EAAAnR,WAAA,EAEAoR,IACAA,EAAAnQ,SAAA,EACAmQ,EAAAC,OAAA,GAGAH,GACA9H,EAAAkI,gBAGA,CAEA3G,uBAIAC,wBAIAsF,kBAAAzD,GAEA,IAAAwE,EAAArkB,KAAAqkB,eAEAM,EAAA3kB,KAAA2kB,iBAEAC,GAAAD,CAAAA,GAGAC,EAAA9c,UAAA2E,SAAA,cAAA,IAIA7H,aAAA5E,KAAA8d,cAAA,gCAAA,EAEAuG,EAAAvc,UAAAC,IAAA,uBAAA,EACAsc,EAAAvc,UAAAC,IAAA,yBAAA,EAEA8T,EAAA,IAAAvG,gBAAA,IAAAO,qBAAAwO,EACA,CACAtY,WAAA,CAAA,UAAA,UACA+P,UAAA,CAAA,iBAAA,oBACA7T,QAAA,CAAA,IAAA,IACA,EACA,CACA8T,SAAA,IACAC,OAAA,gCACA,CACA,CAAA,EAEApX,aAAA5E,KAAA,gCAAA,EAEA6b,EAAA5F,KAAA,EAEAkB,MAAA0E,EAAAlG,SAEA0O,EAAAvc,UAAAmU,OAAA,sBAAA,EACAoI,EAAAvc,UAAAmU,OAAA,uBAAA,EACAoI,EAAAvc,UAAAmU,OAAA,yBAAA,EAEA2I,EAAA9c,UAAAC,IAAA,uBAAA,EACA6c,EAAA9c,UAAAC,IAAA,sBAAA,EACA6c,EAAA9c,UAAAC,IAAA,yBAAA,EAEAnD,aAAA5E,KAAA8d,cAAA,uCAAA,GAEAsG,EAAA,IAAA9O,gBAAA,IAAAO,qBAAA+O,EACA,CACA7Y,WAAA,CAAA,SAAA,WACA+P,UAAA,CAAA,kBAAA,kBACA7T,QAAA,CAAA,IAAA,IACA,EACA,CACA8T,SAAA,IACAC,OAAA,gCACA,CACA,CAAA,GAEA/F,KAAA,EAEA2O,EAAA9c,UAAAmU,OAAA,yBAAA,EAEA9E,MAAAiN,EAAAzO,SAEA/Q,aAAA5E,KAAA8d,cAAA,iCAAA,EAEA,CAEA,EAEA+G,6BAAAne,kBACAxC,cACA0C,MAAA,EACAE,QAAAC,UAIA,CACAuQ,oBAEAtX,KAAAmb,MAAAnb,KAAA+Q,cAAA,yBAAA,EACA/Q,KAAA8kB,aAAA9kB,KAAA+Q,cAAA,2BAAA,EACA/Q,KAAA+kB,KAAA/kB,KAAAkH,QAAA,gBAAA,EAEA,UAAAlH,KAAAmb,MAAAtY,MAEA7C,KAAAqH,SAAA5F,GAAA,WAAA,4BAAAzB,KAAAglB,sBAAA5kB,KAAAJ,IAAA,CAAA,EACAA,KAAAqH,SAAA5F,GAAA,QAAA,4BAAAzB,KAAAilB,cAAA7kB,KAAAJ,IAAA,CAAA,GAEA,UAAAA,KAAAmb,MAAApR,SAEA/J,KAAAqH,SAAA5F,GAAA,SAAA,0BAAAzB,KAAAklB,oBAAA9kB,KAAAJ,IAAA,CAAA,EAGAA,KAAAqH,SAAA5F,GAAA,QAAA,4BAAAzB,KAAAmlB,QAAA/kB,KAAAJ,IAAA,CAAA,CAGA,CACAmlB,QAAAxiB,GAEA3C,KAAAmb,MAAA1b,MAAAO,KAAA8kB,aAAApF,UACA9a,aAAA5E,KAAA,6BAAA,CAEA,CACAklB,oBAAAviB,GAEA,IAAAyiB,EAAAziB,EAAAG,OAAA2B,QAAA9B,EAAAG,OAAA4B,eAAAib,QAAAyF,aAEAplB,KAAA8kB,aAAAngB,UAAAygB,GAAAziB,EAAAG,OAAA2B,QAAA9B,EAAAG,OAAA4B,eAAAjF,MAEAmF,aAAA5E,KAAA,6BAAA,CAEA,CACAglB,sBAAAriB,GACA,GAAA,GAAA0D,MAAAgf,OAAAC,aAAA3iB,EAAA4iB,KAAA,CAAA,EAEA,OADA5iB,EAAAmB,eAAA,EACA,CAAA,CAEA,CACAmhB,cAAAtiB,GAEA,IAAAsD,EAAA0T,OAAA3Z,KAAA8kB,aAAApF,SAAA,EACA8F,EAAA7L,OAAA3Z,KAAAmb,MAAAqK,GAAA,EACAC,EAAA9L,OAAA3Z,KAAAmb,MAAAsK,GAAA,EAEAD,GACAA,EAAAvf,GACAjG,KAAAmb,MAAA1b,MAAA+lB,EACAxlB,KAAA8kB,aAAApF,UAAA8F,GAKAC,GACAxf,EAAAwf,GAAA,GAAAxf,GACAjG,KAAAmb,MAAA1b,MAAAgmB,EACAzlB,KAAA8kB,aAAApF,UAAA+F,IAKAzlB,KAAAmb,MAAA1b,MAAAO,KAAA8kB,aAAApF,UAEA9a,aAAA5E,KAAA,6BAAA,EAEA,CAEA,EAEA0lB,uCAAAhf,kBAEAxC,cACA0C,MAAA,EACAE,QAAAC,UAIA,CACAuQ,oBAEA/L,QAAAoa,IAAA,0BAAA,EAEA3lB,KAAA4lB,sBAAA5lB,KAAA+Q,cAAA,sBAAA,EACA/Q,KAAA6lB,aAAA7lB,KAAA4lB,sBAAA7U,cAAA,MAAA,EAEA/Q,KAAA8lB,iBAAA9lB,KAAA+Q,cAAA,iBAAA,EACA/Q,KAAA+lB,QAAA/lB,KAAA8lB,iBAAA/U,cAAA,MAAA,EAEA/Q,KAAAgmB,eAAAhmB,KAAA+Q,cAAA,sCAAA,EACA/Q,KAAAimB,eAAAjmB,KAAA+Q,cAAA,sCAAA,EACA/Q,KAAAqiB,WAAAriB,KAAA+Q,cAAA,kCAAA,EAEA/Q,KAAAqiB,WAAA/gB,iBAAA,QAAAtB,KAAAkmB,iBAAA9lB,KAAAJ,IAAA,CAAA,EAEAA,KAAA6lB,aAAAvkB,iBAAA,SAAAtB,KAAAmmB,qBAAA/lB,KAAAJ,IAAA,CAAA,EACAA,KAAA+lB,QAAAzkB,iBAAA,SAAAtB,KAAAomB,gBAAAhmB,KAAAJ,IAAA,CAAA,EAEAA,KAAAqmB,iBAAArmB,KAAAkH,QAAA,sBAAA,EAAA6J,cAAA,kBAAA,CAEA,CAEAoV,qBAAAxjB,GAKA,OAHAA,EAAAmB,eAAA,EACA9D,KAAAsmB,YAAA,EAEA,CAAA,CAEA,CAEAF,gBAAAzjB,GAKA,OAHAA,EAAAmB,eAAA,EACA9D,KAAAqmB,iBAAA3O,OAAA,EAEA,CAAA,CAEA,CAEA4O,oBAEA,IAAAC,EAAA,IAAAjR,gBAAA,IAAAO,qBAAA7V,KAAA4lB,sBACA,CACAvZ,QAAA,CAAA,QAAA,QACApE,QAAA,CAAA,IAAA,IACA,EACA,CACA8T,SAAA,IACAC,OAAA,gCACA,CACA,CAAA,EACAuK,EAAAtQ,KAAA,EAEAkB,MAAAoP,EAAA5Q,SAEA3V,KAAA4lB,sBAAA9d,UAAAC,IAAA,QAAA,EACA/H,KAAA8lB,iBAAAhe,UAAAmU,OAAA,QAAA,EAEA,IAAA3G,gBAAA,IAAAO,qBAAA7V,KAAA8lB,iBACA,CACAzZ,QAAA,CAAA,OAAA,SACApE,QAAA,CAAA,IAAA,IACA,EACA,CACA8T,SAAA,IACAC,OAAA,gCACA,CACA,CAAA,EAEA/F,KAAA,CAEA,CAEAiQ,yBAEA,IAAAK,EAAA,IAAAjR,gBAAA,IAAAO,qBAAA7V,KAAA8lB,iBACA,CACAzZ,QAAA,CAAA,QAAA,QACApE,QAAA,CAAA,IAAA,IACA,EACA,CACA8T,SAAA,IACAC,OAAA,gCACA,CACA,CAAA,EACAuK,EAAAtQ,KAAA,EAEAkB,MAAAoP,EAAA5Q,SAEA3V,KAAA8lB,iBAAAhe,UAAAC,IAAA,QAAA,EACA/H,KAAA4lB,sBAAA9d,UAAAmU,OAAA,QAAA,EAEA,IAAA3G,gBAAA,IAAAO,qBAAA7V,KAAA4lB,sBACA,CACAvZ,QAAA,CAAA,OAAA,SACApE,QAAA,CAAA,IAAA,IACA,EACA,CACA8T,SAAA,IACAC,OAAA,gCACA,CACA,CAAA,EAEA/F,KAAA,CAEA,CAEA,EAEAuQ,gCAAA9f,kBAEAxC,cACA0C,MAAA,EACAE,QAAAC,UAIA,CACAuQ,oBAIAtX,KAAAymB,UAAAzmB,KAAA+Q,cAAA,kBAAA,EACA/Q,KAAA0mB,YAAA1mB,KAAA+Q,cAAA,uCAAA,EACA/Q,KAAA6lB,aAAA7lB,KAAA+Q,cAAA,qBAAA,EACA/Q,KAAAgmB,eAAAhmB,KAAA+Q,cAAA,0CAAA,CAKA,CAEAmV,yBAIA,IAAAK,EAAA,IAAAjR,gBAAA,IAAAO,qBAAA7V,KAAAymB,UACA,CACApa,QAAA,CAAA,QAAA,QACAyP,UAAA,CAAA,gBAAA,oBACA7T,QAAA,CAAA,IAAA,IACA,EACA,CACA8T,SAAA,IACAC,OAAA,gCACA,CACA,CAAA,EACAuK,EAAAtQ,KAAA,EAEAkB,MAAAoP,EAAA5Q,SAEA3V,KAAAymB,UAAA3e,UAAAC,IAAA,QAAA,EACA/H,KAAA6lB,aAAA/d,UAAAmU,OAAA,QAAA,EAEA,IAAA3G,gBAAA,IAAAO,qBAAA7V,KAAA6lB,aACA,CACAxZ,QAAA,CAAA,OAAA,SACAyP,UAAA,CAAA,kBAAA,kBACA7T,QAAA,CAAA,IAAA,IACA,EACA,CACA8T,SAAA,IACAC,OAAA,gCACA,CACA,CAAA,EAEA/F,KAAA,CAEA,CAEA0Q,sBAIA,IAAAJ,EAAA,IAAAjR,gBAAA,IAAAO,qBAAA7V,KAAA6lB,aACA,CACAxZ,QAAA,CAAA,QAAA,QACAyP,UAAA,CAAA,gBAAA,oBACA7T,QAAA,CAAA,IAAA,IACA,EACA,CACA8T,SAAA,IACAC,OAAA,gCACA,CACA,CAAA,EACAuK,EAAAtQ,KAAA,EAEAkB,MAAAoP,EAAA5Q,SAEA3V,KAAA6lB,aAAA/d,UAAAC,IAAA,QAAA,EACA/H,KAAAymB,UAAA3e,UAAAmU,OAAA,QAAA,EAEA,IAAA3G,gBAAA,IAAAO,qBAAA7V,KAAAymB,UACA,CACApa,QAAA,CAAA,OAAA,SACAyP,UAAA,CAAA,kBAAA,kBACA7T,QAAA,CAAA,IAAA,IACA,EACA,CACA8T,SAAA,IACAC,OAAA,gCACA,CACA,CAAA,EAEA/F,KAAA,CAEA,CAEA,EAEA2Q,uBAAAlgB,kBAEAxC,cACA0C,MAAA,EACAE,QAAAC,UAIA,CACAuQ,oBAEAtX,KAAAuX,OAAAvX,KAAAuJ,iBAAA,yBAAA,CAEA,CAEA,EAEAsd,2BAAAngB,kBAEAxC,cACA0C,MAAA,EAKAE,QAAAC,UAIA,CACAuQ,oBAEAtX,KAAAuX,OAAAxK,QAAA,KAEA,EAEA/M,KAAA6X,MAAA,CAAA,EAEA7X,KAAAsB,iBAAA,8BAAAtB,KAAAqE,gBAAAjE,KAAAJ,IAAA,CAAA,EACAA,KAAAsB,iBAAA,uBAAAtB,KAAA8mB,SAAA1mB,KAAAJ,IAAA,CAAA,EACAA,KAAAsB,iBAAA,yBAAAtB,KAAA+mB,WAAA3mB,KAAAJ,IAAA,CAAA,CAEA,CAEAqE,gBAAA1B,GAEAwY,EAAAxY,EAAAG,OAAAiO,cAAA,yBAAA,EAEA/Q,KAAAgnB,WACAhnB,KAAAinB,cAAA9L,CAAA,EACAnb,KAAAgnB,SAAArL,KAAA,EAGA3b,KAAAgnB,SAAA1L,KAAA,GAIAtb,KAAAknB,SAAA,CAGA,CAEAA,WAEA,GAAAlnB,KAAAuX,OAAA9U,QAKAzC,EAAAA,KAAAuD,aAAA,UAAA,GAMAvD,KAAAuD,aAAA,oBAAA,GAGA,GAAAvD,KAAA8H,UAAA2E,SAAA,wBAAA,GAOA,IAAAvL,IAAAmB,EAAA,EAAAA,EAAArC,KAAAuX,OAAA9U,OAAAJ,CAAA,GAIA,GAAA,CAFArC,KAAAuX,OAAAlV,GAEA5C,MAEA,OADAmF,aAAA5E,KAAA,wBAAA,EACA,CAAA,EAMA,OAhCA4E,aAAA5E,KAAA,sBAAA,EAgCA,CAAA,CAEA,CAEAinB,cAAA9L,GAEAja,IAAAimB,EAMA,OAJAhM,IACAgM,EAAA,UAAAhM,EAAA5a,QAAA4a,EAAA1W,QAAA0W,EAAAzW,eAAAyW,GAGAA,CAAAA,EAAA3W,aAAA,kBAAA,GACA2iB,EAAA1nB,OAAA0b,EAAA3W,aAAA,kBAAA,CAWA,CAEAuiB,aAEA/mB,KAAA6X,MAAA,CAAA,EAEA7X,KAAAgnB,UAAA,GAAAhnB,KAAAgnB,SAAAE,SAAA,EACAlnB,KAAAgnB,SAAA1L,KAAA,EAIAtb,KAAAonB,WAAA7a,SAAA,CAAA,CAEA,CAEAua,WAEA9mB,KAAA6X,MAAA,CAAA,EAEA7X,KAAAgnB,WACAhnB,KAAAonB,WAAA7a,SAAA,CAAA,GAGAvM,KAAAgnB,WAEAhnB,KAAAgnB,SAAAzjB,aAAA,UAAA,GAIAvD,KAAAgnB,SAAAzjB,aAAA,oBAAA,GAAA,GAAAvD,KAAAgnB,SAAAlf,UAAA2E,SAAA,wBAAA,IACAzM,KAAAgnB,SAAAE,SAAA,CAKA,CAEAvL,OACA3b,KAAA8H,UAAAmU,OAAA,wBAAA,CACA,CAEAX,OACAtb,KAAA8H,UAAAC,IAAA,wBAAA,CACA,CAEAwP,aACA,OAAAvX,KAAAuJ,iBAAA,yBAAA,CACA,CAEA6d,iBACA,OAAApnB,KAAAkH,QAAA,2BAAA,EAAA6J,cAAA,yBAAA,CACA,CAEAsW,eACAnmB,IAAAgb,EAAAlc,KAAAoc,uBACA,KAAAF,GAAA,CACA,GAAAA,EAAAha,QAAA,gBAAA,EAAA,OAAAga,EACAA,EAAAA,EAAAE,sBACA,CACA,CAEA4K,eACA9lB,IAAAgb,EAAAlc,KAAAmc,mBACA,KAAAD,GAAA,CACA,GAAAA,EAAAha,QAAA,gBAAA,EAAA,OAAAga,EACAA,EAAAA,EAAAC,kBACA,CACA,CAEA,EAEAmL,uBAAA5gB,kBACAxC,cACA0C,MAAA,EACAE,QAAAC,UAIA,CACAuQ,oBAEAtX,KAAAmb,MAAAnb,KAAA+Q,cAAA,mBAAA,EACA/Q,KAAAunB,MAAAvnB,KAAAkH,QAAA,YAAA,EAEAlH,KAAAsB,iBAAA,QAAAtB,KAAAwnB,QAAApnB,KAAAJ,IAAA,CAAA,CAEA,CACAwnB,QAAA7kB,GAEA,GAAA3C,KAAAynB,WAEAznB,KAAAunB,MAAAG,MAAA,EACA1nB,KAAAunB,MAAApM,MAAA1b,MAAAO,KAAA2f,QAAAlgB,MACAO,KAAAunB,MAAAI,WAAA3nB,KAAA2f,QAAAiI,IAAA,EAEA5nB,KAAA8H,UAAAC,IAAA,qBAAA,GAIAnD,aAAA5E,KAAA,mBAAA,CAEA,CAEAynB,eACA,OAAAznB,KAAA8H,UAAA2E,SAAA,qBAAA,CACA,CAEA2a,iBACA,OAAApnB,KAAAkH,QAAA,2BAAA,EAAA6J,cAAA,yBAAA,CACA,CAEA,EAEA8W,wBAAAnhB,kBAEAxC,cACA0C,MAAA,CACA,CAEA0Q,oBAEAtX,KAAAunB,MAAAvnB,KAAAuJ,iBAAA,WAAA,EACAvJ,KAAAmb,MAAAnb,KAAA+Q,cAAA,oBAAA,EACA/Q,KAAA4nB,KAAA5nB,KAAA+Q,cAAA,mBAAA,EAEA/Q,KAAAsB,iBAAA,oBAAAtB,KAAAqE,gBAAAjE,KAAAJ,IAAA,CAAA,EACAA,KAAAsB,iBAAA,mBAAAtB,KAAA8mB,SAAA1mB,KAAAJ,IAAA,CAAA,EACAA,KAAAsB,iBAAA,qBAAAtB,KAAA+mB,WAAA3mB,KAAAJ,IAAA,CAAA,CAEA,CAEAqE,gBAAA1B,GAEA,GAAA3C,KAAAknB,SAAA,EACAtiB,aAAA5E,KAAA,kBAAA,EAGA4E,aAAA5E,KAAA,oBAAA,CAGA,CAEA0nB,QACA1nB,KAAAunB,MAAAxa,QAAA,IACA+a,EAAAhgB,UAAAmU,OAAA,qBAAA,CACA,CAAA,CACA,CAEAiL,WAEA,MAAAlnB,CAAAA,CAAAA,KAAAmb,MAAA1b,KAMA,CAEAsoB,kBAEA,IAAAxB,EAAA,IAAAjR,gBAAA,IAAAO,qBAAA7V,KAAA4nB,KACA,CACA7b,WAAA,CAAA,UAAA,UACA+P,UAAA,CAAA,gBAAA,oBACA7T,QAAA,CAAA,IAAA,IACA,EACA,CACA8T,SAAA,IACAC,OAAA,gCACA,CACA,CAAA,EACAuK,EAAAtQ,KAAA,EAEAkB,MAAAoP,EAAA5Q,SAEA3V,KAAA4nB,KAAAlI,UAAA1f,KAAA4nB,KAAAjI,QAAAqI,QAEA,IAAA1S,gBAAA,IAAAO,qBAAA7V,KAAA4nB,KACA,CACA7b,WAAA,CAAA,SAAA,WACA+P,UAAA,CAAA,kBAAA,kBACA7T,QAAA,CAAA,IAAA,IACA,EACA,CACA8T,SAAA,IACAC,OAAA,gCACA,CACA,CAAA,EACA/F,KAAA,CAEA,CAEA0R,iBAAAC,GAEA,IAIArB,EAJAqB,KAIArB,EAAA,IAAAjR,gBAAA,IAAAO,qBAAA7V,KAAA4nB,KACA,CACA7b,WAAA,CAAA,UAAA,UACA+P,UAAA,CAAA,gBAAA,oBACA7T,QAAA,CAAA,IAAA,IACA,EACA,CACA8T,SAAA,IACAC,OAAA,gCACA,CACA,CAAA,GACA/F,KAAA,EAEAkB,MAAAoP,EAAA5Q,SAEA3V,KAAA4nB,KAAAlI,UAAAkI,EAEA,IAAAtS,gBAAA,IAAAO,qBAAA7V,KAAA4nB,KACA,CACA7b,WAAA,CAAA,SAAA,WACA+P,UAAA,CAAA,kBAAA,kBACA7T,QAAA,CAAA,IAAA,IACA,EACA,CACA8T,SAAA,IACAC,OAAA,gCACA,CACA,CAAA,EACA/F,KAAA,EAEA,CAEA8Q,aAEA/mB,KAAAgnB,UAAA,GAAAhnB,KAAAgnB,SAAAE,SAAA,GACAlnB,KAAAgnB,SAAA1L,KAAA,EAGAtb,KAAAgnB,WACAhnB,KAAAonB,WAAA7a,SAAA,CAAA,EAGA,CAEAua,WAEA9mB,KAAAgnB,UACAhnB,KAAAgnB,SAAArL,KAAA,EAGA3b,KAAAgnB,WACAhnB,KAAAonB,WAAA7a,SAAA,CAAA,EAGA,CAEAoP,OACA3b,KAAA8H,UAAAmU,OAAA,oBAAA,CACA,CAEAX,OACAtb,KAAA8H,UAAAC,IAAA,oBAAA,CACA,CAEAwP,aACA,OAAAvX,KAAAuJ,iBAAA,yBAAA,CACA,CAEA6d,iBACA,OAAApnB,KAAAkH,QAAA,2BAAA,EAAA6J,cAAA,yBAAA,CACA,CAEAsW,eACAnmB,IAAAgb,EAAAlc,KAAAoc,uBACA,KAAAF,GAAA,CACA,GAAAA,EAAAha,QAAA,YAAA,EAAA,OAAAga,EACAA,EAAAA,EAAAE,sBACA,CACA,CAEA4K,eACA9lB,IAAAgb,EAAAlc,KAAAmc,mBACA,KAAAD,GAAA,CACA,GAAAA,EAAAha,QAAA,YAAA,EAAA,OAAAga,EACAA,EAAAA,EAAAC,kBACA,CACA,CAEA,EAEAmL,uBAAA5gB,kBACAxC,cACA0C,MAAA,EACAE,QAAAC,UAIA,CACAuQ,oBAEAtX,KAAAmb,MAAAnb,KAAA+Q,cAAA,mBAAA,EACA/Q,KAAAunB,MAAAvnB,KAAAkH,QAAA,YAAA,EAEAlH,KAAAsB,iBAAA,QAAAtB,KAAAwnB,QAAApnB,KAAAJ,IAAA,CAAA,CAEA,CACAwnB,QAAA7kB,GAEA,GAAA3C,KAAAynB,WAEAznB,KAAAunB,MAAAG,MAAA,EACA1nB,KAAAunB,MAAApM,MAAA1b,MAAAO,KAAA2f,QAAAlgB,MACAO,KAAAunB,MAAAI,WAAA3nB,KAAA2f,QAAAiI,IAAA,EAEA5nB,KAAA8H,UAAAC,IAAA,qBAAA,GAIAnD,aAAA5E,KAAA,mBAAA,CAEA,CAEAynB,eACA,OAAAznB,KAAA8H,UAAA2E,SAAA,qBAAA,CACA,CAEA2a,iBACA,OAAApnB,KAAAkH,QAAA,2BAAA,EAAA6J,cAAA,yBAAA,CACA,CAEA,EAEAkX,0BAAAvhB,kBAEAxC,cACA0C,MAAA,CACA,CAEA0Q,qBAMAmN,SACAzkB,KAAAkoB,gBAAA,UAAA,CACA,CAEAC,UACAnoB,KAAAuM,SAAA,CAAA,CACA,CAEA8H,WACArU,KAAAyD,cAAA8F,iBAAA,eAAA,EAAAwD,QAAAkC,IACAA,EAAAnH,UAAAmU,OAAA,QAAA,CACA,CAAA,EACAjc,KAAA8H,UAAAC,IAAA,QAAA,CACA,CAEAqL,aACApT,KAAA8H,UAAAmU,OAAA,QAAA,CACA,CAqBA,EAEAmM,8BAAA1hB,kBAEAxC,cACA0C,MAAA,EACAE,QAAAC,UAGA,CAEAuQ,qBAIA2L,SAAAA,GAEA,UAAA,OAAAA,GAIAjjB,KAAAgI,MAAAoa,YAAA,kBAAAa,CAAA,CAEA,CAEA,EAGAoF,0BAAA3hB,kBAEAxC,cACA0C,MAAA,EACAE,QAAAC,UAGA,CAEAuQ,oBAEAqJ,aAAA2H,QAAA,SAAA,GAAAxhB,QAAAC,aACAmZ,SAAAC,KAAAtf,OAAAqX,cAAAkI,UAAAmI,MAGAvoB,KAAAwoB,mBAAA,EAEAxoB,KAAAyoB,kBAAA,EAGA,IACAC,EADA1oB,KAAA+Q,cAAA,yBAAA,EACAA,cAAA,+BAAA,EACA4X,EAAA3oB,KAAA4oB,WAAAxM,uBAMAsM,EAAA/jB,UAJAgkB,EAIA,OAHA,YAMA,CAEAE,iBACA,OAAA7oB,KAAA+Q,cAAA,2CAAA,CACA,CAEA+X,qBACA,OAAA9oB,KAAA+Q,cAAA,+CAAA,CACA,CAIAgY,iBACA,OAAA/oB,KAAA+Q,cAAA,kBAAA,CACA,CAIAiY,iBAEA,OAAAloB,SAAAiQ,cAAA,0BAAA,CAEA,CAIA0N,oBAEA,IACAwK,EADAjpB,KAAA+Q,cAAA,yBAAA,EACAA,cAAA,+BAAA,EAEA,GAAA/Q,KAAAkpB,iBACAD,EAAAtkB,UAAA,aAGAskB,EAAAtkB,UAAA,MAGA,CAEAwY,KAAAxa,GAEAA,EAAAG,OAGA9C,KAAAmpB,QAAAxmB,CAAA,CAEA,CAEAya,KAAAza,GAEAA,EAAAG,OAGA9C,KAAA2oB,QAAA,CAEA,CAEAtL,KAAA1a,GAEAA,EAAAG,OACAiO,cAAA,+BAAA,EAEA/Q,KAAA4oB,WACA5oB,KAAA4oB,WAAAxM,uBAGApc,KAAA2oB,QAAA,EAGAzI,SAAAC,KAAAtf,OAAAqX,cAAAkI,UAAAmI,IAGA,CAEAY,UAEAnpB,KAAA4oB,WAAA,IACAO,EAAAnpB,KAAA4oB,WAAAzM,mBAEAgN,GACAnpB,KAAAopB,QAAAD,CAAA,CAGA,CAEAR,UAEA3oB,KAAA4oB,WAAA,IACAD,EAAA3oB,KAAA4oB,WAAAxM,uBAEAuM,GACA3oB,KAAAopB,QAAAT,CAAA,CAGA,CAEAS,cAAA9J,GAEA,IAAAsJ,EAAA5oB,KAAA4oB,WAQA/M,GALA+M,EAAA9gB,UAAAC,IAAA,6BAAA,EACAshB,EAAAvhB,UAAAC,IAAA,6BAAA,EAEAnD,aAAAgkB,EAAA,kCAAA,EAEA,IAAAtT,gBAAA,IAAAO,qBAAA+S,EACA,CACA7c,WAAA,CAAA,UAAA,UACA+P,UAAA,CAAA,iBAAA,oBACA7T,QAAA,CAAA,IAAA,IACA,EACA,CACA8T,SAAA,IACAC,OAAA,gCACA,CACA,CAAA,GAsBAoI,GApBAvI,EAAA5F,KAAA,EAEAkB,MAAA0E,EAAAlG,SAEAiT,EAAA9gB,UAAAmU,OAAA,0BAAA,EACA2M,EAAA9gB,UAAAmU,OAAA,6BAAA,EAEAoN,EAAAvhB,UAAAC,IAAA,0BAAA,EAIAshB,EAAAtkB,cAAA,IAAAC,YAAA,2CAAA,CACAC,QAAA,CAAA,EACAC,OAAA,CACAoa,IAAA,CACAza,KAAAwkB,EAAA1J,QAAAZ,OACA,CACA,CACA,CAAA,CAAA,EAEA,IAAAzJ,gBAAA,IAAAO,qBAAAwT,EACA,CACAtd,WAAA,CAAA,SAAA,WACA+P,UAAA,CAAA,kBAAA,kBACA7T,QAAA,CAAA,IAAA,IACA,EACA,CACA8T,SAAA,IACAC,OAAA,gCACA,CACA,CAAA,GAEAoI,EAAAnO,KAAA,EAEAoT,EAAAvhB,UAAAmU,OAAA,6BAAA,EAEA9E,MAAAiN,EAAAzO,SAEA/Q,aAAAykB,EAAA,mCAAA,CAEA,CAEAC,qBAEA,IAAApK,EAAAlf,KAAAuJ,iBAAA,kBAAA,EAAA9G,OAEA0mB,GADAnpB,KAAA4oB,WACA5oB,KAAA4oB,WAAAzM,oBAEAnc,KAAA0d,mBAAA,EAEA,GAAAwB,IACAlf,KAAA8oB,eAAAvc,SAAA,CAAA,EACAvM,KAAA6oB,WAAA7gB,MAAA,gBAGAmhB,GAKAnpB,KAAA6oB,WAAAtc,SAAA,CAAA,EACAvM,KAAA8oB,eAAAvc,SAAA,CAAA,IALAvM,KAAA6oB,WAAAtc,SAAA,CAAA,EACAvM,KAAA8oB,eAAAvc,SAAA,CAAA,EAOA,CAEAqc,iBACA,OAAA5oB,KAAA+Q,cAAA,2BAAA,CACA,CAEAmY,uBACA,IAAArI,EAAA7gB,KAAAuJ,iBAAA,kBAAA,EACA,IAAArI,IAAAwhB,EAAA,EAAAA,EAAA7B,EAAApe,OAAAigB,CAAA,GAEA,GAAA,GADA7B,EAAA6B,GACA5a,UAAA2E,SAAA,0BAAA,EACA,OAAAiW,EAAA,CAGA,CAIA5E,oBACA,OAAAhd,SAAAiQ,cAAA,qBAAA,CACA,CACAmS,wBACA,OAAApiB,SAAAiQ,cAAA,qBAAA,CACA,CAEA6N,sBAEA1d,IAAA4D,EAAA2b,KAAA8I,MAAA5I,aAAA2H,QAAA,SAAA,CAAA,EAEAxhB,QAAAC,aACAjC,EAAA0kB,gBAGAxpB,KAAAkjB,kBAAAve,UAAA,GAEA,IAAAzD,IAAAmB,EAAA,EAAAA,EAAAyC,EAAA+b,KAAApe,OAAAJ,CAAA,GAAA,CAEA,IAAAwC,EAAAC,EAAA+b,KAAAxe,GAAAwC,KACA+M,EAAA,IAAAvP,EAAA,SAAA,GAGArC,KAAAkjB,kBAAAC,mBAAA,YAAArL,UAAA4C,YAAA,CACA7V,KAAAA,EACA8V,wBAAAtY,EAAA,MACAuY,QAAAhJ,CACA,CAAA,CAAA,CAEA,CAEA5R,KAAA6e,kBAAA,CAEA,CAEAA,oBACA7e,KAAA8d,cAAAhW,UAAAmU,OAAA,6BAAA,CACA,CACAuC,oBACAxe,KAAA8d,cAAAhW,UAAAC,IAAA,6BAAA,CACA,CAEAkW,mBAAAtb,GAEA,IAAA8mB,EAAA9mB,EAAAG,OAAAoE,QAAA,eAAA,EAEAwiB,EADA1pB,KAAAkjB,kBACAnS,cAAA,sBAAA,EACA4Y,EAAAF,EAAA9J,QAAAL,IACAA,EAAAxe,SAAAiQ,4CAAA4Y,KAAA,EAEAD,EAAAtW,WAAA,EACAqW,EAAApV,SAAA,EAEArU,KAAAopB,QAAA9J,CAAA,CAEA,CAIAmJ,0BAEAzoB,KAAA4e,oBAAA,EAEAzH,MAAAnX,KAAA4pB,sBAAA,EAEA5pB,KAAA+oB,WAAAc,UAAA,EAEAte,QAAAoa,IAAA3lB,KAAAgpB,UAAA,EAEAhpB,KAAAgpB,WAAAc,cAAA,EACA9pB,KAAAgpB,WAAAe,cAAA,EAGAnlB,aAAA9D,SAAAC,gBAAA,QAAA,EAEAuO,WAAA,KACAtP,KAAAgqB,eAAA1O,KAAA,CACA,EAAA,GAAA,CAEA,CAEAsO,8BAEA1oB,IAAA4D,EAAA2b,KAAA8I,MAAA5I,aAAA2H,QAAA,SAAA,CAAA,GAGAxjB,EADAgC,QAAAC,WACAyiB,eAGA1kB,IAAAgC,QAAAC,aACAmZ,SAAAC,KAAAtf,OAAAqX,cAAAkI,UAAAmI,MAGA,IAAAnX,EAAAtQ,SAAAiQ,cAAA,mBAAA,EAEA8P,EAAA/b,EAAA+b,KAIA3f,IAAA+W,EAAA,GAEA,IAAA/W,IAAAmB,EAAA,EAAAA,EAAAwe,EAAApe,OAAAJ,CAAA,GAAA,CAEA,IAAAid,EAAAuB,EAAAxe,GACA0c,EAAAO,EAAAza,KACAqa,EAAA7c,EAAA,EAKA4nB,EAAAnpB,SAAAge,cAAA,KAAA,EAEA5d,IAAAke,EAaAA,GADAA,GADAA,GADAA,GADAA,GANAA,EADA,cAAAE,EAAAiC,kBACA1gB,OAAAqX,cAAAC,QAAA+R,yBAGArpB,OAAAqX,cAAAC,QAAAmH,KAGApF,WAAA,YAAAgF,CAAA,GACAhF,WAAA,sBAAA+E,cAAAF,CAAA,CAAA,GACA7E,WAAA,WAAA6E,CAAA,GACA7E,WAAA,gCAjBA,CAiBA,GACAA,WAAA,iCAjBA,CAiBA,EAEA+P,EAAAtlB,UAAAya,EAEAnH,GAAAgS,EAAAtlB,SAEA,CAEAyM,EAAAzM,UAAAsT,EAMA/W,IAAAipB,EAAA,GAEAhT,MAAAjP,QAAAkiB,WAAAvJ,EAAA1T,IAAA8J,MAAAqI,IAEA,IAAAwB,EAAA3J,MAAAnX,KAAAqqB,+BAAA/K,CAAA,EACAgL,EAAAlrB,OAAAmrB,OAAA,CAAAC,YAAA1J,CAAA,EAAAxB,CAAA,EAEA6K,EAAAhoB,KAAAmoB,CAAA,CAEA,CAAA,CAAA,EAIA,IAAAppB,IAAAmB,EAAA,EAAAA,EAAA8nB,EAAA1nB,OAAAJ,CAAA,GAAA,CAEA,IAAAid,EAAA6K,EAAA9nB,GACAooB,EAAAnL,EAAAkL,YAEAE,EAAA1qB,KAAA+Q,iDAAAuO,EAAAza,QAAA,EACA8lB,EAAA3qB,KAAA+Q,iDAAAuO,EAAAza,4BAAA,EACA+lB,EAAA9pB,SAAAiQ,cAAA,qBAAA,EAEA7P,IAAA2pB,EAAA,GACAC,EAAA,GAEA,GAAA,EAAAL,EAAAhoB,OAAA,CAEA,IAAAvB,IAAA4gB,EAAA,EAAAA,EAAA2I,EAAAhoB,OAAAqf,CAAA,GAAA,CAEA,IAAA9J,EAAAyS,EAAA3I,GAAApY,KACAqhB,EAAAtK,KAAAC,UAAA1I,CAAA,EACAM,EAAAmS,EAAA3I,GAAApY,KAAAzI,GAAAsE,QAAA,yBAAA,EAAA,EACAylB,EAAA,GAAAhT,EAAAiT,eAEAC,EAAApT,UAAAC,mBAAA,CAAAC,QAAAA,EAAAK,MAAAyJ,EAAA,CAAA,CAAA,EACA5Y,EAAApI,SAAAge,cAAA,KAAA,EAEA5V,EAAAvE,UAAAumB,EAEAhiB,EAAA6H,cAAA,sBAAA,EACA4O,QAAAoL,YAAAA,EAIA,GAAAC,GACA9hB,EAAA6H,cAAA,sBAAA,EAAAjJ,UAAAC,IAAA,+BAAA,EAGA8iB,GAAA3hB,EAAAvE,UAEAimB,EAAA7Z,cAAA,uBAAAuH,CAAA,IACAwS,GAAAhT,UAAAqC,iBAAA,CAAAnC,QAAAA,CAAA,CAAA,EAGA,CAEA2S,EAAAhmB,UAAAkmB,EACAD,EAAAzH,mBAAA,YAAA2H,CAAA,EAEA,EAAAxL,EAAAkL,YAAA/nB,QAAA6c,EAAAkL,YAAA,GAAA9gB,KAEAihB,EAAA5Z,cAAA,sBAAA,EAAAjJ,UAAAC,IAAA,mCAAA,EAEA2iB,EAAA3Z,cAAA,yBAAA,EAAApM,UAAA2a,EAAAkL,YAAA/e,OAEA,MAGAof,GAAA,YAIA,CAKA,IAAA3pB,IAAAmB,EAAA,EAAAA,EAAA+O,EAAA7H,iBAAA,kBAAA,EAAA9G,OAAAJ,CAAA,GAAA,CACA,IAAA7B,EAAA4Q,EAAA7H,iBAAA,kBAAA,EAAAlH,GACA,EAAAA,GACA7B,EAAAsH,UAAAmU,OAAA,0BAAA,CAEA,CAEAjc,KAAAspB,mBAAA,CAEA,CAIAe,qCAAA/K,GAIApe,IAAAiqB,EACAC,EAEAC,EACAC,EAGAD,EADA/L,EAAAqC,cAAA,GACA,QAEArC,EAAAqC,cAAA,GACA,QAEA,SAIA2J,EADA,cAAAhM,EAAAkC,eACA,aAEA,eAAAlC,EAAAkC,eACA,cAEA,QAGAtgB,IAAAqqB,EAkDA,OAhDA,SAAAF,EACA,eAAAC,EACAC,EAAA,MAGA,SAAAD,EACAC,EAAA,MAGA,cAAAD,IACAC,EAAA,OAIA,SAAAF,EACA,eAAAC,EACAC,EAAA,MAGA,SAAAD,EACAC,EAAA,MAGA,cAAAD,IACAC,EAAA,OAIA,UAAAF,IACA,eAAAC,EACAC,EAAA,MAGA,SAAAD,EACAC,EAAA,MAGA,cAAAD,IACAC,EAAA,QAMAA,EAFAjM,EAAAiC,mBAAA,IAAAjC,EAAAiC,mBAAA,QAAAjC,EAAAiC,kBAEAjC,EAAAiC,kBAIAgK,GACA,IAAA,MACAJ,EAAAtqB,OAAAqX,cAAAsT,YAAAC,IAAAxqB,GACAmqB,EAAAvqB,OAAAqX,cAAAsT,YAAAC,IAAAhgB,QACA,MACA,IAAA,MACA0f,EAAAtqB,OAAAqX,cAAAsT,YAAAE,IAAAzqB,GACAmqB,EAAAvqB,OAAAqX,cAAAsT,YAAAE,IAAAjgB,QACA,MACA,IAAA,MACA0f,EAAAtqB,OAAAqX,cAAAsT,YAAAG,IAAA1qB,GACAmqB,EAAAvqB,OAAAqX,cAAAsT,YAAAG,IAAAlgB,QACA,MACA,IAAA,MACA0f,EAAAtqB,OAAAqX,cAAAsT,YAAAI,IAAA3qB,GACAmqB,EAAAvqB,OAAAqX,cAAAsT,YAAAI,IAAAngB,QACA,MACA,IAAA,MACA0f,EAAAtqB,OAAAqX,cAAAsT,YAAAK,IAAA5qB,GACAmqB,EAAAvqB,OAAAqX,cAAAsT,YAAAK,IAAApgB,QACA,MACA,IAAA,MACA0f,EAAAtqB,OAAAqX,cAAAsT,YAAAM,IAAA7qB,GACAmqB,EAAAvqB,OAAAqX,cAAAsT,YAAAM,IAAArgB,QACA,MACA,IAAA,MACA0f,EAAAtqB,OAAAqX,cAAAsT,YAAAO,IAAA9qB,GACAmqB,EAAAvqB,OAAAqX,cAAAsT,YAAAO,IAAAtgB,QACA,MACA,IAAA,MACA0f,EAAAtqB,OAAAqX,cAAAsT,YAAAQ,IAAA/qB,GACAmqB,EAAAvqB,OAAAqX,cAAAsT,YAAAQ,IAAAvgB,QACA,MACA,IAAA,MACA0f,EAAAtqB,OAAAqX,cAAAsT,YAAAS,IAAAhrB,GACAmqB,EAAAvqB,OAAAqX,cAAAsT,YAAAS,IAAAxgB,QACA,MACA,IAAA,+BACA0f,EAAAtqB,OAAAqX,cAAAsT,YAAAU,UAAAjrB,GACAmqB,EAAAvqB,OAAAqX,cAAAsT,YAAAU,UAAAzgB,QACA,MACA,IAAA,yCACA0f,EAAAtqB,OAAAqX,cAAAsT,YAAAW,gBAAAlrB,GACAmqB,EAAAvqB,OAAAqX,cAAAsT,YAAAW,gBAAA1gB,QACA,MACA,IAAA,iBACA0f,EAAAtqB,OAAAqX,cAAAsT,YAAAY,OAAAnrB,GACAmqB,EAAAvqB,OAAAqX,cAAAsT,YAAAY,OAAA3gB,QACA,MACA,IAAA,oBACA0f,EAAAtqB,OAAAqX,cAAAsT,YAAAa,kBAAAprB,GACAmqB,EAAAvqB,OAAAqX,cAAAsT,YAAAa,kBAAA5gB,QACA,MACA,IAAA,eACA0f,EAAAtqB,OAAAqX,cAAAsT,YAAAc,MAAArrB,GACAmqB,EAAAvqB,OAAAqX,cAAAsT,YAAAc,MAAA7gB,QACA,MACA,IAAA,eACA0f,EAAAtqB,OAAAqX,cAAAsT,YAAAe,MAAAtrB,GACAmqB,EAAAvqB,OAAAqX,cAAAsT,YAAAe,MAAA9gB,QACA,MACA,IAAA,iBACA0f,EAAAtqB,OAAAqX,cAAAsT,YAAAgB,OAAAvrB,GACAmqB,EAAAvqB,OAAAqX,cAAAsT,YAAAgB,OAAA/gB,QACA,MACA,IAAA,kCACA0f,EAAAtqB,OAAAqX,cAAAsT,YAAAiB,oBAAAxrB,GACAmqB,EAAAvqB,OAAAqX,cAAAsT,YAAAiB,oBAAAhhB,QACA,MACA,IAAA,eACA0f,EAAAtqB,OAAAqX,cAAAsT,YAAAkB,MAAAzrB,GACAmqB,EAAAvqB,OAAAqX,cAAAsT,YAAAkB,MAAAjhB,QACA,MACA,IAAA,eACA0f,EAAAtqB,OAAAqX,cAAAsT,YAAAmB,aAAA1rB,GACAmqB,EAAAvqB,OAAAqX,cAAAsT,YAAAmB,aAAAlhB,QACA,MACA,IAAA,2BACA0f,EAAAtqB,OAAAqX,cAAAsT,YAAAoB,GAAA3rB,GACAmqB,EAAAvqB,OAAAqX,cAAAsT,YAAAoB,GAAAnhB,QACA,MACA,IAAA,eACA0f,EAAAtqB,OAAAqX,cAAAsT,YAAAqB,aAAA5rB,GACAmqB,EAAAvqB,OAAAqX,cAAAsT,YAAAqB,aAAAphB,OAEA,CAEA,MAAAqhB,EAAA;;mDAEA3B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;QA8DA2B,EAAA,EAEAvhB,QAAAoa,IAAArG,CAAA,EA2BAyN,EAAA5V,MAAA6V,MAAAC,IAAAC,WAAAC,OAAA,EAAAC,IAjBA,CACAnW,MAAA,CAAA,EACAoW,YAAA,CAAA,EACAC,OAAA,OACAC,QAAA,CACAC,oCAAAP,IAAAC,WAAAC,OAAA,EAAAM,aACAC,eAAA,qBACA,EACAtpB,KAAA0oB,EAAA,CACA,CAQA,EACAnY,KAAAoY,GAAAA,EAAAY,KAAA,CAAA,EACAhZ,KAAA8V,GACAA,CACA,EAGAlf,QAAAoa,IAAAoH,CAAA,EAEAa,EAAAb,EAAAjoB,KAAA+oB,WAAApD,SAAApR,MAIA,OAFAuU,EAAAniB,QAAA2f,EAEAwC,CAGA,CAEA5D,qBACA,OAAAhqB,KAAA+Q,cAAA,sBAAA,CACA,CAIAwR,kBACA,OAAAzhB,SAAAiQ,cAAA,mBAAA,CACA,CAEA2M,qBAEA,IAAAmF,EAAA7iB,KAAAuiB,YAAA5C,QAAAmD,wBAAAnJ,OAAA3Z,KAAAuiB,YAAA5C,QAAAmD,uBAAA,EAAA,EAAA,GAEAjC,EAAA/f,SAAAyI,iBAAA,kBAAA,EAEAukB,EAAA,GAAAjN,EAAApe,OAAA,EAAAoe,EAAApe,OAAA,EACAvB,IAAA6sB,EAAA,EAEA,IAAA7sB,IAAAmB,EAAA,EAAAA,EAAAwe,EAAApe,OAAAJ,CAAA,GAAA,CACA,IAAA7B,EAAAqgB,EAAAxe,GACA,GAAA,GAAA7B,EAAAsH,UAAA2E,SAAA,0BAAA,EACAshB,CAAA,QAEA,GAAA,GAAAvtB,EAAAsH,UAAA2E,SAAA,0BAAA,EAEA,KAEA,CAEAwW,GAAA8K,EAAAD,EAAA,EAEA9tB,KAAAuiB,YAAAU,SAAAA,CAAA,CAEA,CAIA+K,sBAAArrB,GAEA,IAAA6Z,EAAA7Z,EAAAG,OAAAoE,QAAA,QAAA,EAIA+mB,GAFAzR,EAAAkI,iBAAA,EAEA1kB,CAAAA,CAAAA,KAAAkuB,cAEA,GAAAD,EACAjuB,KAAAkuB,aAAAC,KAAA,CAAA,EAGAhX,MAAAnX,KAAAouB,kBAAA,EAGA5R,EAAA6R,eAAA,CAEA,CAEAC,6BAAA3rB,GAEA,IAAA6Z,EAAA7Z,EAAAG,OAAAoE,QAAA,QAAA,EACAqnB,EAAA/R,EAAAtV,QAAA,eAAA,EACAsjB,EAAA1pB,SAAAiQ,cAAA,cAAA,EAEAyL,EAAAkI,iBAAA,EAEAvN,MAAAqT,EAAA4D,kBAAA,EACAG,EAAAJ,KAAA,CAAA,EAEA3R,EAAA6R,eAAA,CAEA,CAEAH,mBACA,OAAAptB,SAAAiQ,cAAA,sBAAA,CACA,CAEAqd,0BAIA,IAAAI,EAAA1tB,SAAAyI,iBAAA,iCAAA,EAIAzE,EAAA,CAAA2pB,MAAA,EAAA,EAEAC,EAAAC,KAAA,EAEA,IAAAztB,IAAAmB,EAAA,EAAAA,EAAAmsB,EAAA/rB,OAAAJ,CAAA,GAAA,CAEA,IAAA2V,EAAAwW,EAAAnsB,GACAid,EAAAkP,EAAAnsB,GAAA6E,QAAA,kBAAA,EAEA0nB,EAAAjV,OAAA3B,EAAA2H,QAAAkP,SAAA,EACAC,EAAAnV,OAAA3B,EAAA2H,QAAAoP,aAAA,EAEA,GAAAzP,EAAA,CAEA,IACAP,EADAO,EAAA0P,SACAnqB,KAEAoqB,EAAAC,4BAAA5P,EAAA0P,QAAA,EACAG,EAAAnX,EAAAgC,SAEAoV,EAAAC,wBAAAJ,EAAAE,CAAA,EACAjuB,IAAAouB,EAAA,EACAC,EAAA,EAIAA,EAFAH,GAAA,GACAE,EAAAF,EACA,IAGAE,EAAAE,KAAAC,MAAAL,EAAA,CAAA,EACAI,KAAAC,MAAAL,EAAA,CAAA,GAIA5iB,EAAA,CACAvL,GAAA2tB,EACAc,SAAAJ,CACA,EAEA9iB,EAAAmjB,WAAA,CACAC,KAAA7Q,EACA8Q,OAAA9Q,EAAA,IAAA2P,EACAoB,cAAA/Q,EAAA,gBACA,EAEAja,EAAA2pB,MAAAtsB,KAAAqK,CAAA,EAEA,GAAA+iB,KAGAQ,EAAA,CACA9uB,GAAA6tB,EACAY,SAAAH,CACA,GAEAI,WAAA,CACAC,KAAA7Q,EACA8Q,OAAA9Q,EAAA,IAAA2P,EACAoB,cAAA/Q,EAAA,gBACA,EAEAja,EAAA2pB,MAAAtsB,KAAA4tB,CAAA,GAIA,aAAA,OAAAC,oBAEAC,EAAA,CACAhvB,GAAA+uB,kBAAAzW,SAAA,GAAAtY,GACAyuB,SAAA,EACAC,WAAA,CACAC,KAAA7Q,EACA8Q,OAAA9Q,EAAA,IAAA2P,EACAoB,cAAA/Q,EAAA,gBACA,CACA,EAEAja,EAAA2pB,MAAAtsB,KAAA8tB,CAAA,EAIA,MAOAnrB,EAAA2pB,MAAAtsB,KALA,CACAlB,GAAA2tB,EACAc,SAAA,CACA,CAEA,CAIA,CAWAvY,MATAA,MAAA6V,MAAAnsB,OAAA6E,eAAAwqB,OAAAC,WAAA,MAAA,CACA/rB,KAAAqc,KAAAC,UAAA5b,CAAA,EACAwoB,OAAA,OACAC,QAAA,CACAG,eAAA,mBACA0C,mBAAA,gBACA,CACA,CAAA,GAEAzC,KAAA,EAIAX,MAAAnsB,OAAA6E,eAAAwqB,OAAAG,QAAA,KAAA,EAAA1b,KAAAsC,MAAAqZ,IACAC,EAAApZ,MAAAmZ,EAAA3C,KAAA,EACA7sB,SAAAC,gBAAAgE,cAAA,IAAAC,YAAA,eAAA,CACAC,QAAA,CAAA,EACAC,OAAA,CACAsrB,KAAAD,CACA,CACA,CAAA,CAAA,EACAzvB,SAAAC,gBAAAgE,cAAA,IAAAC,YAAA,eAAA,CACAC,QAAA,CAAA,EACAC,OAAA,CACAsrB,KAAAD,EACAE,aAAA,WAAA5vB,OAAA6E,eAAAC,SAAA+qB,UAAA,OAAA1wB,KAAAkH,QAAA,SAAA,CACA,CACA,CAAA,CAAA,CACA,CAAA,CAEA,CAIAypB,6BAEA,OAAA3wB,KAAAuJ,iBAAA,iCAAA,CAEA,CAEAqnB,6BAGA,OADA5wB,KAAA4oB,WACArf,iBAAA,iCAAA,CAEA,CAEAsnB,qBAIAC,qBASAtI,qBAEAxoB,KAAAqH,SAAA5F,GAAA,QAAA,0BAAAzB,KAAAqd,KAAAjd,KAAAJ,IAAA,CAAA,EACAA,KAAAqH,SAAA5F,GAAA,QAAA,0BAAAzB,KAAAod,KAAAhd,KAAAJ,IAAA,CAAA,EACAA,KAAAqH,SAAA5F,GAAA,QAAA,0BAAAzB,KAAAmd,KAAA/c,KAAAJ,IAAA,CAAA,EAEAA,KAAAqH,SAAA5F,GAAA,QAAA,8BAAAzB,KAAAguB,gBAAA5tB,KAAAJ,IAAA,CAAA,EAEAA,KAAAsB,iBAAA,2CAAA,WAEAiK,QAAAoa,IAAA,qBAAA,EACApa,QAAAoa,IAAA3lB,KAAA4oB,UAAA,EAEA5oB,KAAAspB,mBAAA,EACAtpB,KAAAye,kBAAA,EAEAze,KAAA+oB,WAAAc,UAAAlnB,MAAAuC,OAAAoa,GAAA,EAEAtf,KAAAkjB,kBAAAwE,MAAA,EACA1nB,KAAAkjB,kBAAA7O,SAAArU,KAAA4oB,WAAA/jB,IAAA,CAEA,EAAAzE,KAAAJ,IAAA,CAAA,EAEAA,KAAAsB,iBAAA,mCAAA,aAMAlB,KAAAJ,IAAA,CAAA,EAEAA,KAAAsB,iBAAA,2CAAA,WAGAtB,KAAA+oB,WAAAc,UAAA,EACA7pB,KAAA+oB,WAAAgI,YAAA,EAEA/wB,KAAAgpB,WAAAc,cAAA,EACA9pB,KAAAgpB,WAAAe,cAAA,CAEA,EAAA3pB,KAAAJ,IAAA,CAAA,EAEAA,KAAAqH,SAAA5F,GAAA,QAAA,gBAAAzB,KAAAie,mBAAA7d,KAAAJ,IAAA,CAAA,EAEAA,KAAAsB,iBAAA,mCAAA,SAAAqB,GAEA3C,KAAA+oB,WAAAc,UAAA,EACA7pB,KAAA+oB,WAAAgI,YAAA,EAEA/wB,KAAAgpB,WAAAe,cAAA,CAEA,EAAA3pB,KAAAJ,IAAA,CAAA,EAEAc,SAAAC,gBAAAO,iBAAA,YAAA,WACAtB,KAAAuiB,YAAAU,SAAA,CAAA,CACA,EAAA7iB,KAAAJ,IAAA,CAAA,EAEAc,SAAAC,gBAAAO,iBAAA,aAAA,WACAtB,KAAA0d,mBAAA,CACA,EAAAtd,KAAAJ,IAAA,CAAA,CAEA,CAEA,EAEAif,cAAA,SAAApa,GAEA,GAAAA,GAAA,UAAA,OAAAA,EAWA,MAPA,KAAAA,EAAA7C,MAAA,CAAA,CAAA,EACA6C,GAAA,IAGAA,GAAA,KAGAA,CAEA,EAEAmsB,oCAAAtqB,kBAEAxC,cACA0C,MAAA,EACAE,QAAAC,UAGA,CAEAuQ,oBACAtX,KAAAixB,eAAAjxB,KAAA+Q,cAAA,4BAAA,EACA/Q,KAAAkxB,gBAAAlxB,KAAA+Q,cAAA,iBAAA,CACA,CAEAggB,YAAAzR,GAEA,IAAA6R,EAAArwB,SAAAiQ,cAAA,cAAA,EAEAqgB,GADA,GAAAD,EAAAvI,YACAyI,2BAAA,EACAC,EAAAtxB,KAAA+Q,cAAA,kBAAA,EACAwgB,EAAAnsB,YAAAgsB,CAAA,EAEAE,EAAA3sB,UAAA4sB,CAEA,CAEAC,sBAIA1wB,SAAAiQ,cAAA,cAAA,EACA6X,WACArf,iBAAA,iCAAA,EAEA,OANA,CAQA,CAEAsgB,UAAAvK,GAEApe,IAAA6d,EAEA,IAAA0S,EAAA3wB,SAAAiQ,cAAA,2BAAA,EASAiO,GAHAD,EAJAO,EAIAA,EAAAza,KAHA4sB,EAAA9R,QAAAZ,QAMAE,cAAAF,CAAA,GAEA/e,KAAAixB,eAAAvR,UAAAV,EACAhf,KAAAkxB,gBAAAxR,UAAAX,CAEA,CAEA2S,sBACA,OAAA5wB,SAAAiQ,cAAA,mBAAA,CACA,CAEAyZ,kBACA,OAAA1pB,SAAAiQ,cAAA,cAAA,CACA,CAEA,EAEA4gB,8BAAAjrB,kBAEAxC,cACA0C,MAAA,EACAE,QAAAC,UAGA,CAEAuQ,qBAMA,EAEAsa,6BAAAlrB,kBAEAxC,cACA0C,MAAA,EACAE,QAAAC,UAGA,CAEA8qB,6BACA,OAAA7xB,KAAAuJ,iBAAA,iCAAA,EAAA9G,MACA,CAEAqvB,4BAEA,OADA9xB,KAAAwE,aAAA,OAAA,EAAAmV,OAAA3Z,KAAAwE,aAAA,OAAA,CAAA,EAAA,CAEA,CAEAutB,mBACA,OAAA/xB,KAAA+Q,cAAA,uBAAA,CACA,CAEA0Y,UAIA,OAFA3oB,SAAAiQ,cAAA,cAAA,EAEAmS,kBAAAnS,8CAAA/Q,KAAA6E,QAAA,CAEA,CAEAA,WACA,OAAA7E,KAAA2f,QAAAZ,OACA,CAEAiQ,eAEA,GAAA,CAAArO,aAAA2H,QAAA,SAAA,EACA,MAAA,CAAA,EAGApnB,IAAA8wB,EAAAvR,KAAA8I,MAAA5I,aAAA2H,QAAA,SAAA,CAAA,EAMAzH,GAJA/Z,QAAAC,aACAjC,KAAA0kB,gBAGAwI,EAAAnR,MAEA,IAAA3f,IAAAmB,EAAA,EAAAA,EAAAwe,EAAApe,OAAAJ,CAAA,GAAA,CACA,IAAAid,EAAAuB,EAAAxe,GACA,GAAAid,EAAAza,OAAA7E,KAAA6E,KACA,OAAAya,CAEA,CAEA,CAEAhI,oBAEAtX,KAAAwoB,mBAAA,CAEA,CAEA6I,6BAIA,IASAvsB,EAIAmqB,EACAE,EAEAO,EAhBAlB,EAAAxuB,KAAAuJ,iBAAA,iCAAA,EAEArI,IAAAkwB,EAAA,EAEA,IAAA,MAAApZ,KAAAwW,EAEApvB,OAAAgC,eAAAoC,KAAAgrB,EAAAxW,CAAA,IAGAlT,GADAtE,EAAAguB,EAAAxW,IACAlT,KAEA4qB,EAAA,EAEAT,EAAAC,4BAAAlvB,KAAAgvB,QAAA,EACAG,EAAA3uB,EAAAwZ,SAEA0V,EAAAL,wBAAAJ,EAAAE,CAAA,EAEAiC,GAAAtsB,EAAA4U,MAAAgW,GAMA,OAAA0B,CAEA,CAEA5I,qBAEAxoB,KAAAsB,iBAAA,mCAAAtB,KAAAiyB,iBAAA7xB,KAAAJ,IAAA,CAAA,CAEA,CAEAiyB,iBAAAtvB,GAMA3C,KAAAkyB,YAAA,CAEA,CAEAA,cAEAlyB,KAAA6xB,wBAAA7xB,KAAA8xB,sBACA9xB,KAAA+xB,aAAA5J,QAAA,EAGAnoB,KAAA+xB,aAAAtN,OAAA,CAGA,CAEA,EAEA0N,iCAAAzrB,kBAEAxC,cACA0C,MAAA,EACAE,QAAAC,UAGA,CAEAuQ,qBAIAgE,aAEAtb,KAAA8H,UAAAC,IAAA,iCAAA,EAEA,IAAA8T,EAAA,IAAAvG,gBAAA,IAAAO,qBAAA7V,KACA,CACA+L,WAAA,CAAA,UAAA,UACA9D,QAAA,CAAA,IAAA,IACA,EACA,CACA8T,SAAA,IACAC,OAAA,gCACA,CACA,CAAA,EAEAH,EAAA5F,KAAA,EAEAkB,MAAA0E,EAAAlG,SAEA3V,KAAA8H,UAAAC,IAAA,8BAAA,EAEA/H,KAAA8H,UAAAmU,OAAA,+BAAA,EACAjc,KAAA8H,UAAAmU,OAAA,iCAAA,CAEA,CAEAN,aAEA3b,KAAA8H,UAAAC,IAAA,iCAAA,EAEA,IAAA8T,EAAA,IAAAvG,gBAAA,IAAAO,qBAAA7V,KACA,CACA+L,WAAA,CAAA,SAAA,WACA9D,QAAA,CAAA,IAAA,IACA,EACA,CACA8T,SAAA,IACAC,OAAA,gCACA,CACA,CAAA,EAEAH,EAAA5F,KAAA,EAEAkB,MAAA0E,EAAAlG,SAEA3V,KAAA8H,UAAAC,IAAA,8BAAA,EAEA/H,KAAA8H,UAAAmU,OAAA,+BAAA,EACAjc,KAAA8H,UAAAmU,OAAA,iCAAA,CAEA,CAEA,EAGAmW,kCAAA1rB,kBACA4Q,oBAEAtX,KAAAqyB,UAAA/oB,MAAAkO,KAAAxX,KAAAuJ,iBAAA,sBAAA,CAAA,EACAvJ,KAAAsyB,iBAAAtyB,KAAA+Q,cAAA,wBAAA,EACA/Q,KAAAuyB,mBAAAvyB,KAAA+Q,cAAA,wBAAA,EAEA,EAAA/Q,KAAAqyB,UAAA5vB,SACAzC,KAAAsB,iBAAA,8BAAAtB,KAAA0d,mBAAAtd,KAAAJ,IAAA,CAAA,EACAA,KAAAsB,iBAAA,iBAAAtB,KAAAwyB,SAAApyB,KAAAJ,IAAA,CAAA,EACAA,KAAAsB,iBAAA,iBAAAtB,KAAAmd,KAAA/c,KAAAJ,IAAA,CAAA,EACA8G,QAAAC,aACA/G,KAAAsB,iBAAA,uBAAA,GAAAtB,KAAA+O,OAAApM,EAAAG,OAAAuV,MAAA,CAAA1V,EAAAuC,OAAAiC,IAAA,CAAA,CAIA,CAEAqrB,WACAxyB,KAAA+O,OAAA,CAAA,GAAA/O,KAAAqyB,WAAAI,QAAA,EAAAC,KAAA,GAAAlmB,EAAAmmB,wBAAA,EAAAta,KAAA,CACA,CACA8E,OACAnd,KAAA+O,OAAA/O,KAAAqyB,UAAA9iB,UAAA,GAAA/C,EAAAomB,yBAAA,CAAA,CACA,CACA7jB,OAAAsJ,EAAA/B,EAAA,CAAA,GACAuc,EAAA7yB,KAAAqyB,UAAAha,GAAAlM,sBAAA,EACAnM,KAAAuyB,mBAAAO,SAAA,CACAC,SAAAzc,EAAA,SAAA,OACA0c,KAAAxD,KAAAC,MAAAoD,EAAAG,KAAAnyB,OAAAoyB,WAAA,EAAAJ,EAAA5mB,MAAA,CAAA,CACA,CAAA,CACA,CACAyR,mBAAA/a,GACA,IAAAgG,EACA,OAAAA,EAAA3I,KAAAsyB,mBAAA3pB,EAAAX,MAAAoa,YAAA,cAAAzf,EAAAuC,OAAA+d,SAAA,GAAA,CACA,CAIAwB,SACAzkB,KAAA8H,UAAAmU,OAAA,iCAAA,CACA,CAEAkM,UACAnoB,KAAA8H,UAAAC,IAAA,iCAAA,CACA,CAEA,EAIAmrB,iCAAAxsB,kBAEA4Q,oBAEAtX,KAAAwoB,mBAAA,CAEA,CAEA2K,eAAAxwB,GAEA3C,KAAA+O,OAAA,CAEA,CAEAyZ,qBAEAxoB,KAAAqH,SAAA5F,GAAA,QAAA,uCAAAzB,KAAAmzB,eAAA/yB,KAAAJ,IAAA,CAAA,CAEA,CAEA+O,SAIA,GAFA/O,KAAA8H,UAAA2E,SAAA,gCAAA,GAIAzM,KAAA8H,UAAAC,IAAA,gCAAA,EACA/H,KAAAozB,YAAA1T,UAAA7e,OAAAqX,cAAAmb,WAAAtb,mBAAA0P,SAEA7iB,aAAA5E,KAAA,oCAAA,IAKAA,KAAA8H,UAAAmU,OAAA,gCAAA,EACAjc,KAAAozB,YAAA1T,UAAA7e,OAAAqX,cAAAmb,WAAAtb,mBAAAub,YAEA1uB,aAAA5E,KAAA,sCAAA,GAIA4E,aAAA5E,KAAA,kCAAA,CAEA,CAIA8E,WACA,OAAA2b,KAAA8I,MAAAvpB,KAAA2f,QAAA4T,WAAA,CACA,CAEAC,eACA,OAAA/S,KAAA8I,MAAAvpB,KAAA2f,QAAA4T,WAAA,EAAA7Z,KACA,CAEAM,eACA,OAAAha,KAAA2f,QAAA3F,QACA,CAIAwC,aACA,OAAAxc,KAAA+Q,cAAA,sCAAA,CACA,CAEAqiB,kBACA,OAAApzB,KAAA+Q,cAAA,uCAAA,CACA,CAEAsH,YACA,MAAA,CAAA,GAAArY,KAAAiD,WAAAuH,UAAAhJ,QAAAxB,IAAA,CACA,CAEA4yB,gCACA,MAAA,QAAA/xB,OAAA6E,eAAAC,SAAA8tB,UACAzzB,KAAAmM,sBAAA,EAAA6mB,KAAAnyB,OAAAoyB,WAAA,EAEAjzB,KAAAmM,sBAAA,EAAAunB,MAAA7yB,OAAAoyB,WAAA,CAEA,CACAN,+BACA,MAAA,QAAA9xB,OAAA6E,eAAAC,SAAA8tB,UACAzzB,KAAAmM,sBAAA,EAAAunB,MAAA7yB,OAAAoyB,WAAA,EAEAjzB,KAAAmM,sBAAA,EAAA6mB,KAAAnyB,OAAAoyB,WAAA,CAEA,CACA,EAGAb,kCAAA1rB,kBACA4Q,oBAEAtX,KAAAqyB,UAAA/oB,MAAAkO,KAAAxX,KAAAuJ,iBAAA,sBAAA,CAAA,EACAvJ,KAAAsyB,iBAAAtyB,KAAA+Q,cAAA,wBAAA,EACA/Q,KAAAuyB,mBAAAvyB,KAAA+Q,cAAA,wBAAA,EAEA,EAAA/Q,KAAAqyB,UAAA5vB,SACAzC,KAAAsB,iBAAA,8BAAAtB,KAAA0d,mBAAAtd,KAAAJ,IAAA,CAAA,EACAA,KAAAsB,iBAAA,iBAAAtB,KAAAwyB,SAAApyB,KAAAJ,IAAA,CAAA,EACAA,KAAAsB,iBAAA,iBAAAtB,KAAAmd,KAAA/c,KAAAJ,IAAA,CAAA,EACA8G,QAAAC,aACA/G,KAAAsB,iBAAA,uBAAA,GAAAtB,KAAA+O,OAAApM,EAAAG,OAAAuV,MAAA,CAAA1V,EAAAuC,OAAAiC,IAAA,CAAA,CAIA,CAEAqrB,WACAxyB,KAAA+O,OAAA,CAAA,GAAA/O,KAAAqyB,WAAAI,QAAA,EAAAC,KAAA,GAAAlmB,EAAAmmB,wBAAA,EAAAta,KAAA,CACA,CACA8E,OACAnd,KAAA+O,OAAA/O,KAAAqyB,UAAA9iB,UAAA,GAAA/C,EAAAomB,yBAAA,CAAA,CACA,CACA7jB,OAAAsJ,EAAA/B,EAAA,CAAA,GACAuc,EAAA7yB,KAAAqyB,UAAAha,GAAAlM,sBAAA,EACAnM,KAAAuyB,mBAAAO,SAAA,CACAC,SAAAzc,EAAA,SAAA,OACA0c,KAAAxD,KAAAC,MAAAoD,EAAAG,KAAAnyB,OAAAoyB,WAAA,EAAAJ,EAAA5mB,MAAA,CAAA,CACA,CAAA,CACA,CACAyR,mBAAA/a,GACA,IAAAgG,EACA,OAAAA,EAAA3I,KAAAsyB,mBAAA3pB,EAAAX,MAAAoa,YAAA,cAAAzf,EAAAuC,OAAA+d,SAAA,GAAA,CACA,CAIAwB,SACAzkB,KAAA8H,UAAAmU,OAAA,iCAAA,CACA,CAEAkM,UACAnoB,KAAA8H,UAAAC,IAAA,iCAAA,CACA,CAEA,EAIA4rB,oCAAAjtB,kBAEA4Q,oBAEAtX,KAAAwoB,mBAAA,EAEAxoB,KAAAwqB,YAAA1pB,SAAAiQ,cAAA,cAAA,CAEA,CAEAyX,sBAMAsB,gBAEA,IACAxK,EADAxe,SAAAiQ,cAAA,cAAA,EAAA6X,WACAoG,SAGA4E,EAAA5zB,KAAAuJ,iBAAA,4BAAA,EACAsqB,EAAA7zB,KAAA+Q,cAAA,8BAAA,EACA+iB,EAAA9zB,KAAA+Q,cAAA,kCAAA,EACAgjB,EAAA/zB,KAAA+Q,cAAA,gCAAA,EACAijB,EAAAh0B,KAAA+Q,cAAA,gCAAA,EACAkjB,EAAAj0B,KAAA+Q,cAAA,6BAAA,EACAmjB,EAAAl0B,KAAA+Q,cAAA,0CAAA,EAGA,MAAAojB,EAAA7U,EAAAza,KACA,IAAAuvB,EAAA9U,EAAA8B,OACAiT,EAsCA,SAAA50B,GAEAyB,IAAAozB,EAGAA,EADA,QAAA70B,EACA,uBAGA,KAAAA,EAAA,QAGA,OAAA60B,CAEA,EAnDAhV,EAAAiC,iBAAA,EACAgT,EAoDA,SAAAC,GAEAtzB,IAAAuzB,EAGAA,EADAD,EAAA,GACAA,EAGAhF,KAAAC,MAAA+E,EAAA,EAAA,EAGA,OAAAC,CACA,EAhEAnV,EAAAqC,aAAA,EACA+S,EAmEA,GAnEApV,EAAAqC,cAmEA,QAAA,SAlEAgT,EAeA,SAAAl1B,GAEAyB,IAAA0zB,EAEA,OAAAn1B,EACAm1B,EAAA,kBAGA,UAAAn1B,EACAm1B,EAAA,oBAGA,QAAAn1B,IACAm1B,EAAA,iBAGA,OAAAA,CAEA,EAjCAtV,EAAAmC,cAAA,EACAoT,EAAA,SAAAvV,EAAA2B,IAAA,MAAA,MAGA4S,EAAAnU,UAAA0U,EACAN,EAAApU,UAAA6U,EACAR,EAAArU,UAAAgV,EACAV,EAAAtU,UAAAiV,EACAV,EAAAvU,UAAA2U,EACAH,EAAAxU,UAAAmV,EAEAjB,EAAA7mB,QAAAvM,IACAA,EAAAkf,UAAAyU,CACA,CAAA,CAyDA,CAEApK,cAAAjlB,GAEA,IACAwa,EADAxe,SAAAiQ,cAAA,cAAA,EAAA6X,WACAoG,SAEA8F,EAAAh0B,SAAAiQ,cAAA,2BAAA,EACA,MAAAiH,EAAA8c,EAAA/jB,cAAA,iCAAA,EAAA+jB,EAAA/jB,cAAA,iCAAA,EAAA+jB,EAAA/jB,cAAA,uBAAA,EACA,IAAAga,EAAAtK,KAAA8I,MAAAvR,EAAA2H,QAAAoL,WAAA,EAEAgK,EAAAl0B,OAAAqX,cAAA8c,MAAAC,YACAC,EAAAH,EAAA,EAMAI,GAJA5pB,QAAAoa,IAAAoF,CAAA,EAIAqK,oBAAA9V,CAAA,GAEA+V,EAAA,IAAAF,EACAG,EAAA,GAAAH,EACAI,EAAA,IAAAJ,EACAK,EAAAL,CAAAA,EAEAhG,EAAAnX,EAAAgC,SACAyb,EAAAtG,EAAA,GACAuG,EAAA/b,OAAAoR,EAAA4K,aAAAl2B,KAAA,EAEAm2B,EAAApG,KAAAqG,MAAAR,EAAAI,CAAA,EACAK,EAAAtG,KAAAqG,MAAAP,EAAAG,CAAA,EACAM,EAAAvG,KAAAqG,MAAAN,EAAAE,CAAA,EACAO,EAAAxG,KAAAqG,MAAAL,EAAAC,CAAA,EAEAQ,EAAAZ,EAAAK,EACAQ,EAAAZ,EAAAI,EACAS,EAAAZ,EAAAG,EACAU,EAAAZ,EAAAE,EAEAzG,EAAAC,4BAAA5P,CAAA,EAEA+W,EAAAhH,wBAAAJ,EAAAE,CAAA,EAEAmH,EAAA,GAAAD,EACAE,EAAAtH,EAAAyG,EAEAc,EAAAF,EAAAvB,EACA0B,EAAAF,EAAAxB,EA8DA2B,GA5DAnrB,QAAAoa;;;;;;;;0CAQAwP;;;;;;yBAMAJ;0BACAG;;;;;gCAKAjG;;oBAEAoG;oBACAC;oBACAC;oBACAC;;;;;;4BAMAI;4BACAE;4BACAC;4BACAC;;yBAEAM;;;;;;0BAMAL;0BACAC;0BACAC;0BACAC;;uBAEAG;;;;6BAIApH;6BACAkH;;;;OAIA,EAIAr2B,KAAA+Q,cAAA,gCAAA,GAEA4lB,EAAA32B,KAAA+Q,cAAA,gCAAA,EACA6lB,EAAA52B,KAAA+Q,cAAA,8BAAA,EACA8lB,EAAA72B,KAAA+Q,cAAA,4BAAA,EAAAA,cAAA,eAAA,EACA+lB,EAAA92B,KAAA+Q,cAAA,4BAAA,EAAAA,cAAA,aAAA,EACAgmB,EAAA/2B,KAAA+Q,cAAA,4BAAA,EAAAA,cAAA,eAAA,EACAimB,EAAAh3B,KAAA+Q,cAAA,4BAAA,EAAAA,cAAA,aAAA,EACAkmB,EAAAj3B,KAAA+Q,cAAA,4BAAA,EAAAA,cAAA,eAAA,EACAmmB,EAAAl3B,KAAA+Q,cAAA,4BAAA,EAAAA,cAAA,aAAA,EACAomB,EAAAn3B,KAAA+Q,cAAA,4BAAA,EAAAA,cAAA,eAAA,EACAqmB,EAAAp3B,KAAA+Q,cAAA,4BAAA,EAAAA,cAAA,aAAA,EAEAsmB,EA6BArf,EAAAjH,cAAA,UAAA,EAAApM,UA5BA2yB,EAAAC,iBAAAf,CAAA,EACAgB,EAAAD,iBAAAd,CAAA,EACAgB,EAAAF,iBAAA3B,CAAA,EACA8B,EAAAH,iBAAAtB,CAAA,EACA0B,EAAAJ,iBAAAzB,CAAA,EACA8B,EAAAL,iBAAArB,CAAA,EACA2B,EAAAN,iBAAAxB,CAAA,EACA+B,EAAAP,iBAAApB,CAAA,EACA4B,EAAAR,iBAAAvB,CAAA,EACAgC,EAAAT,iBAAAnB,CAAA,EAEAM,EAAA/xB,UAAA0yB,EAEAV,EAAAhyB,UAAA2yB,EACAV,EAAAjyB,UAAA6yB,EACAX,EAAAlyB,UAAA8yB,EACAX,EAAAnyB,UAAA+yB,EACAX,EAAApyB,UAAAgzB,EACAX,EAAAryB,UAAAizB,EACAX,EAAAtyB,UAAAkzB,EACAX,EAAAvyB,UAAAmzB,EACAX,EAAAxyB,UAAAozB,EACAX,EAAAzyB,UAAAqzB,CAYA,CAEA,EAEAC,gCAAAvxB,kBAEAxC,cACA0C,MAAA,EACAE,QAAAC,UAGA,CAEAuQ,qBAIA,EAEA4gB,oCAAAxxB,kBAEAxC,cACA0C,MAAA,EACAE,QAAAC,UAGA,CAEAuQ,qBAIA6gB,cACA,OAAAn4B,KAAAuJ,iBAAA,eAAA,CACA,CAEA8K,SAAA+jB,GAEAA,GAAA,UAAA,OAAAA,IAIA3O,EAAAzpB,KAAA+Q,6BAAAqnB,KAAA,IAGA3O,EAAA3hB,UAAAC,IAAA,QAAA,CAGA,CAEA2f,QAEA1nB,KAAAm4B,QAAAprB,QAAAvM,IACAA,EAAAsH,UAAAmU,OAAA,QAAA,CACA,CAAA,CAEA,CAEA,EA6BAoc,mBA3BAx3B,OAAAy3B,eAAAC,OAAA,uBAAAtd,kBAAA,EACApa,OAAAy3B,eAAAC,OAAA,iBAAAlhB,aAAA,EACAxW,OAAAy3B,eAAAC,OAAA,kBAAAlc,aAAA,EACAxb,OAAAy3B,eAAAC,OAAA,aAAA1b,SAAA,EACAhc,OAAAy3B,eAAAC,OAAA,kBAAA1T,cAAA,EAEAhkB,OAAAy3B,eAAAC,OAAA,sBAAA/R,iBAAA,EACA3lB,OAAAy3B,eAAAC,OAAA,8BAAA7S,wBAAA,EACA7kB,OAAAy3B,eAAAC,OAAA,YAAA3R,QAAA,EACA/lB,OAAAy3B,eAAAC,OAAA,iBAAA1R,YAAA,EACAhmB,OAAAy3B,eAAAC,OAAA,aAAA1Q,SAAA,EACAhnB,OAAAy3B,eAAAC,OAAA,YAAAjR,QAAA,EACAzmB,OAAAy3B,eAAAC,OAAA,gBAAAtQ,WAAA,EACApnB,OAAAy3B,eAAAC,OAAA,oBAAAnQ,eAAA,EACAvnB,OAAAy3B,eAAAC,OAAA,uBAAApG,kBAAA,EAEAtxB,OAAAy3B,eAAAC,OAAA,mBAAAvH,qBAAA,EACAnwB,OAAAy3B,eAAAC,OAAA,eAAAlQ,WAAA,EACAxnB,OAAAy3B,eAAAC,OAAA,oBAAA5G,eAAA,EACA9wB,OAAAy3B,eAAAC,OAAA,mBAAA3G,cAAA,EACA/wB,OAAAy3B,eAAAC,OAAA,wBAAAnG,mBAAA,EACAvxB,OAAAy3B,eAAAC,OAAA,uBAAArF,kBAAA,EACAryB,OAAAy3B,eAAAC,OAAA,0BAAA5E,qBAAA,EAEA9yB,OAAAy3B,eAAAC,OAAA,sBAAAN,iBAAA,EACAp3B,OAAAy3B,eAAAC,OAAA,sBAAAL,qBAAA,gBAEAxxB,kBAEAxC,cACA0C,MAAA,EACAE,QAAAC,UAGA,CAEAuQ,oBAEAtX,KAAAiG,OAAA,EAEAjG,KAAAojB,UAAApjB,KAAAkH,QAAA,YAAA,EACAlH,KAAAw4B,kBAAAx4B,KAAA+Q,cAAA,YAAA,EACA/Q,KAAAy4B,eAAAz4B,KAAA+Q,cAAA,aAAA,EAAA7J,QAAA,iBAAA,EAEAlH,KAAAy4B,eAAAn3B,iBAAA,8BAAAtB,KAAA04B,iBAAAt4B,KAAAJ,IAAA,CAAA,CAEA,CAEA04B,iBAAA/1B,GAEAA,EAAAG,OAEAmD,EADAtD,EAAAG,OAAAiO,cAAA,OAAA,EACAtR,MAEAO,KAAAiG,OAAA0T,OAAA1T,CAAA,EAEAjG,KAAA24B,YAAA,CAEA,CAEAA,cAIA,GAAA34B,EAAAA,KAAAw4B,kBAAAjvB,iBAAA,iBAAA,EAAA9G,QAAAzC,KAAAiG,QAAAjG,KAAAiG,QAAA,GAAA,CAIA,KAAAjG,KAAAw4B,kBAAAjvB,iBAAA,iBAAA,EAAA9G,QAAAzC,KAAAiG,QACAjG,KAAAw4B,kBAAAjvB,iBAAA,iBAAA,EAAA9G,OAAAzC,KAAAiG,QAEAjG,KAAAw4B,kBAAAjvB,iBAAA,iBAAA,EAAAvJ,KAAAw4B,kBAAAjvB,iBAAA,iBAAA,EAAA9G,OAAA,GAAAwZ,OAAA,EACAjc,KAAAw4B,kBAAAjvB,iBAAA,uBAAA,EAAAvJ,KAAAw4B,kBAAAjvB,iBAAA,uBAAA,EAAA9G,OAAA,GAAAwZ,OAAA,IAKAjc,KAAAw4B,kBAAArV,mBAAA,YAAArL,UAAAiD,eAAA,CACA9Z,GAAA,aAAAjB,KAAAw4B,kBAAAjvB,iBAAA,iBAAA,EAAA9G,OAAA,GACAoC,KAAA,aAAA7E,KAAAw4B,kBAAAjvB,iBAAA,iBAAA,EAAA9G,OAAA,GACAuY,YAAA,YACA,CAAA,CAAA,EAEAhb,KAAAw4B,kBAAArV,mBAAA,YAAArL,UAAA+C,iBAAA,CAAAG,YAAA,GAAA,CAAA,CAAA,GAOA,IAAA9Z,IAAAmB,EAAA,EAAAA,EAAArC,KAAAw4B,kBAAAjvB,iBAAA,uBAAA,EAAA9G,OAAAJ,CAAA,GAAA,CACA,IAAA7B,EAAAR,KAAAw4B,kBAAAjvB,iBAAA,uBAAA,EAAAlH,GACA,GAAAA,EACA7B,EAAAkf,UAAA,QAGArd,GAAArC,KAAAw4B,kBAAAjvB,iBAAA,uBAAA,EAAA9G,OAAA,EACAjC,EAAAkf,UAAA,IAGArd,GAAArC,KAAAw4B,kBAAAjvB,iBAAA,uBAAA,EAAA9G,OAAA,EACAjC,EAAAkf,UAAA,IAGAlf,EAAAkf,UAAA,GAEA,CAxCA,CA0CA,CAEA,GAEAkZ,iCAAAlyB,kBAEAxC,cACA0C,MAAA,EACAE,QAAAC,UAGA,CAEAuQ,qBAIA,EAEAuhB,iCAAAD,mBAEA10B,cACA0C,MAAA,EACAE,QAAAC,UAGA,CAEAuQ,qBAIA,EAEAwhB,iCAAAF,mBAEA10B,cACA0C,MAAA,EACAE,QAAAC,UAGA,CAEAuQ,qBAIA,EAEAyhB,kCAAAH,mBAEA10B,cACA0C,MAAA,EACAE,QAAAC,UAGA,CAEAuQ,qBAIA,EAEA0hB,uCAAAJ,mBAEA10B,cACA0C,MAAA,EACAE,QAAAC,UAGA,CAEAuQ,qBAIA,EAEA2hB,uCAAAL,mBAEA10B,cACA0C,MAAA,EACAE,QAAAC,UAGA,CAEAuQ,qBAIA,EAEA4hB,gCAAAN,mBAEA10B,cACA0C,MAAA,EACAE,QAAAC,UAGA,CAEAuQ,qBAIA,EAEA6hB,2BAAAzyB,kBAEAxC,cACA0C,MAAA,EACAE,QAAAC,UAGA,CAEAuQ,oBAEAtX,KAAA+O,OAAA/O,KAAA+Q,cAAA,QAAA,EAIA7P,IAAAuD,EAAA,GAEAA,EAAA20B,WAAAp5B,CAAAA,CAAAA,KAAAuD,aAAA,YAAA,EAEAvD,KAAAq5B,WAAAC,WAAAl5B,KAAAJ,KAAA+O,OAAAtK,CAAA,CAEA,CAEA80B,SACAv5B,KAAAq5B,WAAAE,OAAA,CACA,CAEA,EAkBAC,8BAhBA34B,OAAAy3B,eAAAC,OAAA,uBAAAF,iBAAA,EAEAx3B,OAAAy3B,eAAAC,OAAA,wBAAAK,kBAAA,EACA/3B,OAAAy3B,eAAAC,OAAA,wBAAAM,kBAAA,EAEAh4B,OAAAy3B,eAAAC,OAAA,wBAAAO,kBAAA,EACAj4B,OAAAy3B,eAAAC,OAAA,yBAAAQ,mBAAA,EACAl4B,OAAAy3B,eAAAC,OAAA,+BAAAS,wBAAA,EACAn4B,OAAAy3B,eAAAC,OAAA,+BAAAU,wBAAA,EACAp4B,OAAAy3B,eAAAC,OAAA,uBAAAW,iBAAA,EAEAp4B,SAAAQ,iBAAA,mBAAA,IACAT,OAAAy3B,eAAAC,OAAA,gBAAAY,YAAA,CACA,CAAA,QAKAj1B,cAEAlE,KAAAmE,gBAAA,IAAAH,aAAAlD,SAAAsD,IAAA,EACApE,KAAAmE,gBAAA1C,GAAA,QAAA,4BAAAzB,KAAAqE,gBAAAjE,KAAAJ,IAAA,CAAA,EACAA,KAAAmE,gBAAA1C,GAAA,SAAA,4BAAAzB,KAAAy5B,WAAAr5B,KAAAJ,IAAA,CAAA,CAEA,CAEAqE,gBAAA1B,EAAAG,GAEAH,EAAAG,OACAwB,EAAAxD,SAAAyD,eAAAzB,EAAA0B,aAAA,yBAAA,CAAA,EAEAF,IACA,WAAAxB,EAAAvC,UACAuC,EAAAA,EAAA2B,QAAA3B,EAAA4B,gBAEAJ,EAAA7E,MAAAqD,EAAAS,aAAA,OAAA,EAAAT,EAAA0B,aAAA,OAAA,EAAA1B,EAAArD,MAGA,CAEAg6B,WAAA92B,EAAAG,GAEAH,EAAAG,OACAwB,EAAAxD,SAAAyD,eAAAzB,EAAA0B,aAAA,yBAAA,CAAA,EAEAF,IACA,WAAAxB,EAAAvC,UACAuC,EAAAA,EAAA2B,QAAA3B,EAAA4B,gBAEAJ,EAAA7E,MAAAqD,EAAAS,aAAA,OAAA,EAAAT,EAAA0B,aAAA,OAAA,EAAA1B,EAAArD,MAGA,CAEA,GAEAi6B,6BAEAx1B,cAEAlE,KAAAmE,gBAAA,IAAAH,aAAAlD,SAAAsD,IAAA,EACApE,KAAAmE,gBAAA1C,GAAA,QAAA,gCAAAzB,KAAAqE,gBAAAjE,KAAAJ,IAAA,CAAA,CAEA,CAEAqE,gBAAA1B,EAAAG,GAEA,IAAA62B,EAAA74B,SAAAyD,eAAAzB,EAAA0B,aAAA,6BAAA,CAAA,EACAo1B,EAAA94B,SAAAyD,eAAAzB,EAAA0B,aAAA,4BAAA,CAAA,EAEA/E,EAAAqD,EAAArD,MAEAo6B,EAAAp6B,EAAA+G,MAAA,GAAA,EACAtF,IAAA44B,EACAC,EAIAA,EAFA,CAAA,GAAAt6B,EAAA+B,QAAA,GAAA,GACAs4B,EAAAD,EAAA,GACAA,EAAAA,EAAAp3B,OAAA,GAAAo3B,EAAAA,EAAAp3B,OAAA,GAAA,KAGAq3B,EAAAr6B,EACA,IAGAk6B,GACAA,EAAAK,aAAA,QAAAF,CAAA,EAGAF,GACAA,EAAAI,aAAA,QAAAD,CAAA,CAGA,CAEA,EAIA,IAAAP,6BACA,IAAAE,uBA8CA,MAAAO,qBAAAn5B,SAAAyI,iBAAA,6BAAA,EA0BA,SAAA2wB,oBAGAp5B,SAAAyI,iBAAA,6BAAA,EAAAwD,QAAAkC,IAEA,IAAAkrB,EAAAlrB,EAAAzK,aAAA,2BAAA,EACA41B,EAAAt5B,SAAAyD,eAAA41B,CAAA,EAEAC,IAIAA,EAAA36B,MAAA,GACA26B,EAAApyB,MAAA,iBAIA,GAFAqyB,EAAAD,EAAA7wB,iBAAA,yBAAA,GAEA9G,SACA43B,EAAAttB,QAAAutB,IAEAA,EAAA76B,MAAA,GACA66B,EAAAv1B,cAAA,IAAAw1B,MAAA,QAAA,CAAA,EAEAD,EAAApzB,QAAA,eAAA,GACAozB,EAAApzB,QAAA,eAAA,EAAAqyB,OAAA,CAGA,CAAA,CAGA,CAAA,CAEA,CAxDAU,qBAAAltB,QAAAvM,IAEAA,EAAAc,iBAAA,SAAAqB,IAGAw3B,EADAx3B,EAAAG,OACA0B,aAAA,2BAAA,EACAtD,IAAAk5B,EAEAD,IACAC,EAAAt5B,SAAAyD,eAAA41B,CAAA,GAGAD,kBAAA,EAEAE,IAIAA,EAAApyB,MAAA,kBAEA,CAAA,CAEA,CAAA,EAoCA,MAAAwyB,gBAAA15B,SAAAyI,iBAAA,yBAAA,EAEAixB,gBAAAztB,QAAAvM,IAEAA,EAAAc,iBAAA,SAAAqB,IAEA,IAAA83B,EAAA93B,EAAAG,OACA43B,EAAA55B,SAAAiQ,cAAA,0BAAA,EAEA4pB,EAAA,IAAAF,EAAAh7B,MAAAm7B,KAAA,EAAAH,EAAAh7B,MAAAm7B,KAAA,EAAAH,EAAAh7B,MAAAm7B,KAAA,EAAA,IAEAC,EAAAr6B,EAAAgE,aAAA,oCAAA,EACAs2B,EAAAh6B,SAAAyD,eAAA/D,EAAAgE,aAAA,oCAAA,CAAA,EAEAq2B,GAAAC,IACAt6B,EAAAf,OAAAo7B,GACAC,EAAAhzB,UAAAmU,OAAA,QAAA,EACA6e,EAAA/pB,cAAA,OAAA,EAAAkC,MAAA,GAGA6nB,EAAAhzB,UAAAC,IAAA,QAAA,GAIA2yB,EAAAV,aAAA,OAAAS,EAAAj2B,aAAA,MAAA,CAAA,EACAk2B,EAAAV,aAAA,QAAAW,CAAA,CAEA,CAAA,CAEA,CAAA", "file": "quiz.js", "sourcesContent": ["  var __defProp = Object.defineProperty;\n  var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\n  var __publicField = (obj, key, value) => {\n    __defNormalProp(obj, typeof key !== \"symbol\" ? key + \"\" : key, value);\n    return value;\n  };\n\n  // node_modules/ftdomdelegate/main.js\n  function Delegate(root) {\n    this.listenerMap = [{}, {}];\n    if (root) {\n      this.root(root);\n    }\n    this.handle = Delegate.prototype.handle.bind(this);\n    this._removedListeners = [];\n  }\n  Delegate.prototype.root = function (root) {\n    const listenerMap = this.listenerMap;\n    let eventType;\n    if (this.rootElement) {\n      for (eventType in listenerMap[1]) {\n        if (listenerMap[1].hasOwnProperty(eventType)) {\n          this.rootElement.removeEventListener(eventType, this.handle, true);\n        }\n      }\n      for (eventType in listenerMap[0]) {\n        if (listenerMap[0].hasOwnProperty(eventType)) {\n          this.rootElement.removeEventListener(eventType, this.handle, false);\n        }\n      }\n    }\n    if (!root || !root.addEventListener) {\n      if (this.rootElement) {\n        delete this.rootElement;\n      }\n      return this;\n    }\n    this.rootElement = root;\n    for (eventType in listenerMap[1]) {\n      if (listenerMap[1].hasOwnProperty(eventType)) {\n        this.rootElement.addEventListener(eventType, this.handle, true);\n      }\n    }\n    for (eventType in listenerMap[0]) {\n      if (listenerMap[0].hasOwnProperty(eventType)) {\n        this.rootElement.addEventListener(eventType, this.handle, false);\n      }\n    }\n    return this;\n  };\n  Delegate.prototype.captureForType = function (eventType) {\n    return [\"blur\", \"error\", \"focus\", \"load\", \"resize\", \"scroll\"].indexOf(eventType) !== -1;\n  };\n  Delegate.prototype.on = function (eventType, selector, handler, useCapture) {\n    let root;\n    let listenerMap;\n    let matcher;\n    let matcherParam;\n    if (!eventType) {\n      throw new TypeError(\"Invalid event type: \" + eventType);\n    }\n    if (typeof selector === \"function\") {\n      useCapture = handler;\n      handler = selector;\n      selector = null;\n    }\n    if (useCapture === void 0) {\n      useCapture = this.captureForType(eventType);\n    }\n    if (typeof handler !== \"function\") {\n      throw new TypeError(\"Handler must be a type of Function\");\n    }\n    root = this.rootElement;\n    listenerMap = this.listenerMap[useCapture ? 1 : 0];\n    if (!listenerMap[eventType]) {\n      if (root) {\n        root.addEventListener(eventType, this.handle, useCapture);\n      }\n      listenerMap[eventType] = [];\n    }\n    if (!selector) {\n      matcherParam = null;\n      matcher = matchesRoot.bind(this);\n    } else if (/^[a-z]+$/i.test(selector)) {\n      matcherParam = selector;\n      matcher = matchesTag;\n    } else if (/^#[a-z0-9\\-_]+$/i.test(selector)) {\n      matcherParam = selector.slice(1);\n      matcher = matchesId;\n    } else {\n      matcherParam = selector;\n      matcher = Element.prototype.matches;\n    }\n    listenerMap[eventType].push({\n      selector,\n      handler,\n      matcher,\n      matcherParam\n    });\n    return this;\n  };\n  Delegate.prototype.off = function (eventType, selector, handler, useCapture) {\n    let i;\n    let listener;\n    let listenerMap;\n    let listenerList;\n    let singleEventType;\n    if (typeof selector === \"function\") {\n      useCapture = handler;\n      handler = selector;\n      selector = null;\n    }\n    if (useCapture === void 0) {\n      this.off(eventType, selector, handler, true);\n      this.off(eventType, selector, handler, false);\n      return this;\n    }\n    listenerMap = this.listenerMap[useCapture ? 1 : 0];\n    if (!eventType) {\n      for (singleEventType in listenerMap) {\n        if (listenerMap.hasOwnProperty(singleEventType)) {\n          this.off(singleEventType, selector, handler);\n        }\n      }\n      return this;\n    }\n    listenerList = listenerMap[eventType];\n    if (!listenerList || !listenerList.length) {\n      return this;\n    }\n    for (i = listenerList.length - 1; i >= 0; i--) {\n      listener = listenerList[i];\n      if ((!selector || selector === listener.selector) && (!handler || handler === listener.handler)) {\n        this._removedListeners.push(listener);\n        listenerList.splice(i, 1);\n      }\n    }\n    if (!listenerList.length) {\n      delete listenerMap[eventType];\n      if (this.rootElement) {\n        this.rootElement.removeEventListener(eventType, this.handle, useCapture);\n      }\n    }\n    return this;\n  };\n  Delegate.prototype.handle = function (event) {\n    let i;\n    let l;\n    const type = event.type;\n    let root;\n    let phase;\n    let listener;\n    let returned;\n    let listenerList = [];\n    let target;\n    const eventIgnore = \"ftLabsDelegateIgnore\";\n    if (event[eventIgnore] === true) {\n      return;\n    }\n    target = event.target;\n    if (target.nodeType === 3) {\n      target = target.parentNode;\n    }\n    if (target.correspondingUseElement) {\n      target = target.correspondingUseElement;\n    }\n    root = this.rootElement;\n    phase = event.eventPhase || (event.target !== event.currentTarget ? 3 : 2);\n    switch (phase) {\n      case 1:\n        listenerList = this.listenerMap[1][type];\n        break;\n      case 2:\n        if (this.listenerMap[0] && this.listenerMap[0][type]) {\n          listenerList = listenerList.concat(this.listenerMap[0][type]);\n        }\n        if (this.listenerMap[1] && this.listenerMap[1][type]) {\n          listenerList = listenerList.concat(this.listenerMap[1][type]);\n        }\n        break;\n      case 3:\n        listenerList = this.listenerMap[0][type];\n        break;\n    }\n    let toFire = [];\n    l = listenerList.length;\n    while (target && l) {\n      for (i = 0; i < l; i++) {\n        listener = listenerList[i];\n        if (!listener) {\n          break;\n        }\n        if (target.tagName && [\"button\", \"input\", \"select\", \"textarea\"].indexOf(target.tagName.toLowerCase()) > -1 && target.hasAttribute(\"disabled\")) {\n          toFire = [];\n        } else if (listener.matcher.call(target, listener.matcherParam, target)) {\n          toFire.push([event, target, listener]);\n        }\n      }\n      if (target === root) {\n        break;\n      }\n      l = listenerList.length;\n      target = target.parentElement || target.parentNode;\n      if (target instanceof HTMLDocument) {\n        break;\n      }\n    }\n    let ret;\n    for (i = 0; i < toFire.length; i++) {\n      if (this._removedListeners.indexOf(toFire[i][2]) > -1) {\n        continue;\n      }\n      returned = this.fire.apply(this, toFire[i]);\n      if (returned === false) {\n        toFire[i][0][eventIgnore] = true;\n        toFire[i][0].preventDefault();\n        ret = false;\n        break;\n      }\n    }\n    return ret;\n  };\n  Delegate.prototype.fire = function (event, target, listener) {\n    return listener.handler.call(target, event, target);\n  };\n  function matchesTag(tagName, element) {\n    return tagName.toLowerCase() === element.tagName.toLowerCase();\n  }\n  function matchesRoot(selector, element) {\n    if (this.rootElement === window) {\n      return element === document || element === document.documentElement || element === window;\n    }\n    return this.rootElement === element;\n  }\n  function matchesId(id, element) {\n    return id === element.id;\n  }\n  Delegate.prototype.destroy = function () {\n    this.off();\n    this.root();\n  };\n  var main_default = Delegate;\n\n  // js/components/input-binding-manager.js\n  var InputBindingManager = class {\n    constructor() {\n      this.delegateElement = new main_default(document.body);\n      this.delegateElement.on(\"change\", \"[data-bind-value]\", this._onValueChanged.bind(this));\n    }\n    _onValueChanged(event, target) {\n      const boundElement = document.getElementById(target.getAttribute(\"data-bind-value\"));\n      if (boundElement) {\n        if (target.tagName === \"SELECT\") {\n          target = target.options[target.selectedIndex];\n        }\n        boundElement.innerHTML = target.hasAttribute(\"title\") ? target.getAttribute(\"title\") : target.value;\n      }\n    }\n  };\n\n  // js/helper/event.js\n  function triggerEvent(element, name, data = {}) {\n    element.dispatchEvent(new CustomEvent(name, {\n      bubbles: true,\n      detail: data\n    }));\n  }\n  function triggerNonBubblingEvent(element, name, data = {}) {\n    element.dispatchEvent(new CustomEvent(name, {\n      bubbles: false,\n      detail: data\n    }));\n  }\n\n  // js/helper/currency.js\n  function formatMoney(cents, format = \"\") {\n    if (typeof cents === \"string\") {\n      cents = cents.replace(\".\", \"\");\n    }\n    const placeholderRegex = /\\{\\{\\s*(\\w+)\\s*\\}\\}/, formatString = format || window.themeVariables.settings.moneyFormat;\n    function defaultTo(value2, defaultValue) {\n      return value2 == null || value2 !== value2 ? defaultValue : value2;\n    }\n    function formatWithDelimiters(number, precision, thousands, decimal) {\n      precision = defaultTo(precision, 2);\n      thousands = defaultTo(thousands, \",\");\n      decimal = defaultTo(decimal, \".\");\n      if (isNaN(number) || number == null) {\n        return 0;\n      }\n      number = (number / 100).toFixed(precision);\n      let parts = number.split(\".\"), dollarsAmount = parts[0].replace(/(\\d)(?=(\\d\\d\\d)+(?!\\d))/g, \"$1\" + thousands), centsAmount = parts[1] ? decimal + parts[1] : \"\";\n      return dollarsAmount + centsAmount;\n    }\n    let value = \"\";\n    switch (formatString.match(placeholderRegex)[1]) {\n      case \"amount\":\n        value = formatWithDelimiters(cents, 2);\n        break;\n      case \"amount_no_decimals\":\n        value = formatWithDelimiters(cents, 0);\n        break;\n      case \"amount_with_space_separator\":\n        value = formatWithDelimiters(cents, 2, \" \", \".\");\n        break;\n      case \"amount_with_comma_separator\":\n        value = formatWithDelimiters(cents, 2, \".\", \",\");\n        break;\n      case \"amount_with_apostrophe_separator\":\n        value = formatWithDelimiters(cents, 2, \"'\", \".\");\n        break;\n      case \"amount_no_decimals_with_comma_separator\":\n        value = formatWithDelimiters(cents, 0, \".\", \",\");\n        break;\n      case \"amount_no_decimals_with_space_separator\":\n        value = formatWithDelimiters(cents, 0, \" \");\n        break;\n      case \"amount_no_decimals_with_apostrophe_separator\":\n        value = formatWithDelimiters(cents, 0, \"'\");\n        break;\n    }\n    if (formatString.indexOf(\"with_comma_separator\") !== -1) {\n      return formatString.replace(placeholderRegex, value);\n    } else {\n      return formatString.replace(placeholderRegex, value);\n    }\n  }\n\n  // js/custom-element/custom-html-element.js\n  var CustomHTMLElement = class extends HTMLElement {\n    constructor() {\n      super();\n      this._hasSectionReloaded = false;\n      if (Shopify.designMode) {\n        this.rootDelegate.on(\"shopify:section:select\", (event) => {\n          const parentSection = this.closest(\".shopify-section\");\n          if (event.target === parentSection && event.detail.load) {\n            this._hasSectionReloaded = true;\n          }\n        });\n      }\n    }\n    get rootDelegate() {\n      return this._rootDelegate = this._rootDelegate || new main_default(document.documentElement);\n    }\n    get delegate() {\n      return this._delegate = this._delegate || new main_default(this);\n    }\n    showLoadingBar() {\n      triggerEvent(document.documentElement, \"theme:loading:start\");\n    }\n    hideLoadingBar() {\n      triggerEvent(document.documentElement, \"theme:loading:end\");\n    }\n    untilVisible(intersectionObserverOptions = { rootMargin: \"30px 0px\", threshold: 0 }) {\n      const onBecameVisible = () => {\n        this.classList.add(\"became-visible\");\n        this.style.opacity = \"1\";\n      };\n      return new Promise((resolve) => {\n        if (window.IntersectionObserver) {\n          this.intersectionObserver = new IntersectionObserver((event) => {\n            if (event[0].isIntersecting) {\n              this.intersectionObserver.disconnect();\n              requestAnimationFrame(() => {\n                resolve();\n                onBecameVisible();\n              });\n            }\n          }, intersectionObserverOptions);\n          this.intersectionObserver.observe(this);\n        } else {\n          resolve();\n          onBecameVisible();\n        }\n      });\n    }\n    disconnectedCallback() {\n      var _a;\n      this.delegate.destroy();\n      this.rootDelegate.destroy();\n      (_a = this.intersectionObserver) == null ? void 0 : _a.disconnect();\n      delete this._delegate;\n      delete this._rootDelegate;\n    }\n  };\n\n  // node_modules/tabbable/dist/index.esm.js\n  var candidateSelectors = [\"input\", \"select\", \"textarea\", \"a[href]\", \"button\", \"[tabindex]\", \"audio[controls]\", \"video[controls]\", '[contenteditable]:not([contenteditable=\"false\"])', \"details>summary:first-of-type\", \"details\"];\n  var candidateSelector = /* @__PURE__ */ candidateSelectors.join(\",\");\n  var matches = typeof Element === \"undefined\" ? function () {\n  } : Element.prototype.matches || Element.prototype.msMatchesSelector || Element.prototype.webkitMatchesSelector;\n  var getCandidates = function getCandidates2(el, includeContainer, filter) {\n    var candidates = Array.prototype.slice.apply(el.querySelectorAll(candidateSelector));\n    if (includeContainer && matches.call(el, candidateSelector)) {\n      candidates.unshift(el);\n    }\n    candidates = candidates.filter(filter);\n    return candidates;\n  };\n  var isContentEditable = function isContentEditable2(node) {\n    return node.contentEditable === \"true\";\n  };\n  var getTabindex = function getTabindex2(node) {\n    var tabindexAttr = parseInt(node.getAttribute(\"tabindex\"), 10);\n    if (!isNaN(tabindexAttr)) {\n      return tabindexAttr;\n    }\n    if (isContentEditable(node)) {\n      return 0;\n    }\n    if ((node.nodeName === \"AUDIO\" || node.nodeName === \"VIDEO\" || node.nodeName === \"DETAILS\") && node.getAttribute(\"tabindex\") === null) {\n      return 0;\n    }\n    return node.tabIndex;\n  };\n  var sortOrderedTabbables = function sortOrderedTabbables2(a, b) {\n    return a.tabIndex === b.tabIndex ? a.documentOrder - b.documentOrder : a.tabIndex - b.tabIndex;\n  };\n  var isInput = function isInput2(node) {\n    return node.tagName === \"INPUT\";\n  };\n  var isHiddenInput = function isHiddenInput2(node) {\n    return isInput(node) && node.type === \"hidden\";\n  };\n  var isDetailsWithSummary = function isDetailsWithSummary2(node) {\n    var r = node.tagName === \"DETAILS\" && Array.prototype.slice.apply(node.children).some(function (child) {\n      return child.tagName === \"SUMMARY\";\n    });\n    return r;\n  };\n  var getCheckedRadio = function getCheckedRadio2(nodes, form) {\n    for (var i = 0; i < nodes.length; i++) {\n      if (nodes[i].checked && nodes[i].form === form) {\n        return nodes[i];\n      }\n    }\n  };\n  var isTabbableRadio = function isTabbableRadio2(node) {\n    if (!node.name) {\n      return true;\n    }\n    var radioScope = node.form || node.ownerDocument;\n    var queryRadios = function queryRadios2(name) {\n      return radioScope.querySelectorAll('input[type=\"radio\"][name=\"' + name + '\"]');\n    };\n    var radioSet;\n    if (typeof window !== \"undefined\" && typeof window.CSS !== \"undefined\" && typeof window.CSS.escape === \"function\") {\n      radioSet = queryRadios(window.CSS.escape(node.name));\n    } else {\n      try {\n        radioSet = queryRadios(node.name);\n      } catch (err) {\n        console.error(\"Looks like you have a radio button with a name attribute containing invalid CSS selector characters and need the CSS.escape polyfill: %s\", err.message);\n        return false;\n      }\n    }\n    var checked = getCheckedRadio(radioSet, node.form);\n    return !checked || checked === node;\n  };\n  var isRadio = function isRadio2(node) {\n    return isInput(node) && node.type === \"radio\";\n  };\n  var isNonTabbableRadio = function isNonTabbableRadio2(node) {\n    return isRadio(node) && !isTabbableRadio(node);\n  };\n  var isHidden = function isHidden2(node, displayCheck) {\n    if (getComputedStyle(node).visibility === \"hidden\") {\n      return true;\n    }\n    var isDirectSummary = matches.call(node, \"details>summary:first-of-type\");\n    var nodeUnderDetails = isDirectSummary ? node.parentElement : node;\n    if (matches.call(nodeUnderDetails, \"details:not([open]) *\")) {\n      return true;\n    }\n    if (!displayCheck || displayCheck === \"full\") {\n      while (node) {\n        if (getComputedStyle(node).display === \"none\") {\n          return true;\n        }\n        node = node.parentElement;\n      }\n    } else if (displayCheck === \"non-zero-area\") {\n      var _node$getBoundingClie = node.getBoundingClientRect(), width = _node$getBoundingClie.width, height = _node$getBoundingClie.height;\n      return width === 0 && height === 0;\n    }\n    return false;\n  };\n  var isDisabledFromFieldset = function isDisabledFromFieldset2(node) {\n    if (isInput(node) || node.tagName === \"SELECT\" || node.tagName === \"TEXTAREA\" || node.tagName === \"BUTTON\") {\n      var parentNode = node.parentElement;\n      while (parentNode) {\n        if (parentNode.tagName === \"FIELDSET\" && parentNode.disabled) {\n          for (var i = 0; i < parentNode.children.length; i++) {\n            var child = parentNode.children.item(i);\n            if (child.tagName === \"LEGEND\") {\n              if (child.contains(node)) {\n                return false;\n              }\n              return true;\n            }\n          }\n          return true;\n        }\n        parentNode = parentNode.parentElement;\n      }\n    }\n    return false;\n  };\n  var isNodeMatchingSelectorFocusable = function isNodeMatchingSelectorFocusable2(options, node) {\n    if (node.disabled || isHiddenInput(node) || isHidden(node, options.displayCheck) || isDetailsWithSummary(node) || isDisabledFromFieldset(node)) {\n      return false;\n    }\n    return true;\n  };\n  var isNodeMatchingSelectorTabbable = function isNodeMatchingSelectorTabbable2(options, node) {\n    if (!isNodeMatchingSelectorFocusable(options, node) || isNonTabbableRadio(node) || getTabindex(node) < 0) {\n      return false;\n    }\n    return true;\n  };\n  var tabbable = function tabbable2(el, options) {\n    options = options || {};\n    var regularTabbables = [];\n    var orderedTabbables = [];\n    var candidates = getCandidates(el, options.includeContainer, isNodeMatchingSelectorTabbable.bind(null, options));\n    candidates.forEach(function (candidate, i) {\n      var candidateTabindex = getTabindex(candidate);\n      if (candidateTabindex === 0) {\n        regularTabbables.push(candidate);\n      } else {\n        orderedTabbables.push({\n          documentOrder: i,\n          tabIndex: candidateTabindex,\n          node: candidate\n        });\n      }\n    });\n    var tabbableNodes = orderedTabbables.sort(sortOrderedTabbables).map(function (a) {\n      return a.node;\n    }).concat(regularTabbables);\n    return tabbableNodes;\n  };\n  var focusableCandidateSelector = /* @__PURE__ */ candidateSelectors.concat(\"iframe\").join(\",\");\n  var isFocusable = function isFocusable2(node, options) {\n    options = options || {};\n    if (!node) {\n      throw new Error(\"No node provided\");\n    }\n    if (matches.call(node, focusableCandidateSelector) === false) {\n      return false;\n    }\n    return isNodeMatchingSelectorFocusable(options, node);\n  };\n\n  // node_modules/focus-trap/dist/focus-trap.esm.js\n  function ownKeys(object, enumerableOnly) {\n    var keys = Object.keys(object);\n    if (Object.getOwnPropertySymbols) {\n      var symbols = Object.getOwnPropertySymbols(object);\n      if (enumerableOnly) {\n        symbols = symbols.filter(function (sym) {\n          return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n        });\n      }\n      keys.push.apply(keys, symbols);\n    }\n    return keys;\n  }\n  function _objectSpread2(target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i] != null ? arguments[i] : {};\n      if (i % 2) {\n        ownKeys(Object(source), true).forEach(function (key) {\n          _defineProperty(target, key, source[key]);\n        });\n      } else if (Object.getOwnPropertyDescriptors) {\n        Object.defineProperties(target, Object.getOwnPropertyDescriptors(source));\n      } else {\n        ownKeys(Object(source)).forEach(function (key) {\n          Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n        });\n      }\n    }\n    return target;\n  }\n  function _defineProperty(obj, key, value) {\n    if (key in obj) {\n      Object.defineProperty(obj, key, {\n        value,\n        enumerable: true,\n        configurable: true,\n        writable: true\n      });\n    } else {\n      obj[key] = value;\n    }\n    return obj;\n  }\n  var activeFocusTraps = function () {\n    var trapQueue = [];\n    return {\n      activateTrap: function activateTrap(trap) {\n        if (trapQueue.length > 0) {\n          var activeTrap = trapQueue[trapQueue.length - 1];\n          if (activeTrap !== trap) {\n            activeTrap.pause();\n          }\n        }\n        var trapIndex = trapQueue.indexOf(trap);\n        if (trapIndex === -1) {\n          trapQueue.push(trap);\n        } else {\n          trapQueue.splice(trapIndex, 1);\n          trapQueue.push(trap);\n        }\n      },\n      deactivateTrap: function deactivateTrap(trap) {\n        var trapIndex = trapQueue.indexOf(trap);\n        if (trapIndex !== -1) {\n          trapQueue.splice(trapIndex, 1);\n        }\n        if (trapQueue.length > 0) {\n          trapQueue[trapQueue.length - 1].unpause();\n        }\n      }\n    };\n  }();\n  var isSelectableInput = function isSelectableInput2(node) {\n    return node.tagName && node.tagName.toLowerCase() === \"input\" && typeof node.select === \"function\";\n  };\n  var isEscapeEvent = function isEscapeEvent2(e) {\n    return e.key === \"Escape\" || e.key === \"Esc\" || e.keyCode === 27;\n  };\n  var isTabEvent = function isTabEvent2(e) {\n    return e.key === \"Tab\" || e.keyCode === 9;\n  };\n  var delay = function delay2(fn) {\n    return setTimeout(fn, 0);\n  };\n  var findIndex = function findIndex2(arr, fn) {\n    var idx = -1;\n    arr.every(function (value, i) {\n      if (fn(value)) {\n        idx = i;\n        return false;\n      }\n      return true;\n    });\n    return idx;\n  };\n  var valueOrHandler = function valueOrHandler2(value) {\n    for (var _len = arguments.length, params = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n      params[_key - 1] = arguments[_key];\n    }\n    return typeof value === \"function\" ? value.apply(void 0, params) : value;\n  };\n  var getActualTarget = function getActualTarget2(event) {\n    return event.target.shadowRoot && typeof event.composedPath === \"function\" ? event.composedPath()[0] : event.target;\n  };\n  var createFocusTrap = function createFocusTrap2(elements, userOptions) {\n    var doc = (userOptions === null || userOptions === void 0 ? void 0 : userOptions.document) || document;\n    var config = _objectSpread2({\n      returnFocusOnDeactivate: true,\n      escapeDeactivates: true,\n      delayInitialFocus: true\n    }, userOptions);\n    var state = {\n      containers: [],\n      tabbableGroups: [],\n      nodeFocusedBeforeActivation: null,\n      mostRecentlyFocusedNode: null,\n      active: false,\n      paused: false,\n      delayInitialFocusTimer: void 0\n    };\n    var trap;\n    var getOption = function getOption2(configOverrideOptions, optionName, configOptionName) {\n      return configOverrideOptions && configOverrideOptions[optionName] !== void 0 ? configOverrideOptions[optionName] : config[configOptionName || optionName];\n    };\n    var containersContain = function containersContain2(element) {\n      return !!(element && state.containers.some(function (container) {\n        return container.contains(element);\n      }));\n    };\n    var getNodeForOption = function getNodeForOption2(optionName) {\n      var optionValue = config[optionName];\n      if (typeof optionValue === \"function\") {\n        for (var _len2 = arguments.length, params = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {\n          params[_key2 - 1] = arguments[_key2];\n        }\n        optionValue = optionValue.apply(void 0, params);\n      }\n      if (!optionValue) {\n        if (optionValue === void 0 || optionValue === false) {\n          return optionValue;\n        }\n        throw new Error(\"`\".concat(optionName, \"` was specified but was not a node, or did not return a node\"));\n      }\n      var node = optionValue;\n      if (typeof optionValue === \"string\") {\n        node = doc.querySelector(optionValue);\n        if (!node) {\n          throw new Error(\"`\".concat(optionName, \"` as selector refers to no known node\"));\n        }\n      }\n      return node;\n    };\n    var getInitialFocusNode = function getInitialFocusNode2() {\n      var node = getNodeForOption(\"initialFocus\");\n      if (node === false) {\n        return false;\n      }\n      if (node === void 0) {\n        if (containersContain(doc.activeElement)) {\n          node = doc.activeElement;\n        } else {\n          var firstTabbableGroup = state.tabbableGroups[0];\n          var firstTabbableNode = firstTabbableGroup && firstTabbableGroup.firstTabbableNode;\n          node = firstTabbableNode || getNodeForOption(\"fallbackFocus\");\n        }\n      }\n      if (!node) {\n        throw new Error(\"Your focus-trap needs to have at least one focusable element\");\n      }\n      return node;\n    };\n    var updateTabbableNodes = function updateTabbableNodes2() {\n      state.tabbableGroups = state.containers.map(function (container) {\n        var tabbableNodes = tabbable(container);\n        if (tabbableNodes.length > 0) {\n          return {\n            container,\n            firstTabbableNode: tabbableNodes[0],\n            lastTabbableNode: tabbableNodes[tabbableNodes.length - 1]\n          };\n        }\n        return void 0;\n      }).filter(function (group) {\n        return !!group;\n      });\n      if (state.tabbableGroups.length <= 0 && !getNodeForOption(\"fallbackFocus\")) {\n        throw new Error(\"Your focus-trap must have at least one container with at least one tabbable node in it at all times\");\n      }\n    };\n    var tryFocus = function tryFocus2(node) {\n      if (node === false) {\n        return;\n      }\n      if (node === doc.activeElement) {\n        return;\n      }\n      if (!node || !node.focus) {\n        tryFocus2(getInitialFocusNode());\n        return;\n      }\n      node.focus({\n        preventScroll: !!config.preventScroll\n      });\n      state.mostRecentlyFocusedNode = node;\n      if (isSelectableInput(node)) {\n        node.select();\n      }\n    };\n    var getReturnFocusNode = function getReturnFocusNode2(previousActiveElement) {\n      var node = getNodeForOption(\"setReturnFocus\", previousActiveElement);\n      return node ? node : node === false ? false : previousActiveElement;\n    };\n    var checkPointerDown = function checkPointerDown2(e) {\n      var target = getActualTarget(e);\n      if (containersContain(target)) {\n        return;\n      }\n      if (valueOrHandler(config.clickOutsideDeactivates, e)) {\n        trap.deactivate({\n          returnFocus: config.returnFocusOnDeactivate && !isFocusable(target)\n        });\n        return;\n      }\n      if (valueOrHandler(config.allowOutsideClick, e)) {\n        return;\n      }\n      e.preventDefault();\n    };\n    var checkFocusIn = function checkFocusIn2(e) {\n      var target = getActualTarget(e);\n      var targetContained = containersContain(target);\n      if (targetContained || target instanceof Document) {\n        if (targetContained) {\n          state.mostRecentlyFocusedNode = target;\n        }\n      } else {\n        e.stopImmediatePropagation();\n        tryFocus(state.mostRecentlyFocusedNode || getInitialFocusNode());\n      }\n    };\n    var checkTab = function checkTab2(e) {\n      var target = getActualTarget(e);\n      updateTabbableNodes();\n      var destinationNode = null;\n      if (state.tabbableGroups.length > 0) {\n        var containerIndex = findIndex(state.tabbableGroups, function (_ref) {\n          var container = _ref.container;\n          return container.contains(target);\n        });\n        if (containerIndex < 0) {\n          if (e.shiftKey) {\n            destinationNode = state.tabbableGroups[state.tabbableGroups.length - 1].lastTabbableNode;\n          } else {\n            destinationNode = state.tabbableGroups[0].firstTabbableNode;\n          }\n        } else if (e.shiftKey) {\n          var startOfGroupIndex = findIndex(state.tabbableGroups, function (_ref2) {\n            var firstTabbableNode = _ref2.firstTabbableNode;\n            return target === firstTabbableNode;\n          });\n          if (startOfGroupIndex < 0 && state.tabbableGroups[containerIndex].container === target) {\n            startOfGroupIndex = containerIndex;\n          }\n          if (startOfGroupIndex >= 0) {\n            var destinationGroupIndex = startOfGroupIndex === 0 ? state.tabbableGroups.length - 1 : startOfGroupIndex - 1;\n            var destinationGroup = state.tabbableGroups[destinationGroupIndex];\n            destinationNode = destinationGroup.lastTabbableNode;\n          }\n        } else {\n          var lastOfGroupIndex = findIndex(state.tabbableGroups, function (_ref3) {\n            var lastTabbableNode = _ref3.lastTabbableNode;\n            return target === lastTabbableNode;\n          });\n          if (lastOfGroupIndex < 0 && state.tabbableGroups[containerIndex].container === target) {\n            lastOfGroupIndex = containerIndex;\n          }\n          if (lastOfGroupIndex >= 0) {\n            var _destinationGroupIndex = lastOfGroupIndex === state.tabbableGroups.length - 1 ? 0 : lastOfGroupIndex + 1;\n            var _destinationGroup = state.tabbableGroups[_destinationGroupIndex];\n            destinationNode = _destinationGroup.firstTabbableNode;\n          }\n        }\n      } else {\n        destinationNode = getNodeForOption(\"fallbackFocus\");\n      }\n      if (destinationNode) {\n        e.preventDefault();\n        tryFocus(destinationNode);\n      }\n    };\n    var checkKey = function checkKey2(e) {\n      if (isEscapeEvent(e) && valueOrHandler(config.escapeDeactivates, e) !== false) {\n        e.preventDefault();\n        trap.deactivate();\n        return;\n      }\n      if (isTabEvent(e)) {\n        checkTab(e);\n        return;\n      }\n    };\n    var checkClick = function checkClick2(e) {\n      if (valueOrHandler(config.clickOutsideDeactivates, e)) {\n        return;\n      }\n      var target = getActualTarget(e);\n      if (containersContain(target)) {\n        return;\n      }\n      if (valueOrHandler(config.allowOutsideClick, e)) {\n        return;\n      }\n      e.preventDefault();\n      e.stopImmediatePropagation();\n    };\n    var addListeners = function addListeners2() {\n      if (!state.active) {\n        return;\n      }\n      activeFocusTraps.activateTrap(trap);\n      state.delayInitialFocusTimer = config.delayInitialFocus ? delay(function () {\n        tryFocus(getInitialFocusNode());\n      }) : tryFocus(getInitialFocusNode());\n      doc.addEventListener(\"focusin\", checkFocusIn, true);\n      doc.addEventListener(\"mousedown\", checkPointerDown, {\n        capture: true,\n        passive: false\n      });\n      doc.addEventListener(\"touchstart\", checkPointerDown, {\n        capture: true,\n        passive: false\n      });\n      doc.addEventListener(\"click\", checkClick, {\n        capture: true,\n        passive: false\n      });\n      doc.addEventListener(\"keydown\", checkKey, {\n        capture: true,\n        passive: false\n      });\n      return trap;\n    };\n    var removeListeners = function removeListeners2() {\n      if (!state.active) {\n        return;\n      }\n      doc.removeEventListener(\"focusin\", checkFocusIn, true);\n      doc.removeEventListener(\"mousedown\", checkPointerDown, true);\n      doc.removeEventListener(\"touchstart\", checkPointerDown, true);\n      doc.removeEventListener(\"click\", checkClick, true);\n      doc.removeEventListener(\"keydown\", checkKey, true);\n      return trap;\n    };\n    trap = {\n      activate: function activate(activateOptions) {\n        if (state.active) {\n          return this;\n        }\n        var onActivate = getOption(activateOptions, \"onActivate\");\n        var onPostActivate = getOption(activateOptions, \"onPostActivate\");\n        var checkCanFocusTrap = getOption(activateOptions, \"checkCanFocusTrap\");\n        if (!checkCanFocusTrap) {\n          updateTabbableNodes();\n        }\n        state.active = true;\n        state.paused = false;\n        state.nodeFocusedBeforeActivation = doc.activeElement;\n        if (onActivate) {\n          onActivate();\n        }\n        var finishActivation = function finishActivation2() {\n          if (checkCanFocusTrap) {\n            updateTabbableNodes();\n          }\n          addListeners();\n          if (onPostActivate) {\n            onPostActivate();\n          }\n        };\n        if (checkCanFocusTrap) {\n          checkCanFocusTrap(state.containers.concat()).then(finishActivation, finishActivation);\n          return this;\n        }\n        finishActivation();\n        return this;\n      },\n      deactivate: function deactivate(deactivateOptions) {\n        if (!state.active) {\n          return this;\n        }\n        clearTimeout(state.delayInitialFocusTimer);\n        state.delayInitialFocusTimer = void 0;\n        removeListeners();\n        state.active = false;\n        state.paused = false;\n        activeFocusTraps.deactivateTrap(trap);\n        var onDeactivate = getOption(deactivateOptions, \"onDeactivate\");\n        var onPostDeactivate = getOption(deactivateOptions, \"onPostDeactivate\");\n        var checkCanReturnFocus = getOption(deactivateOptions, \"checkCanReturnFocus\");\n        if (onDeactivate) {\n          onDeactivate();\n        }\n        var returnFocus = getOption(deactivateOptions, \"returnFocus\", \"returnFocusOnDeactivate\");\n        var finishDeactivation = function finishDeactivation2() {\n          delay(function () {\n            if (returnFocus) {\n              tryFocus(getReturnFocusNode(state.nodeFocusedBeforeActivation));\n            }\n            if (onPostDeactivate) {\n              onPostDeactivate();\n            }\n          });\n        };\n        if (returnFocus && checkCanReturnFocus) {\n          checkCanReturnFocus(getReturnFocusNode(state.nodeFocusedBeforeActivation)).then(finishDeactivation, finishDeactivation);\n          return this;\n        }\n        finishDeactivation();\n        return this;\n      },\n      pause: function pause() {\n        if (state.paused || !state.active) {\n          return this;\n        }\n        state.paused = true;\n        removeListeners();\n        return this;\n      },\n      unpause: function unpause() {\n        if (!state.paused || !state.active) {\n          return this;\n        }\n        state.paused = false;\n        updateTabbableNodes();\n        addListeners();\n        return this;\n      },\n      updateContainerElements: function updateContainerElements(containerElements) {\n        var elementsAsArray = [].concat(containerElements).filter(Boolean);\n        state.containers = elementsAsArray.map(function (element) {\n          return typeof element === \"string\" ? doc.querySelector(element) : element;\n        });\n        if (state.active) {\n          updateTabbableNodes();\n        }\n        return this;\n      }\n    };\n    trap.updateContainerElements(elements);\n    return trap;\n  };\n\n  // js/helper/animation.js\n  var CustomAnimation = class {\n    constructor(effect) {\n      this._effect = effect;\n      this._playState = \"idle\";\n      this._finished = Promise.resolve();\n    }\n    get finished() {\n      return this._finished;\n    }\n    get animationEffects() {\n      return this._effect instanceof CustomKeyframeEffect ? [this._effect] : this._effect.animationEffects;\n    }\n    cancel() {\n      this.animationEffects.forEach((animationEffect) => animationEffect.cancel());\n    }\n    finish() {\n      this.animationEffects.forEach((animationEffect) => animationEffect.finish());\n    }\n    play() {\n      this._playState = \"running\";\n      this._effect.play();\n      this._finished = this._effect.finished;\n      this._finished.then(() => {\n        this._playState = \"finished\";\n      }, (rejection) => {\n        this._playState = \"idle\";\n      });\n    }\n  };\n  var CustomKeyframeEffect = class {\n    constructor(target, keyframes, options = {}) {\n      if (!target) {\n        return;\n      }\n      if (\"Animation\" in window) {\n        this._animation = new Animation(new KeyframeEffect(target, keyframes, options));\n      } else {\n        options[\"fill\"] = \"forwards\";\n        this._animation = target.animate(keyframes, options);\n        this._animation.pause();\n      }\n      this._animation.addEventListener(\"finish\", () => {\n        target.style.opacity = keyframes.hasOwnProperty(\"opacity\") ? keyframes[\"opacity\"][keyframes[\"opacity\"].length - 1] : null;\n        target.style.visibility = keyframes.hasOwnProperty(\"visibility\") ? keyframes[\"visibility\"][keyframes[\"visibility\"].length - 1] : null;\n      });\n    }\n    get finished() {\n      if (!this._animation) {\n        return Promise.resolve();\n      }\n      return this._animation.finished ? this._animation.finished : new Promise((resolve) => this._animation.onfinish = resolve);\n    }\n    play() {\n      if (this._animation) {\n        this._animation.startTime = null;\n        this._animation.play();\n      }\n    }\n    cancel() {\n      if (this._animation) {\n        this._animation.cancel();\n      }\n    }\n    finish() {\n      if (this._animation) {\n        this._animation.finish();\n      }\n    }\n  };\n  var GroupEffect = class {\n    constructor(childrenEffects) {\n      this._childrenEffects = childrenEffects;\n      this._finished = Promise.resolve();\n    }\n    get finished() {\n      return this._finished;\n    }\n    get animationEffects() {\n      return this._childrenEffects.flatMap((effect) => {\n        return effect instanceof CustomKeyframeEffect ? effect : effect.animationEffects;\n      });\n    }\n  };\n  var ParallelEffect = class extends GroupEffect {\n    play() {\n      const promises = [];\n      for (const effect of this._childrenEffects) {\n        effect.play();\n        promises.push(effect.finished);\n      }\n      this._finished = Promise.all(promises);\n    }\n  };\n  var SequenceEffect = class extends GroupEffect {\n    play() {\n      this._finished = new Promise(async (resolve, reject) => {\n        try {\n          for (const effect of this._childrenEffects) {\n            effect.play();\n            await effect.finished;\n          }\n          resolve();\n        } catch (exception) {\n          reject();\n        }\n      });\n    }\n  };\n\n  var RevealingForm = class extends CustomHTMLElement {\n\n    constructor() {\n      super();\n    }\n\n    connectedCallback() {\n\n      this.inputs = Array.from(this.querySelectorAll(\"revealing-form-input\"));\n      this.actions = this.querySelector(\"revealing-form-actions\");\n      this.submit = this.actions.querySelector('button');\n\n      this._setupBehaviour();\n\n      this.updateSubmit();\n\n    }\n\n    updateSubmit() {\n      if (this.valid == true) {\n        this.submit.disabled = false;\n      }\n      else {\n        this.submit.disabled = true;\n      }\n    }\n\n    get valid() {\n\n      let valid = false;\n\n      for (let i = 0; i < this.inputs.length; i++) {\n        const input = this.inputs[i];\n        if (input.valid == false) {\n          return false;\n        }\n      }\n\n      return true;\n\n    }\n\n    _setupBehaviour() {\n\n    }\n\n  }\n\n  var Templates = {\n\n    quizResultsProduct(options) {\n\n      if (!options || !options.product) {\n        return;\n      }\n      \n      let html = window.quizVariables.results.results_product;\n      \n      const index = options.index;\n\n      const product = options.product;\n      const product_id = product.id.replace(\"gid://shopify/Product/\", \"\")\n      const product_title = product.title;\n      const product_description = (!product.shortDescription) ? \"\" : product.shortDescription.value;\n      const product_color = (!product.productColor) ? \"var(---color--highlight)\" : product.productColor.value;\n      const product_url = product.url;\n      const product_image_url = (!product.quizResultImage) ? product.images.edges[0].node.url : product.quizResultImage.reference.image.originalSrc;\n\n      const variant = product.variants.edges[0].node;\n      const variant_first_id = variant.id.replace('gid://shopify/ProductVariant/', \"\");\n      const variant_first_price = (!variant.price) ? \"\" : Number(variant.price.amount) * 100;\n      const variant_first_compare_price = (!variant.compareAtPrice) ? \"\" : Number(variant.compareAtPrice.amount) * 100;\n      const variant_first_calories = (!variant.calories.value) ? \"\" : Number(variant.calories.value);\n\n      const variant_last = product.variants.edges[product.variants.edges.length - 1].node;\n      const variant_last_id = variant_last.id.replace('gid://shopify/ProductVariant/', \"\");\n      // const variant_last_price = (!variant.price) ? \"\" : Number(variant.price.amount) * 100;\n      // const variant_last_compare_price = (!variant.compareAtPrice) ? \"\" : Number(variant.compareAtPrice.amount) * 100;\n      // const variant_last_calories = (!variant.calories.value) ? \"\" : Number(variant.calories.value);\n\n      html = html.replaceAll(\"$INDEX\", index);\n\n      html = html.replaceAll(\"$PRODUCT_ID\", product_id);\n      html = html.replaceAll(\"$PRODUCT_SHORT_DESCRIPTION\", product_description);\n      html = html.replaceAll(\"$PRODUCT_TITLE\", product_title);\n      html = html.replaceAll(\"$PRODUCT_COLOR\", product_color);\n      html = html.replaceAll(\"$PRODUCT_URL\", product_url);\n      html = html.replaceAll(\"$PRODUCT_IMAGE_URL\", product_image_url);\n      \n      html = html.replaceAll(\"$VARIANT_ID_FIRST\", variant_first_id);\n      html = html.replaceAll(\"$VARIANT_PRICE\", variant_first_price);\n      html = html.replaceAll(\"$VARIANT_PRICE_COMPARE\", variant_first_compare_price);\n      html = html.replaceAll(\"$VARIANT_METAFIELD_CALORIES\", variant_first_calories);\n\n      html = html.replaceAll(\"$VARIANT_ID_LAST\", variant_last_id);\n\n      return html;\n\n    },\n\n    quizResultsModal(options) {\n\n      if (!options || !options.product) {\n        return;\n      }\n\n      let html = window.quizVariables.results.results_product_modal;\n\n      const product = options.product;\n      const product_id = product.id.replace(\"gid://shopify/Product/\", \"\")\n      const product_title = product.title;\n      const product_color = (!product.productColor) ? \"\" : product.productColor.value;\n      const product_description = product.descriptionHtml.indexOf('<h5>Ingredients</h5>') ? product.descriptionHtml.split('<h5>Ingredients</h5>')[0] : product.descriptionHtml; \n      const product_url = product.onlineStoreUrl;\n      const product_image_url = product.images.edges[0].node.url;\n\n      const variant = product.variants.edges[0].node;\n      const variant_id = variant.id.replace('gid://shopify/ProductVariant/', \"\");\n      const variant_price = (!variant.price) ? \"\" : Number(variant.price.amount) * 100;\n      const variant_compare_price = (!variant.compareAtPrice) ? \"\" : Number(variant.compareAtPrice.amount) * 100;\n\n      const variant_image = variant.id.replace('gid://shopify/ProductVariant/', \"\");\n\n      html = html.replaceAll(\"$PRODUCT_ID\", product_id);\n      html = html.replaceAll(\"$PRODUCT_DESCRIPTION\", product_description);\n      html = html.replaceAll(\"$PRODUCT_TITLE\", product_title);\n      html = html.replaceAll(\"$PRODUCT_COLOR\", product_color);\n      html = html.replaceAll(\"$PRODUCT_URL\", product_url);\n      html = html.replaceAll(\"$PRODUCT_IMAGE_URL\", product_image_url);\n\n      html = html.replaceAll(\"$VARIANT_ID\", variant_id);\n      html = html.replaceAll(\"$VARIANT_PRICE\", variant_price);\n      html = html.replaceAll(\"$VARIANT_PRICE_COMPARE\", variant_compare_price);\n\n      return html;\n\n    },\n\n    quizStepTab: function (options) {\n\n      if (!options) {\n        return;\n      }\n\n      let name = !options.name ? 'Dog Name' : options.name;\n      let attributes = !options.attributes ? '' : options.attributes;\n      let classes = !options.classes ? '' : options.classes;\n\n      let html =\n        `<quiz-step-tab class=\"quiz-step-tab button button--tab ${classes}\" data-name=\"${options.name}\" ${attributes}>\n          ${options.name}\n        </quiz-step-tab>`;\n\n      return html;\n\n    },\n\n    quizStepLineText: function (options) {\n\n      if (!options) {\n        return;\n      }\n\n      return `<span class=\"quiz-step-line__text\">${options.text}</span>`\n\n    },\n\n    expandingInput: function (options) {\n\n      if (!options) {\n        return;\n      }\n\n      let html = \"\";\n\n      let id = \"expanding-input\";\n      let name = \"expanding-input\";\n      let placeholder = \"Expanding Input\";\n\n      if (options) {\n\n        if (options.id) {\n          id = options.id;\n        }\n\n        if (options.name) {\n          name = options.name;\n        }\n\n        if (options.placeholder) {\n          placeholder = options.placeholder;\n        }\n\n      }\n\n      html = `\n      <expanding-input>\n        <span class=\"expanding-input__display quiz-step-line__input quiz-input\" role=\"textbox\" contenteditable data-default=\"${placeholder}\"></span>\n        <input type=\"hidden\" class=\"expanding-input__input\" name=\"${name}\" id=\"${id}\">\n      </expanding-input>\n      `\n\n      return html;\n\n    }\n\n  }\n\n  var RevealingFormInput = class extends CustomHTMLElement {\n\n    constructor() {\n      super();\n    }\n\n    connectedCallback() {\n\n      this.revealingForm = this.closest(\"revealing-form\");\n      this._setupBehaviour();\n\n    }\n    _setupBehaviour() {\n\n      this.input.addEventListener(\"input\", this.inputOnInput.bind(this));\n\n    }\n    inputOnInput(event) {\n\n      this.revealingForm.updateSubmit();\n\n      if (this.input.value == \"\") {\n        if (this.prevInput) {\n          if (this.prevInput.input.value == \"\") {\n            this.hide();\n            this.prevInput.input.focus();\n            return;\n          }\n        }\n      }\n\n      if (this.nextInput) {\n        if (this.nextInput.valid == true) {\n          return;\n        }\n      }\n\n      if (this.valid == true && this.isLastInput == false) {\n        if (this.nextInput) {\n          if (this.nextInput.visible == false) {\n            if (this.nextInput.animating == false) {\n              this.nextInput.show();\n            }\n          }\n        }\n      }\n      else {\n        if (this.nextInput) {\n          if (this.nextInputsFull == false) {\n            this.nextInput.hide();\n          }\n        }\n      }\n\n    }\n    async hide() {\n\n      this.classList.add(\"revealing-form-input--animating\");\n\n      const animation = new CustomAnimation(new CustomKeyframeEffect(this,\n        {\n          visibility: [\"visible\", \"hidden\"],\n          transform: [\"translateX(0)\", \"translateX(-20%)\"],\n          opacity: [\"1\", \"0\"]\n        },\n        {\n          duration: 300,\n          easing: \"cubic-bezier(0.23, 1, 0.32, 1)\"\n        }\n      ));\n\n      animation.play();\n\n      await animation.finished;\n\n      this.classList.remove(\"revealing-form-input--visible\");\n      this.classList.remove(\"revealing-form-input--animating\");\n\n    }\n    async show() {\n\n      this.classList.add(\"revealing-form-input--animating\");\n\n      const animation = new CustomAnimation(new CustomKeyframeEffect(this,\n        {\n          visibility: [\"hidden\", \"visible\"],\n          transform: [\"translateX(-20%)\", \"translateX(0)\"],\n          opacity: [\"0\", \"1\"]\n        },\n        {\n          duration: 300,\n          easing: \"cubic-bezier(0.23, 1, 0.32, 1)\"\n        }\n      ));\n\n      animation.play();\n\n      await animation.finished;\n\n      this.classList.add(\"revealing-form-input--visible\");\n      this.classList.remove(\"revealing-form-input--animating\");\n\n    }\n    get valid() {\n      if (this.input.value != \"\") {\n        return true;\n      }\n      return false;\n    }\n    get visible() {\n      return this.classList.contains(\"revealing-form-input--visible\");\n    }\n    get animating() {\n      return this.classList.contains(\"revealing-form-input--animating\");\n    }\n    get input() {\n      return this.querySelector(\"input\");\n    }\n    get isLastInput() {\n      if (typeof this.nextInput == \"undefined\") {\n        return true;\n      }\n      else {\n        return false;\n      }\n    }\n    get nextInputsFull() {\n      let sibling = this.nextElementSibling;\n      while (sibling) {\n        if (sibling.matches(\"revealing-form-input\")) {\n          if (sibling.input.value != \"\") {\n            return true;\n          }\n        };\n        sibling = sibling.nextElementSibling\n      }\n      return false;\n    }\n    get nextInput() {\n      let sibling = this.nextElementSibling;\n      while (sibling) {\n        if (sibling.matches(\"revealing-form-input\")) return sibling;\n        sibling = sibling.nextElementSibling\n      }\n    }\n    get prevInput() {\n      let sibling = this.previousElementSibling;\n      while (sibling) {\n        if (sibling.matches(\"revealing-form-input\")) return sibling;\n        sibling = sibling.previousElementSibling\n      }\n    }\n  }\n\n  var SplitPageStep = class extends CustomHTMLElement {\n\n    constructor() {\n      super();\n    }\n\n    connectedCallback() {\n\n      this.nextButtons = this.querySelectorAll(\"[data-split-page-next]\");\n      this.prevButtons = this.querySelectorAll(\"[data-split-page-prev]\");\n\n      this.nextButtons.forEach((button) => {\n        button.addEventListener(\"click\", this.gotoNextStep.bind(this));\n      });\n\n      this.prevButtons.forEach((button) => {\n        button.addEventListener(\"click\", this.gotoPrevStep.bind(this));\n      });\n\n    }\n\n    async gotoNextStep() {\n\n      if (!this.nextStep) {\n        return;\n      }\n\n      this.classList.add(\"split-page-step--animating\");\n      this.nextStep.classList.add(\"split-page-step--animating\");\n\n      triggerEvent(this.nextStep, \"quiz:split-page:next-step:start\");\n\n      const animation = new CustomAnimation(new CustomKeyframeEffect(this,\n        {\n          visibility: [\"visible\", \"hidden\"],\n          transform: [\"translateY(0%)\", \"translateY(-20%)\"],\n          opacity: [\"1\", \"0\"]\n        },\n        {\n          duration: 300,\n          easing: \"cubic-bezier(0.23, 1, 0.32, 1)\"\n        }\n      ));\n\n      animation.play();\n\n      await animation.finished;\n      this.classList.remove(\"split-page-step--visible\");\n      this.classList.remove(\"split-page-step--animating\");\n\n      const animation2 = new CustomAnimation(new CustomKeyframeEffect(this.nextStep,\n        {\n          visibility: [\"hidden\", \"visible\"],\n          transform: [\"translateY(20%)\", \"translateY(0%)\"],\n          opacity: [\"0\", \"1\"]\n        },\n        {\n          duration: 300,\n          easing: \"cubic-bezier(0.23, 1, 0.32, 1)\"\n        }\n      ));\n\n      animation2.play();\n\n      triggerEvent(this.nextStep, \"quiz:split-page:next-step:arrive\");\n\n      this.nextStep.classList.add(\"split-page-step--visible\");\n      this.nextStep.classList.remove(\"split-page-step--animating\");\n\n    }\n\n    async gotoPrevStep() {\n\n      if (!this.prevStep) {\n        return;\n      }\n\n      triggerEvent(this.prevStep, \"quiz:split-page:prev-step:start\");\n\n      this.classList.add(\"split-page-step--animating\");\n      this.prevStep.classList.add(\"split-page-step--animating\");\n\n      const animation = new CustomAnimation(new CustomKeyframeEffect(this,\n        {\n          visibility: [\"visible\", \"hidden\"],\n          transform: [\"translateY(0%)\", \"translateY(-20%)\"],\n          opacity: [\"1\", \"0\"]\n        },\n        {\n          duration: 300,\n          easing: \"cubic-bezier(0.23, 1, 0.32, 1)\"\n        }\n      ));\n\n      animation.play();\n\n      await animation.finished;\n      this.classList.remove(\"split-page-step--visible\");\n      this.classList.remove(\"split-page-step--animating\");\n\n      const animation2 = new CustomAnimation(new CustomKeyframeEffect(this.prevStep,\n        {\n          visibility: [\"hidden\", \"visible\"],\n          transform: [\"translateY(20%)\", \"translateY(0%)\"],\n          opacity: [\"0\", \"1\"]\n        },\n        {\n          duration: 300,\n          easing: \"cubic-bezier(0.23, 1, 0.32, 1)\"\n        }\n      ));\n\n      animation2.play();\n\n      this.prevStep.classList.add(\"split-page-step--visible\");\n      this.prevStep.classList.remove(\"split-page-step--animating\");\n\n    }\n\n    get nextStep() {\n      let sibling = this.nextElementSibling;\n      while (sibling) {\n        if (sibling.matches(\"split-page-step\")) return sibling;\n        sibling = sibling.nextElementSibling\n      }\n    }\n\n    get prevStep() {\n      let sibling = this.previousElementSibling;\n      while (sibling) {\n        if (sibling.matches(\"split-page-step\")) return sibling;\n        sibling = sibling.previousElementSibling\n      }\n    }\n\n  }\n\n  var QuizSteps = class extends CustomHTMLElement {\n    static get observedAttributes() {\n      return [\"open\"];\n    }\n    constructor() {\n      super();\n      if (Shopify.designMode) {\n        // this.rootDelegate.on(\"shopify:section:select\", (event) => filterShopifyEvent(event, this, () => this.open = true));\n        // this.rootDelegate.on(\"shopify:section:deselect\", (event) => filterShopifyEvent(event, this, () => this.open = false));\n      }\n    }\n    connectedCallback() {\n      // this.delegate.on(\"click\", \".openable__overlay\", () => this.open = false);\n\n      this.dogNames = [];\n\n      this.buttonBuildQuiz = this.querySelector(\"[data-quiz-button-populate-dogs]\");\n      \n      this.substepContainer = this.querySelector(\".quiz-step--dogs-substeps\");\n\n      this.delegate.on(\"click\", \"[data-quiz-button-dog-complete]\", this._dogComplete.bind(this));\n      this.delegate.on(\"click\", \"[data-quiz-button-next]\", this.next.bind(this));\n      this.delegate.on(\"click\", \"[data-quiz-button-prev]\", this.prev.bind(this));\n      this.delegate.on(\"click\", \"[data-quiz-button-back]\", this.back.bind(this));\n      this.delegate.on(\"click\", \"[data-quiz-button-skip]\", this.skipToResults.bind(this));\n      \n      this.delegate.on(\"click\", \"[data-quiz-button-update-dog]\", this._onClickUpdateDog.bind(this));\n      this.delegate.on(\"change\", \"[data-dog-sex]\", this._onChangeDogSex.bind(this));\n      \n      // Steps\n      this.addEventListener(\"quiz:quiz-steps:next-step:start\", this._onQuizNextStep.bind(this));\n      this.addEventListener(\"quiz:quiz-steps:next-step:transitioning\", this._updateProgressBar.bind(this));\n      this.addEventListener(\"quiz:quiz-steps:next-step:end\", this._onQuizNextStepEnd.bind(this));\n\n      this.addEventListener(\"quiz:quiz-steps:prev-step:start\", this._onQuizPrevStep.bind(this));\n      this.addEventListener(\"quiz:quiz-steps:prev-step:transitioning\", this._updateProgressBar.bind(this));\n      this.addEventListener(\"quiz:quiz-steps:prev-step:end\", this._onQuizPrevStepEnd.bind(this));\n\n      // Sub Navigation\n      if (this.subNavigation) {\n        // this.delegate.on(\"click\", \"quiz-step-tab\", this.onClickSubstepTab.bind(this));\n        this.subNavigation.addEventListener(\"quiz:quiz-steps:sub-step:start\", this._onQuizSubstepStart.bind(this));\n        this.subNavigation.addEventListener(\"quiz:quiz-steps:sub-step:arrive\", this._onQuizSubstepArrive.bind(this));\n        this.delegate.on(\"click\", \"quiz-step-tab\", this._onClickSubstepTab.bind(this));\n        // this.subNavigation.addEventListener(\"quiz:quiz-steps:next-substep\", this._updateProgressBar.bind(this));\n        // this.subNavigation.addEventListener(\"quiz:quiz-steps:prev-substep\", this._updateProgressBar.bind(this));\n        this.subNavigation.addEventListener(\"quiz:quiz-steps:sub-step:step-updated\", this._updateProgressBar.bind(this));\n      }\n      \n      if (window.quizVariables.customer) {\n        \n        // Logged In\n\n      } else {\n\n        // Not Logged In\n        \n        if (this.querySelector(\"#request-email-form\")) {\n          this.querySelector(\"#request-email-form\").addEventListener(\"submit\", this._onGuestEmailFormSubmit.bind(this)); \n        }\n\n        if (this.querySelector(\"[data-quiz-request-email-skip]\")) {\n          this.querySelector(\"[data-quiz-request-email-skip]\").addEventListener(\"click\", this._onGuestEmailFormSkip.bind(this));\n        }\n\n      }\n      \n      this.addEventListener(\"quiz:quiz-steps:submit-quiz\", this._submitQuiz.bind(this));\n\n      if (this.buttonBuildQuiz) {\n        this.buttonBuildQuiz.addEventListener(\"click\", this._buildQuiz.bind(this));\n      }\n\n      this._setupNavigation();\n\n    }\n\n    _onGuestEmailFormSkip(event) {\n\n      event.preventDefault();\n      \n      triggerEvent(this, \"quiz:quiz-steps:submit-quiz\");\n\n    }\n\n    _onGuestEmailFormSubmit(event) {\n      \n      event.preventDefault();\n\n      triggerEvent(this, \"quiz:quiz-steps:submit-quiz\");\n\n    }\n\n    _onQuizPrevStep(event) {\n      this.hideSubNavigation();\n    }\n\n    _onQuizNextStep(event) {\n    }\n\n    _onQuizPrevStepEnd(event) {\n      this.hideSubNavigation();\n      this._updateNavigation();\n    }\n\n    _onQuizNextStepEnd(event) {\n      this._updateNavigation();\n    }\n\n    /*----- Building Quiz ----- */\n\n    _buildQuiz(event) {\n\n      this._storeDogNames();\n\n      this._buildSubteps();\n      this._buildSubNavigation();\n\n      this.showSubNavigation();\n\n    }\n\n    _storeDogNames() {\n\n      const dogNamesContainer = this.querySelector(\"#dog-names\");\n      const dogNames = dogNamesContainer.querySelectorAll(\"input\");\n\n      this.dogNames = [];\n\n      dogNames.forEach(e => {\n        this.dogNames.push(e.value);\n      });\n\n    };\n\n    _buildSubteps() {\n\n      /* ----- Prepare the steps HTML ----- */\n\n      let html = \"\";\n\n      let container = document.createElement('quiz-step-dog-details');\n      container.classList.add(\"quiz-step__inner\");\n\n      for (let i = 0; i < this.dogNames.length; i++) {\n\n        const dogName = this.dogNames[i];\n        const dogNamePossessive = getPossessive(this.dogNames[i]);\n        const dogNumber = i + 1;\n\n        let temporaryElement = document.createElement('div');\n        let dogHTML = window.quizVariables.steps.dog.replaceAll(\"$DOGNUM\", dogNumber);\n        temporaryElement.innerHTML = dogHTML;\n        \n        const dataNumber = temporaryElement.querySelectorAll('[data-dog]');\n        const termsName = temporaryElement.querySelectorAll('[data-dog-name]');\n        const termsNamePossessive = temporaryElement.querySelectorAll('[data-dog-name-possessive]');\n\n        termsName.forEach(e => {\n          e.innerText = dogName;\n        });\n\n        dataNumber.forEach(e => {\n          e.dataset.dog = dogNumber;\n        });\n\n        termsNamePossessive.forEach(e => {\n          e.dataset.dog = dogNamePossessive;\n        });\n\n        html += temporaryElement.innerHTML;\n\n      }\n\n      /* ----- Add the dog substeps HTML to the container ----- */\n\n      container.innerHTML = html;\n\n      this.substepContainer.innerHTML = \"\";\n      this.substepContainer.appendChild(container);\n\n      // Make the first substep visible.\n      this.substepContainer.querySelector(\"[data-quiz-substep]\").classList.add(\"quiz-substep--active\");\n\n    }\n\n    _onChangeDogSex(event) {\n\n      const input = event.target.closest(\"[data-dog-sex]\");\n      const substepContainer = input.closest('.quiz-step--dogs-substeps');\n      const substep = input.closest('[data-quiz-substep]');\n      const dogNumber = substep.dataset.dog;\n      const dogSex = substep.querySelector('[data-dog-sex]').value;\n      const substeps = substepContainer.querySelectorAll(`[data-quiz-substep][data-dog=\"${dogNumber}\"]`);\n\n      substeps.forEach(substep => {\n\n        const termsNeutered = substep.querySelectorAll(`[data-term-neutered]`);\n\n        termsNeutered.forEach(e => {\n          e.innerText = dogSex === \"Male\" ? \"neutered\" : \"spayed\";\n        });\n\n      });\n\n    }\n\n    _onClickUpdateDog(event) {\n\n      const button = event.target.closest(\"[data-quiz-button-update-dog]\");\n      const substep = button.closest('[data-quiz-substep]');\n      \n      const dogNumber = substep.dataset.dog;\n      const dogSex = substep.querySelector('[data-dog-sex]').value;\n\n      const substeps = this.querySelectorAll(`[data-quiz-substep][data-dog=\"${dogNumber}\"]`)\n\n      substeps.forEach(substep => {\n\n        const termsNeutered = substep.querySelectorAll(`[data-term-neutered]`);\n        const termsSexPossessive = substep.querySelectorAll(`[data-term-sex-possessive]`);\n        const termsSexPronoun = substep.querySelectorAll(`[data-dog-sex-pronoun]`);\n\n        termsSexPronoun.forEach(e => {\n          e.innerText = dogSex === \"Male\" ? \"He\" : \"She\";\n        });\n\n        termsNeutered.forEach(e => {\n          e.innerText = dogSex === \"Male\" ? \"neutered\" : \"spayed\";\n        });\n\n        termsSexPossessive.forEach(e => {\n          e.innerText = dogSex === \"Male\" ? \"His\" : \"Her\";\n        });\n\n      });\n      \n    }\n\n    /*----- Submitting Quiz ----- */\n\n    _submitQuiz(event) {\n      \n      this._storeDogData();\n      \n      location.href = window.quizVariables.locations.post_data;\n      return;\n\n    }\n\n    _storeDogData() {\n\n      const data = this._sanitiseDogData(this._getDogData());\n      const dataString = JSON.stringify(data);\n\n      localStorage.setItem(\"dogData\", dataString);\n\n    }\n\n    _getDogData() {\n\n      let data = {\n        \"dogs\": []\n      };\n\n      for (let i = 0; i < this.dogNames.length; i++) {\n        \n        const dogNumber = i + 1;\n\n        let dogData = {};\n\n        dogData.name = stripNewlines(document.querySelector(`quiz-step-dog-general[data-dog=\"${dogNumber}\"] [data-dog-name]`).innerText);\n        dogData.sex = stripNewlines(document.querySelector(`[name=\"dog-${dogNumber}-sex\"]`).value);\n\n        dogData.neutered = document.querySelector(`[name=\"dog-${dogNumber}-neutered\"]`).value === \"is\" ? true : false;\n        dogData.breed = stripNewlines(document.querySelector(`[name=\"dog-${dogNumber}-breed\"]`).value);\n        \n        dogData.weight = Number(document.querySelector(`[name=\"dog-${dogNumber}-weight\"]`).value);\n        dogData.ideal_weight = Number(document.querySelector(`[name=\"dog-${dogNumber}-ideal-weight\"]`).value);\n        dogData.has_health_issue = document.querySelector(`[name=\"dog-${dogNumber}-has-health-issue\"]`).value === \"has\" ? true : false;\n        dogData.prescription_diet = stripNewlines(document.querySelector(`[name=\"dog-${dogNumber}-prescription-diet\"]`).value);\n        dogData.weight_profile = stripNewlines(document.querySelector(`[name=\"dog-${dogNumber}-weight-profile\"]`).value);\n        dogData.activity_level = stripNewlines(document.querySelector(`[name=\"dog-${dogNumber}-activity-level\"]`).value);\n\n        // Age\n        const age = document.querySelector(`[name=\"dog-${dogNumber}-age\"]`).value;\n        const ageType = document.querySelector(`[name=\"dog-${dogNumber}-age-type\"]`).value;\n\n        if (ageType == \"Year\") {\n          dogData.age_in_months = age * 12;\n        }\n        else\n        if (ageType == \"Month\") {\n          dogData.age_in_months = Number(age);\n        }\n\n        data.dogs.push(dogData);\n\n      }\n\n      let customerEmail;\n\n      if (document.querySelector(\"#request_email\")) {\n        customerEmail = document.querySelector(\"#request_email\").value;\n      }\n      else\n      if (window.quizVariables.customer) {\n        customerEmail = window.quizVariables.customer.email;\n      }\n      \n      if (customerEmail) {\n        data.customer = {\n          email: customerEmail\n        }\n      }\n\n      return data;\n\n    }\n\n    _sanitiseDogData(data) {\n\n      // console.log('_sanitiseDogData');\n      // console.log(data);\n\n      if (!data) {\n        return;\n      }\n      \n      const dogData = data;\n\n      for (let d = 0; d < dogData.dogs.length; d++) {\n        const dog = dogData.dogs[d];\n        dog.name = dog.name === \"\" ? \"None\" : dog.name;\n        dog.sex = dog.sex === \"\" ? \"None\" : dog.sex;\n        dog.neutered = dog.neutered === \"\" ? \"None\" : dog.neutered;\n        dog.breed = dog.breed === \"\" ? \"None\" : dog.breed;\n        dog.weight = dog.weight === \"\" ? \"0\" : dog.weight;\n        dog.ideal_weight = dog.ideal_weight === \"\" ? \"0\" : dog.ideal_weight;\n        dog.has_health_issue = dog.has_health_issue === \"\" ? \"None\" : dog.has_health_issue;\n        dog.prescription_diet = dog.prescription_diet === \"\" ? \"None\" : dog.prescription_diet;\n        dog.weight_profile = dog.weight_profile === \"\" ? \"None\" : dog.weight_profile;\n        dog.activity_level = dog.activity_level === \"\" ? \"None\" : dog.activity_level;\n        dog.age_in_months = dog.age_in_months === \"\" ? \"None\" : dog.age_in_months;\n      }\n\n      // console.log(dogData);\n\n      return dogData;\n\n    }\n\n    /*----- Navigation ----- */\n\n    get navigation() {\n      \n      return this.querySelector('.quiz-navigation');\n\n    }\n\n    _setupNavigation() {\n\n      const navigationHeight = this.navigation ? this.navigation.clientHeight : undefined;\n      const subnavigationHeight = this.subNavigation ? this.subNavigation.clientHeight : undefined;\n\n      if (navigationHeight) {\n        document.documentElement.style.setProperty('--quiz-navigation-height', navigationHeight + 'px');\n      }\n\n      if (subnavigationHeight) {\n        document.documentElement.style.setProperty('--quiz-sub-navigation-height', subnavigationHeight + 'px');\n      }\n\n      this._updateNavigation();\n\n    }\n\n    _updateNavigation() {\n\n      const backButton = this.querySelector('[data-quiz-button-back]');\n      const backButtonLabel = backButton.querySelector('.quiz-navigation-button__text');\n\n      if (this.stepNumber == 1) {\n        backButton.classList.add(\"quiz-navigation-button--hidden\");\n      }\n      else {\n        backButton.classList.remove(\"quiz-navigation-button--hidden\");\n      }\n\n    }\n\n    /*----- ProgressBar ----- */\n    \n    get progressBar() {\n      return document.querySelector('quiz-progress-bar');\n    }\n\n    get totalMainSteps() {\n      return this.querySelectorAll('.quiz-step').length;\n    }\n\n    get currentMainStepNumber() {\n      const steps = this.querySelectorAll('.quiz-step');\n      for (let s = 0; s < steps.length; s++) {\n        const currentStep = steps[s];\n        if (currentStep.classList.contains('quiz-step--entering') == true) {\n          return s;\n        }\n      }\n      return 3;\n    }\n\n    get totalDogSteps() {\n      return this.querySelectorAll('.quiz-substep').length;\n    }\n\n    get currentDogStepNumber() {\n      const steps = this.querySelectorAll('.quiz-substep');\n      for (let s = 0; s < steps.length; s++) {\n        const currentStep = steps[s];\n        if (currentStep.classList.contains('quiz-substep--active') == true) {\n          return s + 1;\n        }\n      }\n    }\n\n    _updateProgressBar() {\n\n      const barSegments = 2;\n\n      const start = this.progressBar.dataset.startingProgressSegment ? Number(this.progressBar.dataset.startingProgressSegment) / barSegments : 1 / barSegments;\n\n      let mainStepProgress = (this.currentMainStepNumber / this.totalMainSteps) * (1 / 5);\n      let dogStepProgress = this.currentMainStepNumber >= 3 ? (this.currentDogStepNumber / this.totalDogSteps) * (4 / 5) : 0;\n\n      let progress = start + (dogStepProgress + mainStepProgress) / barSegments;\n\n      this.progressBar.progress(progress);\n\n    }\n\n    /*----- Sub Navigation ----- */\n    \n    get subNavigation() {\n      return document.querySelector('quiz-sub-navigation');\n    }\n    get subNavigationTabs() {\n      return document.querySelector('quiz-dog-navigation');\n    }\n\n    _buildSubNavigation() {\n\n      this.subNavigationTabs.innerHTML = \"\";\n\n      for (let i = 0; i < this.dogNames.length; i++) {\n        \n        const name = this.dogNames[i];\n        const active = i === 0 ? 'active' : '';\n        const disabled = active === 'active' ? '' : 'disabled';\n\n        this.subNavigationTabs.insertAdjacentHTML('beforeend', Templates.quizStepTab({ \n          name: name, \n          attributes: `data-dog=\"${i + 1}\" ${disabled}`,\n          classes: `${active}`\n        }));\n\n      }\n\n      this.showSubNavigation();\n\n    }\n\n    showSubNavigation() {\n      this.subNavigation.classList.remove('quiz-sub-navigation--hidden');\n    }\n    hideSubNavigation() {\n      this.subNavigation.classList.add('quiz-sub-navigation--hidden');\n    }\n\n    _onClickSubstepTab(event) {\n\n      const quizSteps = document.querySelector(\"quiz-steps\");\n      const tab = event.target.closest(\"quiz-step-tab\");\n      const dog = tab.dataset.dog;\n      // const destinationTab = document.querySelector(`quiz-step-tab[data-dog=\"${dog}\"]`); \n      const destinationStep = document.querySelector(`quiz-step-dog-general[data-dog=\"${dog}\"]`);\n\n      // tab.deactivate();\n      // destinationTab.activate();\n\n      quizSteps.gotoSubstep(destinationStep);\n\n    }\n\n\n    /*----- Steps ----- */\n    \n    get currentStep() {\n      return this.querySelector(\".quiz-step--active\");\n    }\n    get stepNumber() {\n      const steps = this.querySelectorAll('.quiz-step');\n      for (let s = 0; s < steps.length; s++) {\n        const currentStep = steps[s];\n        if (currentStep.classList.contains('quiz-step--active') == true) {\n          return s + 1;\n        }\n      }\n    }\n    get nextStep() {\n      let sibling = this.currentStep.nextElementSibling;\n      while (sibling) {\n        if (sibling.matches(\".quiz-step\")) return sibling;\n        sibling = sibling.nextElementSibling\n      }\n    }\n    get prevStep() {\n      let sibling = this.currentStep.previousElementSibling;\n      while (sibling) {\n        if (sibling.matches(\".quiz-step\")) return sibling;\n        sibling = sibling.previousElementSibling\n      }\n    }\n    get animatingStep() {\n      return this.querySelector(\".quiz-step--animating\");\n    }\n\n    getStepType(button) {\n\n      let stepType;\n\n      if (button.closest(\"quiz-step-dog-details\")) {\n        stepType = 'substep';\n      }\n      else {\n        stepType = 'step';\n      }\n\n      return stepType;\n\n    }\n\n    skipToResults() {\n      location.href = window.quizVariables.locations.results;\n    }\n\n    scrollToTop() {\n      window.scrollTo(0, 0);\n    }\n\n    next(event) {\n\n      const button = event.target;\n      let stepType = this.getStepType(button);;\n\n      if (stepType == \"step\") {\n        this.gotoNextStep(event);\n      }\n      else if (stepType == \"substep\") {\n        this.gotoNextSubstep(event);\n      }\n\n      this.scrollToTop();\n\n    }\n\n    prev(event) {\n\n      const button = event.target;\n      let stepType = this.getStepType(button);;\n\n      if (stepType == \"step\") {\n        this.gotoPrevStep(event);\n      }\n      else\n      if (stepType == \"substep\") {\n        this.gotoPrevSubstep(event);\n      }\n\n      this.scrollToTop();\n\n    }\n\n    back(event) {\n\n      // this.prev(event);\n\n      const button = event.target;\n      let stepType = this.getStepType(button);;\n\n      if (this.currentStep.querySelector(\".quiz-substep\")) {\n        // Current step has substeps.\n        if (this.prevSubstep) {\n          this.gotoPrevSubstep(event);\n        }\n        else {\n          this.gotoPrevStep(event);\n        }\n      }\n      else {\n        this.gotoPrevStep(event);\n      }\n\n      triggerEvent(this, \"quiz:quiz-steps:back\");\n\n    }\n\n    async gotoNextStep(event) {\n\n      const button = event.currentTarget;\n      const nextStep = this.nextStep;\n      const currentStep = this.currentStep;\n      const animatingStep = this.animatingStep;\n\n      if (!nextStep || animatingStep) {\n        return;\n      }\n\n      triggerEvent(this, \"quiz:quiz-steps:next-step:start\");\n\n      currentStep.classList.add(\"quiz-step--animating\");\n      currentStep.classList.add(\"quiz-step--leaving\");\n\n      nextStep.classList.add(\"quiz-step--animating\");\n      nextStep.classList.add(\"quiz-step--entering\");\n\n      triggerEvent(this, \"quiz:quiz-steps:next-step:transitioning\");\n\n      if (window.matchMedia(window.themeVariables.breakpoints.phone).matches) {\n\n        // console.log(\"Next - Mobile\");\n\n        const animation1 = new CustomAnimation(new CustomKeyframeEffect(currentStep,\n          {\n            visibility: [\"visible\", \"hidden\"],\n            transform: [\"translateX(0%)\", \"translateX(-100%)\"],\n            opacity: [\"1\", \"0\"]\n          },\n          {\n            duration: 500,\n            easing: \"cubic-bezier(0.23, 1, 0.32, 1)\"\n          }\n        ));\n\n        animation1.play();\n        await animation1.finished;\n\n        currentStep.classList.remove(\"quiz-step--active\");\n        currentStep.classList.remove(\"quiz-step--animating\");\n        currentStep.classList.remove(\"quiz-step--leaving\");\n\n        const animation2 = new CustomAnimation(new CustomKeyframeEffect(nextStep,\n          {\n            visibility: [\"hidden\", \"visible\"],\n            transform: [\"translateX(100%)\", \"translateX(0)\"],\n            opacity: [\"0\", \"1\"]\n          },\n          {\n            duration: 500,\n            easing: \"cubic-bezier(0.23, 1, 0.32, 1)\"\n          }\n        ));\n\n        animation2.play();\n        await animation2.finished;\n\n        triggerEvent(this, \"quiz:quiz-steps:next-step:arrive\");\n\n        nextStep.classList.add(\"quiz-step--active\");\n        nextStep.classList.remove(\"quiz-step--animating\");\n        nextStep.classList.remove(\"quiz-step--entering\");\n\n        triggerEvent(this, \"quiz:quiz-steps:next-step:end\");\n\n      }\n      else {\n\n        // console.log(\"Next - Desktop\");\n\n        const animation = new CustomAnimation(new ParallelEffect([\n          new CustomKeyframeEffect(currentStep,\n            {\n              visibility: [\"visible\", \"hidden\"],\n              transform: [\"translateX(0%)\", \"translateX(-100%)\"],\n              opacity: [\"1\", \"0\"]\n            },\n            {\n              duration: 1000,\n              easing: \"cubic-bezier(0.23, 1, 0.32, 1)\"\n            }\n          ),\n          new CustomKeyframeEffect(nextStep,\n            {\n              visibility: [\"hidden\", \"visible\"],\n              transform: [\"translateX(100%)\", \"translateX(0)\"],\n              opacity: [\"0\", \"1\"]\n            },\n            {\n              duration: 1000,\n              easing: \"cubic-bezier(0.23, 1, 0.32, 1)\"\n            }\n          )\n        ]));\n\n        animation.play();\n\n        await animation.finished;\n\n        triggerEvent(this, \"quiz:quiz-steps:next-step:arrive\");\n\n        currentStep.classList.remove(\"quiz-step--active\");\n        currentStep.classList.remove(\"quiz-step--animating\");\n        currentStep.classList.remove(\"quiz-step--leaving\");\n\n        nextStep.classList.add(\"quiz-step--active\");\n        nextStep.classList.remove(\"quiz-step--animating\");\n        nextStep.classList.remove(\"quiz-step--entering\");\n\n        triggerEvent(this, \"quiz:quiz-steps:next-step:end\");\n\n      }\n\n    }\n    async gotoPrevStep(event) {\n\n      const button = event.currentTarget;\n      const prevStep = this.prevStep;\n      const currentStep = this.currentStep;\n      const animatingStep = this.animatingStep;\n\n      if (!prevStep || animatingStep) {\n        return;\n      }\n\n      triggerEvent(this, \"quiz:quiz-steps:prev-step:start\");\n\n      currentStep.classList.add(\"quiz-step--animating\");\n      currentStep.classList.add(\"quiz-step--leaving\");\n\n      prevStep.classList.add(\"quiz-step--animating\");\n      prevStep.classList.add(\"quiz-step--entering\");\n\n      triggerEvent(this, \"quiz:quiz-steps:prev-step:transitioning\");\n\n      if (window.matchMedia(window.themeVariables.breakpoints.phone).matches) {\n\n        // console.log(\"Prev - Mobile\");\n\n        const animation1 = new CustomAnimation(new CustomKeyframeEffect(currentStep,\n          {\n            visibility: [\"visible\", \"hidden\"],\n            transform: [\"translateX(0)\", \"translateX(100%)\"],\n            opacity: [\"1\", \"0\"]\n          },\n          {\n            duration: 500,\n            easing: \"cubic-bezier(0.23, 1, 0.32, 1)\"\n          }\n        ));\n\n        animation1.play();\n        await animation1.finished;\n\n        currentStep.classList.remove(\"quiz-step--active\");\n        currentStep.classList.remove(\"quiz-step--animating\");\n        currentStep.classList.remove(\"quiz-step--leaving\");\n\n        const animation2 = new CustomAnimation(new CustomKeyframeEffect(prevStep,\n          {\n            visibility: [\"hidden\", \"visible\"],\n            transform: [\"translateX(-100%)\", \"translateX(0)\"],\n            opacity: [\"0\", \"1\"]\n          },\n          {\n            duration: 500,\n            easing: \"cubic-bezier(0.23, 1, 0.32, 1)\"\n          }\n        ));\n\n        animation2.play();\n        await animation2.finished;\n\n        triggerEvent(this, \"quiz:quiz-steps:next-step:arrive\");\n\n        prevStep.classList.add(\"quiz-step--active\");\n        prevStep.classList.remove(\"quiz-step--animating\");\n        prevStep.classList.remove(\"quiz-step--entering\");\n\n        triggerEvent(this, \"quiz:quiz-steps:next-step:end\");\n\n      }\n      else {\n\n        // console.log(\"Prev - Desktop\");\n\n        const animation = new CustomAnimation(new ParallelEffect([\n          new CustomKeyframeEffect(currentStep,\n            {\n              visibility: [\"visible\", \"hidden\"],\n              transform: [\"translateX(0)\", \"translateX(100%)\"],\n              opacity: [\"1\", \"0\"]\n            },\n            {\n              duration: 1000,\n              easing: \"cubic-bezier(0.23, 1, 0.32, 1)\"\n            }\n          ),\n          new CustomKeyframeEffect(prevStep,\n            {\n              visibility: [\"hidden\", \"visible\"],\n              transform: [\"translateX(-100%)\", \"translateX(0)\"],\n              opacity: [\"0\", \"1\"]\n            },\n            {\n              duration: 1000,\n              easing: \"cubic-bezier(0.23, 1, 0.32, 1)\"\n            }\n          )\n        ]));\n\n        animation.play();\n\n        await animation.finished;\n\n        triggerEvent(this, \"quiz:quiz-steps:next-step:arrive\");\n\n        currentStep.classList.remove(\"quiz-step--active\");\n        currentStep.classList.remove(\"quiz-step--animating\");\n        currentStep.classList.remove(\"quiz-step--leaving\");\n\n        prevStep.classList.add(\"quiz-step--active\");\n        prevStep.classList.remove(\"quiz-step--animating\");\n        prevStep.classList.remove(\"quiz-step--entering\");\n\n        triggerEvent(this, \"quiz:quiz-steps:next-step:end\");\n\n      }\n\n    }\n\n    /*----- Sub Steps ----- */\n\n    get currentSubstep() {\n      return this.querySelector(\".quiz-substep--active\");\n    }\n    get nextSubstep() {\n      let sibling = this.currentSubstep.nextElementSibling;\n      while (sibling) {\n        if (sibling.matches(\".quiz-substep\")) return sibling;\n        sibling = sibling.nextElementSibling\n      }\n    }\n    get prevSubstep() {\n      let sibling = this.currentSubstep.previousElementSibling;\n      while (sibling) {\n        if (sibling.matches(\".quiz-substep\")) return sibling;\n        sibling = sibling.previousElementSibling\n      }\n    }\n\n    gotoNextSubstep(event) {\n      \n      if (this.nextSubstep) {\n        this.gotoSubstep(this.nextSubstep);\n        triggerEvent(this.subNavigationTabs, \"quiz:quiz-steps:next-substep\");\n        return;\n      }\n\n      if (this.nextStep) {\n        this.gotoNextStep(event);\n      }\n      else {\n        triggerEvent(this, \"quiz:quiz-steps:submit-quiz\");\n      }\n\n    }\n\n    gotoPrevSubstep() {\n      this.gotoSubstep(this.prevSubstep);\n      triggerEvent(this.subNavigationTabs, \"quiz:quiz-steps:prev-substep\");\n    }\n\n    _dogComplete(event) {\n\n      const button = event.target.closest(\"button\");\n\n      const currentSubstep = this.currentSubstep;\n      const nextSubstep = currentSubstep.nextElementSibling;\n      const dogNumber = currentSubstep.dataset.dog;\n\n      const currentTab = this.subNavigation.querySelector(`quiz-step-tab[data-dog=\"${dogNumber}\"]`);\n      const nextTab = currentTab.nextElementSibling;\n\n      currentTab.deactivate();\n\n      if (nextTab) {\n        nextTab.activate();\n        nextTab.enable();\n      }\n\n      if (!nextSubstep) {\n        button._startTransition;\n      }\n\n    }\n\n    _onQuizSubstepStart() {\n    \n    }\n\n    _onQuizSubstepArrive() {\n    \n    }\n\n    async gotoSubstep(substep) {\n\n      const currentSubstep = this.currentSubstep;\n      const destinationSubstep = substep;\n      const animatingSubstep = this.animatingSubstep;\n\n      if (!destinationSubstep || animatingSubstep) {\n        return;\n      }\n      if (!destinationSubstep.classList.contains(\"quiz-substep\")) {\n        return;\n      }\n\n      triggerEvent(this.subNavigation, \"quiz:quiz-steps:sub-step:start\");\n\n      currentSubstep.classList.add(\"quiz-substep--visible\");\n      currentSubstep.classList.add(\"quiz-substep--animating\");\n\n      const animation = new CustomAnimation(new CustomKeyframeEffect(currentSubstep,\n        {\n          visibility: [\"visible\", \"hidden\"],\n          transform: [\"translateY(0%)\", \"translateY(-20%)\"],\n          opacity: [\"1\", \"0\"]\n        },\n        {\n          duration: 300,\n          easing: \"cubic-bezier(0.23, 1, 0.32, 1)\"\n        }\n      ));\n\n      triggerEvent(this, \"quiz:quiz-steps:sub-step:leave\");\n\n      animation.play();\n\n      await animation.finished;\n\n      currentSubstep.classList.remove(\"quiz-substep--active\");\n      currentSubstep.classList.remove(\"quiz-substep--visible\");\n      currentSubstep.classList.remove(\"quiz-substep--animating\");\n\n      destinationSubstep.classList.add(\"quiz-substep--visible\");\n      destinationSubstep.classList.add(\"quiz-substep--active\");\n      destinationSubstep.classList.add(\"quiz-substep--animating\");\n\n      triggerEvent(this.subNavigation, \"quiz:quiz-steps:sub-step:step-updated\");\n\n      const animation2 = new CustomAnimation(new CustomKeyframeEffect(destinationSubstep,\n        {\n          visibility: [\"hidden\", \"visible\"],\n          transform: [\"translateY(20%)\", \"translateY(0%)\"],\n          opacity: [\"0\", \"1\"]\n        },\n        {\n          duration: 300,\n          easing: \"cubic-bezier(0.23, 1, 0.32, 1)\"\n        }\n      ));\n\n      animation2.play();\n\n      destinationSubstep.classList.remove(\"quiz-substep--animating\");\n\n      await animation2.finished;\n\n      triggerEvent(this.subNavigation, \"quiz:quiz-steps:sub-step:arrive\");\n\n    }\n\n  };\n\n  var ExpandingInput = class extends CustomHTMLElement {\n    constructor() {\n      super();\n      if (Shopify.designMode) {\n        // this.rootDelegate.on(\"shopify:section:select\", (event) => filterShopifyEvent(event, this, () => this.open = true));\n        // this.rootDelegate.on(\"shopify:section:deselect\", (event) => filterShopifyEvent(event, this, () => this.open = false));\n      }\n    }\n    connectedCallback() {\n\n      this.input = this.querySelector(\".expanding-input__input\");\n      this.inputDisplay = this.querySelector(\".expanding-input__display\");\n      this.line = this.closest(\"quiz-step-line\");\n\n      if (this.input.type == \"number\") {\n        // this.max = this.input.max;\n        this.delegate.on(\"keypress\", \".expanding-input__display\", this.onInputNumberValidate.bind(this));\n        this.delegate.on(\"keyup\", \".expanding-input__display\", this.onInputNumber.bind(this));\n      }\n      else if (this.input.nodeName == \"SELECT\") {\n        // this.delegate.on(\"click\", \".expanding-input__display\", this.onInputSelect.bind(this));\n        this.delegate.on(\"change\", \".expanding-input__input\", this.onInputSelectChange.bind(this));\n      }\n      else {\n        this.delegate.on(\"keyup\", \".expanding-input__display\", this.onInput.bind(this));\n      }\n\n    }\n    onInput(event) {\n\n      this.input.value = this.inputDisplay.innerText;\n      triggerEvent(this, \"quiz:expanding-input:change\");\n\n    }\n    onInputSelectChange(event) {\n\n      const displayValue = event.target.options[event.target.selectedIndex].dataset.displayValue;\n\n      this.inputDisplay.innerHTML = displayValue ? displayValue : event.target.options[event.target.selectedIndex].value;\n\n      triggerEvent(this, \"quiz:expanding-input:change\");\n\n    }\n    onInputNumberValidate(event) {\n      if (isNaN(String.fromCharCode(event.which)) == true) {\n        event.preventDefault();\n        return false;\n      }\n    }\n    onInputNumber(event) {\n\n      const number = Number(this.inputDisplay.innerText);\n      const max = Number(this.input.max);\n      const min = Number(this.input.min);\n\n      if (max) {\n        if (number > max) {\n          this.input.value = max;\n          this.inputDisplay.innerText = max;\n          return;\n        }\n      }\n\n      if (min) {\n        if (number < min && number != 0) {\n          this.input.value = min;\n          this.inputDisplay.innerText = min;\n          return;\n        }\n      }\n\n      this.input.value = this.inputDisplay.innerText;\n\n      triggerEvent(this, \"quiz:expanding-input:change\");\n\n    }\n\n  }\n\n  var QuizCustomerRegisterForm = class extends CustomHTMLElement {\n\n    constructor() {\n      super();\n      if (Shopify.designMode) {\n        // this.rootDelegate.on(\"shopify:section:select\", (event) => filterShopifyEvent(event, this, () => this.open = true));\n        // this.rootDelegate.on(\"shopify:section:deselect\", (event) => filterShopifyEvent(event, this, () => this.open = false));\n      }\n    }\n    connectedCallback() {\n\n      console.log('QuizCustomerRegisterForm');\n\n      this.registerFormContainer = this.querySelector('[data-register-form]');\n      this.registerForm = this.registerFormContainer.querySelector(\"form\");\n\n      this.kycFormContainer = this.querySelector('[data-kyc-form]');\n      this.kycForm = this.kycFormContainer.querySelector(\"form\");\n      \n      this.registerButton = this.querySelector('[data-register-form-register-button]');\n      this.continueButton = this.querySelector('[data-register-form-continue-button]');\n      this.backButton = this.querySelector('[data-register-form-back-button]');\n      \n      this.backButton.addEventListener(\"click\", this.showRegisterForm.bind(this));\n      \n      this.registerForm.addEventListener(\"submit\", this.onRegisterFormSubmit.bind(this));\n      this.kycForm.addEventListener(\"submit\", this.onKYCFormSubmit.bind(this));\n\n      this.realRegisterForm = this.closest(\".quiz-customer-forms\").querySelector(\"[data-real-form]\");\n\n    }\n\n    onRegisterFormSubmit(event) {\n\n      event.preventDefault();\n      this.showKYCForm();\n\n      return false;\n\n    }\n\n    onKYCFormSubmit(event) {\n\n      event.preventDefault();\n      this.realRegisterForm.submit();\n\n      return false;\n\n    }\n\n    async showKYCForm() {\n\n      const animateOut = new CustomAnimation(new CustomKeyframeEffect(this.registerFormContainer,\n        {\n          display: [\"block\", \"none\"],\n          opacity: [\"1\", \"0\"]\n        },\n        {\n          duration: 250,\n          easing: \"cubic-bezier(0.23, 1, 0.32, 1)\"\n        }\n      ));\n      animateOut.play();\n\n      await animateOut.finished;\n      \n      this.registerFormContainer.classList.add(\"hidden\");\n      this.kycFormContainer.classList.remove(\"hidden\");\n\n      const animateIn = new CustomAnimation(new CustomKeyframeEffect(this.kycFormContainer,\n        {\n          display: [\"none\", \"block\"],\n          opacity: [\"0\", \"1\"]\n        },\n        {\n          duration: 250,\n          easing: \"cubic-bezier(0.23, 1, 0.32, 1)\"\n        }\n      ));\n      \n      animateIn.play();\n\n    }\n\n    async showRegisterForm() {\n\n      const animateOut = new CustomAnimation(new CustomKeyframeEffect(this.kycFormContainer,\n        {\n          display: [\"block\", \"none\"],\n          opacity: [\"1\", \"0\"]\n        },\n        {\n          duration: 250,\n          easing: \"cubic-bezier(0.23, 1, 0.32, 1)\"\n        }\n      ));\n      animateOut.play(); \n\n      await animateOut.finished;\n      \n      this.kycFormContainer.classList.add(\"hidden\");\n      this.registerFormContainer.classList.remove(\"hidden\");\n\n      const animateIn = new CustomAnimation(new CustomKeyframeEffect(this.registerFormContainer,\n        {\n          display: [\"none\", \"block\"],\n          opacity: [\"0\", \"1\"]\n        },\n        {\n          duration: 250,\n          easing: \"cubic-bezier(0.23, 1, 0.32, 1)\"\n        }\n      ));\n      \n      animateIn.play();\n\n    }\n\n  }\n\n  var QuizCustomerForms = class extends CustomHTMLElement {\n\n    constructor() {\n      super();\n      if (Shopify.designMode) {\n        // this.rootDelegate.on(\"shopify:section:select\", (event) => filterShopifyEvent(event, this, () => this.open = true));\n        // this.rootDelegate.on(\"shopify:section:deselect\", (event) => filterShopifyEvent(event, this, () => this.open = false));\n      }\n    }\n    connectedCallback() {\n\n      // console.log(\"QuizCustomerForms\");\n\n      this.loginForm = this.querySelector('.quiz-login-form');\n      this.loginButton = this.querySelector('[data-action-quiz-start-login-button]');\n      this.registerForm = this.querySelector('.quiz-register-form');\n      this.registerButton = this.querySelector('[data-action-quiz-start-register-button]');\n\n      // this.loginButton.addEventListener(\"click\", this.showLoginForm.bind(this));\n      // this.registerButton.addEventListener(\"click\", this.showRegisterForm.bind(this));\n\n    }\n\n    async showRegisterForm() {\n\n      // console.log('showRegisterForm');\n\n      const animateOut = new CustomAnimation(new CustomKeyframeEffect(this.loginForm,\n        {\n          display: [\"block\", \"none\"],\n          transform: [\"translateX(0)\", \"translateX(-20%)\"],\n          opacity: [\"1\", \"0\"]\n        },\n        {\n          duration: 250,\n          easing: \"cubic-bezier(0.23, 1, 0.32, 1)\"\n        }\n      ));\n      animateOut.play();\n\n      await animateOut.finished;\n      \n      this.loginForm.classList.add(\"hidden\");\n      this.registerForm.classList.remove(\"hidden\");\n\n      const animateIn = new CustomAnimation(new CustomKeyframeEffect(this.registerForm,\n        {\n          display: [\"none\", \"block\"],\n          transform: [\"translateX(20%)\", \"translateX(0%)\"],\n          opacity: [\"0\", \"1\"]\n        },\n        {\n          duration: 250,\n          easing: \"cubic-bezier(0.23, 1, 0.32, 1)\"\n        }\n      ));\n      \n      animateIn.play();\n\n    }\n\n    async showLoginForm() {\n\n      // console.log('showLoginForm');\n\n      const animateOut = new CustomAnimation(new CustomKeyframeEffect(this.registerForm,\n        {\n          display: [\"block\", \"none\"],\n          transform: [\"translateX(0)\", \"translateX(-20%)\"],\n          opacity: [\"1\", \"0\"]\n        },\n        {\n          duration: 250,\n          easing: \"cubic-bezier(0.23, 1, 0.32, 1)\"\n        }\n      ));\n      animateOut.play();\n\n      await animateOut.finished;\n\n      this.registerForm.classList.add(\"hidden\");\n      this.loginForm.classList.remove(\"hidden\");\n\n      const animateIn = new CustomAnimation(new CustomKeyframeEffect(this.loginForm,\n        {\n          display: [\"none\", \"block\"],\n          transform: [\"translateX(20%)\", \"translateX(0%)\"],\n          opacity: [\"0\", \"1\"]\n        },\n        {\n          duration: 250,\n          easing: \"cubic-bezier(0.23, 1, 0.32, 1)\"\n        }\n      ));\n\n      animateIn.play();\n\n    }\n\n  }\n\n  var QuizStep = class extends CustomHTMLElement {\n\n    constructor() {\n      super();\n      if (Shopify.designMode) {\n        // this.rootDelegate.on(\"shopify:section:select\", (event) => filterShopifyEvent(event, this, () => this.open = true));\n        // this.rootDelegate.on(\"shopify:section:deselect\", (event) => filterShopifyEvent(event, this, () => this.open = false));\n      }\n    }\n    connectedCallback() {\n\n      this.inputs = this.querySelectorAll(\"input, select, textarea\");\n\n    }\n\n  }\n\n  var QuizStepLine = class extends CustomHTMLElement {\n\n    constructor() {\n      super();\n\n      // this.delegateElement = new main_default(document.body);\n      // this.delegateElement.on(\"change\", \".expanding-input__input\", this._onValueChanged.bind(this));\n\n      if (Shopify.designMode) {\n        // this.rootDelegate.on(\"shopify:section:select\", (event) => filterShopifyEvent(event, this, () => this.open = true));\n        // this.rootDelegate.on(\"shopify:section:deselect\", (event) => filterShopifyEvent(event, this, () => this.open = false));\n      }\n    }\n    connectedCallback() {\n\n      this.inputs.forEach((element) => {\n        // element.addEventListener(\"change\", this._onValueChanged.bind(this));\n      })\n\n      this.valid = false;\n\n      this.addEventListener(\"quiz:expanding-input:change\", this._onValueChanged.bind(this));\n      this.addEventListener(\"quiz:step-line:valid\", this._onValid.bind(this));\n      this.addEventListener(\"quiz:step-line:invalid\", this._onInvalid.bind(this));\n\n    }\n\n    _onValueChanged(event) {\n\n      let input = event.target.querySelector(\".expanding-input__input\");\n\n      if (this.nextLine) {\n        if (this._showNextLine(input)) {\n          this.nextLine.show();\n        }\n        else {\n          this.nextLine.hide();\n        }\n      }\n\n      this.validate();\n\n      \n    }\n\n    validate() {\n\n      if (!this.inputs.length) {\n        triggerEvent(this, \"quiz:step-line:valid\");\n        return true;\n      }\n\n      if (this.hasAttribute(\"optional\")) {\n        // Is optional, always valid.\n        triggerEvent(this, \"quiz:step-line:valid\");\n        return true;\n      }\n\n      if (this.hasAttribute(\"optional-if-hidden\")) {\n\n        // Is optional if hidden\n        if (this.classList.contains(\"quiz-step-line--hidden\") == true) {\n          triggerEvent(this, \"quiz:step-line:valid\");\n          return true;\n        }\n        \n      }\n\n      for (let i = 0; i < this.inputs.length; i++) {\n\n        const input = this.inputs[i];\n\n        if (!input.value) {\n          triggerEvent(this, \"quiz:step-line:invalid\");\n          return false;\n        }\n\n      }\n\n      triggerEvent(this, \"quiz:step-line:valid\");\n      return true;\n\n    }\n\n    _showNextLine(input) {\n\n      let inputElement;\n\n      if (input) {\n        inputElement = input.tagName == \"SELECT\" ? input.options[input.selectedIndex] : input;\n      }\n\n      if (input.getAttribute(\"show-nextline-if\")) {\n        if (inputElement.value == input.getAttribute(\"show-nextline-if\")) {\n          return true;\n        }\n        else {\n          return false;\n        }\n      }\n      else {\n        return true;\n      }\n\n    }\n\n    _onInvalid() {\n\n      this.valid = false;\n\n      if (this.nextLine && this.nextLine.validate() == false) {\n        this.nextLine.hide();\n        return;\n      }\n\n      this.nextButton.disabled = true;\n\n    }\n\n    _onValid() {\n\n      this.valid = true;\n\n      if (!this.nextLine) {\n        this.nextButton.disabled = false;\n      }\n\n      if (this.nextLine) {\n\n        if (this.nextLine.hasAttribute(\"optional\")) {\n          this.nextLine.validate();\n        }\n        else \n        if (this.nextLine.hasAttribute(\"optional-if-hidden\") && this.nextLine.classList.contains(\"quiz-step-line--hidden\") == true) {\n          this.nextLine.validate();\n        }\n\n      }\n\n    }\n\n    show() {\n      this.classList.remove(\"quiz-step-line--hidden\");\n    }\n\n    hide() {\n      this.classList.add(\"quiz-step-line--hidden\");\n    }\n\n    get inputs() {\n      return this.querySelectorAll(\"input, select, textarea\");\n    }\n\n    get nextButton() {\n      return this.closest(\".quiz-step, .quiz-substep\").querySelector(\"[data-quiz-button-next]\");\n    }\n\n    get prevLine() {\n      let sibling = this.previousElementSibling;\n      while (sibling) {\n        if (sibling.matches(\"quiz-step-line\")) return sibling;\n        sibling = sibling.previousElementSibling\n      }\n    }\n\n    get nextLine() {\n      let sibling = this.nextElementSibling;\n      while (sibling) {\n        if (sibling.matches(\"quiz-step-line\")) return sibling;\n        sibling = sibling.nextElementSibling\n      }\n    }\n\n  }\n\n  var QuizTile = class extends CustomHTMLElement {\n    constructor() {\n      super();\n      if (Shopify.designMode) {\n        // this.rootDelegate.on(\"shopify:section:select\", (event) => filterShopifyEvent(event, this, () => this.open = true));\n        // this.rootDelegate.on(\"shopify:section:deselect\", (event) => filterShopifyEvent(event, this, () => this.open = false));\n      }\n    }\n    connectedCallback() {\n\n      this.input = this.querySelector(\".quiz-tile__input\");\n      this.tiles = this.closest(\"quiz-tiles\");\n\n      this.addEventListener(\"click\", this.onClick.bind(this));\n\n    }\n    onClick(event) {\n\n      if (this.selected == false) {\n\n        this.tiles.reset();\n        this.tiles.input.value = this.dataset.value;\n        this.tiles.updateHint(this.dataset.hint);\n\n        this.classList.add(\"quiz-tile--selected\");\n\n      }\n\n      triggerEvent(this, \"quiz:tiles:change\");\n\n    }\n\n    get selected() {\n      return this.classList.contains(\"quiz-tile--selected\");\n    }\n\n    get nextButton() {\n      return this.closest(\".quiz-step, .quiz-substep\").querySelector(\"[data-quiz-button-next]\");\n    }\n\n  }\n\n  var QuizTiles = class extends CustomHTMLElement {\n\n    constructor() {\n      super();\n    }\n\n    connectedCallback() {\n\n      this.tiles = this.querySelectorAll(\"quiz-tile\");\n      this.input = this.querySelector(\".quiz-tiles__input\");\n      this.hint = this.querySelector(\".quiz-tiles__hint\");\n\n      this.addEventListener(\"quiz:tiles:change\", this._onValueChanged.bind(this));\n      this.addEventListener(\"quiz:tiles:valid\", this._onValid.bind(this));\n      this.addEventListener(\"quiz:tiles:invalid\", this._onInvalid.bind(this));\n\n    }\n\n    _onValueChanged(event) {\n\n      if (this.validate() == true) {\n        triggerEvent(this, \"quiz:tiles:valid\");\n      }\n      else {\n        triggerEvent(this, \"quiz:tiles:invalid\");\n      }\n\n    }\n\n    reset() {\n      this.tiles.forEach((tile) => {\n        tile.classList.remove(\"quiz-tile--selected\");\n      });\n    }\n\n    validate() {\n\n      if (this.input.value) {\n        return true;\n      } else {\n        return false;\n      }\n\n    }\n\n    async resetHint() {\n\n      const animateOut = new CustomAnimation(new CustomKeyframeEffect(this.hint,\n        {\n          visibility: [\"visible\", \"hidden\"],\n          transform: [\"translateY(0)\", \"translateY(-20%)\"],\n          opacity: [\"1\", \"0\"]\n        },\n        {\n          duration: 250,\n          easing: \"cubic-bezier(0.23, 1, 0.32, 1)\"\n        }\n      ));\n      animateOut.play();\n\n      await animateOut.finished;\n\n      this.hint.innerText = this.hint.dataset.default;\n\n      const animateIn = new CustomAnimation(new CustomKeyframeEffect(this.hint,\n        {\n          visibility: [\"hidden\", \"visible\"],\n          transform: [\"translateY(20%)\", \"translateY(0%)\"],\n          opacity: [\"0\", \"1\"]\n        },\n        {\n          duration: 250,\n          easing: \"cubic-bezier(0.23, 1, 0.32, 1)\"\n        }\n      ));\n      animateIn.play();\n\n    }\n\n    async updateHint(hint) {\n\n      if (!hint) {\n        return;\n      }\n\n      const animateOut = new CustomAnimation(new CustomKeyframeEffect(this.hint,\n        {\n          visibility: [\"visible\", \"hidden\"],\n          transform: [\"translateY(0)\", \"translateY(-20%)\"],\n          opacity: [\"1\", \"0\"]\n        },\n        {\n          duration: 250,\n          easing: \"cubic-bezier(0.23, 1, 0.32, 1)\"\n        }\n      ));\n      animateOut.play();\n\n      await animateOut.finished;\n\n      this.hint.innerText = hint;\n\n      const animateIn = new CustomAnimation(new CustomKeyframeEffect(this.hint,\n        {\n          visibility: [\"hidden\", \"visible\"],\n          transform: [\"translateY(20%)\", \"translateY(0%)\"],\n          opacity: [\"0\", \"1\"]\n        },\n        {\n          duration: 250,\n          easing: \"cubic-bezier(0.23, 1, 0.32, 1)\"\n        }\n      ));\n      animateIn.play();\n\n    }\n\n    _onInvalid() {\n\n      if (this.nextLine && this.nextLine.validate() == false) {\n        this.nextLine.hide();\n      }\n\n      if (!this.nextLine) {\n        this.nextButton.disabled = true;\n      }\n\n    }\n\n    _onValid() {\n\n      if (this.nextLine) {\n        this.nextLine.show();\n      }\n\n      if (!this.nextLine) {\n        this.nextButton.disabled = false;\n      }\n\n    }\n\n    show() {\n      this.classList.remove(\"quiz-tiles--hidden\");\n    }\n\n    hide() {\n      this.classList.add(\"quiz-tiles--hidden\");\n    }\n\n    get inputs() {\n      return this.querySelectorAll(\"input, select, textarea\");\n    }\n\n    get nextButton() {\n      return this.closest(\".quiz-step, .quiz-substep\").querySelector(\"[data-quiz-button-next]\");\n    }\n\n    get prevLine() {\n      let sibling = this.previousElementSibling;\n      while (sibling) {\n        if (sibling.matches(\"quiz-tiles\")) return sibling;\n        sibling = sibling.previousElementSibling\n      }\n    }\n\n    get nextLine() {\n      let sibling = this.nextElementSibling;\n      while (sibling) {\n        if (sibling.matches(\"quiz-tiles\")) return sibling;\n        sibling = sibling.nextElementSibling\n      }\n    }\n\n  }\n\n  var QuizTile = class extends CustomHTMLElement {\n    constructor() {\n      super();\n      if (Shopify.designMode) {\n        // this.rootDelegate.on(\"shopify:section:select\", (event) => filterShopifyEvent(event, this, () => this.open = true));\n        // this.rootDelegate.on(\"shopify:section:deselect\", (event) => filterShopifyEvent(event, this, () => this.open = false));\n      }\n    }\n    connectedCallback() {\n\n      this.input = this.querySelector(\".quiz-tile__input\");\n      this.tiles = this.closest(\"quiz-tiles\");\n\n      this.addEventListener(\"click\", this.onClick.bind(this));\n\n    }\n    onClick(event) {\n\n      if (this.selected == false) {\n\n        this.tiles.reset();\n        this.tiles.input.value = this.dataset.value;\n        this.tiles.updateHint(this.dataset.hint);\n\n        this.classList.add(\"quiz-tile--selected\");\n\n      }\n\n      triggerEvent(this, \"quiz:tiles:change\");\n\n    }\n\n    get selected() {\n      return this.classList.contains(\"quiz-tile--selected\");\n    }\n\n    get nextButton() {\n      return this.closest(\".quiz-step, .quiz-substep\").querySelector(\"[data-quiz-button-next]\");\n    }\n\n  }\n\n  var QuizStepTab = class extends CustomHTMLElement {\n\n    constructor() {\n      super();\n    }\n\n    connectedCallback() {\n\n      // this.addEventListener(\"click\", this._onClickSubstepTab.bind(this));\n\n    }\n\n    enable() {\n      this.removeAttribute(\"disabled\");\n    }\n\n    disable() {\n      this.disabled = true;\n    }\n\n    activate() {\n      this.parentElement.querySelectorAll(\"quiz-step-tab\").forEach(e => {\n        e.classList.remove(\"active\");\n      });\n      this.classList.add(\"active\");\n    }\n\n    deactivate() {\n      this.classList.remove(\"active\");\n    }\n\n    /*\n\n    _onClickSubstepTab(event) {\n\n      const quizSteps = document.querySelector(\"quiz-steps\");\n      const tab = event.target.closest(\"quiz-step-tab\");\n      const dog = tab.dataset.dog;\n      const destinationTab = document.querySelector(`quiz-step-tab[data-dog=\"${dog}\"]`);\n      const destinationStep = document.querySelector(`quiz-step-dog-general[data-dog=\"${dog}\"]`);\n\n      tab.deactivate();\n      destinationTab.activate();\n\n      quizSteps.gotoSubstep(destinationStep);\n\n    }\n\n    */\n\n  }\n\n  var QuizProgressBar = class extends CustomHTMLElement {\n\n    constructor() {\n      super();\n      if (Shopify.designMode) {\n        // this.rootDelegate.on(\"shopify:section:deselect\", (event) => filterShopifyEvent(event, this, () => this.open = false));\n      }\n    }\n\n    connectedCallback() {\n\n    }\n\n    progress(progress) {\n\n      if (!(typeof progress == \"number\")) {\n        return;\n      }\n\n      this.style.setProperty('--quiz-progress', progress);\n\n    }\n\n  }\n\n\n  var QuizResults = class extends CustomHTMLElement {\n    \n    constructor() {\n      super();\n      if (Shopify.designMode) {\n        // this.rootDelegate.on(\"shopify:section:deselect\", (event) => filterShopifyEvent(event, this, () => this.open = false));\n      }\n    }\n\n    connectedCallback() {\n\n      if (!localStorage.getItem(\"dogData\") && !Shopify.designMode) {\n        location.href = window.quizVariables.locations.quiz;\n      }\n\n      this._addEventListeners();\n\n      this._buildQuizResults()\n\n      // Initial Button\n      const button = this.querySelector('[data-quiz-button-back]');\n      const buttonText = button.querySelector('.quiz-navigation-button__text');\n      const prevDog = this.currentDog.previousElementSibling;\n\n      if (!prevDog) {\n        buttonText.innerHTML = \"Start Over\";\n      }\n      else {\n        buttonText.innerHTML = \"Back\";\n      }\n      \n    }\n\n    get buttonNext() {\n      return this.querySelector('.quiz-sticky-form [data-quiz-button-next]');\n    }\n\n    get buttonCheckout() {\n      return this.querySelector('.quiz-sticky-form [data-quiz-button-checkout]');\n    }\n\n    /* ----- Navigation ----- */\n\n    get stickyForm() {\n      return this.querySelector('quiz-sticky-form');\n    }\n\n    /* ----- Navigation ----- */\n\n    get calculator() {\n      \n      return document.querySelector('.quiz-feeding-calculator');\n\n    }\n\n    /* ----- Navigation ----- */\n\n    _updateNavigation() {\n\n      const backButton = this.querySelector('[data-quiz-button-back]');\n      const backButtonLabel = backButton.querySelector('.quiz-navigation-button__text');\n\n      if (this.currentDogNumber == 1) {\n        backButtonLabel.innerHTML = \"Start Over\";\n      }\n      else {\n        backButtonLabel.innerHTML = \"Back\";\n      }\n\n    }\n\n    next(event) {\n\n      const button = event.target;\n      // let stepType = this.getStepType(button);;\n\n      this.nextDog(event);\n\n    }\n\n    prev(event) {\n\n      const button = event.target;\n      // let stepType = this.getStepType(button);;\n\n      this.prevDog();\n\n    }\n\n    back(event) {\n\n      const button = event.target;\n      const buttonText = button.querySelector('.quiz-navigation-button__text');\n\n      const currentDog = this.currentDog;\n      const prevDog = this.currentDog.previousElementSibling;\n\n      if (prevDog) {\n        this.prevDog();\n      }\n      else {\n        location.href = window.quizVariables.locations.quiz;\n      }\n\n    }\n\n    nextDog() {\n\n      const currentDog = this.currentDog;\n      const nextDog = this.currentDog.nextElementSibling;\n\n      if (nextDog) {\n        this.gotoDog(nextDog);\n      }\n\n    }\n\n    prevDog() {\n\n      const currentDog = this.currentDog;\n      const prevDog = this.currentDog.previousElementSibling;\n\n      if (prevDog) {\n        this.gotoDog(prevDog);\n      }\n\n    }\n\n    async gotoDog(dog) {\n\n      const currentDog = this.currentDog;\n      const destinationDog = dog;\n\n      currentDog.classList.add(\"quiz-results-dog--animating\");\n      destinationDog.classList.add(\"quiz-results-dog--animating\");\n\n      triggerEvent(currentDog, \"quiz:quiz-results:next-dog:start\");\n\n      const animation = new CustomAnimation(new CustomKeyframeEffect(currentDog,\n        {\n          visibility: [\"visible\", \"hidden\"],\n          transform: [\"translateY(0%)\", \"translateY(-20%)\"],\n          opacity: [\"1\", \"0\"]\n        },\n        {\n          duration: 300,\n          easing: \"cubic-bezier(0.23, 1, 0.32, 1)\"\n        }\n      ));\n\n      animation.play();\n\n      await animation.finished;\n\n      currentDog.classList.remove(\"quiz-results-dog--active\");\n      currentDog.classList.remove(\"quiz-results-dog--animating\");\n\n      destinationDog.classList.add(\"quiz-results-dog--active\");\n\n      // triggerEvent(currentDog, \"quiz:quiz-results:next-dog:in-transition\", detail: { dog: dog });\n\n      destinationDog.dispatchEvent(new CustomEvent(\"quiz:quiz-results:next-dog:in-transition\", {\n        bubbles: true,\n        detail: {\n          dog: {\n            name: destinationDog.dataset.dogName\n          }\n        }\n      }));\n\n      const animation2 = new CustomAnimation(new CustomKeyframeEffect(destinationDog,\n        {\n          visibility: [\"hidden\", \"visible\"],\n          transform: [\"translateY(20%)\", \"translateY(0%)\"],\n          opacity: [\"0\", \"1\"]\n        },\n        {\n          duration: 300,\n          easing: \"cubic-bezier(0.23, 1, 0.32, 1)\"\n        }\n      ));\n\n      animation2.play();\n\n      destinationDog.classList.remove(\"quiz-results-dog--animating\");\n\n      await animation2.finished;\n\n      triggerEvent(destinationDog, \"quiz:quiz-results:next-dog:arrive\");\n\n    }\n\n    _updateDogProgress() {\n      \n      const dogNumber = this.querySelectorAll('quiz-results-dog').length;\n      const currentDog = this.currentDog;\n      const nextDog = this.currentDog.nextElementSibling;\n\n      this._updateProgressBar();\n\n      if (dogNumber == 1) {\n        this.buttonCheckout.disabled = false;\n        this.buttonNext.style = \"display:none\";\n      }\n\n      if (!nextDog) {\n        this.buttonNext.disabled = true;\n        this.buttonCheckout.disabled = false;\n      }\n      else {\n        this.buttonNext.disabled = false;\n        this.buttonCheckout.disabled = true;\n      }\n\n    }\n\n    get currentDog() {\n      return this.querySelector(\".quiz-results-dog--active\");\n    }\n\n    get currentDogNumber() {\n      const dogs = this.querySelectorAll('quiz-results-dog');\n      for (let s = 0; s < dogs.length; s++) {\n        const currentDog = dogs[s];\n        if (currentDog.classList.contains('quiz-results-dog--active') == true) {\n          return s + 1;\n        }\n      }\n    }\n\n    /*----- Sub Navigation ----- */\n\n    get subNavigation() {\n      return document.querySelector('quiz-sub-navigation');\n    }\n    get subNavigationTabs() {\n      return document.querySelector('quiz-dog-navigation');\n    }\n\n    _buildSubNavigation() {\n\n      let data = JSON.parse(localStorage.getItem(\"dogData\"));\n\n      if (Shopify.designMode) {\n        data = defaultDogData;\n      }\n\n      this.subNavigationTabs.innerHTML = \"\";\n\n      for (let i = 0; i < data.dogs.length; i++) {\n\n        const name = data.dogs[i].name;\n        const active = i === 0 ? 'active' : '';\n        const disabled = '';\n\n        this.subNavigationTabs.insertAdjacentHTML('beforeend', Templates.quizStepTab({\n          name: name,\n          attributes: `data-dog=\"${i + 1}\" ${disabled}`,\n          classes: `${active}`\n        }));\n\n      }\n\n      this.showSubNavigation();\n\n    }\n\n    showSubNavigation() {\n      this.subNavigation.classList.remove('quiz-sub-navigation--hidden');\n    }\n    hideSubNavigation() {\n      this.subNavigation.classList.add('quiz-sub-navigation--hidden');\n    }\n\n    _onClickSubstepTab(event) {\n\n      const tab = event.target.closest(\"quiz-step-tab\");\n      const tabs = this.subNavigationTabs;\n      const tabActive = tabs.querySelector(\"quiz-step-tab.active\");\n      const dogIndex = tab.dataset.dog;\n      const dog = document.querySelector(`quiz-results-dog[data-dog=\"${dogIndex}\"]`);\n\n      tabActive.deactivate();\n      tab.activate();\n\n      this.gotoDog(dog);\n\n    }\n\n    /* ----- Quiz Results ----- */\n\n    async _buildQuizResults() {\n\n      this._buildSubNavigation();\n\n      await this._buildQuizResultsDogs();\n      \n      this.stickyForm.updateDog();\n\n      console.log(this.calculator);\n\n      this.calculator.updateSummary();\n      this.calculator.updateProduct();\n\n      // This centres the results on some screen sizes.\n      triggerEvent(document.documentElement, \"resize\");\n\n      setTimeout(() => {\n        this.loadingOverlay.hide();\n      }, 500);\n\n    }\n\n    async _buildQuizResultsDogs() {\n\n      let data = JSON.parse(localStorage.getItem(\"dogData\"));\n\n      if (Shopify.designMode) {\n        data = defaultDogData;\n      }\n\n      if (!data && !Shopify.designMode) {\n        location.href = window.quizVariables.locations.quiz;\n      }\n\n      const container = document.querySelector('quiz-results-dogs');\n\n      let dogs = data.dogs;\n\n      /* ----- PRINT DOG STEPS ----- */\n\n      let html = \"\";\n\n      for (let i = 0; i < dogs.length; i++) {\n\n        const dog = dogs[i];\n        const dogName = dog.name;\n        const dogNumber = i + 1;\n        \n        const productRecommendationLimit = 1;\n        const productRecommendationNumber = 3;\n\n        let temp = document.createElement('div');\n        \n        let dogHTML;\n\n        if (dog.prescription_diet == \"Not Listed\") {\n          dogHTML = window.quizVariables.results.dog_condition_not_listed;\n        }\n        else {\n          dogHTML = window.quizVariables.results.dog;\n        }\n\n        dogHTML = dogHTML.replaceAll(\"$DOGINDEX\", dogNumber);\n        dogHTML = dogHTML.replaceAll(\"$DOGNAME_POSSESSIVE\", getPossessive(dogName));\n        dogHTML = dogHTML.replaceAll(\"$DOGNAME\", dogName);\n        dogHTML = dogHTML.replaceAll(\"$PRODUCT_RECOMMENDATION_LIMIT\", productRecommendationLimit);\n        dogHTML = dogHTML.replaceAll(\"$PRODUCT_RECOMMENDATION_NUMBER\", productRecommendationNumber);\n\n        temp.innerHTML = dogHTML;\n\n        html += temp.innerHTML;\n\n      }\n\n      container.innerHTML = html;\n\n      /* ----- POPULATE PRODUCTS ----- */\n\n      // Fetch the dogs' product recommendations from the API.\n\n      let dogsWithData = [];\n\n      await Promise.allSettled(dogs.map(async (dog) => {\n\n        const dogData = await this.fetchDogProductRecommendations(dog);\n        const dogDataFinal = Object.assign({ quizResults: dogData }, dog)\n\n        dogsWithData.push(dogDataFinal);\n\n      }));\n\n      /* ----- POPULATE CONTENT ----- */\n\n      for (let i = 0; i < dogsWithData.length; i++) {\n        \n        const dog = dogsWithData[i];\n        const products = dog.quizResults;\n\n        const dogContainer = this.querySelector(`quiz-results-dog[data-dog-name=\"${dog.name }\"]`);\n        const productsContainer = this.querySelector(`quiz-results-dog[data-dog-name=\"${dog.name }\"] .quiz-results__list`);\n        const modalContainer = document.querySelector(`quiz-results-modals`);\n\n        let dogResultsHTML = \"\";\n        let modalsHTML = \"\";\n\n        if (products.length > 0) {\n          \n          for (let d = 0; d < products.length; d++) {\n            \n            const product = products[d].node;\n            const productJson = JSON.stringify(product);\n            const product_id = products[d].node.id.replace(\"gid://shopify/Product/\", \"\")\n            const product_available = product.totalInventory == 0 ? false : true;\n\n            let productHTML = Templates.quizResultsProduct({ product: product, index: (d + 1) });\n            let el = document.createElement(\"div\");\n            \n            el.innerHTML = productHTML;\n\n            const productElement = el.querySelector('quiz-results-product');\n            productElement.dataset.productJson = productJson;\n\n            // Unavailable Product.\n\n            if (product_available == false) {\n              el.querySelector(\"quiz-results-product\").classList.add(\"quiz-results-product--soldout\");\n            }\n\n            dogResultsHTML += el.innerHTML;\n\n            if (!modalContainer.querySelector(`#quiz-popup--recipe-${ product_id }`)) {\n              modalsHTML += Templates.quizResultsModal({ product: product });\n            }\n\n          }\n\n          productsContainer.innerHTML = dogResultsHTML;\n          modalContainer.insertAdjacentHTML('beforeend', modalsHTML);\n\n          let firstProduct = dog.quizResults.length > 0 ? dog.quizResults[0].node : null;\n\n          productsContainer.querySelector(\"quiz-results-product\").classList.add(\"quiz-results-product--recommended\");\n\n          dogContainer.querySelector(\".initial-recommendation\").innerHTML = dog.quizResults.message;\n\n        }\n        else {\n          \n          dogResultsHTML += \"No Results\";\n\n        }\n\n      }\n\n      // Only hide inactive dog steps after the DOM elements have been added to the page\n      // so that their progress bars are properly initiated.\n\n      for (let i = 0; i < container.querySelectorAll(\"quiz-results-dog\").length; i++) {\n        const element = container.querySelectorAll(\"quiz-results-dog\")[i];\n        if (i > 0) {\n          element.classList.remove(\"quiz-results-dog--active\");\n        }\n      }\n\n      this._updateDogProgress();\n      \n    }\n\n    \n \n    async fetchDogProductRecommendations(dog) {\n\n      /* ----- Get Dog vitals and use them to compile a list of tags.) ----- */\n\n      let collectionID;\n      let collectionMessage;\n\n      let ageTag;\n      let weightTag;\n\n      if (dog.age_in_months < 12 ) { \n        ageTag = \"Puppy\";\n      } else\n      if (dog.age_in_months < 84 ) {\n        ageTag = \"Adult\";\n      } else { \n        ageTag = \"Senior\"; \n      }\n\n      if (dog.weight_profile == \"Overweight\" ) {\n        weightTag = \"Overweight\";\n      } else\n      if (dog.weight_profile == \"Underweight\" ) {\n        weightTag = \"Underweight\";\n      } else {\n        weightTag = \"Ideal\";\n      }\n\n      let collection_tag;\n\n      if (ageTag == \"Puppy\") {\n        if (weightTag == \"Underweight\") {\n          collection_tag = \"HPU\";\n        }\n        else\n        if (weightTag == \"Ideal\") {\n          collection_tag = \"HPR\";\n        }\n        else\n        if (weightTag == \"Overweight\") {\n          collection_tag = \"HPO\";\n        }\n      }\n      else\n      if (ageTag == \"Adult\") {\n        if (weightTag == \"Underweight\") {\n          collection_tag = \"HAU\";\n        }\n        else\n        if (weightTag == \"Ideal\") {\n          collection_tag = \"HAR\";\n        }\n        else\n        if (weightTag == \"Overweight\") {\n          collection_tag = \"HAO\";\n        }\n      }\n      else\n      if (ageTag == \"Senior\") {\n        if (weightTag == \"Underweight\") {\n          collection_tag = \"HSU\";\n        }\n        else\n        if (weightTag == \"Ideal\") {\n          collection_tag = \"HSR\";\n        }\n        else\n        if (weightTag == \"Overweight\") {\n          collection_tag = \"HSO\";\n        }\n      }\n\n      if (dog.prescription_diet && dog.prescription_diet != \"\" && dog.prescription_diet != \"None\") {\n\n        collection_tag = dog.prescription_diet\n        \n      }\n\n      switch (collection_tag) {\n        case 'HPU':\n          collectionID = window.quizVariables.collections.hpu.id;\n          collectionMessage = window.quizVariables.collections.hpu.message;\n          break;\n        case 'HPR':\n          collectionID = window.quizVariables.collections.hpr.id;\n          collectionMessage = window.quizVariables.collections.hpr.message;\n          break;\n        case 'HPO':\n          collectionID = window.quizVariables.collections.hpo.id;\n          collectionMessage = window.quizVariables.collections.hpo.message;\n          break;\n        case 'HAU':\n          collectionID = window.quizVariables.collections.hau.id;\n          collectionMessage = window.quizVariables.collections.hau.message;\n          break;\n        case 'HAR':\n          collectionID = window.quizVariables.collections.har.id;\n          collectionMessage = window.quizVariables.collections.har.message;\n          break;\n        case 'HAO':\n          collectionID = window.quizVariables.collections.hao.id;\n          collectionMessage = window.quizVariables.collections.hao.message;\n          break;\n        case 'HSU':\n          collectionID = window.quizVariables.collections.hsu.id;\n          collectionMessage = window.quizVariables.collections.hsu.message;\n          break;\n        case 'HSR':\n          collectionID = window.quizVariables.collections.hsr.id;\n          collectionMessage = window.quizVariables.collections.hsr.message;\n          break;\n        case 'HSO':\n          collectionID = window.quizVariables.collections.hso.id;\n          collectionMessage = window.quizVariables.collections.hso.message;\n          break;\n        case 'Allergies and/or Skin Issues':\n          collectionID = window.quizVariables.collections.allergies.id;\n          collectionMessage = window.quizVariables.collections.allergies.message;\n          break;\n        case 'Calcium Oxalate and/or Struvite Stones':\n          collectionID = window.quizVariables.collections.calcium_oxolate.id;\n          collectionMessage = window.quizVariables.collections.calcium_oxolate.message;\n          break;\n        case 'Cancer Related':\n          collectionID = window.quizVariables.collections.cancer.id;\n          collectionMessage = window.quizVariables.collections.cancer.message;\n          break;\n        case 'Grain Intolerance':\n          collectionID = window.quizVariables.collections.grain_intolerance.id;\n          collectionMessage = window.quizVariables.collections.grain_intolerance.message;\n          break;\n        case 'Heart Issues':\n          collectionID = window.quizVariables.collections.heart.id;\n          collectionMessage = window.quizVariables.collections.heart.message;\n          break;\n        case 'Joint Issues':\n          collectionID = window.quizVariables.collections.joint.id;\n          collectionMessage = window.quizVariables.collections.joint.message;\n          break;\n        case 'Kidney Disease':\n          collectionID = window.quizVariables.collections.kidney.id;\n          collectionMessage = window.quizVariables.collections.kidney.message;\n          break;\n        case 'Kidney Disease and Pancreatitis':\n          collectionID = window.quizVariables.collections.kidney_pancreatitis.id;\n          collectionMessage = window.quizVariables.collections.kidney_pancreatitis.message;\n          break;\n        case 'Liver Issues':\n          collectionID = window.quizVariables.collections.liver.id;\n          collectionMessage = window.quizVariables.collections.liver.message;\n          break;\n        case 'Pancreatitis':\n          collectionID = window.quizVariables.collections.pancreatitis.id;\n          collectionMessage = window.quizVariables.collections.pancreatitis.message;\n          break;\n        case 'Stomach and/or GI Issues':\n          collectionID = window.quizVariables.collections.gi.id;\n          collectionMessage = window.quizVariables.collections.gi.message;\n          break;\n        case 'Urate Stones':\n          collectionID = window.quizVariables.collections.urate_stones.id;\n          collectionMessage = window.quizVariables.collections.urate_stones.message;\n          break;\n      }\n      \n      const query = () => `\n      {\n        collection(id: \"gid://shopify/Collection/${collectionID}\") {\n          products(first: 10) {\n            edges {\n              node {\n                id\n                title\n                descriptionHtml\n                onlineStoreUrl\n                tags\n                totalInventory\n                cup_calories: metafield(namespace: \"nutrition\", key: \"calories_cup\") {\n                  value\n                }\n                images(first: 1) {\n                  edges {\n                    node {\n                      url\n                    }\n                  }\n                }\n                variants: variants(first: 100) {\n                  edges {\n                    node {\n                      id\n                      title\n                      quantityAvailable\n                      price {\n                        amount\n                      }\n                      compareAtPrice {\n                        amount\n                      }\n                      calories: metafield(namespace: \"quiz\", key: \"calories\") {\n                        value\n                      }\n                    }\n                  }\n                }\n                shortDescription: metafield(namespace: \"custom\", key: \"short_description\") {\n                  value\n                  type\n                }\n                productColor: metafield(namespace: \"quiz\", key: \"product_color\") {\n                  value\n                  type\n                }\n                quizResultImage: metafield(namespace: \"custom\", key: \"quiz_results_image\") {\n                  reference {\n                    ... on MediaImage {\n                      image {\n                        originalSrc\n                      }\n                    }\n                  }\n                }\n              }\n            }\n          }\n        }\n      }\n      `;\n\n      const myquery = query();\n\n      console.log(dog);\n      /*\n      // console.log('------------------------------');\n      // console.log(collection_tag);\n      // console.log(collectionID);\n      // console.log(myquery);\n      */\n\n      const GRAPHQL_BODY = () => {\n\n        return {\n          'async': true,\n          'crossDomain': true,\n          'method': 'POST',\n          'headers': {\n            'X-Shopify-Storefront-Access-Token': API.STOREFRONT.CONFIG().ACCESS_TOKEN,\n            'Content-Type': 'application/graphql',\n          },\n          'body': query()\n        };\n\n      };\n\n      /*\n      // console.log(\"BODY\");\n      */\n\n      const response = await fetch(API.STOREFRONT.CONFIG().URL, GRAPHQL_BODY())\n        .then(response => response.json())\n        .then(products => {\n          return products;\n        });\n\n      // console.log(\"RESPONSE\");\n      console.log(response);\n\n      let responseProducts = response.data.collection.products.edges;\n\n      responseProducts.message = collectionMessage;\n\n      return responseProducts;\n      // return filteredResponse;\n\n    }\n\n    get loadingOverlay() {\n      return this.querySelector('quiz-loading-overlay');\n    }\n\n    /*----- ProgressBar ----- */\n\n    get progressBar() {\n      return document.querySelector('quiz-progress-bar');\n    }\n\n    _updateProgressBar() {\n\n      const start = this.progressBar.dataset.startingProgressSegment ? Number(this.progressBar.dataset.startingProgressSegment) / 2 : 1 / 2;\n      \n      let dogs = document.querySelectorAll('quiz-results-dog');\n\n      let totalStepsNumber = dogs.length == 1 ? 1 : dogs.length - 1;\n      let currentStepNumber = 0;\n\n      for (let i = 0; i < dogs.length; i++) {\n        const element = dogs[i];\n        if (element.classList.contains(\"quiz-results-dog--active\") == false) {\n          currentStepNumber++;\n        }\n        else if (element.classList.contains(\"quiz-results-dog--active\") == true) {\n          // currentStepNumber++;\n          break;\n        }\n      }\n\n      let progress = start + ( (currentStepNumber / totalStepsNumber) / 2 );\n      \n      this.progressBar.progress(progress);\n\n    }\n\n    /*----- Checkout ----- */\n\n    async onClickCheckout(event) {\n\n      const button = event.target.closest(\"button\");\n\n      button._startTransition();\n\n      let hasUpsells = !this.popupUpsells ? false : true;\n\n      if (hasUpsells == true) {\n        this.popupUpsells.open = true;\n      }\n      else {\n        await this.proceedToCheckout();\n      }\n\n      button._endTransition();\n\n    }\n\n    async onClickCheckoutUpsells(event) {\n\n      const button = event.target.closest(\"button\");\n      const modal = button.closest('modal-content');\n      const quizResults = document.querySelector(\"quiz-results\");\n\n      button._startTransition();\n\n      await quizResults.proceedToCheckout();\n      modal.open = false;\n\n      button._endTransition();\n\n    }\n\n    get popupUpsells() {\n      return document.querySelector(\"#quiz-popup--upsells\");\n    }\n\n    async proceedToCheckout() {\n\n      // Find all products to be added to cart.\n\n      const selectedProducts = document.querySelectorAll(\".quiz-results-product--selected\");\n\n      // Add all products to the cart via the cart API.\n\n      let data = { items:[] };\n\n      const dogBoxesID = uuid();\n\n      for (let i = 0; i < selectedProducts.length; i++) {\n\n        const product = selectedProducts[i];\n        const dog = selectedProducts[i].closest('quiz-results-dog');\n        \n        const variant16oz = Number(product.dataset.variantId);\n        const variant64oz = Number(product.dataset.variantIdLast);\n\n        if (dog) {\n          \n          const dogData = dog.quizData;\n          const dogName = dogData.name;\n          \n          const transitionCalories = calculateTransitionCalories(dog.quizData);\n          const productCalories = product.calories;\n\n          let baseQuantity = calculatePacketQuantity((transitionCalories / productCalories));\n          let quantity1 = 1;\n          let quantity2 = 0;\n\n          if (baseQuantity <= 8) {\n            quantity1 = baseQuantity;\n            quantity2 = 0;\n          }\n          else {\n            quantity1 = Math.floor(baseQuantity % 4);\n            quantity2 = Math.floor(baseQuantity / 4);\n          }\n\n          // 16oz\n          let item = {\n            id: variant16oz,\n            quantity: quantity1\n          };\n\n          item.properties = {\n            \"_dog\": dogName,\n            \"_boxID\": dogName + '_' + dogBoxesID,\n            \"Starter Box\": `${dogName}'s Starter Box`\n          };\n\n          data.items.push(item);\n\n          if (quantity2 != 0) {\n\n            // 64oz\n            let item2 = {\n              id: variant64oz,\n              quantity: quantity2\n            };\n            \n            item2.properties = {\n              \"_dog\": dogName,\n              \"_boxID\": dogName + '_' + dogBoxesID,\n              \"Starter Box\": `${dogName}'s Starter Box`\n            };\n\n            data.items.push(item2);\n\n          }\n\n          if (typeof StarterBoxFreebie != \"undefined\") {\n\n            let item3 = {\n              id: StarterBoxFreebie.variants[0].id,\n              quantity: 1,\n              properties: {\n                \"_dog\": dogName,\n                \"_boxID\": dogName + '_' + dogBoxesID,\n                \"Starter Box\": `${dogName}'s Starter Box`\n              }\n            }\n\n            data.items.push(item3);\n\n          }\n\n        } else {\n\n          let item = {\n            id: variant16oz,\n            quantity: 1\n          };\n\n          data.items.push(item);\n\n        }\n\n      }\n\n      const response = await fetch(`${window.themeVariables.routes.cartAddUrl}.js`, {\n        body: JSON.stringify(data),\n        method: \"POST\",\n        headers: {\n          'Content-Type': 'application/json',\n          \"X-Requested-With\": \"XMLHttpRequest\"\n        }\n      });\n\n      const cartContent = await response.json();\n\n      // Open the cart/\n\n      fetch(`${window.themeVariables.routes.cartUrl}.js`).then(async (response2) => {\n        const cartContent = await response2.json();\n        document.documentElement.dispatchEvent(new CustomEvent(\"cart:updated\", {\n          bubbles: true,\n          detail: {\n            cart: cartContent\n          }\n        }));\n        document.documentElement.dispatchEvent(new CustomEvent(\"cart:refresh\", {\n          bubbles: true,\n          detail: {\n            cart: cartContent,\n            openMiniCart: window.themeVariables.settings.cartType === \"drawer\" && this.closest(\".drawer\") === null\n          }\n        }));\n      });\n\n    }\n\n    /*----- Utilities ----- */\n\n    get getAllSelectedProducts() {\n      \n      return this.querySelectorAll('.quiz-results-product--selected');\n\n    }\n\n    get getDogSelectedProducts() {\n      \n      const dog = this.currentDog;\n      return dog.querySelectorAll('.quiz-results-product--selected');\n\n    }\n\n    get totalPriceAll() {\n\n    }\n\n    get totalPriceDog() {\n\n      \n\n    }\n\n    \n    /*----- Initiation ----- */\n\n    _addEventListeners() {\n\n      this.delegate.on(\"click\", \"[data-quiz-button-back]\", this.back.bind(this));\n      this.delegate.on(\"click\", \"[data-quiz-button-prev]\", this.prev.bind(this));\n      this.delegate.on(\"click\", \"[data-quiz-button-next]\", this.next.bind(this));\n      \n      this.delegate.on(\"click\", \"[data-quiz-button-checkout]\", this.onClickCheckout.bind(this));\n      \n      this.addEventListener(\"quiz:quiz-results:next-dog:in-transition\", function(){\n\n        console.log('onNextDogTransition');\n        console.log(this.currentDog);\n\n        this._updateDogProgress();\n        this._updateNavigation();\n\n        this.stickyForm.updateDog(event.detail.dog);\n\n        this.subNavigationTabs.reset();\n        this.subNavigationTabs.activate(this.currentDog.name);\n\n      }.bind(this));\n      \n      this.addEventListener(\"quiz:quiz-results:next-dog:start\", function(){\n\n        // console.log('onNextDogStart');\n        // this.calculator.updateSummary();\n        // this.calculator.updateProduct();\n\n      }.bind(this));\n\n      this.addEventListener(\"quiz:quiz-results:next-dog:in-transition\", function () {\n\n\n        this.stickyForm.updateDog();\n        this.stickyForm.updatePrice();\n\n        this.calculator.updateSummary();\n        this.calculator.updateProduct();\n\n      }.bind(this));\n\n      this.delegate.on(\"click\", \"quiz-step-tab\", this._onClickSubstepTab.bind(this));\n\n      this.addEventListener(\"quiz:quiz-results-product:change\", function(event) {\n        \n        this.stickyForm.updateDog();\n        this.stickyForm.updatePrice();\n        \n        this.calculator.updateProduct();\n\n      }.bind(this));\n\n      document.documentElement.addEventListener(\"cart:open\", function() {\n        this.progressBar.progress(1)\n      }.bind(this));\n\n      document.documentElement.addEventListener(\"cart:close\", function() {\n        this._updateProgressBar();\n      }.bind(this));\n\n    }\n\n  }\n\n  var getPossessive = function(name) {\n\n    if (!name || typeof name != \"string\") {\n      return;\n    }\n\n    if (name.slice(-1) == \"s\") {\n      name = name + \"'\";\n    }\n    else {\n      name = name + \"'s\";\n    }\n\n    return name;\n\n  }\n\n  var QuizResultsStickyForm = class extends CustomHTMLElement {\n\n    constructor() {\n      super();\n      if (Shopify.designMode) {\n        // this.rootDelegate.on(\"shopify:section:deselect\", (event) => filterShopifyEvent(event, this, () => this.open = false));\n      }\n    }\n\n    connectedCallback() {\n      this.dogNameElement = this.querySelector(\"[data-dog-name-possessive]\");\n      this.dogNameElement2 = this.querySelector(\"[data-dog-name]\");\n    }\n\n    updatePrice(dog) {\n\n      const quizResultsContainer = document.querySelector('quiz-results');\n      const currentDog = (dog) ? dog : quizResultsContainer.currentDog;\n      const totalPrice = currentDog.totalSelectedProductsPrice();\n      const boxPriceElement = this.querySelector('[data-box-price]');\n      const totalPriceFormatted = formatMoney(totalPrice);\n\n      boxPriceElement.innerHTML = totalPriceFormatted;\n\n    }\n\n    calculateTotalPrice() {\n\n      let price = 0;\n\n      const quizResultsContainer = document.querySelector('quiz-results');\n      const dog = quizResultsContainer.currentDog;\n      const selectedProducts = dog.querySelectorAll('.quiz-results-product--selected');\n\n      return price;\n\n    }\n\n    updateDog(dog) {\n\n      let dogName;\n\n      const activeDogStep = document.querySelector('.quiz-results-dog--active');\n\n      if (!dog) {\n        dogName = activeDogStep.dataset.dogName;\n      }\n      else {\n        dogName = dog.name;\n      }\n\n      let dogNamePossessive = getPossessive(dogName);\n\n      this.dogNameElement.innerText = dogNamePossessive;\n      this.dogNameElement2.innerText = dogName;\n\n    }\n\n    get quizResultsDogs() {\n      return document.querySelector(\"quiz-results-dogs\");\n    }\n\n    get quizResults() {\n      return document.querySelector(\"quiz-results\");\n    }\n\n  }\n\n  var QuizResultsDogs = class extends CustomHTMLElement {\n\n    constructor() {\n      super();\n      if (Shopify.designMode) {\n        // this.rootDelegate.on(\"shopify:section:deselect\", (event) => filterShopifyEvent(event, this, () => this.open = false));\n      }\n    }\n\n    connectedCallback() {\n\n\n\n    }\n\n  }\n  \n  var QuizResultsDog = class extends CustomHTMLElement {\n\n    constructor() {\n      super();\n      if (Shopify.designMode) {\n        // this.rootDelegate.on(\"shopify:section:deselect\", (event) => filterShopifyEvent(event, this, () => this.open = false));\n      }\n    }\n\n    get selectedProductsNumber() {\n      return this.querySelectorAll('.quiz-results-product--selected').length;\n    }\n\n    get selectedProductsLimit() {\n      const limit = this.getAttribute(\"limit\") ? Number(this.getAttribute(\"limit\")) : 0\n      return limit;\n    }\n\n    get productsList() {\n      return this.querySelector(\"quiz-results-products\");\n    }\n\n    get tab() {\n\n      const QuizResults = document.querySelector('quiz-results');\n      \n      return QuizResults.subNavigationTabs.querySelector(`quiz-step-tab[data-dog-name=\"${this.name}\"]`);\n\n    }\n    \n    get name() {\n      return this.dataset.dogName;\n    }\n\n    get quizData() {\n\n      if (!localStorage.getItem(\"dogData\")) {\n        return false;\n      }\n\n      let storage = JSON.parse(localStorage.getItem(\"dogData\"));\n\n      if (Shopify.designMode) {\n        data = defaultDogData;\n      }\n\n      const dogs = storage.dogs;\n\n      for (let i = 0; i < dogs.length; i++) {\n        const dog = dogs[i];\n        if (dog.name === this.name) {\n          return dog;\n        }\n      }\n\n    }\n\n    connectedCallback() {\n\n      this._addEventListeners();\n\n    }\n\n    totalSelectedProductsPrice() {\n\n      // Calculate Price\n\n      const selectedProducts = this.querySelectorAll('.quiz-results-product--selected');\n\n      let totalPrice = 0;\n\n      for (const product in selectedProducts) {\n\n        if (Object.hasOwnProperty.call(selectedProducts, product)) {\n\n          const element = selectedProducts[product];\n          const data = element.data;\n          \n          let quantity = 1;\n\n          const transitionCalories = calculateTransitionCalories(this.quizData);\n          const productCalories = element.calories;\n\n          quantity = calculatePacketQuantity((transitionCalories / productCalories));\n\n          totalPrice += data.price * quantity;\n          \n        }\n\n      }\n\n      return totalPrice;\n\n    }\n\n    _addEventListeners() {\n\n      this.addEventListener(\"quiz:quiz-results-product:change\", this._onProductChange.bind(this));\n\n    }\n\n    _onProductChange(event) {\n\n      // const product = event.target.closest(\"quiz-results-product\");\n      // const dog = product.closest(\"quiz-results-dog\");\n      // const dogs = dog.closest(\"quiz-results-dogs\");\n\n      this._checkLimit();\n\n    }\n\n    _checkLimit() {\n\n      if (this.selectedProductsNumber >= this.selectedProductsLimit) {\n        this.productsList.disable();\n      }\n      else {\n        this.productsList.enable();\n      }\n\n    }\n\n  }\n  \n  var QuizLoadingOverlay = class extends CustomHTMLElement {\n\n    constructor() {\n      super();\n      if (Shopify.designMode) {\n        // this.rootDelegate.on(\"shopify:section:deselect\", (event) => filterShopifyEvent(event, this, () => this.open = false));\n      }\n    }\n\n    connectedCallback() {\n\n    }\n\n    async hide() {\n\n      this.classList.add(\"quiz-loading-overlay--animating\");\n\n      const animation = new CustomAnimation(new CustomKeyframeEffect(this,\n        {\n          visibility: [\"visible\", \"hidden\"],\n          opacity: [\"1\", \"0\"]\n        },\n        {\n          duration: 1000,\n          easing: \"cubic-bezier(0.23, 1, 0.32, 1)\"\n        }\n      ));\n\n      animation.play();\n\n      await animation.finished;\n\n      this.classList.add(\"quiz-loading-overlay--hidden\");\n\n      this.classList.remove(\"quiz-loading-overlay--visible\");\n      this.classList.remove(\"quiz-loading-overlay--animating\");\n\n    }\n\n    async show() {\n\n      this.classList.add(\"quiz-loading-overlay--animating\");\n\n      const animation = new CustomAnimation(new CustomKeyframeEffect(this,\n        {\n          visibility: [\"hidden\", \"visible\"],\n          opacity: [\"0\", \"1\"]\n        },\n        {\n          duration: 1000,\n          easing: \"cubic-bezier(0.23, 1, 0.32, 1)\"\n        }\n      ));\n\n      animation.play();\n\n      await animation.finished;\n\n      this.classList.add(\"quiz-loading-overlay--hidden\");\n\n      this.classList.remove(\"quiz-loading-overlay--visible\");\n      this.classList.remove(\"quiz-loading-overlay--animating\");\n\n    }\n\n  }\n\n  // js/custom-element/section/gallery/quiz-results-products.js\n  var QuizResultsProducts = class extends CustomHTMLElement {\n    connectedCallback() {\n\n      this.listItems = Array.from(this.querySelectorAll(\"quiz-results-product\"));\n      this.scrollBarElement = this.querySelector(\".gallery__progress-bar\");\n      this.listWrapperElement = this.querySelector(\".gallery__list-wrapper\");\n\n      if (this.listItems.length > 1) {\n        this.addEventListener(\"scrollable-content:progress\", this._updateProgressBar.bind(this));\n        this.addEventListener(\"prev-next:prev\", this.previous.bind(this));\n        this.addEventListener(\"prev-next:next\", this.next.bind(this));\n        if (Shopify.designMode) {\n          this.addEventListener(\"shopify:block:select\", (event) => this.select(event.target.index, !event.detail.load));\n        }\n      }\n\n    }\n\n    previous() {\n      this.select([...this.listItems].reverse().find((item) => item.isOnLeftHalfPartOfScreen).index);\n    }\n    next() {\n      this.select(this.listItems.findIndex((item) => item.isOnRightHalfPartOfScreen));\n    }\n    select(index, animate = true) {\n      const boundingRect = this.listItems[index].getBoundingClientRect();\n      this.listWrapperElement.scrollBy({\n        behavior: animate ? \"smooth\" : \"auto\",\n        left: Math.floor(boundingRect.left - window.innerWidth / 2 + boundingRect.width / 2)\n      });\n    }\n    _updateProgressBar(event) {\n      var _a;\n      (_a = this.scrollBarElement) == null ? void 0 : _a.style.setProperty(\"--transform\", `${event.detail.progress}%`);\n    }\n\n    // Custom\n\n    enable() {\n      this.classList.remove(\"quiz-results-products--disabled\");\n    }\n\n    disable() {\n      this.classList.add(\"quiz-results-products--disabled\");\n    }\n\n  };\n  \n\n  // js/custom-element/section/gallery/quiz-results-product.js\n  var QuizResultsProduct = class extends CustomHTMLElement {\n    \n    connectedCallback() {\n      \n      this._addEventListeners();\n\n    }\n\n    _onClickSelect(event) {\n\n      this.select();\n\n    }\n\n    _addEventListeners() {\n\n      this.delegate.on(\"click\", \"[data-quiz-results-product-checkbox]\", this._onClickSelect.bind(this));\n\n    }\n\n    select() {\n\n      const selected = this.classList.contains('quiz-results-product--selected');\n\n      if (selected == false) {\n        \n        this.classList.add('quiz-results-product--selected');\n        this.buttonLabel.innerText = window.quizVariables.components.quizResultsProduct.selected;\n\n        triggerEvent(this, \"quiz:quiz-results-product:selected\");\n\n      }\n      else {\n        \n        this.classList.remove('quiz-results-product--selected');\n        this.buttonLabel.innerText = window.quizVariables.components.quizResultsProduct.add_to_cart;\n\n        triggerEvent(this, \"quiz:quiz-results-product:deselected\");\n\n      }\n\n      triggerEvent(this, \"quiz:quiz-results-product:change\");\n\n    }\n\n    // Data\n\n    get data() {\n      return JSON.parse(this.dataset.variantData);\n    }\n\n    get getPrice () {\n      return JSON.parse(this.dataset.variantData).price;\n    }\n    \n    get calories () {\n      return this.dataset.calories;\n    }\n\n    // UI Elements\n\n    get button() {\n      return this.querySelector('[data-quiz-results-product-checkbox]');\n    }\n\n    get buttonLabel() {\n      return this.querySelector('.quiz-results-product__checkbox-label');\n    }\n\n    get index() {\n      return [...this.parentNode.children].indexOf(this);\n    }\n\n    get isOnRightHalfPartOfScreen() {\n      if (window.themeVariables.settings.direction === \"ltr\") {\n        return this.getBoundingClientRect().left > window.innerWidth / 2;\n      } else {\n        return this.getBoundingClientRect().right < window.innerWidth / 2;\n      }\n    }\n    get isOnLeftHalfPartOfScreen() {\n      if (window.themeVariables.settings.direction === \"ltr\") {\n        return this.getBoundingClientRect().right < window.innerWidth / 2;\n      } else {\n        return this.getBoundingClientRect().left > window.innerWidth / 2;\n      }\n    }\n  };\n\n  // js/custom-element/section/gallery/quiz-results-products.js\n  var QuizResultsProducts = class extends CustomHTMLElement {\n    connectedCallback() {\n\n      this.listItems = Array.from(this.querySelectorAll(\"quiz-results-product\"));\n      this.scrollBarElement = this.querySelector(\".gallery__progress-bar\");\n      this.listWrapperElement = this.querySelector(\".gallery__list-wrapper\");\n\n      if (this.listItems.length > 1) {\n        this.addEventListener(\"scrollable-content:progress\", this._updateProgressBar.bind(this));\n        this.addEventListener(\"prev-next:prev\", this.previous.bind(this));\n        this.addEventListener(\"prev-next:next\", this.next.bind(this));\n        if (Shopify.designMode) {\n          this.addEventListener(\"shopify:block:select\", (event) => this.select(event.target.index, !event.detail.load));\n        }\n      }\n\n    }\n\n    previous() {\n      this.select([...this.listItems].reverse().find((item) => item.isOnLeftHalfPartOfScreen).index);\n    }\n    next() {\n      this.select(this.listItems.findIndex((item) => item.isOnRightHalfPartOfScreen));\n    }\n    select(index, animate = true) {\n      const boundingRect = this.listItems[index].getBoundingClientRect();\n      this.listWrapperElement.scrollBy({\n        behavior: animate ? \"smooth\" : \"auto\",\n        left: Math.floor(boundingRect.left - window.innerWidth / 2 + boundingRect.width / 2)\n      });\n    }\n    _updateProgressBar(event) {\n      var _a;\n      (_a = this.scrollBarElement) == null ? void 0 : _a.style.setProperty(\"--transform\", `${event.detail.progress}%`);\n    }\n\n    // Custom\n\n    enable() {\n      this.classList.remove(\"quiz-results-products--disabled\");\n    }\n\n    disable() {\n      this.classList.add(\"quiz-results-products--disabled\");\n    }\n\n  };\n\n\n  // js/custom-element/section/gallery/quiz-results-product.js\n  var QuizFeedingCalculator = class extends CustomHTMLElement {\n\n    connectedCallback() {\n\n      this._addEventListeners();\n\n      this.quizResults = document.querySelector(\"quiz-results\");\n\n    }\n\n    _addEventListeners() {\n\n      // this.delegate.on(\"click\", \"[data-quiz-results-product-checkbox]\", this._onClickSelect.bind(this));\n\n    }\n\n    updateSummary() {\n\n      const quizDog = document.querySelector(\"quiz-results\").currentDog;\n      const dog = quizDog.quizData;\n\n      // const labelProductName = this.querySelector('[data-calculator-product-name]');\n      const labelsDogName = this.querySelectorAll('[data-calculator-dog-name]');\n      const labelDogWeight = this.querySelector('[data-calculator-dog-weight]');\n      const labelDogAgeNumber = this.querySelector('[data-calculator-dog-age-number]');\n      const labelDogAgeType = this.querySelector('[data-calculator-dog-age-type]');\n      const labelDogActivity = this.querySelector('[data-calculator-dog-activity]');\n      const labelDogCondition = this.querySelector('[data-calculator-condition]');\n      const labelDogPronoun = this.querySelector('[data-calculator-dog-pronoun-possessive]');\n\n      // const valueProductName = getProductName();\n      const valueDogName = dog.name;\n      const valueDogWeight = dog.weight;\n      const valueDogCondition = getDogCondition(dog.prescription_diet);\n      const valueDogAgeNumber = getDogAgeNumber(dog.age_in_months);\n      const valueDogAgeType = getDogAgeType(dog.age_in_months);\n      const valueDogActivity = getDogActivity(dog.activity_level);\n      const valueDogPronoun = dog.sex === \"Male\" ? \"him\" : \"her\";\n      \n      // labelProductName.innerText = valueProductName;\n      labelDogWeight.innerText = valueDogWeight;\n      labelDogAgeNumber.innerText = valueDogAgeNumber;\n      labelDogAgeType.innerText = valueDogAgeType;\n      labelDogActivity.innerText = valueDogActivity;\n      labelDogCondition.innerText = valueDogCondition;\n      labelDogPronoun.innerText = valueDogPronoun;\n\n      labelsDogName.forEach(element => {\n        element.innerText = valueDogName;\n      });\n\n      function getDogActivity(value) {\n\n        let activity;\n\n        if (value == \"Low\") {\n          activity = \"not very active\"\n        }\n        else\n        if (value == \"Normal\") {\n          activity = \"moderately active\"\n        }\n        else\n        if (value == \"High\") {\n          activity = \"highly active\"\n        }\n\n        return activity;\n\n      }\n\n      function getDogCondition(value) {\n\n        let condition;\n\n        if (value == \"None\") {\n          condition = \"no prescription diet\"\n        }\n        else {\n          condition = \"a \" + value + \" diet\";\n        }\n\n        return condition;\n\n      }\n\n      function getDogAgeNumber(ageInMonths) {\n\n        let ageNumber;\n\n        if (ageInMonths < 12) {\n          ageNumber = ageInMonths;\n        }\n        else {\n          ageNumber = Math.floor(ageInMonths / 12);\n        }\n\n        return ageNumber;\n      }\n\n      function getDogAgeType(ageInMonths) {\n\n        return ageInMonths > 11 ? \"years\" : \"months\";\n\n      }\n\n    }\n\n    updateProduct(data) {\n\n      const quizDog = document.querySelector(\"quiz-results\").currentDog;\n      const dog = quizDog.quizData;\n\n      const dogProducts = document.querySelector('.quiz-results-dog--active');\n      const product = !dogProducts.querySelector('.quiz-results-product--selected') ? dogProducts.querySelector('.quiz-results-product') : dogProducts.querySelector('.quiz-results-product--selected');\n      const productJson = JSON.parse(product.dataset.productJson);\n\n      const daysInBox = window.quizVariables.boxes.days_in_box;\n      const weeksInBox = daysInBox / 7;\n\n      console.log(productJson);\n\n      // Calculate Calories\n\n      const totalCaloriesPerDay = calculateKCalPerDay(dog);\n\n      const caloriesPeriod1 = totalCaloriesPerDay * 0.25;\n      const caloriesPeriod2 = totalCaloriesPerDay * 0.5;\n      const caloriesPeriod3 = totalCaloriesPerDay * 0.75;\n      const caloriesPeriod4 = totalCaloriesPerDay * 1;\n\n      const productCalories = product.calories;\n      const caloriesPerOunce = productCalories / 16;\n      const caloriesPerCup = Number(productJson.cup_calories.value);\n\n      const ouncesPeriod1 = Math.round(caloriesPeriod1 / caloriesPerOunce);\n      const ouncesPeriod2 = Math.round(caloriesPeriod2 / caloriesPerOunce);\n      const ouncesPeriod3 = Math.round(caloriesPeriod3 / caloriesPerOunce);\n      const ouncesPeriod4 = Math.round(caloriesPeriod4 / caloriesPerOunce);\n\n      const cupsPeriod1 = caloriesPeriod1 / caloriesPerCup;\n      const cupsPeriod2 = caloriesPeriod2 / caloriesPerCup;\n      const cupsPeriod3 = caloriesPeriod3 / caloriesPerCup;\n      const cupsPeriod4 = caloriesPeriod4 / caloriesPerCup;\n\n      const transitionCalories = calculateTransitionCalories(dog);\n\n      const quantity16Oz = calculatePacketQuantity((transitionCalories / productCalories));\n      \n      const totalOunces = quantity16Oz * 16;\n      const totalCups = transitionCalories / caloriesPerCup;\n\n      const totalOuncesPerDay = totalOunces / daysInBox;\n      const totalCupsPerDay = totalCups / daysInBox;\n\n      console.log(`\n\n        ====================\n        CALORIE CALCULATIONS\n        ====================\n\n        DOG\n        \n        Total Calories Needed Per Day - ${totalCaloriesPerDay}\n        \n        --------------------\n        \n        BOX\n        \n        Days Per Box - ${daysInBox}\n        Weeks Per Box - ${weeksInBox}\n\n        --------------------\n        \n        CALORIES\n        TRANSITION CALORIES - ${transitionCalories}\n\n        DAY 1-3 - ${caloriesPeriod1}\n        DAY 4-6 - ${caloriesPeriod2}\n        DAY 7-9 - ${caloriesPeriod3}\n        DAY 10+ - ${caloriesPeriod4}\n\n        --------------------\n        \n        OUNCES\n        \n        Period 1 Ounces - ${ouncesPeriod1}\n        Period 2 Ounces - ${ouncesPeriod2}\n        Period 3 Ounces - ${ouncesPeriod3}\n        Period 4 Ounces - ${ouncesPeriod4}\n\n        TOTAL OUNCES - ${totalOunces}\n\n        --------------------\n\n        CUPS\n        \n        Period 1 Cups - ${cupsPeriod1}\n        Period 2 Cups - ${cupsPeriod2}\n        Period 3 Cups - ${cupsPeriod3}\n        Period 4 Cups - ${cupsPeriod4}\n\n        Total Cups - ${totalCups}\n\n        --------------------\n\n        Calories in 16Oz - ${productCalories}\n        Quantity of 16Oz - ${quantity16Oz}\n\n        --------------------\n      \n      `);\n\n      // Calculate Calories\n\n      const labelProductName = this.querySelector('[data-calculator-product-name]');\n\n      const labelTotalOunces = this.querySelector('[data-calculator-total-ounces]');\n      const labelTotalCups = this.querySelector('[data-calculator-total-cups]');\n      const labelPeriod1Ounces = this.querySelector('[data-calculator-period-1]').querySelector('[data-ounces]')\n      const labelPeriod1Cups = this.querySelector('[data-calculator-period-1]').querySelector('[data-cups]')\n      const labelPeriod2Ounces = this.querySelector('[data-calculator-period-2]').querySelector('[data-ounces]')\n      const labelPeriod2Cups = this.querySelector('[data-calculator-period-2]').querySelector('[data-cups]')\n      const labelPeriod3Ounces = this.querySelector('[data-calculator-period-3]').querySelector('[data-ounces]')\n      const labelPeriod3Cups = this.querySelector('[data-calculator-period-3]').querySelector('[data-cups]')\n      const labelPeriod4Ounces = this.querySelector('[data-calculator-period-4]').querySelector('[data-ounces]')\n      const labelPeriod4Cups = this.querySelector('[data-calculator-period-4]').querySelector('[data-cups]')\n\n      const valueProductName = getProductName();\n      const valueTotalOunces = fractionQuarters(totalOuncesPerDay);\n      const valueTotalCups = fractionQuarters(totalCupsPerDay);\n      const valuePeriod1Ounces = fractionQuarters(ouncesPeriod1);\n      const valuePeriod1Cups = fractionQuarters(cupsPeriod1);\n      const valuePeriod2Ounces = fractionQuarters(ouncesPeriod2);\n      const valuePeriod2Cups = fractionQuarters(cupsPeriod2);\n      const valuePeriod3Ounces = fractionQuarters(ouncesPeriod3);\n      const valuePeriod3Cups = fractionQuarters(cupsPeriod3);\n      const valuePeriod4Ounces = fractionQuarters(ouncesPeriod4);\n      const valuePeriod4Cups = fractionQuarters(cupsPeriod4);\n\n      labelProductName.innerHTML = valueProductName;\n\n      labelTotalOunces.innerHTML = valueTotalOunces;\n      labelTotalCups.innerHTML = valueTotalCups;\n      labelPeriod1Ounces.innerHTML = valuePeriod1Ounces;\n      labelPeriod1Cups.innerHTML = valuePeriod1Cups;\n      labelPeriod2Ounces.innerHTML = valuePeriod2Ounces;\n      labelPeriod2Cups.innerHTML = valuePeriod2Cups;\n      labelPeriod3Ounces.innerHTML = valuePeriod3Ounces;\n      labelPeriod3Cups.innerHTML = valuePeriod3Cups;\n      labelPeriod4Ounces.innerHTML = valuePeriod4Ounces;\n      labelPeriod4Cups.innerHTML = valuePeriod4Cups;\n\n      function getProductName() {\n\n        let name;\n\n        name = product.querySelector('.heading').innerHTML;\n\n        return name;\n\n      }\n\n    }\n\n  };\n\n  var QuizSubNavigation = class extends CustomHTMLElement {\n\n    constructor() {\n      super();\n      if (Shopify.designMode) {\n        // this.rootDelegate.on(\"shopify:section:deselect\", (event) => filterShopifyEvent(event, this, () => this.open = false));\n      }\n    }\n\n    connectedCallback() {\n\n    }\n\n  }\n\n  var QuizSubNavigationTabs = class extends CustomHTMLElement {\n\n    constructor() {\n      super();\n      if (Shopify.designMode) {\n        // this.rootDelegate.on(\"shopify:section:deselect\", (event) => filterShopifyEvent(event, this, () => this.open = false));\n      }\n    }\n\n    connectedCallback() {\n\n    }\n\n    get allTabs() {\n      return this.querySelectorAll(\"quiz-step-tab\");\n    }\n\n    activate(label) {\n\n      if (!label || typeof label != 'string') {\n        return;\n      }\n\n      const tab = this.querySelector(`[data-name=\"${label}\"]`);\n\n      if (tab) {\n        tab.classList.add(\"active\");\n      }\n\n    }\n\n    reset() {\n\n      this.allTabs.forEach(element => {\n        element.classList.remove(\"active\");\n      });\n\n    }\n\n  }\n  \n  window.customElements.define(\"revealing-form-input\", RevealingFormInput);\n  window.customElements.define(\"revealing-form\", RevealingForm);\n  window.customElements.define(\"split-page-step\", SplitPageStep);\n  window.customElements.define(\"quiz-steps\", QuizSteps);\n  window.customElements.define(\"expanding-input\", ExpandingInput);\n\n  window.customElements.define(\"quiz-customer-forms\", QuizCustomerForms);\n  window.customElements.define(\"quiz-customer-register-form\", QuizCustomerRegisterForm);\n  window.customElements.define(\"quiz-step\", QuizStep);\n  window.customElements.define(\"quiz-step-line\", QuizStepLine);\n  window.customElements.define(\"quiz-tiles\", QuizTiles);\n  window.customElements.define(\"quiz-tile\", QuizTile);\n  window.customElements.define(\"quiz-step-tab\", QuizStepTab);\n  window.customElements.define(\"quiz-progress-bar\", QuizProgressBar);\n  window.customElements.define(\"quiz-loading-overlay\", QuizLoadingOverlay);\n\n  window.customElements.define(\"quiz-sticky-form\", QuizResultsStickyForm);\n  window.customElements.define(\"quiz-results\", QuizResults);\n  window.customElements.define(\"quiz-results-dogs\", QuizResultsDogs);\n  window.customElements.define(\"quiz-results-dog\", QuizResultsDog);\n  window.customElements.define(\"quiz-results-products\", QuizResultsProducts);\n  window.customElements.define(\"quiz-results-product\", QuizResultsProduct);\n  window.customElements.define(\"quiz-feeding-calculator\", QuizFeedingCalculator);\n\n  window.customElements.define(\"quiz-sub-navigation\", QuizSubNavigation);\n  window.customElements.define(\"quiz-dog-navigation\", QuizSubNavigationTabs);\n\n  var QuizStepDogNumber = class extends CustomHTMLElement {\n\n    constructor() {\n      super();\n      if (Shopify.designMode) {\n        // this.rootDelegate.on(\"shopify:section:deselect\", (event) => filterShopifyEvent(event, this, () => this.open = false));\n      }\n    }\n\n    connectedCallback() {\n\n      this.number = 0;\n\n      this.quizSteps = this.closest(\"quiz-steps\");\n      this.dogNamesContainer = this.querySelector(\"#dog-names\");\n      this.dogNumberInput = this.querySelector(\"#dog-number\").closest(\"expanding-input\");\n      \n      this.dogNumberInput.addEventListener(\"quiz:expanding-input:change\", this._onNumberChanged.bind(this));\n\n    }\n\n    _onNumberChanged(event) {\n\n      let expandingInput = event.target;\n      let input = event.target.querySelector(\"input\");\n      let number = input.value;\n\n      this.number = Number(number);\n\n      this._updateLine();\n\n    }\n\n    _updateLine() {\n\n      // Update Inputs\n\n      if (this.dogNamesContainer.querySelectorAll(\"expanding-input\").length == this.number || this.number <= 0) {\n        return;\n      }\n\n      while (this.dogNamesContainer.querySelectorAll(\"expanding-input\").length != this.number) {\n        if (this.dogNamesContainer.querySelectorAll(\"expanding-input\").length > this.number) {\n\n          this.dogNamesContainer.querySelectorAll(\"expanding-input\")[this.dogNamesContainer.querySelectorAll(\"expanding-input\").length - 1].remove();\n          this.dogNamesContainer.querySelectorAll(\".quiz-step-line__text\")[this.dogNamesContainer.querySelectorAll(\".quiz-step-line__text\").length - 1].remove();\n\n        }\n        else {\n\n          this.dogNamesContainer.insertAdjacentHTML('beforeend', Templates.expandingInput({\n            id: `dog-name-${this.dogNamesContainer.querySelectorAll(\"expanding-input\").length + 1}`,\n            name: `dog-name-${this.dogNamesContainer.querySelectorAll(\"expanding-input\").length + 1}`,\n            placeholder: \"Pet's Name\",\n          }));\n\n          this.dogNamesContainer.insertAdjacentHTML('beforeend', Templates.quizStepLineText({ placeholder: \".\" }));\n\n        }\n      }\n\n      // Update Text Lines\n\n      for (let i = 0; i < this.dogNamesContainer.querySelectorAll(\".quiz-step-line__text\").length; i++) {\n        const element = this.dogNamesContainer.querySelectorAll(\".quiz-step-line__text\")[i];\n        if (i == 0) {\n          element.innerText = \"named\";\n        }\n        else\n          if (i == this.dogNamesContainer.querySelectorAll(\".quiz-step-line__text\").length - 1) {\n            element.innerText = \".\";\n          }\n          else\n            if (i == this.dogNamesContainer.querySelectorAll(\".quiz-step-line__text\").length - 2) {\n              element.innerText = \"&\";\n            }\n            else {\n              element.innerText = \",\";\n            }\n      }\n\n    }\n\n  }\n\n  var QuizStepDogSubstep = class extends CustomHTMLElement {\n\n    constructor() {\n      super();\n      if (Shopify.designMode) {\n        // this.rootDelegate.on(\"shopify:section:deselect\", (event) => filterShopifyEvent(event, this, () => this.open = false));\n      }\n    }\n\n    connectedCallback() {\n\n    }\n\n  }\n\n  var QuizStepDogDetails = class extends QuizStepDogSubstep {\n\n    constructor() {\n      super();\n      if (Shopify.designMode) {\n        // this.rootDelegate.on(\"shopify:section:deselect\", (event) => filterShopifyEvent(event, this, () => this.open = false));\n      }\n    }\n\n    connectedCallback() {\n      \n    }\n\n  }\n\n  var QuizStepDogGeneral = class extends QuizStepDogSubstep {\n\n    constructor() {\n      super();\n      if (Shopify.designMode) {\n        // this.rootDelegate.on(\"shopify:section:deselect\", (event) => filterShopifyEvent(event, this, () => this.open = false));\n      }\n    }\n\n    connectedCallback() {\n    \n    }\n\n  }\n\n  var QuizStepDogActivity = class extends QuizStepDogSubstep {\n\n    constructor() {\n      super();\n      if (Shopify.designMode) {\n        // this.rootDelegate.on(\"shopify:section:deselect\", (event) => filterShopifyEvent(event, this, () => this.open = false));\n      }\n    }\n\n    connectedCallback() {\n    \n    }\n\n  }\n\n  var QuizStepDogWeightProfile = class extends QuizStepDogSubstep {\n\n    constructor() {\n      super();\n      if (Shopify.designMode) {\n        // this.rootDelegate.on(\"shopify:section:deselect\", (event) => filterShopifyEvent(event, this, () => this.open = false));\n      }\n    }\n\n    connectedCallback() {\n    \n    }\n\n  }\n\n  var QuizStepDogWeightDetails = class extends QuizStepDogSubstep {\n\n    constructor() {\n      super();\n      if (Shopify.designMode) {\n        // this.rootDelegate.on(\"shopify:section:deselect\", (event) => filterShopifyEvent(event, this, () => this.open = false));\n      }\n    }\n\n    connectedCallback() {\n    \n    }\n\n  }\n\n  var QuizStepDogHealth = class extends QuizStepDogSubstep {\n\n    constructor() {\n      super();\n      if (Shopify.designMode) {\n        // this.rootDelegate.on(\"shopify:section:deselect\", (event) => filterShopifyEvent(event, this, () => this.open = false));\n      }\n    }\n\n    connectedCallback() {\n      \n    }\n\n  }\n\n  var StyledSelect = class extends CustomHTMLElement {\n\n    constructor() {\n      super();\n      if (Shopify.designMode) {\n        // this.rootDelegate.on(\"shopify:section:deselect\", (event) => filterShopifyEvent(event, this, () => this.open = false));\n      }\n    }\n\n    connectedCallback() {\n\n      this.select = this.querySelector(\"select\");\n\n      // console.log(this.select);\n\n      let options = {};\n\n      options.searchable = this.hasAttribute(\"searchable\") ? true : false;\n      \n      this.niceselect = NiceSelect.bind(this.select, options);\n\n    }\n\n    update() {\n      this.niceselect.update();\n    }\n\n  }\n\n  window.customElements.define(\"quiz-step-dog-number\", QuizStepDogNumber);\n\n  window.customElements.define(\"quiz-step-dog-substep\", QuizStepDogSubstep);\n  window.customElements.define(\"quiz-step-dog-details\", QuizStepDogDetails);\n\n  window.customElements.define(\"quiz-step-dog-general\", QuizStepDogGeneral);\n  window.customElements.define(\"quiz-step-dog-activity\", QuizStepDogActivity);\n  window.customElements.define(\"quiz-step-dog-weight-profile\", QuizStepDogWeightProfile);\n  window.customElements.define(\"quiz-step-dog-weight-details\", QuizStepDogWeightDetails);\n  window.customElements.define(\"quiz-step-dog-health\", QuizStepDogHealth);\n\n  document.addEventListener(\"DOMContentLoaded\", (e) => {\n    window.customElements.define(\"styled-select\", StyledSelect);\n  });\n  \n  // js/components/input-binding-manager.js\n  var InvisibleInputBindingManager = class {\n\n    constructor() {\n\n      this.delegateElement = new main_default(document.body);\n      this.delegateElement.on(\"input\", \"[data-bound-hidden-input]\", this._onValueChanged.bind(this));\n      this.delegateElement.on(\"change\", \"[data-bound-hidden-input]\", this._onChanged.bind(this));\n\n    }\n\n    _onValueChanged(event, target) {\n\n      const sourceInput = event.target;\n      const boundElement = document.getElementById(target.getAttribute(\"data-bound-hidden-input\"));\n\n      if (boundElement) {\n        if (target.tagName === \"SELECT\") {\n          target = target.options[target.selectedIndex];\n        }\n        boundElement.value = target.hasAttribute(\"title\") ? target.getAttribute(\"title\") : target.value;\n      }\n\n    }\n\n    _onChanged(event, target) {\n\n      const sourceInput = event.target;\n      const boundElement = document.getElementById(target.getAttribute(\"data-bound-hidden-radio\"));\n\n      if (boundElement) {\n        if (target.tagName === \"SELECT\") {\n          target = target.options[target.selectedIndex];\n        }\n        boundElement.value = target.hasAttribute(\"title\") ? target.getAttribute(\"title\") : target.value;\n      }\n\n    }\n\n  };\n\n  var FullNameBindingManager = class {\n\n    constructor() {\n\n      this.delegateElement = new main_default(document.body);\n      this.delegateElement.on(\"input\", \"[data-bound-split-name-input]\", this._onValueChanged.bind(this));\n\n    }\n\n    _onValueChanged(event, target) {\n\n      const boundElementFirstName = document.getElementById(target.getAttribute(\"data-bound-first-name-input\"));\n      const boundElementLastName = document.getElementById(target.getAttribute(\"data-bound-last-name-input\"));\n\n      const value = target.value;\n\n      let splitName = value.split(\" \");\n      let firstName;\n      let lastName;\n\n      if (value.indexOf(\" \") != -1) {\n        firstName = splitName[0];\n        lastName = splitName[splitName.length - 1] ? splitName[splitName.length - 1] : \"\";\n      }\n      else {\n        firstName = value;\n        lastName = \"\";\n      }\n\n      if (boundElementFirstName) {\n        boundElementFirstName.setAttribute(\"value\", firstName);\n      }\n\n      if (boundElementLastName) {\n        boundElementLastName.setAttribute(\"value\", lastName);\n      }\n\n    }\n\n  };\n\n\n  (() => {\n    new InvisibleInputBindingManager();\n    new FullNameBindingManager();\n  })();\n\n/*\n\nconst kycOptions = document.querySelectorAll('.kyc-option-radio');\n\nkycOptions.forEach(element => {\n  \n  element.addEventListener(\"change\", event => {\n\n    // Hide all other sub-options\n    document.querySelectorAll(\".input--sub-option\").forEach(e => {\n      e.value = \"\";\n      e.style = \"display: none;\";\n    });\n\n    // Show current selection's sub-options\n    const subOptionContainer = event.target.closest(\".input\").nextElementSibling;\n    const subOptionInput = subOptionContainer.querySelector(\"[data-sub-option-input]\");\n    const hiddenNoteInput = document.querySelector(\"[data-hidden-note-input]\");\n    \n    if (subOptionContainer.classList.contains(\"input--sub-option\")) {\n      subOptionContainer.style = \"display: block;\";\n      hiddenNoteInput.setAttribute(\"name\", \"\");\n\n      var event = new Event('input', {\n        bubbles: true,\n        cancelable: true,\n      });\n\n      subOptionInput.dispatchEvent(event);\n\n    }\n    else {\n      hiddenNoteInput.removeAttribute(\"name\");\n      hiddenNoteInput.removeAttribute(\"value\");\n    }\n\n  });\n\n});\n\n*/\n\n\nconst inputsWithSubOptions = document.querySelectorAll('[data-sub-option-container]');\n\ninputsWithSubOptions.forEach(element => {\n\n  element.addEventListener(\"change\", event => {\n\n    const input = event.target;\n    const subOptionContainerId = input.getAttribute('data-sub-option-container')\n    let subOptionContainer;\n\n    if (subOptionContainerId) {\n      subOptionContainer = document.getElementById(subOptionContainerId)\n    }\n\n    hideAllSubOptions();\n\n    if (!subOptionContainer) {\n      return;\n    }\n\n    subOptionContainer.style = \"display: block;\";\n\n  });\n\n});\n\nfunction hideAllSubOptions() {\n\n  // These are inputs, not containers.\n  document.querySelectorAll(\"[data-sub-option-container]\").forEach(e => {\n\n    const subOptionContainerId = e.getAttribute('data-sub-option-container')\n    const subOptionContainer = document.getElementById(subOptionContainerId); // This is the container.\n\n    if (!subOptionContainer) {\n      return;\n    }\n\n    subOptionContainer.value = \"\";\n    subOptionContainer.style = \"display: none;\";\n\n    const subOptionInputs = subOptionContainer.querySelectorAll(\"[data-sub-option-input]\");\n\n    if (subOptionInputs.length > 0) {\n      subOptionInputs.forEach(subOptionInput => {\n        \n        subOptionInput.value = \"\"; // Clears the visible form input.\n        subOptionInput.dispatchEvent(new Event('change')); // Clears the hidden form input.\n\n        if (subOptionInput.closest(\"styled-select\")) {\n          subOptionInput.closest(\"styled-select\").update(); // Updates the styled select.\n        }\n\n      });\n    }\n\n  });\n\n}\n\nconst inputSubOptions = document.querySelectorAll('[data-sub-option-input]');\n\ninputSubOptions.forEach(element => {\n\n  element.addEventListener(\"change\", event => {\n\n    const visibleNoteInput = event.target;\n    const hiddenNoteInput = document.querySelector(\"[data-hidden-note-input]\");\n\n    let newValue = visibleNoteInput.value.trim() == \"\" ? visibleNoteInput.value.trim() : visibleNoteInput.value.trim() + \",\";\n\n    const revealValue = element.getAttribute(\"data-sub-option-input-reveal-value\");\n    const revealInput = document.getElementById(element.getAttribute(\"data-sub-option-input-reveal-input\"));\n\n    if (revealValue && revealInput) {\n      if(element.value == revealValue) {\n        revealInput.classList.remove(\"hidden\");\n        revealInput.querySelector(\"input\").focus();\n      }\n      else {\n        revealInput.classList.add(\"hidden\");\n      }\n    }\n\n    hiddenNoteInput.setAttribute(\"name\", visibleNoteInput.getAttribute(\"name\"));\n    hiddenNoteInput.setAttribute(\"value\", newValue);\n\n  });\n\n});"]}