@charset "UTF-8";
/* 1. Variables */ /*
$site-width: 1600px;
$container-width: 1200px;
$container-narrow-width: 800px;
$container-extra-narrow-width: 600px;

$container-gutter--desktop: 24px;
$container-gutter--mobile: 24px;

$section-spacer--desktop: 50px;
$section-spacer--mobile: 25px;
*/
/*  ------------------------------
    Grid Variables
    ------------------------------ */
/**
 * Define your breakpoints. The first value is the prefix that shall be used for
 * your classes (e.g. `.small--one-half`), the second value is the media query
 * that the breakpoint fires at.
 */
/**
 * Define which namespaced breakpoints you would like to generate for each of
 * widths, push and pull. This is handy if you only need pull on, say, desk, or
 * you only need a new width breakpoint at mobile sizes. It allows you to only
 * compile as much CSS as you need. All are turned on by default, but you can
 * add and remove breakpoints at will.
 *
 * Push and pull shall only be used if `$push` and/or `$pull` and `$responsive`
 * have been set to ‘true’.
 */
/* 2. Mixins */
/*

  1. Utilities
  2. Responsive
  3. UI Elements
    3.1. Buttons

*/
/*  ==============================
    1. Utilities
    ============================== */
/*  ==============================
    2. Responsive
    ============================== */
/*================ Responsive Show/Hide Helper ================*/
/*================ Responsive Text Alignment Helper ================*/
/*  ==============================
    3. UI Elements
    ============================== */
/*  ------------------------------
    3.1. Buttons
    ------------------------------ */
/* ------------------------------
   Headings
   ------------------------------ */
/* ------------------------------
   Labels
   ------------------------------ */
/* ------------------------------
   Inputs
   ------------------------------ */
/* ------------------------------
   RTE
   ------------------------------ */
/*  ------------------------------
    3.3. Shopify
    ------------------------------ */
/* 3. Fonts  */
/* 4. Basic Styles */
/*  -----------------------------------
    Links
    ----------------------------------- */
.link--animated.link--animated--spaced:after {
  bottom: -0.25em;
}
.link--animated.link--animated--bold {
  font-weight: var(---font-weight-body--bold);
}
.link--animated.link--animated--bold:after {
  height: 2px;
}
.link--animated.link--animated--show-underline:after {
  transform: scaleX(1);
}
.link--animated.link--animated--show-underline:hover:after, .link--animated.link--animated--show-underline:focus:after {
  transform: scaleX(0);
}

h1, .h1,
h2, .h2, .jdgm-rev-widg__title, .jdgm-carousel-title,
h3, .h3,
h4, .h4 {
  line-height: var(---line-height-heading--mobile);
  letter-spacing: var(---letter-spacing-heading--mobile);
}

h1, .h1,
h2, .h2, .jdgm-rev-widg__title, .jdgm-carousel-title,
h3, .h3,
h4, .h4 {
  font-family: var(--heading-font-family);
  font-weight: var(--heading-font-weight);
  font-style: var(--heading-font-style);
  color: rgb(var(--heading-color));
  text-transform: var(--heading-text-transform);
  display: block;
  letter-spacing: var(---letter-spacing-heading--mobile);
  font-weight: var(---font-weight-heading);
}

.heading--large,
.rte .heading--large {
  font-family: var(---font-family-heading);
  font-style: var(---font-style-heading);
  font-weight: var(---font-weight-heading);
  font-variation-settings: "wght" 400;
  letter-spacing: var(---letter-spacing-heading--mobile);
  line-height: var(---line-height-heading--mobile);
  font-size: var(---font-size-h0--mobile);
}
.heading--large span.heading--alternate,
.rte .heading--large span.heading--alternate {
  color: var(---color-heading-1);
  text-transform: none;
  font-weight: var(---font-weight-heading);
}
.heading--large a,
.rte .heading--large a {
  color: var(---color-link);
}
@media only screen and (min-width: 741px) {
  .heading--large,
  .rte .heading--large {
    letter-spacing: var(---letter-spacing-heading--desktop);
    line-height: var(---line-height-heading--desktop);
  }
}
@media only screen and (min-width: 741px) {
  .heading--large,
  .rte .heading--large {
    font-size: var(---font-size-h0--desktop);
  }
}

h1, .h1,
.rte h1, .rte .h1 {
  font-family: var(---font-family-heading);
  font-style: var(---font-style-heading);
  font-weight: var(---font-weight-heading);
  font-variation-settings: "wght" 400;
  letter-spacing: var(---letter-spacing-heading--mobile);
  line-height: var(---line-height-heading--mobile);
  font-size: var(---font-size-h1--mobile);
}
h1 span.heading--alternate, .h1 span.heading--alternate,
.rte h1 span.heading--alternate, .rte .h1 span.heading--alternate {
  color: var(---color-heading-1);
  text-transform: none;
  font-weight: var(---font-weight-heading);
}
h1 a, .h1 a,
.rte h1 a, .rte .h1 a {
  color: var(---color-link);
}
@media only screen and (min-width: 741px) {
  h1, .h1,
  .rte h1, .rte .h1 {
    letter-spacing: var(---letter-spacing-heading--desktop);
    line-height: var(---line-height-heading--desktop);
  }
}
@media only screen and (min-width: 741px) {
  h1, .h1,
  .rte h1, .rte .h1 {
    font-size: var(---font-size-h1--desktop);
  }
}

h2, .h2, .jdgm-rev-widg__title, .jdgm-carousel-title,
.rte h2, .rte .h2 {
  font-family: var(---font-family-heading);
  font-style: var(---font-style-heading);
  font-weight: var(---font-weight-heading);
  font-variation-settings: "wght" 400;
  letter-spacing: var(---letter-spacing-heading--mobile);
  line-height: var(---line-height-heading--mobile);
  font-size: var(---font-size-h2--mobile);
}
h2 span.heading--alternate, .h2 span.heading--alternate, .jdgm-rev-widg__title span.heading--alternate, .jdgm-carousel-title span.heading--alternate,
.rte h2 span.heading--alternate, .rte .h2 span.heading--alternate {
  color: var(---color-heading-1);
  text-transform: none;
  font-weight: var(---font-weight-heading);
}
h2 a, .h2 a, .jdgm-rev-widg__title a, .jdgm-carousel-title a,
.rte h2 a, .rte .h2 a {
  color: var(---color-link);
}
@media only screen and (min-width: 741px) {
  h2, .h2, .jdgm-rev-widg__title, .jdgm-carousel-title,
  .rte h2, .rte .h2 {
    letter-spacing: var(---letter-spacing-heading--desktop);
    line-height: var(---line-height-heading--desktop);
  }
}
@media only screen and (min-width: 741px) {
  h2, .h2, .jdgm-rev-widg__title, .jdgm-carousel-title,
  .rte h2, .rte .h2 {
    font-size: var(---font-size-h2--desktop);
  }
}

h3, .h3,
.rte h3, .rte .h3 {
  font-family: var(---font-family-heading);
  font-style: var(---font-style-heading);
  font-weight: var(---font-weight-heading);
  font-variation-settings: "wght" 400;
  letter-spacing: var(---letter-spacing-heading--mobile);
  line-height: var(---line-height-heading--mobile);
  font-size: var(---font-size-h3--mobile);
}
h3 span.heading--alternate, .h3 span.heading--alternate,
.rte h3 span.heading--alternate, .rte .h3 span.heading--alternate {
  color: var(---color-heading-1);
  text-transform: none;
  font-weight: var(---font-weight-heading);
}
h3 a, .h3 a,
.rte h3 a, .rte .h3 a {
  color: var(---color-link);
}
@media only screen and (min-width: 741px) {
  h3, .h3,
  .rte h3, .rte .h3 {
    letter-spacing: var(---letter-spacing-heading--desktop);
    line-height: var(---line-height-heading--desktop);
  }
}
@media only screen and (min-width: 741px) {
  h3, .h3,
  .rte h3, .rte .h3 {
    font-size: var(---font-size-h3--desktop);
  }
}

h4, .h4,
.rte h4, .rte .h4 {
  font-family: var(---font-family-heading);
  font-style: var(---font-style-heading);
  font-weight: var(---font-weight-heading);
  font-variation-settings: "wght" 400;
  letter-spacing: var(---letter-spacing-heading--mobile);
  line-height: var(---line-height-heading--mobile);
  font-size: var(---font-size-h4--mobile);
}
h4 span.heading--alternate, .h4 span.heading--alternate,
.rte h4 span.heading--alternate, .rte .h4 span.heading--alternate {
  color: var(---color-heading-1);
  text-transform: none;
  font-weight: var(---font-weight-heading);
}
h4 a, .h4 a,
.rte h4 a, .rte .h4 a {
  color: var(---color-link);
}
@media only screen and (min-width: 741px) {
  h4, .h4,
  .rte h4, .rte .h4 {
    letter-spacing: var(---letter-spacing-heading--desktop);
    line-height: var(---line-height-heading--desktop);
  }
}
@media only screen and (min-width: 741px) {
  h4, .h4,
  .rte h4, .rte .h4 {
    font-size: var(---font-size-h4--desktop);
  }
}

h5, .h5,
.rte h5, .rte .h5 {
  font-family: var(---font-family-body);
  font-style: var(---font-style-body);
  font-weight: var(---font-weight-body--bold);
  letter-spacing: var(---letter-spacing-body--mobile);
  line-height: var(---line-height-body--mobile);
  font-size: var(---font-size-h5--mobile);
}
h5 span.heading--alternate, .h5 span.heading--alternate,
.rte h5 span.heading--alternate, .rte .h5 span.heading--alternate {
  color: var(---color-heading-2);
  text-transform: none;
  font-weight: var(---font-weight-body);
}
h5 a, .h5 a,
.rte h5 a, .rte .h5 a {
  color: var(---color-link);
}
@media only screen and (min-width: 741px) {
  h5, .h5,
  .rte h5, .rte .h5 {
    letter-spacing: var(---letter-spacing-heading--desktop);
    line-height: var(---line-height-heading--desktop);
  }
}
@media only screen and (min-width: 741px) {
  h5, .h5,
  .rte h5, .rte .h5 {
    font-size: var(---font-size-h5--desktop);
  }
}

h6, .h6,
.rte h6, .rte .h6 {
  font-family: var(---font-family-body);
  font-style: var(---font-style-body);
  font-weight: var(---font-weight-body--bold);
  letter-spacing: var(---letter-spacing-body--mobile);
  line-height: var(---line-height-body--mobile);
  font-size: var(---font-size-h6--mobile);
}
h6 span.heading--alternate, .h6 span.heading--alternate,
.rte h6 span.heading--alternate, .rte .h6 span.heading--alternate {
  color: var(---color-heading-2);
  text-transform: none;
  font-weight: var(---font-weight-body);
}
h6 a, .h6 a,
.rte h6 a, .rte .h6 a {
  color: var(---color-link);
}
@media only screen and (min-width: 741px) {
  h6, .h6,
  .rte h6, .rte .h6 {
    letter-spacing: var(---letter-spacing-heading--desktop);
    line-height: var(---line-height-heading--desktop);
  }
}
@media only screen and (min-width: 741px) {
  h6, .h6,
  .rte h6, .rte .h6 {
    font-size: var(---font-size-h6--desktop);
  }
}

.subheading {
  color: RGB(var(--subheading-color));
}

.heading.heading--regular {
  letter-spacing: var(---letter-spacing-subheading--mobile);
  text-transform: uppercase;
  font-style: var(---font-style-heading);
  font-weight: var(---font-weight-heading);
  font-family: var(---font-family-heading);
  font-size: var(---font-size-subheading-large--mobile);
}
@media only screen and (min-width: 741px) {
  .heading.heading--regular {
    font-size: var(---font-size-subheading-large--desktop);
  }
}

.product-sticky-form__title,
.heading.heading--small {
  letter-spacing: var(---letter-spacing-subheading--mobile);
  text-transform: uppercase;
  font-style: var(---font-style-heading);
  font-weight: var(---font-weight-heading);
  font-family: var(---font-family-heading);
  font-size: var(---font-size-subheading--mobile);
}
.product-sticky-form__title + p,
.product-sticky-form__title + .h1,
.product-sticky-form__title + h1,
.product-sticky-form__title + .h2,
.product-sticky-form__title + .jdgm-rev-widg__title,
.product-sticky-form__title + .jdgm-carousel-title,
.product-sticky-form__title + h2,
.product-sticky-form__title + .h3,
.product-sticky-form__title + h3,
.product-sticky-form__title + .h4,
.product-sticky-form__title + h4,
.heading.heading--small + p,
.heading.heading--small + .h1,
.heading.heading--small + h1,
.heading.heading--small + .h2,
.heading.heading--small + .jdgm-rev-widg__title,
.heading.heading--small + .jdgm-carousel-title,
.heading.heading--small + h2,
.heading.heading--small + .h3,
.heading.heading--small + h3,
.heading.heading--small + .h4,
.heading.heading--small + h4 {
  margin-top: 12px;
}
.product-sticky-form__title + hr,
.heading.heading--small + hr {
  margin-top: 0;
}
@media only screen and (min-width: 741px) {
  .product-sticky-form__title,
  .heading.heading--small {
    font-size: var(---font-size-subheading--desktop);
  }
}

.heading.heading--xsmall {
  letter-spacing: var(---letter-spacing-subheading--mobile);
  text-transform: uppercase;
  font-style: var(---font-style-heading);
  font-weight: var(---font-weight-heading);
  font-family: var(---font-family-heading);
  font-size: var(---font-size-subheading-small--mobile);
}
@media only screen and (min-width: 741px) {
  .heading.heading--xsmall {
    font-size: var(---font-size-subheading-small--desktop);
  }
}

body {
  line-height: var(---line-height-body--mobile);
}

.text--small {
  margin-top: 0;
  font-size: var(---font-size-body--mobile);
}
@media only screen and (min-width: 741px) {
  .text--small {
    font-size: var(---font-size-body--desktop);
  }
}

.text--xxsmall,
.tiny {
  font-size: var(---font-size-body-xs--mobile);
}
@media only screen and (min-width: 741px) {
  .text--xxsmall,
  .tiny {
    font-size: var(---font-size-body-xs--desktop);
  }
}

.text--xsmall,
.minor {
  font-size: var(---font-size-body-small--mobile);
}
@media only screen and (min-width: 741px) {
  .text--xsmall,
  .minor {
    font-size: var(---font-size-body-small--desktop);
  }
}

.text--large,
.major {
  font-size: var(---font-size-body-large--mobile);
}
@media only screen and (min-width: 741px) {
  .text--large,
  .major {
    font-size: var(---font-size-body-large--desktop);
  }
}

@media only screen and (max-width: 740px) {
  .p--mobile {
    font-size: var(---font-size-body--mobile);
    font-family: var(---font-family-body);
    font-weight: var(---font-weight-body);
  }
}

.text--xxsmall,
.tiny {
  font-size: var(---font-size-body-xs--mobile);
}
@media only screen and (min-width: 741px) {
  .text--xxsmall,
  .tiny {
    font-size: var(---font-size-body-xs--desktop);
  }
}
.text--xxsmall p,
.tiny p {
  font-size: var(---font-size-body-xs--mobile);
}
@media only screen and (min-width: 741px) {
  .text--xxsmall p,
  .tiny p {
    font-size: var(---font-size-body-xs--desktop);
  }
}

@media only screen and (max-width: 740px) {
  .text--xxsmall--mobile,
  .p-tiny--mobile {
    font-family: var(---font-family-body);
    font-weight: var(---font-weight-body);
    font-size: var(---font-size-body-xs--mobile);
  }
}

.text--xsmall,
.minor {
  font-size: var(---font-size-body-small--mobile);
}
@media only screen and (min-width: 741px) {
  .text--xsmall,
  .minor {
    font-size: var(---font-size-body-small--desktop);
  }
}
.text--xsmall p:not(.heading),
.minor p:not(.heading) {
  font-size: var(---font-size-body-small--mobile);
}
@media only screen and (min-width: 741px) {
  .text--xsmall p:not(.heading),
  .minor p:not(.heading) {
    font-size: var(---font-size-body-small--desktop);
  }
}

@media only screen and (max-width: 740px) {
  .text--small--mobile,
  .p-minor--mobile {
    font-size: var(---font-size-body-small--mobile);
    font-family: var(---font-family-body);
    font-weight: var(---font-weight-body);
  }
}

.text--large,
.major {
  font-size: var(---font-size-body-large--mobile);
}
@media only screen and (min-width: 741px) {
  .text--large,
  .major {
    font-size: var(---font-size-body-large--desktop);
  }
}
.text--large p:not(.heading),
.major p:not(.heading) {
  font-size: var(---font-size-body-large--mobile);
}
@media only screen and (min-width: 741px) {
  .text--large p:not(.heading),
  .major p:not(.heading) {
    font-size: var(---font-size-body-large--desktop);
  }
}

@media only screen and (max-width: 740px) {
  .text--large--mobile,
  .p-major--mobile {
    font-size: var(---font-size-body-large--mobile);
    font-family: var(---font-family-body);
    font-weight: var(---font-weight-body);
  }
}

strong, .strong {
  font-weight: var(---font-weight-body--bold);
}

.link.link--strong {
  font-weight: var(---font-weight-body--bold);
  text-decoration: none;
}

.blockquote, blockquote {
  /*
  font-size: var(---font-size-h2--mobile);
  @include respond-to($small-up){
    font-size: var(---font-size-h2--desktop);
  }
  */
  font-size: var(---font-size-h3--mobile);
}
@media only screen and (min-width: 741px) {
  .blockquote, blockquote {
    font-size: var(---font-size-h3--desktop);
  }
}

/* Product Titles */
.product-item .product-item-meta__title {
  font-family: var(---font-family-heading);
  font-weight: var(---font-weight-body--bold);
  line-height: var(---line-height-heading--mobile);
  letter-spacing: var(---letter-spacing-subheading--mobile);
  font-size: var(---font-size-body-large--mobile);
}
@media only screen and (min-width: 741px) {
  .product-item .product-item-meta__title {
    font-size: var(---font-size-body-large--desktop);
  }
}

.product-meta__title {
  font-size: var(---font-size-h1--mobile);
}
@media only screen and (min-width: 741px) {
  .product-meta__title {
    font-size: var(---font-size-h1--desktop);
  }
}

/* Header */
.header__linklist {
  font-weight: var(---font-weight-body--bold);
}

.quiz h1, .quiz .h1,
.quiz h2, .quiz .h2, .quiz .jdgm-rev-widg__title, .quiz .jdgm-carousel-title,
.quiz h3, .quiz .h3,
.quiz h4, .quiz .h4 {
  margin-top: 36px;
  margin-bottom: 12px;
}

.quiz .icon.icon--fill * {
  stroke: none;
  fill: currentColor;
}

html {
  scroll-behavior: smooth;
}

.table--bordered td {
  border-top: 1px solid #e6e6e6;
}

/* 5. Layout */
.split-page {
  background: RGB(var(--section-block-background));
}
@media only screen and (min-width: 1001px) {
  .split-page {
    min-height: 80vh;
    height: 100%;
    display: grid;
    grid-template-columns: 3fr 4fr;
  }
}
.split-page .page-header__text-wrapper {
  margin-top: 0;
  margin-bottom: 38px;
}

.split-page__header {
  padding-top: calc(var(--vertical-breather) * 2);
}

.split-page__footer {
  padding-bottom: calc(var(--vertical-breather) * 2);
  justify-self: flex-end;
}
.split-page__footer .form__secondary-action {
  margin: 0;
}
.split-page__footer .form__secondary-action button,
.split-page__footer .form__secondary-action a {
  font-weight: bold;
  text-decoration: none;
}

.split-page__image {
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}

.split-page__content {
  padding: 0 var(--container-gutter);
}

.split-page__content-wrapper {
  position: relative;
  height: 100%;
  margin: auto;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
@media only screen and (min-width: 1001px) {
  .split-page__content-wrapper {
    max-height: calc(100vh - var(--header-height));
  }
}

html.supports-no-cookies .supports-no-cookies {
  display: none;
}
html.supports-cookies .supports-cookies {
  display: none;
}

/* 6. Sections */
[class*=template-customers] {
  /* ----- Link Bar ----- */
  /* ----- Page Header ----- */
  /* ----- Link Bar ----- */
}
[class*=template-customers] .link-bar {
  --background: var(---background-color--content-reversed-1);
  --text-color: var(---color-text--reversed);
  background: var(--background);
  color: var(--text-color);
}
[class*=template-customers] .link-bar .link-bar__link-item {
  transition: 0.25s color;
}
[class*=template-customers] .link-bar .link-bar__link-item:hover {
  color: var(---color--highlight);
}
[class*=template-customers] .link-bar .link-bar__link-item .text--subdued {
  transition: 0.25s color;
}
[class*=template-customers] .link-bar .link-bar__link-item .text--subdued:hover {
  color: var(---color--danger) !important;
}
[class*=template-customers] .link-bar .text--underlined {
  --text-color: var(---color--highlight);
  color: var(--text-color);
  text-decoration: none;
  cursor: default;
  pointer-events: none;
}
[class*=template-customers] .link-bar .text--underlined:after {
  content: none;
}
[class*=template-customers] .page-header .heading {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.25em;
}
[class*=template-customers] .page-header .bubble-count {
  background-color: var(---color--highlight);
  color: var(---color-text);
  font-weight: 700;
  letter-spacing: -0.075em !important;
  height: 36px;
  width: 36px;
  font-size: 0.5em;
}

.account {
  background-color: var(---background-color--content-1);
}
.account .account__orders-table {
  font-weight: 400;
  font-size: var(---font-fize-body);
}
.account .account__orders-table thead th {
  padding: 0.5rem 0;
}
.account .account__orders-table td {
  padding: 0.5rem 0;
}
.account .account__orders-table .reorder-button {
  letter-spacing: 0;
  min-width: 0;
  padding: 0em 1em;
  line-height: 2.4em;
  background-color: var(---color--highlight);
}
.account .account__order-item-actions {
  display: flex;
  flex-direction: column;
  gap: 0.5em;
}
@media (min-width: 1000px) {
  .account .account__order-item-actions {
    flex-direction: row;
    gap: 20px;
  }
}

.mini-cart {
  --root-background: var(---background-color--content-1--rgb);
  --section-block-background: var(---background-color--content-1--rgb);
  --background: var(---background-color--content-1--rgb);
  width: 100vw;
  /* ----- Loading Overlay ----- */
  /* ----- Drawer Header ----- */
  /* ----- Line Items ----- */
  /* ----- Cart Subscriptions Box ----- */
  /* ----- Tags ----- */
  /* ----- Shipping Details ----- */
}
.mini-cart:after {
  pointer-events: none;
  content: "";
  display: block;
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 1;
  transition: 0.25s background-color;
  background: RGBA(255, 255, 255, 0);
}
.mini-cart.cart-drawer--loading:after {
  background: RGBA(var(---background-color--content-1--rgb), 0.75);
  pointer-events: auto;
}
.mini-cart .drawer__header {
  border-bottom: 0;
  max-height: none;
  height: auto;
}
.mini-cart .drawer__title {
  text-transform: none;
  margin-bottom: 0;
}
.mini-cart .drawer__close-button {
  bottom: 0;
  top: 0;
}
.mini-cart free-shipping-bar {
  padding: 20px 30px;
  margin: 0 0 20px 0;
  background: RGB(var(---background-color--content-2--rgb));
  border-radius: var(---border-radius--general);
}
.mini-cart free-shipping-bar .text--small {
  margin-bottom: 0;
}
.mini-cart .mini-cart__drawer-footer {
  --root-border-color: var(---color-line--light--rgb);
  padding: 20px var(--container-gutter);
}
.mini-cart .product-item-meta__title {
  line-height: 1.2;
  font-size: var(---font-size-body--desktop);
}
.mini-cart .product-item-meta__property {
  font-weight: var(---font-weight-body);
}
.mini-cart .product-item-meta__price-and-remove {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.mini-cart .product-item-meta__price-and-remove .line-item__quantity {
  margin-top: 0;
}
.mini-cart .line-item .line-item__content-wrapper {
  margin-top: 0;
  margin-bottom: 35px;
}
.mini-cart .line-item .line-item__info {
  width: 100%;
}
.mini-cart .line-item .line-item__image {
  border-radius: 6px;
}
.mini-cart .line-item .line-item__image-wrapper {
  margin-right: 10px;
}
@media only screen and (min-width: 1001px) {
  .mini-cart .line-item .line-item__image-wrapper {
    margin-right: 20px;
  }
}
.mini-cart .line-item .line-item__remove-button {
  font-weight: 400;
}
.mini-cart .line-item .product-item-meta__property-list {
  margin: 0;
}
.mini-cart .line-item .product-item-meta__price-list-container {
  margin: 0;
}
.mini-cart .line-item .quantity-selector {
  --quantity-selector-height: 32px;
  overflow: hidden;
}
.mini-cart .line-item .quantity-selector__input {
  font-size: var(---font-size-body-small--desktop);
  font-weight: var(---font-weight-body);
  background: RGB(var(---background-color--content-1--rgb));
}
.mini-cart .line-item .quantity-selector__button {
  background: RGB(var(---background-color--content-1--rgb));
}
.mini-cart .line-item .line-item__remove-button {
  font-size: var(---font-size-body-small--desktop);
  font-weight: var(---font-weight-body);
}
.mini-cart .mini-cart__drawer-prefooter {
  padding: 10px var(--container-gutter);
  text-align: center;
  font-weight: var(---font-weight-body);
  position: relative;
}
.mini-cart .mini-cart__drawer-prefooter:after {
  content: "";
  display: block;
  position: absolute;
  height: 30px;
  width: 100%;
  top: 0;
  transform: translateY(-100%);
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0), rgb(255, 255, 255));
}
.mini-cart .cart-subscriptions {
  display: block;
  margin-bottom: 12px;
  border-radius: 8px;
  background: var(---background-color--content-2);
}
.mini-cart .cart-subscriptions .cart-subscriptions-form__actions {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1em;
}
.mini-cart .cart-subscriptions .cart-subscriptions__form {
  padding: 12px;
  border-top: 1px solid rgba(var(---color--brand-6--rgb), 0.5);
}
@media only screen and (min-width: 1001px) {
  .mini-cart .cart-subscriptions .cart-subscriptions__form {
    padding: 24px;
  }
}
.mini-cart .cart-subscriptions .subscriptions-input {
  margin: 1em 0;
  gap: 10px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
@media only screen and (min-width: 1001px) {
  .mini-cart .cart-subscriptions .subscriptions-input {
    flex-direction: row;
  }
}
.mini-cart .cart-subscriptions .subscriptions-input label {
  font-weight: 700;
  font-size: var(---font-size-body--small);
}
.mini-cart .cart-subscriptions .subscriptions-input select {
  padding: 0.25em 2.5em 0.25em 0.75em;
  border-radius: 8px;
}
.mini-cart .product-item-tags {
  display: flex;
  flex-wrap: wrap;
  margin: 0.5em 0;
  gap: 10px;
}
.mini-cart .product-item-tag {
  display: inline-flex;
  align-items: center;
  padding: 0.35em 0.5em;
  gap: 0.25em;
  background: var(---background-color--secondary);
  border-radius: 4px;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}
.mini-cart .product-item-tag svg * {
  fill: currentColor;
  outline: currentColor;
}
.mini-cart .product-item-tag--prescription {
  background: RGB(var(---background-color--danger--rgb));
  color: RGB(var(---color--danger--rgb));
}
.mini-cart .product-item-tag--subscription {
  background-color: var(---color--highlight);
}
.mini-cart .product-item-tag__icon {
  display: flex;
  align-items: center;
  line-height: 1;
}
.mini-cart .product-item-tag__icon svg {
  width: 20px;
  height: 20px;
}
.mini-cart .product-item-tag__text {
  line-height: 1;
}
.mini-cart .shipping-details {
  --padding-horizontal: 30px;
  --padding-vertical: 20px;
}
.mini-cart .shipping-details__inner {
  margin-top: calc(1 * var(--padding-vertical));
  margin-left: calc(-1 * var(--padding-horizontal));
  margin-right: calc(-1 * var(--padding-horizontal));
  padding-top: var(--padding-vertical);
  padding-left: var(--padding-horizontal);
  padding-right: var(--padding-horizontal);
  border-top: 1px solid RGBA(0, 0, 0, 0.2);
}
.mini-cart .shipping-details__footer {
  margin-top: calc(1 * var(--padding-vertical));
  padding-top: calc(0.5 * var(--padding-vertical));
  padding-left: var(--padding-horizontal);
  padding-right: var(--padding-horizontal);
  border-top: 1px solid RGBA(0, 0, 0, 0.2);
  line-height: 1.2;
}
.mini-cart .shipping-details__header {
  display: flex;
  justify-content: space-between;
}
.mini-cart .shipping-details__heading {
  padding: 0;
  margin: 0;
}
.mini-cart .shipping-details__table {
  width: 100%;
  text-align: left;
  font-size: var(---font-size-body-small--desktop);
}
.mini-cart .shipping-details__table th,
.mini-cart .shipping-details__table td {
  font-size: 0.9em !important;
}
.mini-cart .shipping-details__table tr th,
.mini-cart .shipping-details__table tr td {
  text-align: center;
  padding: 0.1em 0;
}
.mini-cart .shipping-details__table tr th:first-child,
.mini-cart .shipping-details__table tr td:first-child {
  text-align: left;
}
.mini-cart .shipping-details__table tr th:last-child,
.mini-cart .shipping-details__table tr td:last-child {
  text-align: right;
}
.mini-cart .shipping-details__message {
  background-color: var(---background-color--content-1);
  padding: 0.75em;
  margin: 1em 0;
  border-radius: var(--block-border-radius);
  line-height: 1.4;
}
.mini-cart .shipping-details__message p {
  font-size: 0.9em !important;
}
.mini-cart .cart-vet-partner__selector-form {
  border-top: 1px solid var(---color-line--light);
  padding-top: 20px;
  margin-top: 20px;
}

.account-dog-info {
  --text-font-weight: 300;
  display: flex;
  flex-direction: column;
  gap: 30px;
  padding: 24px;
  border-radius: 18px;
  overflow: hidden;
  font-weight: var(--text-font-weight);
  color: RGB(var(--text-color));
}
.account-dog-info strong {
  font-weight: 700;
}
.account-dog-info h1,
.account-dog-info h2,
.account-dog-info h3,
.account-dog-info h4,
.account-dog-info h5,
.account-dog-info h6,
.account-dog-info .h1,
.account-dog-info .h2,
.account-dog-info .jdgm-rev-widg__title,
.account-dog-info .jdgm-carousel-title,
.account-dog-info .h3,
.account-dog-info .h4,
.account-dog-info .h5,
.account-dog-info .h6 {
  color: RGB(var(--heading-color));
}
.account-dog-info .account-dog-info {
  display: flex;
}
.account-dog-info .button-wrapper {
  display: flex;
  flex-direction: column;
  gap: 0.5em;
}
@media only screen and (min-width: 1001px) {
  .account-dog-info .button-wrapper {
    display: flex;
    flex-direction: row;
  }
}
.account-dog-info .button {
  line-height: 1.2 !important;
}
.account-dog-info .button-wrapper--center {
  justify-content: center;
}
@media only screen and (min-width: 1001px) {
  .account-dog-info {
    padding: 48px;
  }
}

@media only screen and (min-width: 1001px) {
  .product__media {
    position: sticky;
    top: calc(var(--announcement-bar-height) + var(--header-height) + var(--vertical-breather));
  }
}

@media only screen and (min-width: 741px) {
  .shopify-section--feeding-calculator {
    margin-top: 150px;
  }
}

.fieldset {
  --form-input-gap: 24px;
  margin: var(--container-gutter) 0;
}
.fieldset:last-child {
  margin-bottom: 0;
}

.feeding-calculator {
  position: relative;
}

.feeding-calculator__icon {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  margin: auto;
  transform: translateY(-65%);
  display: none;
}
@media only screen and (min-width: 741px) {
  .feeding-calculator__icon {
    display: block;
    width: 200px;
    height: 200px;
  }
}
@media only screen and (min-width: 1201px) {
  .feeding-calculator__icon {
    width: 250px;
    height: 250px;
  }
}

.calculator-results-table tr.selected td {
  background: var(---color--brand-1);
}
.calculator-results-table tr.selected .label {
  visibility: visible;
}
.calculator-results-table tr.results-row {
  cursor: pointer;
}
.calculator-results-table th span {
  display: block;
  font-size: 0.75em;
}
.calculator-results-table td .label, .calculator-results-table th .label {
  margin: 0 0.5em;
}
.calculator-results-table .label {
  visibility: hidden;
  padding: 0.4em 0.8em;
}

.results-row button.link {
  display: flex;
  align-items: center;
}
.results-row .results-row__details {
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
  gap: 0.5em;
}
.results-row .results-row__external-link {
  padding: 0.05em 0.25em;
  border: 1px solid RGBA(var(---color-line--rgb), 0.25);
  border-radius: 5px;
  background-color: RGBA(var(---color-line--rgb), 0);
  transition: 0.25s background-color;
}
.results-row .results-row__external-link:hover, .results-row .results-row__external-link:focus {
  background-color: RGBA(var(---color-line--rgb), 0.1);
}

/* ========== Nutrition ========== */
.feeding-calculator-nutrition {
  --product-image-size: 120px;
  --product-image-border-size: 36px;
  --primary-button-background: var(--product-color);
  position: relative;
  padding-top: 30px;
}

.feeding-calculator-nutrition__header {
  position: relative;
  display: flex;
  justify-content: center;
  height: 60px;
  background: RGB(var(--product-color));
  border-top-left-radius: var(--block-border-radius);
  border-top-right-radius: var(--block-border-radius);
}
.feeding-calculator-nutrition__header:after {
  content: "";
  display: block;
  padding: 15px;
  margin: auto;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1;
  --offset: calc(-1 * (var(--product-image-size) - var(--product-image-border-size) * 2));
  transform: translateY(var(--offset));
  background: RGB(var(--product-color));
  border-radius: 120px;
  width: calc(var(--product-image-size) + 30px);
  height: calc(var(--product-image-size) + 30px);
}

.feeding-calculator-nutrition__header-image {
  position: absolute;
  top: 0;
  transform: translateY(-30px);
  z-index: 2;
}

.feeding-calculator-nutrition__content {
  padding-top: calc(var(--vertical-breather) / 2);
  position: relative;
  z-index: 1;
  background: RGB(var(--section-background, var(--background)));
}

.spaced-content--tight {
  gap: 20px;
}

/* ----- Ratings ----- */
.nutrition-ratings {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-wrap: wrap;
  gap: 10px;
}
@media only screen and (min-width: 1001px) {
  .nutrition-ratings {
    gap: 20px;
  }
}

/* ----- Nutrition Summary ----- */
.nutrition-summary {
  display: flex;
  flex-direction: column;
  gap: 20px;
}
@media only screen and (min-width: 741px) {
  .nutrition-summary {
    flex-direction: row;
  }
  .nutrition-summary .nutrition-summary-item {
    width: 100%;
  }
}

.nutrition-summary-item__title {
  margin-bottom: 0.5em;
}

/* ========== Analysis Table ========== */
.nutritional-analysis {
  --row-spacing: 0.35em;
  text-align: left;
  border: 5px solid RGB(var(--heading-color));
  padding: 20px;
  font-size: var(---font-size-body-small--mobile);
}
@media only screen and (min-width: 741px) {
  .nutritional-analysis {
    font-size: var(---font-size-body-small--desktop);
  }
}

.nutritional-analysis__footer {
  margin-top: 20px;
  font-size: var(---font-size-body-xs--mobile);
}
@media only screen and (min-width: 741px) {
  .nutritional-analysis__footer {
    font-size: var(---font-size-body-xs--desktop);
  }
}

/* ----- Analysis Category ----- */
.analysis-category {
  display: block;
}

.analysis-category__header {
  display: flex;
  gap: 10px;
  width: 100%;
  padding: var(--row-spacing) 0;
  border-bottom: 1px solid var(---color-line--light);
}
.analysis-category__header[aria-expanded=true] .analysis-category__button:before {
  content: "-";
}

/* ----- Analysis Header ----- */
.analysis-header {
  display: block;
  padding: var(--row-spacing) 0;
  margin-top: 1em;
  margin-bottom: 5px;
  border-bottom: 1px solid var(---color-line);
}
.analysis-header:first-child {
  margin-top: 0;
}

.analysis-header__title {
  font-size: 20px;
  text-transform: uppercase;
  font-weight: var(---font-weight-body--bold);
}

/* ----- Analysis Category ----- */
.analysis-category__title {
  font-weight: var(---font-weight-body--bold);
}

.analysis-category__button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  font-weight: bold;
  border-radius: 4px;
  background: RGBA(var(--text-color), 0.05);
}
.analysis-category__button:focus, .analysis-category__button:hover {
  background: RGBA(var(--text-color), 0.1);
}
.analysis-category__button:before {
  content: "+";
}

.analysis-category__content {
  display: none;
  width: 100%;
}

.analysis-category__header[aria-expanded=true] + .analysis-category__content {
  display: table !important;
}

@media (min-width: 480px) {
  .analysis-category .analysis-row > *:first-child {
    padding-left: 30px;
  }
}

/* ----- Analysis Table ----- */
.analysis-table {
  display: table;
  width: 100%;
}

.analysis-table-row {
  display: table-row;
  gap: 5px;
  width: 100%;
  line-height: 1.4;
  font-size: 0.95em;
}
.analysis-table-row > * {
  width: 70px;
  text-align: center;
}
@media (min-width: 480px) {
  .analysis-table-row > * {
    width: 100px;
  }
}
.analysis-table-row > *:first-child {
  text-align: left;
  width: auto;
  margin-right: auto;
}
.analysis-table-row > *:last-child {
  text-align: right;
}

.analysis-row {
  width: 100%;
  display: table-row;
}
.analysis-row > * {
  display: table-cell;
  padding-left: 5px;
  padding-right: 5px;
  padding: var(--row-spacing) 0;
  border-bottom: 1px solid var(---color-line--light);
}
.analysis-row > *:last-child {
  text-align: right;
  padding-right: 0;
}

/* ========== Classes ========== */
.product-color {
  color: RGB(var(--product-color));
}

.spaced-content {
  text-align: center;
  display: grid;
  grid-auto-flow: row;
  gap: 32px;
}
.spaced-content > * {
  margin: 0;
}

.spaced-content--tight {
  gap: 20px;
}

.section.section--use-padding {
  margin: 0;
  padding: var(--vertical-breather) 0;
}
.section.section--half-padding {
  --vertical-breather: calc(var(--vertical-breather) / 2);
}
.section.section--double-spacing {
  --vertical-breather: var(--vertical-breather-double);
}
.section.section--no-padding {
  margin: 0;
  padding: 0;
}
.section .container--no-padding {
  padding: 0;
}
.container--smaller {
  max-width: 700px;
}

/* 7. Page-Specific Styles */
/* 8. Components */
.button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5em;
  transition: color 0.25s;
  text-align: center;
  line-height: 1.2;
  min-height: unset;
  font-weight: var(--text-font-bold-weight);
  font-size: var(---font-size-button--mobile);
}
.button:not(.button--text) {
  -webkit-padding-start: unset;
          padding-inline-start: unset;
  -webkit-padding-end: unset;
          padding-inline-end: unset;
  padding: 1em 1.5em;
}
@media only screen and (min-width: 741px) {
  .button {
    font-size: var(---font-size-button--desktop);
  }
}
.button[disabled] {
  --button-background: 154, 154, 154;
  cursor: not-allowed;
  opacity: 0.5;
  background-position: 100% -100%, 100% 100% !important;
}
.button:not(.button--link) {
  min-width: 200px;
}
.button .loader-button__text {
  gap: 16px;
}
.button:not(.button--text) {
  /*
  padding-left: 50px;
  padding-right: 50px;

  @include respond-to($small-down) {
    padding: 0 30px;
  }
  */
}
.button.button--highlight {
  --button-background: var(---color--highlight--rgb);
  --button-text-color: 0, 0, 0;
}
.button.button--tertiary {
  --button-background: var(---color--tertiary--rgb);
  --button-text-color: 0, 0, 0;
}
.button.button--tab {
  min-width: unset;
  padding-left: 0 !important;
  padding-right: 0 !important;
  color: RGB(var(---color--brand-4--rgb));
  cursor: pointer;
}
.button.button--tab[disabled] {
  background: none;
  pointer-events: none;
  cursor: not-allowed;
}
.button.button--tab:not([disabled]) {
  cursor: pointer;
}
.button.button--tab:not([disabled]):hover, .button.button--tab:not([disabled]):focus {
  color: RGB(var(---color--tertiary--rgb));
}
.button.button--tab:not([disabled]).active {
  color: RGB(var(---color--brand-1--rgb));
  cursor: default;
  pointer-events: none;
}
.button.button--text {
  display: inline-flex;
  align-items: center;
  gap: 10px;
  min-width: 0;
  padding-left: 0;
  padding-right: 0;
  font-size: var(---font-size-body-large--desktop);
  font-weight: var(---font-weight-body--bold);
  transition: 0.25s color;
}
.button.button--text:focus, .button.button--text:hover {
  color: var(---color-text--light);
}
.button.button--text .button__icon svg {
  width: 10px;
  height: 10px;
  transform: rotate(45deg);
}
.button.button--hollow {
  background: transparent;
  color: RGB(var(--button-background));
  box-shadow: 0 0 0 1px RGB(var(--button-background));
  transition: 0.25s color, 0.25s box-shadow, 0.25s background;
}
.button.button--hollow:hover {
  background: RGB(var(--button-background));
  color: RGB(var(--button-text-color));
}
.button.button--stealth {
  background: RGB(var(--section-block-background));
  color: RGB(var(--text-color));
  transition: 0.25s color, 0.25s box-shadow, 0.25s background;
}
.button.button--stealth:hover {
  background: RGB(var(--button-background));
  color: RGB(var(--button-text-color));
}
.button.button--tiny {
  padding: 0.25em 0.5em;
  line-height: 24px;
  padding-left: 24px;
  padding-right: 24px;
  min-width: 0;
  font-size: var(---font-size-button--mobile);
}
@media only screen and (max-width: 1000px) {
  .button.button--tiny {
    min-width: unset;
  }
}
@media only screen and (min-width: 1001px) {
  .button.button--tiny {
    height: 36px;
    line-height: 36px;
    font-size: var(---font-size-button--mobile);
  }
}
.button.button--large {
  font-size: var(---font-size-button-large--mobile);
  min-height: unset;
  padding: 0.75em 2em;
  font-size: var(---font-size-button-large--mobile);
}
@media only screen and (max-width: 1000px) {
  .button.button--large {
    min-width: unset;
  }
}
@media only screen and (min-width: 1001px) {
  .button.button--large {
    height: 64px;
  }
}
@media only screen and (min-width: 1001px) {
  .button.button--large {
    font-size: var(---font-size-button-large--desktop);
  }
}

.modal {
  --background: var(---background-color--content-1--rgb);
}
.modal .modal__close-button {
  top: 26px;
  right: 26px;
}
.modal .modal__header {
  text-align: center;
  padding-top: 24px;
}
.modal .modal__title {
  font-size: var(---font-size-button-large--desktop);
}
.modal .modal__content {
  border-radius: 8px;
}
.modal .form__actions {
  margin-top: 2em;
}
@media only screen and (max-width: 740px) {
  .modal .form__actions .button {
    width: 100%;
    justify-content: center;
  }
}

.modal--login .quiz-modal-footer {
  text-align: center;
  padding-bottom: var(--vertical-breather);
  font-size: var(---font-size-body--desktop);
}

.modal--register .modal__content {
  overflow: visible;
}
.modal--register .quiz-modal__image {
  width: 185px;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  margin: auto;
  transform: translateY(-60%);
}
.modal--register .newsletter-modal__content {
  padding-top: 90px !important;
}
.modal--register .quiz-modal-footer {
  margin-top: 20px !important;
}
.modal--register .button--link {
  transform: translateY(calc(var(--vertical-breather)));
}

.recipe-modal .newsletter-modal {
  flex-direction: column;
}
.recipe-modal .newsletter-modal__content {
  padding: var(--container-gutter);
  text-align: left;
}
.recipe-modal .modal__close-button {
  color: var(---color-text--reversed);
  transition: transform 0.25s;
}
.recipe-modal .modal__close-button:hover {
  transform: rotate(90deg);
}

.modal--upsells {
  /* ----- Quiz Results Product ----- */
  /* ----- Layout ----- */
}
.modal--upsells .quiz-results-product {
  width: 300px;
}
.modal--upsells .quiz-results-product__header img {
  margin: auto;
}
.modal--upsells .quiz-results-product__footer {
  display: flex;
  justify-content: center;
  padding: 0 20px;
}
.modal--upsells .quiz-modal-content__header {
  padding: 0 40px;
  margin: auto;
  max-width: 450px;
}
.modal--upsells .newsletter-modal__content {
  max-width: unset;
  padding-top: var(--container-gutter);
  padding-bottom: var(--container-gutter);
  background: var(---background-color--content-2);
}
.modal--upsells .quiz-results-product__footer-price {
  display: flex;
  margin-right: auto;
}
.modal--upsells .quiz-results-product__footer-price .price {
  font-size: 16px;
}
.modal--upsells .price-list {
  display: flex;
  align-items: center;
}
@media only screen and (max-width: 1000px) {
  .modal--upsells .gallery {
    margin-left: calc(var(--container-gutter) * -1);
    margin-right: calc(var(--container-gutter) * -1);
  }
}

.input select,
.input input {
  font-weight: var(--text-font-bold-weight);
}

/* ----- Form Container ----- */
revealing-form,
.revealing-form {
  display: block;
}

/* ----- Inputs ----- */
revealing-form-input,
.revealing-form-input {
  display: none;
}
revealing-form-input.revealing-form-input--visible,
.revealing-form-input.revealing-form-input--visible {
  display: block;
}
revealing-form-input.revealing-form-input--animating,
.revealing-form-input.revealing-form-input--animating {
  display: block;
}

/* ----- Actions ----- */
revealing-form-actions,
.revealing-form__actions {
  display: block;
}

expanding-input {
  position: relative;
}
expanding-input select {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  height: 100%;
  width: 100%;
  margin: 0 !important;
  opacity: 0;
}
expanding-input select:focus + .expanding-input__display, expanding-input select:hover + .expanding-input__display {
  outline: none;
  border-color: var(---color--highlight);
}

.expanding-input__display {
  padding-left: 0.5em;
  padding-right: 0.5em;
  cursor: text;
}
.expanding-input__display:after {
  transition: color 0.25s;
}
.expanding-input__display:empty {
  color: var(---color--secondary);
}
.expanding-input__display:empty:after {
  content: attr(data-default);
  font-weight: 300;
}
.expanding-input__display:focus {
  color: var(---color--highlight);
}

.expanding-input--select {
  cursor: pointer;
}
.expanding-input--select .expanding-input__display {
  padding-right: 50px;
  background-image: url("data:image/svg+xml,%3Csvg width='12' height='6' viewBox='0 0 12 6' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M1 0.5L6 5.5L11 0.5' stroke='%23E6E6E6' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E%0A");
  background-size: 15px;
  background-repeat: no-repeat;
  background-position: calc(100% - 10px);
  pointer-events: none;
}

.expanding-input__input:not(select) {
  position: absolute !important;
  overflow: hidden;
  clip: rect(0 0 0 0);
  height: 1px;
  width: 1px;
  max-width: 1px;
  max-height: 1px;
  font-size: 0;
  margin: -1px;
  padding: 0;
  border: 0;
}

select.expanding-input__input option {
  background-color: #e6e6e6 !important;
  text-align: left !important;
  font-size: var(---font-size-body-large--desktop) !important;
  color: var(---color-text) !important;
}

.box-line-item {
  --box-line-item-padding: 20px;
}
.box-line-item .product-item-tag--frozen {
  background: RGB(var(--root-background));
}

.box-line-item__inner {
  margin-bottom: 20px;
  padding: var(--box-line-item-padding) 0;
  background: rgba(var(---color--brand-5--rgb), 0.25);
  border-radius: 8px;
}

.box-line-item__body {
  display: flex;
  gap: var(--box-line-item-padding);
  padding: 0 var(--box-line-item-padding);
}

.box-line-item__image {
  max-width: 92px;
  width: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}

.line-item__image-inner {
  position: relative;
}

.box-line-item__contents {
  width: 100%;
}

.box-line-item__footer {
  display: flex;
  justify-content: space-between;
  border-top: 1px solid rgba(var(---color--brand-5--rgb), 1);
  padding: 0 var(--box-line-item-padding);
  padding-top: 10px;
  margin-top: 15px;
}

.tile-radio-input:checked + .tile-radio {
  background: var(---color--brand-7);
  border-color: var(---color--brand-7);
  outline: 2px solid var(---color--brand-2);
}

.tile-radios {
  display: flex;
  justify-content: center;
  gap: 15px;
  flex-wrap: wrap;
}

.tile-radio {
  display: flex;
  align-items: center;
  gap: 0.75em;
  width: 100%;
  cursor: pointer;
  text-align: center;
  line-height: 1.1;
  padding: 10px;
  border: 1px solid var(---color-line--light);
  border-radius: 8px;
  box-shadow: 0 0px 0px rgba(0, 0, 0, 0.1);
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
  transition: transform 0.25s, box-shadow 0.25s;
}
.tile-radio:hover {
  transform: scale(1.05);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}
@media only screen and (min-width: 741px) {
  .tile-radio {
    text-align: center;
    width: auto;
    flex-direction: column;
    padding: 20px;
  }
}

.tile-radio__icon {
  pointer-events: none;
  width: 66px;
  height: 66px;
}

.tile-radio__content {
  display: flex;
  flex-direction: column;
  gap: 0.25em;
  text-align: left;
}
@media only screen and (min-width: 741px) {
  .tile-radio__content {
    text-align: center;
  }
}

.tile-radio__title {
  line-height: 1.2;
}

.tile-radio__description {
  line-height: 1.2;
}

.table.table--auto {
  table-layout: auto;
}
.table.table--1 {
  border-radius: 4px;
  overflow: hidden;
}
.table.table--1 tr:hover td {
  background: var(---color--brand-1);
}
.table.table--1 td {
  background: var(---color--brand-7);
}
.table.table--1 td,
.table.table--1 th {
  padding: 0.75em 0.25em;
  text-align: center;
  vertical-align: middle;
  line-height: 1.2;
}
.table.table--1 td:first-child,
.table.table--1 th:first-child {
  padding-left: 0.75em;
  text-align: left;
  font-weight: var(--text-font-bold-weight);
}
.table.table--1 td:last-child,
.table.table--1 th:last-child {
  padding-right: 0.75em;
}
.table.table--1 thead th {
  font-family: var(--heading-font-family);
  font-weight: var(--body-font-weight);
  font-size: 0.9em;
  letter-spacing: 0.05em;
  background: var(---color--brand-2);
  color: var(---color-text--reversed);
}

.shipping-bar {
  --background--unmet: RGBA(235, 87, 87, .3);
  --progress-background: #D9D9D9;
  --loading-bar-background: 255, 255, 255;
  position: relative;
  display: block;
  margin-top: 15px !important;
}
.shipping-bar .shipping-bar__progress {
  background: var(--progress-background);
}
.shipping-bar .shipping-bar__progress:before {
  content: "";
  display: block;
  position: absolute;
  width: 2px;
  height: 100%;
  background: currentColor;
  left: calc(var(--frozen-threshold) * 100%);
  z-index: 10;
}
.shipping-bar.shipping-bar--frozen-food--unmet {
  background: var(--background--unmet) !important;
}
.shipping-bar.shipping-bar--frozen-food--unmet .shipping-bar__progress:after {
  background: var(---color--danger) !important;
}

.shipping-bar__icon {
  --icon-size: 44px;
  position: absolute;
  left: 0;
  top: 0;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: var(--icon-size);
  height: var(--icon-size);
  vertical-align: top;
  background: #fff;
  border-radius: 100%;
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.1);
  transform: translate(-50%, -30%);
}

.shipping-bar__text {
  line-height: 1.4;
  margin-bottom: 0.5em;
  display: inline-block;
}

.vet-sticky-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 9;
  box-shadow: var(---shadow--modal);
  transform: translateY(100%);
  transition: transform 500ms;
}
.vet-sticky-bar[open] {
  display: block;
  transform: translateY(0);
}
.vet-sticky-bar:hover:not([open]) {
  transform: translateY(var(--tease-tug-1));
}
@media only screen and (max-width: 740px) {
  .vet-sticky-bar .cart-vet-text {
    display: block;
    width: 100%;
  }
}
.vet-sticky-bar .cart-vet-partner {
  display: flex;
}
@media only screen and (min-width: 1001px) {
  .vet-sticky-bar .vet-partner-selector-wrapper {
    flex: 1 0 250px;
    max-width: 250px;
  }
}
.vet-sticky-bar .cart-vet-partner__vet {
  justify-content: flex-end;
  padding: 0;
  border: 0;
}
@media only screen and (min-width: 1001px) {
  .vet-sticky-bar .cart-vet-partner__selector-form {
    display: flex;
    justify-content: end;
  }
}
.vet-sticky-bar .cart-vet-partner__selector-button {
  flex: 0 2 max-content;
}
.vet-sticky-bar .cart-vet-partner__vet-text {
  padding: 15px 30px;
  font-size: var(---font-size-h6--desktop);
  background: var(---background-color--secondary);
  border-radius: 10px;
}
@media only screen and (max-width: 740px) {
  .vet-sticky-bar .cart-vet-partner__vet-text {
    display: block;
    width: 100%;
  }
}
.vet-sticky-bar .cart-vet-partner__product-notice {
  display: none;
}

.vet-sticky-bar__tag {
  --tab-background: var(---color--highlight--rgb);
  --tab-color: var(---color--default--rgb);
  --edge-width: 30px;
  --edge-corner-radius: 8px;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  transform: translateY(-100%);
  display: flex;
  align-items: center;
  justify-content: center;
  width: -moz-max-content;
  width: max-content;
  padding: 0.5em;
  margin: auto;
  font-weight: var(--bold-font-weight);
  text-align: center;
  background: RGB(var(--tab-background));
  color: RGB(var(--tab-color));
  transition: background 250ms;
}
.vet-sticky-bar__tag:hover {
  --tab-background: var(---color--highlight--dark--rgb);
}
.vet-sticky-bar__tag:before, .vet-sticky-bar__tag:after {
  content: "";
  display: block;
  position: absolute;
  top: 0;
  z-index: -1;
  background: RGB(var(--tab-background));
  transition: background 250ms;
  width: var(--edge-width);
  height: 100%;
}
.vet-sticky-bar__tag:before {
  left: -10px;
  transform: skew(-10deg);
  border-top-left-radius: var(--edge-corner-radius);
}
.vet-sticky-bar__tag:after {
  right: -10px;
  transform: skew(10deg);
  border-top-right-radius: var(--edge-corner-radius);
}

.vet-sticky-bar__wrapper {
  padding: 20px;
  background-color: RGB(var(--section-background));
}
@media only screen and (max-width: 740px) {
  .vet-sticky-bar__wrapper .container {
    padding: 0;
  }
}

@media only screen and (max-width: 740px) {
  .cart-vet-partner__vet {
    padding-top: 10px;
    border-top: 1px solid var(---color-line--light);
  }
}

.vet-sticky-bar__inner {
  display: flex;
  flex-direction: column;
}
@media only screen and (min-width: 1001px) {
  .vet-sticky-bar__inner {
    flex-direction: row;
    gap: 20px;
  }
}

.vet-sticky-bar__info {
  display: flex;
  gap: 10px;
}

.vet-sticky-bar__actions {
  display: flex;
  justify-content: flex-end;
}

.vet-sticky-bar__text .subheading {
  margin: 0;
}

@keyframes vet-sticky-bar--tease {
  0% {
    transform: translateY(var(--tease-start));
  }
  15% {
    transform: translateY(var(--tease-tug-1));
  }
  30% {
    transform: translateY(var(--tease-start));
  }
  60% {
    transform: translateY(var(--tease-start));
  }
  80% {
    transform: translateY(var(--tease-tug-2));
  }
  100% {
    transform: translateY(var(--tease-start));
  }
}
@media only screen and (max-width: 740px) {
  :root {
    --tease-start: 100%;
    --tease-tug-1: 95%;
    --tease-tug-2: 85%;
  }
}
@media only screen and (min-width: 741px) {
  :root {
    --tease-start: 100%;
    --tease-tug-1: 90%;
    --tease-tug-2: 70%;
  }
}

/*  --------------------------------------------------
    Cart
    -------------------------------------------------- */
.cart-vet-partner {
  --vertical-spacing: 12px;
  --spacing: 12px;
  display: flex;
  flex-direction: column;
  gap: var(--vertical-spacing);
  transition: opacity 0.25s;
}
.cart-vet-partner[loading] {
  pointer-events: none;
  opacity: 0.25;
}
.cart-vet-partner .unlisted-vet-container {
  background: var(---background-color--secondary);
  border-radius: 4px;
  padding: 0.5em 1em;
  text-align: center;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
}

.modal--vet-not-listed .modal__overlay {
  pointer-events: none;
}

.cart-vet-partner__inner {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.cart-vet-partner__selector-form {
  display: grid;
  grid-auto-flow: row;
  gap: var(--spacing);
}
@media only screen and (max-width: 1000px) {
  .cart-vet-partner__selector-form {
    grid-auto-flow: row;
  }
}
@media only screen and (min-width: 1001px) {
  .cart-vet-partner__selector-form select {
    height: 100%;
  }
}

.cart-vet-partner__vet {
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.cart-vet-partner__product-notice {
  text-align: center;
}

.cart-vet-partner__notice {
  display: flex;
  padding: 16px;
  margin-top: var(--spacing);
  gap: var(--spacing);
  border: 2px solid var(---color--default);
  border-radius: var(--block-border-radius);
}

.cart-vet-partner__notice-text .subheading {
  margin-bottom: 0.25em;
}

.cart-vet-text {
  line-height: 1;
}

/*  --------------------------------------------------
    Account Page
    -------------------------------------------------- */
.account-vet-partner {
  max-width: 800px;
  padding: 0 0;
  margin: 0 auto;
}
.account-vet-partner .account-vet-partner__inner {
  margin-top: 20px;
  padding: 20px;
  background: var(---background-color--secondary);
  border-radius: var(--block-border-radius);
}
@media only screen and (min-width: 1001px) {
  .account-vet-partner .account-vet-partner__inner {
    padding: 30px;
  }
}
.account-vet-partner .cart-vet-partner__selector {
  padding-bottom: 20px;
  margin-bottom: 20px;
  border-bottom: 1px solid var(---color-line--light);
}
.account-vet-partner .cart-vet-partner__vet {
  padding: 0;
  border: 0;
}
.account-vet-partner .cart-vet-partner__vet-text {
  font-size: var(--base-font-size);
  padding: 15px 30px;
  border-radius: 10px;
}
.account-vet-partner .cart-vet-partner__notice {
  background: var(---background-color--content-1);
}
.account-vet-partner .cart-vet-partner__product-notice {
  display: none;
}
.account-vet-partner .cart-vet-partner__selector-form .select {
  background: var(---background-color--content-1);
}
.account-vet-partner .cart-vet-notice {
  display: block !important;
  height: auto;
  visibility: visible;
}
.account-vet-partner .cart-vet-notice button {
  display: none;
}
.account-vet-partner .cart-vet-partner__notice {
  padding: 20px;
}
@media (min-width: 1000px) {
  .account-vet-partner .cart-vet-partner__notice {
    padding: 30px;
  }
}
.account-vet-partner .cart-vet-text {
  display: flex;
  justify-content: center;
}
.account-vet-partner .cart-vet-partner {
  display: flex;
  gap: 0;
}

split-page-step {
  display: none;
}
split-page-step.split-page-step--visible {
  display: block;
}
split-page-step .input--select > select {
  width: 100%;
}

.banner {
  border-radius: var(--block-border-radius);
}
.banner.banner--success {
  --text-color: var(---color-text--reversed--rgb);
  color: RGB(var(--text-color));
  background-color: rgb(var(--success-color));
}
.banner.banner--error {
  --text-color: var(---color-text--reversed--rgb);
  color: RGB(var(--text-color));
  background-color: rgb(var(--error-color));
}

.weight-range {
  --track-height: 14px;
  --thumb-size: 40px;
  --thumb-icon-size: 80px;
  display: block;
  margin-top: var(--thumb-icon-size);
  position: relative;
}

.weight-range__inner {
  position: relative;
  line-height: 0;
  height: var(--track-height);
  border-radius: 100px;
  background: RGB(var(---color--brand-2--rgb));
  outline: 2px solid RGB(var(--text-color));
}

.weight-range__thumb {
  pointer-events: none;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
  position: absolute;
  left: var(--range-position);
  transform: translateX(calc(-1 * var(--thumb-icon-size) / 2)) translateY(50%);
  bottom: 0;
  bottom: 50%;
  z-index: 1;
}

.weight-range__range {
  height: var(--track-height);
}

.weight-range__range::range-thumb {
  height: var(--thumb-icon-size);
  width: var(--thumb-icon-size);
  transform: translateY(calc(-1 * var(--thumb-size) / 2 - -1 * var(--track-height) / 2)) translateX(calc(100 * var(--range-offset)));
  opacity: 0;
}

.weight-range__range::slider-thumb {
  height: var(--thumb-icon-size);
  width: var(--thumb-icon-size);
  transform: translateY(calc(-1 * var(--thumb-size) / 2 - -1 * var(--track-height) / 2)) translateX(calc(100 * var(--range-offset)));
  opacity: 0;
}

.weight-range__range::-moz-range-thumb {
  height: var(--thumb-icon-size);
  width: var(--thumb-icon-size);
  transform: translateY(calc(-1 * var(--thumb-size) / 2 - -1 * var(--track-height) / 2)) translateX(calc(100 * var(--range-offset)));
  opacity: 0;
}

.weight-range__range::-webkit-slider-thumb {
  height: var(--thumb-icon-size);
  width: var(--thumb-icon-size);
  transform: translateY(calc(-1 * var(--thumb-size) / 2 - -1 * var(--track-height) / 2)) translateX(calc(100 * var(--range-offset)));
  opacity: 0;
}

.weight-range__range::range-track {
  opacity: 0;
}

.weight-range__range::slider-track {
  opacity: 0;
}

.weight-range__range::-moz-range-track {
  opacity: 0;
}

.weight-range__range::-webkit-slider-runnable-track {
  opacity: 0;
}

.weight-range__track {
  position: absolute;
  left: 0;
  top: 0;
  height: var(--track-height);
  width: var(--range-position);
  background: RGB(var(---color--brand-1--rgb));
  border-radius: 100px;
  pointer-events: none;
}

.weight-range__thumb-icon {
  height: var(--thumb-icon-size);
  width: var(--thumb-icon-size);
  background-image: url(https://cdn.shopify.com/s/files/1/1683/1605/files/pup-thumb.png?v=1719171809);
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
}

.weight-range__thumb-value {
  display: inline-flex;
  padding: 0em 0.5em;
  border-radius: 8px;
  background-color: RGB(var(---color--brand-1--rgb));
  outline: 2px solid RGB(var(--text-color));
  position: absolute;
  top: 0px;
  transform: translateY(-100%);
}

.weight-range__labels {
  display: flex;
  justify-content: space-between;
  margin-top: 0.25em;
  pointer-events: none;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}

.popover,
.mobile-toolbar {
  background: var(---background-color--content-1) !important;
}

.nav-dropdown {
  z-index: 1;
  background: RGB(var(--background));
}

.button {
  line-height: 1.2;
}

.button-wrapper.button-wrapper--vertical {
  display: flex;
  align-items: center;
  gap: 0.5em;
  flex-direction: column;
}

hr, .hr {
  width: 100%;
  margin: 2em auto;
  border-width: 0.5px;
  border-bottom: 0;
  border-style: solid;
  border-color: var(---color-line);
}
hr.hr--light, .hr.hr--light {
  border-color: var(---color-line--light);
}
hr.hr--dark, .hr.hr--dark {
  border-color: var(---color-line--dark);
}
hr.hr--clear, .hr.hr--clear {
  border-color: transparent;
}
hr.hr--small, .hr.hr--small {
  margin: 1em 0;
}
hr.hr--xsmall, .hr.hr--xsmall {
  margin: 0.5em 0;
}
hr.hr--narrow, .hr.hr--narrow {
  max-width: 70px;
  margin-left: auto !important;
  margin-right: auto !important;
}

[data-tooltip]:before {
  font-size: var(---font-size-body-xs) !important;
}

.account-link-current {
  color: var(---color--highlight);
}

/* 9. Apps  */
.product-form__buy-buttons .awt-style-1 .bundleapp-wrapper {
  margin: 0 !important;
}
.product-form__buy-buttons .awt-style-1 .bundleapp-wrapper .bundleapp-plan-selector-group {
  border-radius: 12px;
  border: 0;
  border: 2px solid RGB(var(--border-color));
  transition: 0.25s border, 0.25s background;
}
.product-form__buy-buttons .awt-style-1 .bundleapp-wrapper .bundleapp-plan-selector-group > label {
  font-weight: 800;
  color: var(--text-color);
}
.product-form__buy-buttons .awt-style-1 .bundleapp-wrapper .bundleapp-plan-selector-group.bundleapp-plan-selector-group--selected {
  background: var(---color--highlight);
}
.product-form__buy-buttons .awt-style-1 .bundleapp-wrapper .bundleapp-plan-selector-description {
  line-height: 1.2;
}
.product-form__buy-buttons .awt-style-1 .bundleapp-wrapper .bundleapp-plan-selector-description span {
  background: var(---background-color--content-1);
  font-weight: 400;
  padding: 20px;
  border: 0;
}
.product-form__buy-buttons .awt-style-1 .bundleapp-wrapper .bundleapp-plan-selector-plan {
  margin: 0;
  padding: 0.5em;
}
.product-form__buy-buttons .awt-style-1 .bundleapp-wrapper .bundleapp-plan-selector-plan label {
  font-size: 0.9em;
}
.product-form__buy-buttons .awt-style-1 .bundleapp-wrapper .bundleapp-plan-selector-select {
  padding: 0.4em 0.8em !important;
  margin: 0 !important;
  border-radius: 12px;
  border: 2px solid RGB(var(--border-color)) !important;
}

/* ---------- Marquee Text ---------- */
.section .marquee-horizontal {
  z-index: 3;
}

/* 10. Utility Classes */
/*================ Build Base Grid Classes ================*/
.shown {
  display: block !important;
}

.hidden {
  display: none !important;
}

.text-left {
  text-align: left !important;
}

.text-right {
  text-align: right !important;
}

.text-center {
  text-align: center !important;
}

/*================ Build Responsive Grid Classes ================*/
@media only screen and (min-width: 741px) and (max-width: 999px) {
  .small--shown {
    display: block !important;
  }
  .small--hidden {
    display: none !important;
  }
  .small--text-left {
    text-align: left !important;
  }
  .small--text-right {
    text-align: right !important;
  }
  .small--text-center {
    text-align: center !important;
  }
  .br--small {
    display: block;
  }
}
@media only screen and (min-width: 741px) {
  .small-up--shown {
    display: block !important;
  }
  .small-up--hidden {
    display: none !important;
  }
  .small-up--text-left {
    text-align: left !important;
  }
  .small-up--text-right {
    text-align: right !important;
  }
  .small-up--text-center {
    text-align: center !important;
  }
  .br--small-up {
    display: block;
  }
}
@media only screen and (max-width: 740px) {
  .small-down--shown {
    display: block !important;
  }
  .small-down--hidden {
    display: none !important;
  }
  .small-down--text-left {
    text-align: left !important;
  }
  .small-down--text-right {
    text-align: right !important;
  }
  .small-down--text-center {
    text-align: center !important;
  }
  .br--small-down {
    display: block;
  }
}
@media only screen and (min-width: 1001px) and (max-width: 1199px) {
  .medium--shown {
    display: block !important;
  }
  .medium--hidden {
    display: none !important;
  }
  .medium--text-left {
    text-align: left !important;
  }
  .medium--text-right {
    text-align: right !important;
  }
  .medium--text-center {
    text-align: center !important;
  }
  .br--medium {
    display: block;
  }
}
@media only screen and (min-width: 1001px) {
  .medium-up--shown {
    display: block !important;
  }
  .medium-up--hidden {
    display: none !important;
  }
  .medium-up--text-left {
    text-align: left !important;
  }
  .medium-up--text-right {
    text-align: right !important;
  }
  .medium-up--text-center {
    text-align: center !important;
  }
  .br--medium-up {
    display: block;
  }
}
@media only screen and (max-width: 1000px) {
  .medium-down--shown {
    display: block !important;
  }
  .medium-down--hidden {
    display: none !important;
  }
  .medium-down--text-left {
    text-align: left !important;
  }
  .medium-down--text-right {
    text-align: right !important;
  }
  .medium-down--text-center {
    text-align: center !important;
  }
  .br--medium-down {
    display: block;
  }
}
@media only screen and (min-width: 1201px) and (max-width: 1399px) {
  .large--shown {
    display: block !important;
  }
  .large--hidden {
    display: none !important;
  }
  .large--text-left {
    text-align: left !important;
  }
  .large--text-right {
    text-align: right !important;
  }
  .large--text-center {
    text-align: center !important;
  }
  .br--large {
    display: block;
  }
}
@media only screen and (min-width: 1201px) {
  .large-up--shown {
    display: block !important;
  }
  .large-up--hidden {
    display: none !important;
  }
  .large-up--text-left {
    text-align: left !important;
  }
  .large-up--text-right {
    text-align: right !important;
  }
  .large-up--text-center {
    text-align: center !important;
  }
  .br--large-up {
    display: block;
  }
}
@media only screen and (max-width: 1200px) {
  .large-down--shown {
    display: block !important;
  }
  .large-down--hidden {
    display: none !important;
  }
  .large-down--text-left {
    text-align: left !important;
  }
  .large-down--text-right {
    text-align: right !important;
  }
  .large-down--text-center {
    text-align: center !important;
  }
  .br--large-down {
    display: block;
  }
}
@media only screen and (min-width: 1401px) and (max-width: 1399px) {
  .wide--shown {
    display: block !important;
  }
  .wide--hidden {
    display: none !important;
  }
  .wide--text-left {
    text-align: left !important;
  }
  .wide--text-right {
    text-align: right !important;
  }
  .wide--text-center {
    text-align: center !important;
  }
  .br--wide {
    display: block;
  }
}
@media only screen and (min-width: 1401px) {
  .wide-up--shown {
    display: block !important;
  }
  .wide-up--hidden {
    display: none !important;
  }
  .wide-up--text-left {
    text-align: left !important;
  }
  .wide-up--text-right {
    text-align: right !important;
  }
  .wide-up--text-center {
    text-align: center !important;
  }
  .br--wide-up {
    display: block;
  }
}
@media only screen and (max-width: 1400px) {
  .wide-down--shown {
    display: block !important;
  }
  .wide-down--hidden {
    display: none !important;
  }
  .wide-down--text-left {
    text-align: left !important;
  }
  .wide-down--text-right {
    text-align: right !important;
  }
  .wide-down--text-center {
    text-align: center !important;
  }
  .br--wide-down {
    display: block;
  }
}
.clearfix {
  *zoom: 1;
}
.clearfix::after {
  content: "";
  display: table;
  clear: both;
}

.fallback-text,
.visually-hidden {
  position: absolute !important;
  overflow: hidden;
  clip: rect(0 0 0 0);
  height: 1px;
  width: 1px;
  max-width: 1px;
  max-height: 1px;
  font-size: 0;
  margin: -1px;
  padding: 0;
  border: 0;
}

.hidden {
  display: none;
}

.flex {
  display: flex;
}

.inline-flex {
  display: flex;
}

.align-start {
  align-items: flex-start;
}

.align-center {
  align-items: center;
}

.align-end {
  align-items: flex-end;
}

.justify-start {
  justify-items: flex-start;
}

.justify-center {
  justify-items: center;
}

.justify-end {
  justify-items: flex-end;
}

.gap-05 {
  gap: 0.5em;
}

.uppercase,
.text-transform--uppercase {
  text-transform: uppercase !important;
}

.text-transform--none {
  text-transform: none !important;
}

.strikethrough {
  text-decoration: line-through;
}

.background-color--default {
  background: var(---color--default);
}

.background-color--primary {
  background: var(---color--primary);
}

.background-color--secondary {
  background: var(---color--secondary);
}

.background-color--tertiary {
  background: var(---color--tertiary);
}

.background-color--success {
  background: var(---color--success);
}

.background-color--warning {
  background: var(---color--warning);
}

.background-color--danger {
  background: var(---color--danger);
}

.background-color--info {
  background: var(---color--info);
}

.background-color--link {
  background: var(---color--link);
}

.color--default {
  color: var(---color--default);
}

.color--primary {
  color: var(---color--primary);
}

.color--secondary {
  color: var(---color--secondary);
}

.color--tertiary {
  color: var(---color--tertiary);
}

.color--success {
  color: var(---color--success);
}

.color--warning {
  color: var(---color--warning);
}

.color--danger {
  color: var(---color--danger);
}

.color--info {
  color: var(---color--info);
}

.color--link {
  color: var(---color--link);
}

.justify-content-center {
  justify-content: center !important;
}

.object-position--top {
  -o-object-position: top !important;
     object-position: top !important;
}

.object-position--bottom {
  -o-object-position: bottom !important;
     object-position: bottom !important;
}

.object-position--center {
  -o-object-position: center !important;
     object-position: center !important;
}

.object-position--left {
  -o-object-position: left !important;
     object-position: left !important;
}

.object-position--right {
  -o-object-position: right !important;
     object-position: right !important;
}

.text-align--center {
  text-align: center !important;
}

.text-align--left {
  text-align: left !important;
}

.text-align--right {
  text-align: right !important;
}

@media only screen and (max-width: 740px) {
  .text-align--center--mobile {
    text-align: center !important;
  }
}

@media only screen and (max-width: 740px) {
  .text-align--left--mobile {
    text-align: left !important;
  }
}

@media only screen and (max-width: 740px) {
  .text-align--right--mobile {
    text-align: right !important;
  }
}

.nowrap {
  white-space: nowrap;
}

.no-margin {
  margin: 0 !important;
}

.no-margin--top {
  margin-top: 0 !important;
}

.no-margin--right {
  margin-right: 0 !important;
}

.no-margin--left {
  margin-left: 0 !important;
}

.no-margin--bottom {
  margin-bottom: 0 !important;
}

.no-padding {
  padding: 0 !important;
}

.no-padding--top {
  padding-top: 0 !important;
}

.no-padding--right {
  padding-right: 0 !important;
}

.no-padding--left {
  padding-left: 0 !important;
}

.no-padding--bottom {
  padding-bottom: 0 !important;
}

.padding-left--10 {
  padding-left: 10px !important;
}

.padding-left--20 {
  padding-left: 20px !important;
}

.padding-left--30 {
  padding-left: 30px !important;
}

.padding-left--40 {
  padding-left: 40px !important;
}

.padding-left--50 {
  padding-left: 50px !important;
}

.padding-right--10 {
  padding-right: 10px !important;
}

.padding-right--20 {
  padding-right: 20px !important;
}

.padding-right--30 {
  padding-right: 30px !important;
}

.padding-right--40 {
  padding-right: 40px !important;
}

.padding-right--50 {
  padding-right: 50px !important;
}

.padding-top--10 {
  padding-top: 10px !important;
}

.padding-top--20 {
  padding-top: 20px !important;
}

.padding-top--30 {
  padding-top: 30px !important;
}

.padding-top--40 {
  padding-top: 40px !important;
}

.padding-top--50 {
  padding-top: 50px !important;
}

.padding-bottom--10 {
  padding-bottom: 10px !important;
}

.padding-bottom--20 {
  padding-bottom: 20px !important;
}

.padding-bottom--30 {
  padding-bottom: 30px !important;
}

.padding-bottom--40 {
  padding-bottom: 40px !important;
}

.padding-bottom--50 {
  padding-bottom: 50px !important;
}

body.logged-in .logged-in--hidden {
  display: none !important;
}

body.logged-out .logged-out--hidden {
  display: none !important;
}

.fraction {
  margin-left: 0.25em;
  font-size: 0.75em;
  letter-spacing: -0.1em;
}

/* 11. Third-Party Styles */
.nice-select:active,
.nice-select.open,
.nice-select:focus {
  border-color: rgba(var(--text-color), 0.5);
}

.nice-select:after {
  border-bottom: 2px solid #999;
  border-right: 2px solid #999;
  content: "";
  display: block;
  height: 10px;
  width: 10px;
  margin-top: -6px;
  pointer-events: none;
  position: absolute;
  right: 17px;
  top: 50%;
  transform-origin: center;
  transform: rotate(45deg);
  transition: all 0.15s ease-in-out;
}

.nice-select.open:after {
  transform: rotate(-135deg);
}

.nice-select.open .nice-select-dropdown {
  opacity: 1;
  pointer-events: auto;
  transform: scale(1) translateY(0);
}

.nice-select.disabled {
  border-color: #ededed;
  color: rgba(var(--text-color), 0.5);
  pointer-events: none;
}

.nice-select.disabled:after {
  border-color: rgba(var(--text-color), 0.5);
}

.nice-select.wide {
  width: 100%;
}

.nice-select.wide .nice-select-dropdown {
  left: 0 !important;
  right: 0 !important;
}

.nice-select.right {
  float: right;
}

.nice-select.right .nice-select-dropdown {
  left: auto;
  right: 0;
}

.nice-select .nice-select-dropdown {
  width: 100%;
  margin-top: 4px;
  background-color: rgba(var(--section-background));
  border-radius: 5px;
  box-shadow: 0 0 0 1px rgba(68, 68, 68, 0.11);
  pointer-events: none;
  position: absolute;
  top: 100%;
  left: 0;
  transform-origin: 50% 0;
  transform: scale(0.75) translateY(19px);
  transition: all 0.2s cubic-bezier(0.5, 0, 0, 1.25), opacity 0.15s ease-out;
  z-index: 9;
  opacity: 0;
}

.nice-select .list {
  border-radius: 5px;
  box-sizing: border-box;
  overflow: hidden;
  padding: 0;
  max-height: 210px;
  overflow-y: auto;
}
.nice-select .list li:first-child {
  display: none !important;
}

.nice-select .option {
  cursor: pointer;
  font-weight: 400;
  list-style: none;
  outline: none;
  padding-left: 18px;
  padding-right: 29px;
  text-align: left;
  transition: all 0.2s;
}

.nice-select .option.selected {
  font-weight: bold;
}

.nice-select .option.disabled {
  background-color: rgba(0, 0, 0, 0);
  color: #999;
  cursor: default;
}

.nice-select .optgroup {
  font-weight: bold;
}

.no-csspointerevents .nice-select .nice-select-dropdown {
  display: none;
}

.no-csspointerevents .nice-select.open .nice-select-dropdown {
  display: block;
}

.nice-select .has-multiple {
  white-space: inherit;
  height: auto;
  padding: 7px 12px;
  min-height: 36px;
  line-height: 22px;
}

.nice-select .has-multiple span.current {
  border: 1px solid rgba(var(--text-color), 0.5);
  background: rgba(var(--section-background), 0.8);
  padding: 0 10px;
  border-radius: 3px;
  display: inline-block;
  line-height: 24px;
  font-size: 14px;
  margin-bottom: 3px;
  margin-right: 3px;
}

.nice-select .has-multiple .multiple-options {
  display: block;
  line-height: 24px;
  padding: 0;
}

.nice-select .nice-select-search-box {
  box-sizing: border-box;
  width: 100%;
  padding: 5px;
  pointer-events: none;
  border-radius: 5px 5px 0 0;
}

.nice-select .nice-select-search {
  box-sizing: border-box;
  background-color: rgba(var(--section-background), 0.8);
  border: 1px solid rgba(var(--text-color), 0.5);
  border-radius: 3px;
  color: rgba(var(--text-color), 1);
  display: inline-block;
  vertical-align: middle;
  padding: 7px 12px;
  margin: 0 10px 0 0;
  width: 100%;
  min-height: 36px;
  line-height: 22px;
  height: auto;
  outline: 0 !important;
}

/* ------ Custom ------ */
styled-select select {
  position: absolute !important;
  overflow: hidden;
  clip: rect(0 0 0 0);
  height: 1px;
  width: 1px;
  max-width: 1px;
  max-height: 1px;
  font-size: 0;
  margin: -1px;
  padding: 0;
  border: 0;
}
styled-select > div > select {
  display: none !important;
}
styled-select div.nice-select {
  float: none;
}

.nice-select {
  --section-background: 255, 255, 255;
  display: flex;
  justify-content: flex-start;
  height: 100%;
  width: 100%;
  padding: 0.5em 2em 0.5em 1em;
  border: 1px solid rgba(var(--text-color), 0.25);
  background-color: rgba(var(--section-background), 0.8);
  border-radius: var(--button-border-radius);
  text-align: left;
}
.nice-select:hover {
  background-color: rgba(var(--section-background), 1);
  border: 1px solid rgba(var(--text-color), 0.5);
}
.nice-select:focus {
  border: 1px solid rgba(var(--text-color), 0.5);
}
.nice-select.nice-select--position-top .nice-select-dropdown {
  bottom: calc(100% + 10px);
  top: unset;
}
.nice-select .current {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.nice-select .list {
  border-radius: 0;
}
.nice-select .option {
  padding: 0.5em 1em 0.5em 1em;
  line-height: 1.2;
  color: rgba(var(--text-color), 0.7);
}
.nice-select .option[data-value="Not Listed"] {
  color: rgba(var(--text-color), 1);
  font-style: italic;
  font-weight: var(--text-font-bold-weight) !important;
}
.nice-select .nice-select-dropdown {
  border-radius: var(--block-border-radius);
}
.nice-select .nice-select-search-box input {
  border-radius: 6px;
  border: 1px solid #e6e6e6;
  padding: 0.7em calc(1em - 5px) 0.7em calc(1em - 5px);
}

.cart-vet-partner-select .nice-select-dropdown .option.null.disabled {
  text-transform: uppercase;
  color: RGB(var(--text-color));
  font-weight: 700 !important;
}
.cart-vet-partner-select .nice-select-dropdown .option.null.disabled:hover {
  background: transparent !important;
}
.cart-vet-partner-select .nice-select-dropdown .option.null.disabled:hover:after {
  display: none !important;
}

.nice-select {
  --section-background: var();
  cursor: pointer;
  position: relative;
}
.nice-select .nice-select-dropdown {
  cursor: default;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.25);
}
.nice-select .nice-select-dropdown .list {
  margin-top: 0;
}
.nice-select .nice-select-dropdown .list .option {
  position: relative;
}
.nice-select .nice-select-dropdown .list .option.null {
  font-weight: normal;
}
.nice-select .nice-select-dropdown .list .option:after {
  content: "";
  display: block;
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.1);
  opacity: 0;
  z-index: -1;
}
.nice-select .nice-select-dropdown .list .option:hover:after {
  opacity: 1;
}

/* 12. Animations */
@keyframes fade-in {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
@keyframes fade-out {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}
@keyframes rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
/* ----------------------------------------------
 * Generated by Animista on 2025-4-24 16:5:49
 * Licensed under FreeBSD License.
 * See http://animista.net/license for more info. 
 * w: http://animista.net, t: @cssanimista
 * ---------------------------------------------- */
/**
 * ----------------------------------------
 * animation heartbeat
 * ----------------------------------------
 */
@keyframes heartbeat {
  from {
    transform: scale(1);
    transform-origin: center center;
    animation-timing-function: ease-out;
  }
  10% {
    transform: scale(0.91);
    animation-timing-function: ease-in;
  }
  17% {
    transform: scale(0.98);
    animation-timing-function: ease-out;
  }
  33% {
    transform: scale(0.87);
    animation-timing-function: ease-in;
  }
  45% {
    transform: scale(1);
    animation-timing-function: ease-out;
  }
}
/* ----------------------------------------------
 * Generated by Animista on 2025-4-24 16:5:49
 * Licensed under FreeBSD License.
 * See http://animista.net/license for more info. 
 * w: http://animista.net, t: @cssanimista
 * ---------------------------------------------- */
/**
 * ----------------------------------------
 * animation heartbeat
 * ----------------------------------------
 */
@keyframes heartbeat {
  from {
    transform: scale(1);
    transform-origin: center center;
    animation-timing-function: ease-out;
  }
  10% {
    transform: scale(0.91);
    animation-timing-function: ease-in;
  }
  17% {
    transform: scale(0.98);
    animation-timing-function: ease-out;
  }
  33% {
    transform: scale(0.87);
    animation-timing-function: ease-in;
  }
  45% {
    transform: scale(1);
    animation-timing-function: ease-out;
  }
}
.heartbeat {
  animation: heartbeat 1.5s ease-in-out both;
}

/* ----------------------------------------------
 * Generated by Animista on 2025-4-24 16:9:18
 * Licensed under FreeBSD License.
 * See http://animista.net/license for more info. 
 * w: http://animista.net, t: @cssanimista
 * ---------------------------------------------- */
/**
 * ----------------------------------------
 * animation shake-horizontal
 * ----------------------------------------
 */
@keyframes shake-horizontal {
  0%, 100% {
    transform: translateX(0);
  }
  10%, 30%, 50%, 70% {
    transform: translateX(-5px);
  }
  20%, 40%, 60% {
    transform: translateX(5px);
  }
  80% {
    transform: translateX(3px);
  }
  90% {
    transform: translateX(-3px);
  }
}
.shake-horizontal {
  animation: shake-horizontal 0.8s cubic-bezier(0.455, 0.03, 0.515, 0.955) both;
}