{%- capture status -%}
  {%- form 'recover_customer_password' -%}
    {%- if form.posted_successfully? -%}
      {%- assign is_recover_posted_successfully = true -%}
    {%- else -%}
      {%- assign is_recover_posted_successfully = false -%}
    {%- endif -%}
  {%- endform -%}
{%- endcapture -%}

<section class="quiz quiz--account-login">

  <div class="split-page">

    <div class="split-page__left">

      <div class="split-page__content-wrapper" id="login-form-container" style="display: flex">
        
        <div class="split-page__header">

        </div>

        <div class="split-page__content">

          <div class="page-header">
            <div class="page-header__text-wrapper text-container">
              <h1 class="heading h2">{{ 'customer.login.title' | t }}</h1>
              <p>{{ 'customer.login.instructions' | t }}</p>
            </div>
          </div>
          
          <div class="page-content page-content--small">
            <div class="account__block-list">
              {%- for block in section.blocks -%}
                <div class="account__block-item" {{ block.shopify_attributes }}>
                  {%- case block.type -%}
                    {%- when '@app' -%}
                      {%- render block -%}
          
                    {%- when 'liquid' -%}
                      {{- block.settings.liquid -}}
          
                    {%- when 'login' -%}
                      {%- form 'customer_login', name: 'login', class: 'form' -%}
                        {%- if form.errors -%}
                          <div class="banner banner--error form__banner" id="login-form-error">
                            <span class="banner__ribbon">{% render 'icon' with 'form-error' %}</span>
                            <p class="banner__content">{{ form.errors.messages['form'] }}</p>
                          </div>
                        {%- endif -%}
          
                        <div class="input">
                          <input type="email" id="customer[email]" autocomplete="email" class="input__field" name="customer[email]" placeholder="{{ 'customer.login.email' | t }}" required="required" {% if form.errors %}aria-invalid="true" aria-describedby="login-form-error"{% endif %}>
                          <label for="customer[email]" class="input__label">{{ 'customer.login.email' | t }}</label>
                        </div>
          
                        <div class="input">
                          <input type="password" id="customer[password]" class="input__field" name="customer[password]" placeholder="{{ 'customer.login.password' | t }}" required="required" autocomplete="current-password" {% if form.errors %}aria-invalid="true" aria-describedby="login-form-error"{% endif %}>
                          <label for="customer[password]" class="input__label">{{ 'customer.login.password' | t }}</label>
          
                          <button type="button" class="input__field-link link text--xsmall text--subdued" data-action="switch-login-form">{{ 'customer.login.forgot_password' | t }}</button>
                        </div>

                        <div class="form__actions">

                          <button type="submit" is="loader-button" class="form__submit button button--primary">
                            <span class="button__text">{{ 'customer.login.submit' | t }}</span>
                            <span class="button__icon">{%- render 'icon' with 'nav-arrow-right' -%}</span>
                          </button>
          
                        </div>
          
                      {%- endform -%}
                  {%- endcase -%}
                </div>
              {%- endfor -%}
            </div>
          </div>

        </div>

        <div class="split-page__footer">
          
          <span class="form__secondary-action text--subdued">
            {{- 'customer.login.new_customer' | t -}}
            <a href="{{ routes.account_register_url }}" class="link">{{ 'customer.login.create_account' | t }}</a>
          </span>

        </div>
        
      </div>

      <div class="split-page__content-wrapper" id="recover-form-container" style="display: none">

        {%- form 'recover_customer_password', name: 'recover', class: 'form' -%}
        
          <div class="split-page__header">

          </div>

          <div class="split-page__content">

            <div class="page-header">
              <div class="page-header__text-wrapper text-container">
                <h1 class="heading h2">{{ 'customer.recover_password.title' | t }}</h1>
          
                {%- unless is_recover_posted_successfully -%}
                  <p>{{ 'customer.recover_password.instructions' | t }}</p>
                {%- endunless -%}
              </div>
            </div>
          
            <div class="page-content page-content--small">

              {%- if form.errors -%}
                <div class="banner banner--error form__banner" id="recovery-form-error">
                  <span class="banner__ribbon">{% render 'icon' with 'form-error' %}</span>
                  <p class="banner__content">{{ form.errors.messages['form'] }}</p>
                </div>
              {%- endif -%}
            
              {%- if form.posted_successfully? -%}
                <div class="banner banner--success form__banner">
                  <span class="banner__ribbon">{% render 'icon' with 'form-success' %}</span>
                  <p class="banner__content">{{ 'customer.recover_password.success' | t }}</p>
                </div>
              {%- else -%}
                <div class="input">
                  <input type="email" id="customer[recover_email]" class="input__field" name="email" placeholder="{{ 'customer.recover_password.email' | t }}" required="required" {% if form.errors %}aria-invalid="true" aria-describedby="recovery-form-error"{% endif %}>
                  <label for="customer[recover_email]" class="input__label">{{ 'customer.recover_password.email' | t }}</label>
                </div>

                <div class="form__actions">
                  <button type="submit" is="loader-button" class="form__submit button button--primary">
                    <span class="button__text">{{ 'customer.recover_password.submit' | t }}</span>
                  </button>
                </div>
              {%- endif -%}

            </div>

          </div>

          <div class="split-page__footer">

            <span class="form__secondary-action text--subdued">
              {{- 'customer.recover_password.remember_password' | t -}}
              <button type="button" class="link" data-action="switch-login-form">{{ 'customer.recover_password.back_to_login' | t }}</button>
            </span>

          </div>

        {%- endform -%}
        
      </div>

    </div>
    
    <div class="split-page__right hidden-pocket">

      {% if section.settings.page_banner != blank %}
        <img class="split-page__image" sizes="(max-width: 740px) calc(100vw - 96px), (max-width: 999px) calc(100vw - 160px), 600px" loading="lazy" {% render 'image-attributes', image: section.settings.page_banner, sizes: '600,700,800,1000,1200,1400' %}>
      {% endif %}

    </div>

  </div>

</section>

<script>
  // The script for this is very minimal so we just embed it here
  window.addEventListener('DOMContentLoaded', () => {
    const loginFormElement = document.getElementById('login-form-container'),
      recoverFormElement = document.getElementById('recover-form-container');

      console.log(loginFormElement);

    const switchForms = () => {

      console.log(loginFormElement.style.display);
      console.log(recoverFormElement.style.display);

      loginFormElement.style.display = (window.getComputedStyle(loginFormElement).display) === 'flex' ? 'none' : 'flex';
      recoverFormElement.style.display = (window.getComputedStyle(recoverFormElement).display) === 'flex' ? 'none' : 'flex';
    }

    {% if is_recover_posted_successfully %}
      switchForms();
    {% else %}
      if (window.location.hash === '#recover') {
        switchForms();
      }
    {% endif %}

    Array.from(document.querySelectorAll('[data-action="switch-login-form"]')).forEach((button) => {
      button.addEventListener('click', () => switchForms());
    });
  });
</script>

<style>

  #shopify-section-{{ section.id }} {
    {%- if section.settings.text_background == 'rgba(0,0,0,0)' -%}
      {%- assign section_block_background = settings.background -%}
    {%- else -%}
      {%- assign section_block_background = section.settings.text_background -%}
    {%- endif -%}
  
    {%- if section.settings.text_color == 'rgba(0,0,0,0)' -%}
      {%- assign heading_color = settings.heading_color -%}
      {%- assign text_color = settings.text_color -%}
    {%- else -%}
      {%- assign heading_color = section.settings.text_color -%}
      {%- assign text_color = section.settings.text_color -%}
    {%- endif -%}
  
    {%- if section.settings.button_background == 'rgba(0,0,0,0)' -%}
      {%- assign button_background = settings.primary_button_background -%}
    {%- else -%}
      {%- assign button_background = section.settings.button_background -%}
    {%- endif -%}
  
    {%- if section.settings.button_text_color == 'rgba(0,0,0,0)' -%}
      {%- assign button_text_color = settings.primary_button_text_color -%}
    {%- else -%}
      {%- assign button_text_color = section.settings.button_text_color -%}
    {%- endif -%}
  
    --heading-color: {{ heading_color.red }}, {{ heading_color.green }}, {{ heading_color.blue }};
    --text-color: {{ text_color.red }}, {{ text_color.green }}, {{ text_color.blue }};
    --primary-button-background: {{ button_background.red }}, {{ button_background.green }}, {{ button_background.blue }};
    --primary-button-text-color: {{ button_text_color.red }}, {{ button_text_color.green }}, {{ button_text_color.blue }};
  
    --prev-next-button-background: {{ settings.background.red }}, {{ settings.background.green }}, {{ settings.background.blue }};
    --prev-next-button-color: {{ settings.text_color.red }}, {{ settings.text_color.green }}, {{ settings.text_color.blue }};
  
    --section-block-background: {{ section_block_background.red }}, {{ section_block_background.green }}, {{ section_block_background.blue }};
    
  }

</style>

{% schema %}
{
  "name": "Customer Login",
  "class": "shopify-section--quiz-login",
  "settings": [
    {
      "type": "image_picker",
      "id": "page_banner",
      "info": "2160 x 1080px .jpg recommended, 1080 x 1080px .jpg recommended if split",
      "label": "Banner - Right (Desktop Only)"
    },
    {
      "type": "richtext",
      "id": "footer_text",
      "label": "Footer Text"
    },
    {
      "type": "header",
      "content": "Colors"
    },
    {
      "type": "color",
      "id": "text_background",
      "label": "Text background",
      "default": "rgba(0,0,0,0)"
    },
    {
      "type": "color",
      "id": "text_color",
      "label": "Text color",
      "default": "rgba(0,0,0,0)"
    },
    {
      "type": "color",
      "id": "button_background",
      "label": "Button background",
      "default": "rgba(0,0,0,0)"
    },
    {
      "type": "color",
      "id": "button_text_color",
      "label": "Button text",
      "default": "rgba(0,0,0,0)"
    }
  ],
  "blocks": [
    {
      "type": "@app"
    },
    {
      "type": "liquid",
      "name": "Liquid",
      "settings": [
        {
          "type": "liquid",
          "id": "liquid",
          "label": "Liquid",
          "info": "Add app snippets or other Liquid code to create advanced customizations."
        }
      ]
    },
    {
      "type": "login",
      "name": "Login form",
      "limit": 1
    }
  ]
}
{% endschema %}
