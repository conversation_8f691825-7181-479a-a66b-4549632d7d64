<modal-content section="{{ section.id }}" id="quiz-popup--weight" class="modal modal--quiz">

  <div class="modal__overlay"></div>

  <div class="modal__content">

    <button type="button" class="modal__close-button tap-area" data-action="close" title="{{ 'general.accessibility.close' | t | escape }}">
      {%- render 'icon' with 'close' -%}
    </button>

    <div class="newsletter-modal">

      <div class="newsletter-modal__content newsletter-modal__content--extra text-container text--center">

        <h3 class="newsletter__title">{{ section.settings.title }}</h3>
        
        <div class="newsletter__description rte">
          {{ section.settings.content }}
        </div>
        
        <div class="newsletter__actions padding-top--20">
          <button type="button" class="button button--primary" data-action="close">
            <span class="button__text">{{ section.settings.close_button_text }}</span>
          </a>
        </div>
        
      </div>

    </div>
  </div>
  
</modal-content>

{% schema %}
{
  "name": "Popup - Weight",
  "class": "shopify-section--newsletter quiz",
  "settings": [
    {
      "type": "paragraph",
      "content": "Customers who subscribe will have their email address added to the \"accepts marketing\" [customer list](/admin/customers?query=&accepts_marketing=1)."
    },
    {
      "type": "checkbox",
      "id": "reveal_on_scroll",
      "label": "Reveal on scroll",
      "info": "Show animation when section becomes visible.",
      "default": true
    },
    {
      "type": "image_picker",
      "id": "image",
      "label": "Image",
      "info": "2000 x 980px .jpg recommended"
    },
    {
      "type": "text",
      "id": "title",
      "label": "Title",
      "default": "Popup title"
    },
    {
      "type": "richtext",
      "id": "content",
      "label": "Content",
      "default": "<p>Content for the popup</p>"
    },
    {
      "type": "text",
      "id": "close_button_text",
      "label": "Close Button Text",
      "default": "Got it!"
    }
  ]
}
{% endschema %}