{% assign age = customer.metafields.customer_fields.data['dogs_age'] %}
{% assign weight = customer.metafields.customer_fields.data['dogs_weight_lb'] %}
{% assign activity_level = customer.metafields.customer_fields.data['dogs_activity_level'] %}
{% assign diet_type = customer.metafields.customer_fields.data['dietary_needs'] %}
{% assign name = customer.metafields.customer_fields.data['your_dogs_name'] %}
<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.4/jquery.min.js"></script>
<section id="calculator">
<div class="container">
  <div class="page-content page-content--medium rte">
    <div id="meal-calculator">
      <div id="meal-calculator-form">
        <p class="calculator-form-group m-bottom-0">
      <label class="label" for="dog-weight">{% if name != blank %}{{ name }}'s{% else %}Your Dog's{% endif %} Recommended Weight (lb)</label>
        <input class="input" type="number" id="dog-weight" name="dog-weight" min="1" max="200" value="{{ weight | ceil }}">
        </p>
        <p class="calculator-form-group m-top-15 m-bottom-0">
        <label class="label" for="dog-activity-level">Activity Level</label>
        <select class="select" name="dog-activity-level" id="dog-activity-level" value="{{ activity_level }}">
          <option value="1.3">Moderate</option>
          <option value="1.4">High</option>
        </select>
        </p>
        <p class="calculator-form-group m-top-15">
        <label class="label" for="dog-age">Age</label>
        <select class="select" name="dog-age" id="dog-age" value="{{ age }}">
          <option value="2">1½ - 3 Month</option>
          <option value="1.5">3 - 6 Month</option>
          <option value="1.25">6 - 12 Month</option>
          <option value="1" selected>Adult (1 - 6 Years)</option>
          <option value="0.9">Senior (7+ Years)</option>
        </select>
        </p>
        <p class="calculator-form-group m-top-15">
        <label class="label" for="calculator-method">Diet Type</label>
        <select class="select" name="calculator-method" id="calculator-method" value="{{ diet_type }}">
          <option value="balanced">Complete &amp; Balanced</option>
          <option value="condition">Condition Specific</option>
        </select>
        </p>
        <a id="meal-calculator-submit" class="button button--primary">Calculate</a>
      </div>
      <p id="meal-calculator-error-msg" style="font-weight: bold;"></p>
      <table class="table table-hover" width="100%" style="display: none;" summary="Meal Calculator Results"
                                                              id="meal-calculator-results">
        <thead>
          <tr>
            <th scope="col">DIET</th>
            <th scope="col">OZ/DAY</th>
            <th scope="col">CUPS/DAY</th>
          </tr>
        </thead>
        <tbody>

        {% for block in section.blocks %}
          {% if block.type == "complete" %}
            {% assign product = all_products[block.settings.food] %}
            {% if product.metafields.diet_info.cals != blank %}
            <tr>
              <td>{{ product.title | link_to: product.url }}</td>
              <td id="{{ block.id }}_oz">0</td>
              <td id="{{ block.id }}_cups">0</td>
            </tr>
            {% endif %}
          {% endif %}
        {% endfor %}

        </tbody>
      </table>
      <table class="table table-hover" style="display: none;" summary="Meal Calculator Results" id="meal-calculator-results2">
        <thead>
          <tr>
            <th scope="col">DIET</th>
            <th scope="col">OZ/DAY</th>
            <th scope="col">CUPS/DAY</th>
          </tr>
        </thead>
        <tbody>

        {% for block in section.blocks %}
          {% if block.type == "condition" %}
            {% assign product = all_products[block.settings.food] %}
            {% if product.metafields.diet_info.cals != blank %}
            <tr>
              <td>{{ product.title | link_to: product.url }}</td>
              <td id="{{ block.id }}_oz">0</td>
              <td id="{{ block.id }}_cups">0</td>
            </tr>
          {% endif %}
          {% endif %}
        {% endfor %}

        </tbody>
      </table>
      <p>{{ section.settings.footnotes }}</p>
    </div>
  </div>
</div>
</section>

<!-- Custom Calculator -->

<script>
  (function () {
    var complete = {
      {% for block in section.blocks %}
        {% if block.type == "complete" %}
          {% assign product = all_products[block.settings.food] %}
          {% if product.metafields.diet_info.cals != blank %}
          '{{ block.id }}': { cal: {{ product.metafields.diet_info.cals }} },
          {% endif %}
        {% endif %}
      {% endfor %}
    };

    var condition = {
      {% for block in section.blocks %}
        {% if block.type == "condition" %}
          {% assign product = all_products[block.settings.food] %}
          {% if product.metafields.diet_info.cals != blank %}
          '{{ block.id }}': { cal: {{ product.metafields.diet_info.cals }} },
          {% endif %}
        {% endif %}
      {% endfor %}
    };

    function Dog(weight, activityLevel, age) {
      this.weight = weight;

      this.activityLevel = activityLevel;
      this.age = age;

      this.getWeightInKg = function () {
        return this.weight / 2.2;
      };
    }

    function Calculator(dog) {
      var that = this;
      this.dog = dog;
      this.cups = 0.125;

      this.formula = function () {
        return (70 * Math.pow(that.dog.getWeightInKg(), 0.75) * that.dog.activityLevel) * that.dog.age * (.8);
      }; 

      this.getFormula = function (age) {
        return this.formula
      };

      this.getCaloriesPerDay = function (formula) {
        if (formula) {
          return formula(that.dog);
        }
        return false;
      };

      this.getOzPerDayRoundedToQuarter = function (caloriesPerDay, meal) {
        return Math.round(caloriesPerDay * (1 / meal.cal) * 1) / 1;
      };

      this.getCupsPerDayRoundedToQuarter = function (caloriesPerDay, meal) {
        return Math.round(caloriesPerDay * (1 / meal.cal) * this.cups * 4) / 4;
      };
    }

    function main() {
      var weight = document.getElementById('dog-weight');
      var activityLevel = document.getElementById('dog-activity-level');
      var age = document.getElementById('dog-age');
      var calcResults = document.getElementById('meal-calculator-results');
      var calcResults2 = document.getElementById('meal-calculator-results2');
      var calculatorMethod = document.getElementById('calculator-method');
      var errorMsg = document.getElementById('meal-calculator-error-msg');

      document.getElementById('meal-calculator-submit').addEventListener('click', function () {
        var dog = new Dog(weight.value, activityLevel.value, age.value);

        var calculator = new Calculator(dog);
        var formula = calculator.getFormula(dog.age);

        var caloriesPerDay = calculator.getCaloriesPerDay(formula);
        errorMsg.innerHTML = '';
        calcResults.style.display = 'none';

        calcResults2.style.display = 'none';
        if (caloriesPerDay) {
          if (calculatorMethod.value == 'balanced') {
            for (var meal in complete) {
              if (complete.hasOwnProperty(meal)) {
                document.getElementById(meal + '_oz').innerHTML = calculator.getOzPerDayRoundedToQuarter(caloriesPerDay, complete[meal]);
                document.getElementById(meal + '_cups').innerHTML = calculator.getCupsPerDayRoundedToQuarter(caloriesPerDay, complete[meal]);
                calcResults.style.removeProperty('display');
              }
            }
          } else {
            for (var meal in condition) {
              if (condition.hasOwnProperty(meal)) {
                document.getElementById(meal + '_oz').innerHTML = calculator.getOzPerDayRoundedToQuarter(caloriesPerDay, condition[meal]);
                document.getElementById(meal + '_cups').innerHTML = calculator.getCupsPerDayRoundedToQuarter(caloriesPerDay, condition[meal]);
                calcResults2.style.removeProperty('display');
              }
            }
          }
        } else {
          errorMsg.innerHTML = 'ERROR: ENTER YOUR DOGS RECOMMENDED WEIGHT AT THE TOP';
        }

      });
    }

    window.onload = function () {
      main();
      {% if name != blank %}
        document.getElementById('meal-calculator-submit').click()
      {% endif %}
    };
  })();
</script>

{% comment %}
{% style %}
#calculator #meal-calculator-form .js-qty {
  margin-bottom: 0;
}

#calculator #meal-calculator-form select {
  width: 100%;
  border: 1px solid gray;
  -webkit-border-radius: 0;
     -moz-border-radius: 0;
          border-radius: 0;
  font-size: 16px;
  font-weight: normal;
  color: #000;
  margin-top: 5px;
  background-color: transparent !important;
}

#calculator #meal-calculator-form #dog-weight {
  -webkit-border-radius: 3px;
     -moz-border-radius: 3px;
          border-radius: 3px;
  margin-top: 10px;
}

#calculator #meal-calculator table th {
  border-color: #ffd921;
}

td {
  border-color: #ffd921;
}

#calculator {
  background: #f0f0f0;
  max-width: 884px;
  margin: 0 auto;
}

#calculator #meal-calculator table {
  margin-bottom: 25px;
}

#calculator #meal-calculator .label {
  font-size: 11px;
  text-transform: uppercase;
}

#calculator #meal-calculator-submit {
  background: #414141;
  height: auto;
  width: auto;
  padding: 10px 35px;
  line-height: normal;
  font-size: 14px;
  letter-spacing: .9px;
  border: none;
}

#calculator #meal-calculator-submit:hover {
  background: #5a5a5a;
}

#feeding-calculator header {
  margin-bottom: 0;
}

#feeding-calculator .header-section--wrapper {
  height: auto !important;
}

#calculator {
  /*
   *@media screen and (min-width: 320px) and (max-width: 767px) {
   *  #calculator #meal-calculator-results {
   *    width: 100%;
   *  }
   *}
   */
}

#calculator #meal-calculator-results,
#calculator #meal-calculator-results2 {
  background: #f0f0f0;
  margin: 0 auto;
}

#calculator #meal-calculator-results th,
#calculator #meal-calculator-results2 th {
  background: #414141;
  color: #fff;
}

#calculator #meal-calculator-results td:nth-child(2), #calculator #meal-calculator-results td:nth-child(3),
#calculator #meal-calculator-results2 td:nth-child(2),
#calculator #meal-calculator-results2 td:nth-child(3) {
  text-align: center;
}

#calculator #meal-calculator-results tr:nth-child(2n+1),
#calculator #meal-calculator-results2 tr:nth-child(2n+1) {
  background: #f0f0f0;
}

#calculator #meal-calculator-results tr:nth-child(2n),
#calculator #meal-calculator-results2 tr:nth-child(2n) {
  background: #fff;
}

#calculator .calc-image {
  padding: 0;
}

#calculator .calc-image img {
  display: block;
  width: 70px;
  max-width: unset;
}

{% endstyle %}
{% endcomment %}

{% schema %}
  {
    "name": "Feeding Calculator",
    "settings": [
      {
        "type": "header",
        "content": "Dog Feeding Calculator"
      },
      {
        "type": "paragraph",
        "content": "Adjust the settings and add/remove products to the feeding calculator"
      },
      {
        "id": "footnotes",
        "type": "textarea",
        "label": "Footnotes"
      }
    ],
    "blocks": [
      {
        "name": "Complete & Balanced",
        "type": "complete",
        "settings": [
          {
            "id": "food",
            "label": "Food",
            "type": "product"
          }
        ]
      },
      {
        "name": "Condition Specific",
        "type": "condition",
        "settings": [
          {
            "id": "food",
            "label": "Food",
            "type": "product"
          }
        ]
      }
    ]
  }
{% endschema %}
