  <div class="quiz-step__inner">

    <div class="quiz-step__body quiz-step__body--narrow">

      {% if section.settings.icon != blank %}
        <img class="quiz-step-logo" sizes="(max-width: 740px) calc(100vw - 96px), (max-width: 999px) calc(100vw - 160px), 600px" loading="lazy" {% render 'image-attributes', image: section.settings.icon, sizes: '600,700,800,1000,1200,1400' %}>
      {% endif %}

      {% if section.settings.title != blank %}
        <h1 class="h2 quiz-step-title">{{ section.settings.title }}</h1>
      {% endif %}
  
      {% if section.settings.description != blank %}
        <div class="quiz-step-description text--large">{{ section.settings.description }}</div>
      {% endif %}

      <div class="quiz-step-actions">

        <button type="button" is="loader-button" class="button button--highlight" data-action="nextStep">
          <span class="button__text">{{ 'quiz.general.create_account' | t }}</span>
          <span class="button__icon">{%- render 'icon' with 'nav-arrow-right' -%}</span>
        </button>

        <div class="quiz-step-actions-account">
          <div class="quiz-step-actions__inner">
            {% if customer %}
              {{ 'quiz.general.welcome_back_html' | t: customer_first_name: customer.first_name }}
            {% else %}
              {{ 'quiz.general.have_an_account' | t }}
              <a href="{{ routes.account_register_url }}" class="link">{{ 'quiz.general.login' | t }}</a>
            {% endif %}
          </div>
        </div>

        <button type="button" is="loader-button" class="button button--link" data-quiz-button-skip>
          <span class="button__text">{{ 'quiz.inline-account.navigation.skip' | t }}</span>
          <span class="button__icon">{%- render 'icon' with 'nav-arrow-right' -%}</span>
        </button>
      
      </div>

    </div>

  </div>

  {% render 'quiz-decoration', classes: 'quiz-decoration--1' %}
  {% render 'quiz-decoration', classes: 'quiz-decoration--2' %}


{% schema %}
{
  "name": "Start",
  "class": "shopify-section--quiz-home",
  "settings": [
    {
      "type": "header",
      "content": "Home"
    },
    {
      "type": "image_picker",
      "id": "icon",
      "label": "Icon"
    },
    {
      "type": "text",
      "id": "title",
      "label": "Title",
      "default": "Save Your Pup Profiles"
    },
    {
      "type": "richtext",
      "id": "description",
      "label": "Description",
      "default": "<p>While we prepare your recommended recipes, you can save your pup’s profile by providing your contact details.</p>"
    },
    {
      "type": "richtext",
      "id": "hint",
      "label": "Hint",
      "default": "<p>This takes ~3 minutes per dog.</p>"
    },
    {
      "type": "header",
      "content": "Home"
    },
    {
      "type": "text",
      "id": "button_1_label",
      "label": "Label",
      "default": "Save Your Pup Profiles"
    }
  ]
}
{% endschema %}