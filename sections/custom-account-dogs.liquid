{%- if section.settings.background_type == 'boxed' -%}
  {%- assign is_boxed = true -%}
{%- else -%}
  {%- assign is_boxed = false -%}
{%- endif -%}

{%- if section.settings.background != settings.background and section.settings.background != 'rgba(0,0,0,0)' -%}
  {%- assign blends_with_background = false -%}
{%- else -%}
  {%- assign blends_with_background = true -%}
{%- endif -%}

<style>

  #shopify-section-{{ section.id }} {
    {%- if section.settings.background == 'rgba(0,0,0,0)' -%}
      {%- assign section_background = settings.background -%}
    {%- else -%}
      {%- assign section_background = section.settings.background -%}
    {%- endif -%}

    {%- if section.settings.background != 'rgba(0,0,0,0)' -%}
      {%- assign section_background = section.settings.background -%}
    {%- endif -%}

    {%- if section.settings.heading_color != 'rgba(0,0,0,0)' -%}
      {%- assign heading_color = section.settings.heading_color -%}
    {%- endif -%}

    {%- if section.settings.text_color != 'rgba(0,0,0,0)' -%}
      {%- assign text_color = section.settings.text_color -%}
    {%- endif -%}

    {%- if section.settings.button_background == 'rgba(0,0,0,0)' -%}
      {%- assign button_background = settings.primary_button_background -%}
    {%- else -%}
      {%- assign button_background = section.settings.button_background -%}
    {%- endif -%}

    {%- if section.settings.button_text_color == 'rgba(0,0,0,0)' -%}
      {%- assign button_text_color = settings.primary_button_text_color -%}
    {%- else -%}
      {%- assign button_text_color = section.settings.button_text_color -%}
    {%- endif -%}

    --primary-button-background: {{ button_background.red }}, {{ button_background.green }}, {{ button_background.blue }};
    --primary-button-text-color: {{ button_text_color.red }}, {{ button_text_color.green }}, {{ button_text_color.blue }};

    --heading-color: {{ heading_color.red }}, {{ heading_color.green }}, {{ heading_color.blue }};
    --text-color: {{ text_color.red }}, {{ text_color.green }}, {{ text_color.blue }};
    --section-background: {{ section_background.red }}, {{ section_background.green }}, {{ section_background.blue }};

  }

  #shopify-section-{{ section.id }} .section__body .section__color-wrapper {

    {%- if section.settings.content_background != 'rgba(0,0,0,0)' -%}
      {%- assign content_background = section.settings.content_background -%}
    {%- endif -%}

    {%- if section.settings.content_heading_color != 'rgba(0,0,0,0)' -%}
      {%- assign content_heading_color = section.settings.content_heading_color -%}
    {%- endif -%}

    {%- if section.settings.content_text_color != 'rgba(0,0,0,0)' -%}
      {%- assign content_text_color = section.settings.content_text_color -%}
    {%- endif -%}

    --heading-color: {{ content_heading_color.red }}, {{ content_heading_color.green }}, {{ content_heading_color.blue }};
    --text-color: {{ content_text_color.red }}, {{ content_text_color.green }}, {{ content_text_color.blue }};
    --section-background: {{ content_background.red }}, {{ content_background.green }}, {{ content_background.blue }};

  }
  
</style>

<a id="my-dogs" name="my-dogs" class="anchor"></a>

<section class="section section--use-padding {% unless blends_with_background or is_boxed %}section--flush{% endunless %} section__color-wrapper">

  {%- capture section_header -%}
    {%- if section.settings.subheading != blank -%}
      <h2 class="heading heading--small">{{ section.settings.subheading | escape }}</h2>
    {%- endif -%}

    {%- if section.settings.title != blank -%}
      <h3 class="heading h4">{{ section.settings.title | escape }}</h3>
    {%- endif -%}

    {%- if section.settings.content != blank -%}
      {{- section.settings.content -}}
    {%- endif -%}
  {%- endcapture -%}

  {%- if section_header != blank -%}
    <div class="section__header">
      {{ section_header }}
    </div>
  {%- endif -%}

  <div class="section__body">

    <div class="container">
      
      <div class="content-box text-container">

        <account-dog-info class="account-dog-info section__color-wrapper">

          {%- capture dog_list -%}

            {%- for i in (1..10) -%}
              
              {%- capture weight_profile_stub -%}dog_{{ i }}_weight_profile{% endcapture %}
              {%- capture weight_stub -%}dog_{{ i }}_weight{% endcapture %}
              {%- capture sex_stub -%}dog_{{ i }}_sex{% endcapture %}
              {%- capture prescription_diet_stub -%}dog_{{ i }}_prescription_diet{% endcapture %}
              {%- capture neutered_stub -%}dog_{{ i }}_neutered{% endcapture %}
              {%- capture name_stub -%}dog_{{ i }}_name{% endcapture %}
              {%- capture ideal_weight_stub -%}dog_{{ i }}_ideal_weight{% endcapture %}
              {%- capture has_health_issue_stub -%}dog_{{ i }}_has_health_issue{% endcapture %}
              {%- capture breed_stub -%}dog_{{ i }}_breed{% endcapture %}
              {%- capture age_in_months_stub -%}dog_{{ i }}_age_in_months{% endcapture %}
              {%- capture activity_level_stub -%}dog_{{ i }}_activity_level{% endcapture %}
              
              {%- assign weight_profile = customer.metafields.quiz[weight_profile_stub] -%}
              {%- assign weight = customer.metafields.quiz[weight_stub] -%}
              {%- assign sex = customer.metafields.quiz[sex_stub] -%}
              {%- assign prescription_diet = customer.metafields.quiz[prescription_diet_stub] -%}
              {%- assign neutered = customer.metafields.quiz[neutered_stub] -%}
              {%- assign name = customer.metafields.quiz[name_stub] -%}
              {%- assign ideal_weight = customer.metafields.quiz[ideal_weight_stub] -%}
              {%- assign has_health_issue = customer.metafields.quiz[has_health_issue_stub] -%}
              {%- assign breed = customer.metafields.quiz[breed_stub] -%}
              {%- assign age_in_months = customer.metafields.quiz[age_in_months_stub] -%}
              {%- assign activity_level = customer.metafields.quiz[activity_level_stub] -%}

              {%- if weight == blank or weight == 0.0 -%}
                {%- continue -%}
              {%- endif -%}

              {%- if ideal_weight == blank or ideal_weight == 0.0 -%}
                {%- continue -%}
              {%- endif -%}

              {%- if age_in_months == blank or age_in_months == 0.0 -%}
                {%- continue -%}
              {%- endif -%}

              {%- if weight_profile == blank or weight_profile == "None" -%}
                {%- continue -%}
              {%- endif -%}

              {%- if sex == blank or sex == "None" -%}
                {%- continue -%}
              {%- endif -%}

              {% comment %} {%- if prescription_diet == blank or prescription_diet == "None" -%} {% endcomment %}
                {% comment %} {%- continue -%} {% endcomment %}
              {% comment %} {%- endif -%} {% endcomment %}

              {%- if neutered == blank or neutered == "None" -%}
                {%- continue -%}
              {%- endif -%}

              {%- if name == blank or name == "None" -%}
                {%- continue -%}
              {%- endif -%}

              {%- if has_health_issue == blank or has_health_issue == "None" -%}
                {%- continue -%}
              {%- endif -%}

              {%- if breed == blank or breed == "None" -%}
                {%- continue -%}
              {%- endif -%}

              {%- if activity_level == blank or activity_level == "None" -%}
                {%- continue -%}
              {%- endif -%}


              {% comment %} Parsing {% endcomment %}

              {%- if prescription_diet == blank or prescription_diet == "None" -%}
                {%- assign prescription_diet = "no prescription diet" -%}
              {%- else -%}
                {%- capture prescription_diet -%}a {{ prescription_diet }} diet{%- endcapture -%}
              {%- endif -%}

              {%- if activity_level == "Low" -%}
                {%- assign activity_level = "not very active" -%}
              {%- elsif activity_level == "Normal" -%}
                {%- assign activity_level = "moderately active" -%}
              {%- elsif activity_level == "High" -%}
                {%- assign activity_level = "highly active" -%}
              {%- endif -%}

              {%- assign weight = weight | floor -%}

              {%- if age_in_months >= 12 -%}
                {%- assign age = age_in_months | divided_by: 12 | floor -%}
                {%- if age > 2 -%}
                    {%- assign age_type = "Years" -%}
                  {%- else -%}
                    {%- assign age_type = "Year" -%}
                {%- endif -%}
              {%- else -%}
                {%- if age > 2 -%}
                    {%- assign age_type = "Months" -%}
                  {%- else -%}
                    {%- assign age_type = "Month" -%}
                {%- endif -%}
              {%- endif -%}

              {% comment %} Display {% endcomment %}

              <p class=" text-center">
                <strong data-dog-name>{{ name }}</strong>
                weighs 
                <strong data-dog-weight>{{ weight }}lbs</strong>, 
                is <strong data-age>{{ age }} {{ age_type }}</strong>
                old, is 
                <strong data-activity-level>{{ activity_level }}</strong>
                and has
                <strong data-diet>{{ prescription_diet }}</strong>.
              </p>

            {%- endfor -%}

          {%- endcapture -%}

          <div class="account-dog-info__body">

            {%- if dog_list != blank -%}
              
              {{ dog_list }}

            {%- else -%}

              <div class="text-container text-center">
                <p class="h4">Looks like we haven't met your pups yet!</p>
                <p>Want vet-recommended meal plans for your dogs?</p>
              </div>

            {%- endif -%}
            
          </div>

          <div class="account-dog-info__footer">
            
            <div class="button-wrapper button-wrapper--center">

              {%- if dog_list != blank -%}

                <a class="button button--large button--highlight" href="{{ settings.quiz_bookmark_results }}">
                  <span class="button__text">Go to My Recommendations</span>
                  <span class="button__icon">{%- render 'icon' with 'nav-arrow-right' -%}</span>
                </a>
                
                <a class="button button--large button--primary" href="{{ settings.quiz_bookmark_quiz }}">
                  <span class="button__text">Take the Quiz Again!</span>
                  <span class="button__icon">{%- render 'icon' with 'nav-arrow-right' -%}</span>
                </a>
              
              {%- else -%}

                <a class="button button--large button--highlight" href="{{ settings.quiz_bookmark_quiz }}">
                  <span class="button__text">Take the Quiz!</span>
                  <span class="button__icon">{%- render 'icon' with 'nav-arrow-right' -%}</span>
                </a>
              
              {%- endif -%}
    
            </div>

          </div>
        
        </account-dog-info>

      </div>

    </div>
  
  </div>

</section>

{% schema %}
{
  "name": "Account - My Dogs",
  "class": "shopify-section--account-my-dogs",
  "disabled_on": {
    "groups": ["header", "custom.overlay"]
  },
  "settings": [
    {
      "type": "text",
      "id": "subheading",
      "label": "Subheading",
      "default": "Subheading"
    },
    {
      "type": "text",
      "id": "title",
      "label": "Heading",
      "default": "Talk about your brand"
    },
    {
      "type": "richtext",
      "id": "content",
      "label": "Content",
      "default": "<p>Use this text to share information about your brand with your customers. Describe a product, share announcements, or welcome customers to your store.</p>"
    },
    {
      "type": "text",
      "id": "button_text",
      "label": "Button text"
    },
    {
      "type": "url",
      "id": "button_link",
      "label": "Button link"
    },
    {
      "type": "select",
      "id": "background_type",
      "label": "Background",
      "options": [
        {
          "value": "full_width",
          "label": "Full width"
        },
        {
          "value": "boxed",
          "label": "Boxed"
        }
      ],
      "default": "full_width"
    },
    {
      "type": "select",
      "id": "text_width",
      "label": "Text width",
      "options": [
        {
          "value": "small",
          "label": "Small"
        },
        {
          "value": "medium",
          "label": "Medium"
        },
        {
          "value": "large",
          "label": "Large"
        },
        {
          "value": "fill",
          "label": "Fill screen"
        }
      ],
      "default": "medium"
    },
    {
      "type": "select",
      "id": "text_position",
      "label": "Text position",
      "options": [
        {
          "value": "left",
          "label": "Left"
        },
        {
          "value": "center",
          "label": "Center"
        },
        {
          "value": "right",
          "label": "Right"
        }
      ],
      "default": "center"
    },
    {
      "type": "select",
      "id": "text_alignment",
      "label": "Text alignment",
      "options": [
        {
          "value": "left",
          "label": "Left"
        },
        {
          "value": "center",
          "label": "Center"
        },
        {
          "value": "right",
          "label": "Right"
        }
      ],
      "default": "center"
    },
    {
      "type": "header",
      "content": "Colors"
    },
    {
      "type": "color",
      "id": "background",
      "label": "Background",
      "default": "rgba(0,0,0,0)"
    },
    {
      "type": "color",
      "id": "heading_color",
      "label": "Heading color",
      "default": "rgba(0,0,0,0)"
    },
    {
      "type": "color",
      "id": "text_color",
      "label": "Text color",
      "default": "rgba(0,0,0,0)"
    },
    {
      "type": "color",
      "id": "button_background",
      "label": "Button background",
      "default": "rgba(0,0,0,0)"
    },
    {
      "type": "color",
      "id": "button_text_color",
      "label": "Button text",
      "default": "rgba(0,0,0,0)"
    },
    {
      "type": "paragraph",
      "content": "Content"
    },
    {
      "type": "color",
      "id": "content_background",
      "label": "Background",
      "default": "rgba(0,0,0,0)"
    },
    {
      "type": "color",
      "id": "content_heading_color",
      "label": "Heading color",
      "default": "rgba(0,0,0,0)"
    },
    {
      "type": "color",
      "id": "content_text_color",
      "label": "Text color",
      "default": "rgba(0,0,0,0)"
    }
  ],
  "presets": [
    {
      "name": "Rich text",
      "settings": {}
    }
  ]
}
{% endschema %}