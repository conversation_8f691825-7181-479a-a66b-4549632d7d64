{%- if section.settings.background != settings.background and section.settings.background != 'rgba(0,0,0,0)' -%}
  {%- assign blends_with_background = false -%}
{%- else -%}
  {%- assign blends_with_background = true -%}
{%- endif -%}

<style>
  #shopify-section-{{ section.id }} {
    {%- if section.settings.background == 'rgba(0,0,0,0)' -%}
      {%- assign section_background = settings.background -%}
    {%- else -%}
      {%- assign section_background = section.settings.background -%}
    {%- endif -%}

    {%- if section.settings.text_color == 'rgba(0,0,0,0)' -%}
      {%- assign heading_color = settings.heading_color -%}
      {%- assign text_color = settings.text_color -%}
    {%- else -%}
      {%- assign heading_color = section.settings.text_color -%}
      {%- assign text_color = section.settings.text_color -%}
    {%- endif -%}

    --heading-color: {{ heading_color.red }}, {{ heading_color.green }}, {{ heading_color.blue }};
    --text-color: {{ text_color.red }}, {{ text_color.green }}, {{ text_color.blue }};
    --prev-next-button-background: {{ settings.background.red }}, {{ settings.background.green }}, {{ settings.background.blue }};
    --prev-next-button-color: {{ settings.text_color.red }}, {{ settings.text_color.green }}, {{ settings.text_color.blue }};

    --section-background: {{ section_background.red }}, {{ section_background.green }}, {{ section_background.blue }};
    --section-products-per-row: 2;
  }

  @media screen and (min-width: 741px) {
    #shopify-section-{{ section.id }} {
      --section-products-per-row: 3;
    }
  }

  @media screen and (min-width: 1200px) {
    #shopify-section-{{ section.id }} {
      --section-products-per-row: 4;
    }
  }
</style>

<recently-viewed-products section-id="{{ section.id }}" products-count="{{ section.settings.products_count | escape }}" {% if request.page_type == 'product' %}exclude-product-id="{{ product.id | escape }}"{% endif %} class="section {% unless blends_with_background %}section--flush{% endunless %}">
  {%- if request.page_type == 'search' and search.performed and search.results_count > 0 -%}
    <div class="section__color-wrapper">
      <div class="container {% unless blends_with_background %}vertical-breather{% endunless %}">
        {%- if section.settings.title != blank or section.settings.subheading != blank -%}
          <header class="section__header">
            <div class="text-container">
              {%- if section.settings.subheading != blank -%}
                <h2 class="heading heading--small">{{ section.settings.subheading }}</h2>
              {%- endif -%}

              {%- if section.settings.title != blank -%}
                <h3 class="heading h3">{{ section.settings.title }}</h3>
              {%- endif -%}
            </div>
          </header>
        {%- endif -%}

        <product-list {% if settings.stagger_products_apparition %}stagger-apparition{% endif %} class="product-list product-list--center">
          {%- assign smallest_image_aspect_ratio = 0 -%}

          <div class="scroller">
            <div class="product-list__inner product-list__inner--scroller hide-scrollbar">
              {%- comment -%}
                NOTE: we are doing an inner for loop to preserve the order (from most recently seen to oldest one), as search.results, out of the box,
                will order by title, which is not really desirable.
              {%- endcomment -%}

              {%- assign parsed_terms = search.terms | split: ' OR ' -%}

              {%- for parsed_term in parsed_terms -%}
                {%- assign id = parsed_term | split: 'id:' | last | times: 1 -%}

                {%- for product in search.results -%}
                  {%- comment %}<locksmith:a706>{% endcomment -%}
                    {%- capture var %}{% render 'locksmith-variables', scope: 'subject', subject: product, subject_parent: search, variable: 'transparent' %}{% endcapture %}{% if var == "true" %}{% else %}{% continue %}{% endif -%}
                  {%- comment %}</locksmith:a706>{% endcomment -%}
                  {%- if product.id == id -%}
                    {%- assign smallest_image_aspect_ratio = smallest_image_aspect_ratio | at_least: product.featured_media.aspect_ratio -%}

                    {%- capture sizes_attribute -%}(max-width: 740px) 75vw, min({{ 100.0 | divided_by: 4 | ceil }}vw, {{ 1520.0 | divided_by: 4 | ceil }}px){%- endcapture -%}
                    {%- render 'product-item', product: product, sizes_attribute: sizes_attribute, reveal: settings.stagger_products_apparition -%}

                    {%- break -%}
                  {%- endif -%}
                {%- endfor -%}
              {%- endfor -%}
            </div>
          </div>

          {%- if search.results_count > 4 -%}
            <prev-next-buttons class="product-list__prev-next hidden-pocket" style="--smallest-image-aspect-ratio: {{ smallest_image_aspect_ratio }}">
              <button class="product-list__arrow prev-next-button prev-next-button--prev" disabled data-action="prev">
                <span class="visually-hidden">{{ 'general.accessibility.previous' | t }}</span>
                {%- render 'icon' with 'nav-arrow-left', block: true, direction_aware: true -%}
              </button>

              <button class="product-list__arrow prev-next-button prev-next-button--next" data-action="next">
                <span class="visually-hidden">{{ 'general.accessibility.next' | t }}</span>
                {%- render 'icon' with 'nav-arrow-right', block: true, direction_aware: true -%}
              </button>
            </prev-next-buttons>
          {%- endif -%}
        </product-list>
      </div>
    </div>
  {%- endif -%}
</recently-viewed-products>

{% schema %}
{
  "name": "Recently viewed products",
  "class": "shopify-section--recently-viewed-products",
  "disabled_on": {
    "templates": ["password"],
    "groups": ["header", "custom.overlay"]
  },
  "settings": [
    {
      "type": "paragraph",
      "content": "Section is hidden if no products have been visited."
    },
    {
      "type": "text",
      "id": "subheading",
      "label": "Subheading"
    },
    {
      "type": "text",
      "id": "title",
      "label": "Heading",
      "default": "Recently viewed"
    },
    {
      "type": "range",
      "id": "products_count",
      "min": 4,
      "max": 20,
      "label": "Products count",
      "default": 4
    },
    {
      "type": "header",
      "content": "Colors"
    },
    {
      "type": "color",
      "id": "background",
      "label": "Background",
      "default": "rgba(0,0,0,0)"
    },
    {
      "type": "color",
      "id": "text_color",
      "label": "Text",
      "default": "rgba(0,0,0,0)"
    }
  ],
  "presets": [
    {
      "name": "Recently viewed products"
    }
  ]
}
{% endschema %}