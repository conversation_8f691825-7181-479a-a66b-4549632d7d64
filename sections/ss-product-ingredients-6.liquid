{% comment %}
  ---------------------------------------------------------
  Copyright © 2025 Section Store. All rights reserved.
  Unauthorized copying, modification, distribution, or use
  of this code or any portion of it, is strictly prohibited.
  Violators will be prosecuted to the fullest extent of the law.
  For inquiries or permissions, contact <EMAIL>
  ---------------------------------------------------------
{% endcomment %}

<link
  rel="stylesheet"
  href="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.css"
>

{%- liquid
  assign padding_horizontal = section.settings.padding_horizontal
  assign padding_horizontal_mobile = section.settings.padding_horizontal_mobile
  assign padding_top = section.settings.padding_top
  assign padding_bottom = section.settings.padding_bottom
  assign border_color = section.settings.border_color
  assign border_thickness = section.settings.border_thickness
  assign margin_top = section.settings.margin_top
  assign margin_bottom = section.settings.margin_bottom
  assign margin_horizontal_mobile = section.settings.margin_horizontal_mobile
  assign margin_horizontal = section.settings.margin_horizontal
  assign background_color = section.settings.background_color
  assign background_gradient = section.settings.background_gradient
  assign full_width = section.settings.full_width
  assign content_width = section.settings.content_width
  assign lazy = section.settings.lazy
  assign section_radius = section.settings.section_radius

  assign content_align = section.settings.content_align
  assign content_align_mobile = section.settings.content_align_mobile
  assign content_maxwidth = section.settings.content_maxwidth

  assign cards_view = section.settings.cards_view
  assign cards_view_mobile = section.settings.cards_view_mobile
  assign cards_gap = section.settings.cards_gap
  assign cards_gap_mobile = section.settings.cards_gap_mobile
  assign cards_mt = section.settings.cards_mt
  assign cards_mt_mobile = section.settings.cards_mt_mobile
  assign cards_limits = section.settings.cards_limits

  assign card_border_thickness = section.settings.card_border_thickness
  assign card_border_color = section.settings.card_border_color
  assign card_radius = section.settings.card_radius
  assign card_padding_vertical = section.settings.card_padding_vertical
  assign card_padding_vertical_mobile = section.settings.card_padding_vertical_mobile
  assign card_padding_horizontal = section.settings.card_padding_horizontal
  assign card_padding_horizontal_mobile = section.settings.card_padding_horizontal_mobile
  assign card_content_align = section.settings.card_content_align
  assign card_content_align_mobile = section.settings.card_content_align_mobile
  assign card_bg_color = section.settings.card_bg_color
  assign card_align = section.settings.card_align
  assign card_align_mobile = section.settings.card_align_mobile

  assign title_custom = section.settings.title_custom
  assign title_font = section.settings.title_font
  assign title_size = section.settings.title_size
  assign title_size_mobile = section.settings.title_size_mobile
  assign title_height = section.settings.title_height
  assign title_color = section.settings.title_color

  assign text_custom = section.settings.text_custom
  assign text_font = section.settings.text_font
  assign text_size = section.settings.text_size
  assign text_size_mobile = section.settings.text_size_mobile
  assign text_height = section.settings.text_height
  assign text_color = section.settings.text_color
  assign text_mt = section.settings.text_mt
  assign text_mt_mobile = section.settings.text_mt_mobile

  assign source_custom = section.settings.source_custom
  assign source_font = section.settings.source_font
  assign source_size = section.settings.source_size
  assign source_size_mobile = section.settings.source_size_mobile
  assign source_height = section.settings.source_height
  assign source_color = section.settings.source_color
  assign source_mt = section.settings.source_mt
  assign source_mt_mobile = section.settings.source_mt_mobile

  assign plus_icon_size = section.settings.plus_icon_size
  assign plus_icon_size_mobile = section.settings.plus_icon_size_mobile
  assign plus_icon_bg_color = section.settings.plus_icon_bg_color
  assign plus_icon_color = section.settings.plus_icon_color

  assign see_all = section.settings.see_all
  assign see_less = section.settings.see_less
  assign see_custom = section.settings.see_custom
  assign see_font = section.settings.see_font
  assign see_size = section.settings.see_size
  assign see_size_mobile = section.settings.see_size_mobile
  assign see_height = section.settings.see_height
  assign see_color = section.settings.see_color

  assign modal_slide_padding_vertical = section.settings.modal_slide_padding_vertical
  assign modal_slide_padding_vertical_mobile = section.settings.modal_slide_padding_vertical_mobile
  assign modal_slide_padding_horizontal = section.settings.modal_slide_padding_horizontal
  assign modal_slide_padding_horizontal_mobile = section.settings.modal_slide_padding_horizontal_mobile
  assign modal_slide_border_thickness = section.settings.modal_slide_border_thickness
  assign modal_slide_border_color = section.settings.modal_slide_border_color
  assign modal_slide_radius = section.settings.modal_slide_radius
  assign modal_slide_bg_color = section.settings.modal_slide_bg_color
  assign modal_slide_align = section.settings.modal_slide_align
  assign modal_slide_align_mobile = section.settings.modal_slide_align_mobile
  assign modal_overlay_color = section.settings.modal_overlay_color

  assign modal_horizontal_align = ''
  if modal_slide_align == 'center'
    assign modal_horizontal_align = 'center'
  elsif modal_slide_align == 'right'
    assign modal_horizontal_align = 'end'
  else
    assign modal_horizontal_align = 'start'
  endif

  assign modal_horizontal_align_mobile = ''
  if modal_slide_align_mobile == 'center'
    assign modal_horizontal_align_mobile = 'center'
  elsif modal_slide_align_mobile == 'right'
    assign modal_horizontal_align_mobile = 'end'
  else
    assign modal_horizontal_align_mobile = 'start'
  endif

  assign modal_close_size = section.settings.modal_close_size
  assign modal_close_size_mobile = section.settings.modal_close_size_mobile
  assign modal_close_color = section.settings.modal_close_color

  assign slides_per_view = section.settings.slides_per_view

  assign slide_text_custom = section.settings.slide_text_custom
  assign slide_text_font = section.settings.slide_text_font
  assign slide_text_size = section.settings.slide_text_size
  assign slide_text_size_mobile = section.settings.slide_text_size_mobile
  assign slide_text_height = section.settings.slide_text_height
  assign slide_text_color = section.settings.slide_text_color
  assign slide_text_mt = section.settings.slide_text_mt
  assign slide_text_mt_mobile = section.settings.slide_text_mt_mobile

  assign slide_title_custom = section.settings.slide_title_custom
  assign slide_title_font = section.settings.slide_title_font
  assign slide_title_size = section.settings.slide_title_size
  assign slide_title_size_mobile = section.settings.slide_title_size_mobile
  assign slide_title_height = section.settings.slide_title_height
  assign slide_title_color = section.settings.slide_title_color
  assign slide_title_mt = section.settings.slide_title_mt
  assign slide_title_mt_mobile = section.settings.slide_title_mt_mobile

  assign slide_sub_text_custom = section.settings.slide_sub_text_custom
  assign slide_sub_text_font = section.settings.slide_sub_text_font
  assign slide_sub_text_size = section.settings.slide_sub_text_size
  assign slide_sub_text_size_mobile = section.settings.slide_sub_text_size_mobile
  assign slide_sub_text_height = section.settings.slide_sub_text_height
  assign slide_sub_text_color = section.settings.slide_sub_text_color
  assign slide_sub_text_mt = section.settings.slide_sub_text_mt
  assign slide_sub_text_mt_mobile = section.settings.slide_sub_text_mt_mobile

  assign button = section.settings.button
  assign button_size = section.settings.button_size
  assign button_size_mobile = section.settings.button_size_mobile
  assign button_color = section.settings.button_color
  assign button_hover_color = section.settings.button_hover_color
  assign button_custom = section.settings.button_custom
  assign button_font = section.settings.button_font
  assign button_height = section.settings.button_height
  assign button_padding_vertical = section.settings.button_padding_vertical
  assign button_padding_vertical_mobile = section.settings.button_padding_vertical_mobile
  assign button_padding_horizontal = section.settings.button_padding_horizontal
  assign button_padding_horizontal_mobile = section.settings.button_padding_horizontal_mobile
  assign button_radius = section.settings.button_radius
  assign button_border_thickness = section.settings.button_border_thickness
  assign button_border_color = section.settings.button_border_color
  assign button_border_hover_color = section.settings.button_border_hover_color
  assign button_bg_color = section.settings.button_bg_color
  assign button_bg_hover_color = section.settings.button_bg_hover_color
  assign button_mt = section.settings.button_mt
  assign button_mt_mobile = section.settings.button_mt_mobile
  assign button_style = section.settings.button_style

  assign pagination_custom = section.settings.pagination_custom
  assign pagination_font = section.settings.pagination_font
  assign pagination_size = section.settings.pagination_size
  assign pagination_size_mobile = section.settings.pagination_size_mobile
  assign pagination_height = section.settings.pagination_height
  assign pagination_color = section.settings.pagination_color
  assign pagination_arrow_color = section.settings.pagination_arrow_color

  assign loading_attribute = 'eager'
  if lazy
    assign loading_attribute = 'lazy'
  endif
-%}

{%- style -%}
  {{  title_font | font_face: font_display: 'swap' }}
  {{  text_font | font_face: font_display: 'swap' }}
  {{  source_font | font_face: font_display: 'swap' }}
  {{  slide_text_font | font_face: font_display: 'swap' }}
  {{  see_font | font_face: font_display: 'swap' }}
  {{  slide_title_font | font_face: font_display: 'swap' }}
  {{  slide_sub_text_font | font_face: font_display: 'swap' }}
  {{  button_font | font_face: font_display: 'swap' }}
  {{  pagination_font | font_face: font_display: 'swap' }}

  .section-{{ section.id }} {
    border-top: solid {{ border_color }} {{ border_thickness }}px;
    border-bottom: solid {{ border_color }} {{ border_thickness }}px;
    margin-top: {{ margin_top | times: 0.75 | round: 0 }}px;
    margin-bottom: {{ margin_bottom | times: 0.75 | round: 0 }}px;
    margin-left: {{ margin_horizontal_mobile }}rem;
    margin-right: {{ margin_horizontal_mobile }}rem;
    border-radius: {{ section_radius | times: 0.6 | round: 0 }}px;
    overflow: hidden;
  }

  .section-{{ section.id }}-settings {
    margin: 0 auto;
    padding-top: {{ padding_top | times: 0.75 | round: 0 }}px;
    padding-bottom: {{ padding_bottom | times: 0.75 | round: 0 }}px;
    padding-left: {{ padding_horizontal_mobile }}rem;
    padding-right: {{ padding_horizontal_mobile }}rem;
  }

  .ingredients-cards-{{ section.id }} {
    display: grid;
    grid-template-columns: repeat({{ cards_view_mobile }}, 1fr);
    gap: {{ cards_gap_mobile }}px;
    margin-top: {{ cards_mt_mobile }}px;
  }

  .ingredients-cards-{{ section.id }} .ingredients-card-{{ section.id }}:nth-child(n + {{ cards_limits | plus: 1 }}) {
    display: none;
  }

  .ingredients-cards-{{ section.id }}.active .ingredients-card-{{ section.id }} {
    display: block;
  }

  .ingredients-card-{{ section.id }} {
    position: relative;
    border: {{ card_border_thickness }}px solid {{ card_border_color }};
    border-radius: {{ card_radius }}px;
    background-color: {{ card_bg_color }};
    padding: {{ card_padding_vertical_mobile }}px {{ card_padding_horizontal_mobile }}px;
    overflow: hidden;
  }

  .ingredients-card-image-{{ section.id }} {
    position: absolute;
    top: 0;
    right: 0;
    width: 50%;
    height: 100%;
    z-index: 1;
  }

  .ingredients-card-image-{{ section.id }} img,
  .ingredients-card-image-{{ section.id }} svg {
    display: block;
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .ingredients-card-title-{{ section.id }} {
    margin: 0;
    position: relative;
    z-index: 2;
    text-align: {{ card_align_mobile }};
    font-size: {{ title_size_mobile }}px;
    color: {{ title_color }};
    line-height: {{ title_height }}%;
    text-transform: unset;
    word-break: break-word;
    font-weight: 700;
  }

  .ingredients-card-text-{{ section.id }} {
    margin-top: {{ text_mt_mobile }}px;
    text-align: {{ card_align_mobile }};
    position: relative;
    z-index: 2;
  }

  .ingredients-card-text-{{ section.id }} * {
    margin: 0;
    font-size: {{ text_size_mobile }}px;
    color: {{ text_color }};
    line-height: {{ text_height }}%;
    text-transform: unset;
    word-break: break-word;
  }

  .ingredients-source-{{ section.id }} {
    margin: 0;
    width: 85%;
    position: relative;
    z-index: 2;
    margin-top: {{ source_mt_mobile }}px;
    font-size: {{ source_size_mobile }}px;
    color: {{ source_color }};
    line-height: {{ source_height }}%;
    text-transform: unset;
    word-break: break-word;
    font-weight: 700;
  }

  .ingredients-plus-icon-{{ section.id }} {
    position: absolute;
    bottom: {{ card_padding_vertical_mobile }}px;
    right: {{ card_padding_horizontal_mobile }}px;
    border-radius: 50%;
    overflow: hidden;
    cursor: pointer;
    flex-shrink: 0;
    width: {{ plus_icon_size_mobile }}px;
    height: {{ plus_icon_size_mobile }}px;
    z-index: 2;
  }

  .ingredients-plus-icon-{{ section.id }} svg {
    display: block;
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .ingredients-plus-icon-{{ section.id }} svg {
    background-color: {{ plus_icon_bg_color }};
  }

  .ingredients-plus-icon-{{ section.id }} svg g {
    stroke: {{ plus_icon_color }};
  }

  .ingredients-see-all-wrapper-{{ section.id }} {
    display: flex;
    justify-content: center;
    align-items: center;
    padding-top: 16px;
    margin-top: 40px;
    border-top: 1px solid {{ see_color | hex_to_rgba: 0.16 }};
    gap: 8px;
  }

  .ingredients-see-all-wrapper-{{ section.id }} svg {
    width: 13px;
    height: 13px;
    transition: all 0.25s ease 0s;
  }

  .ingredients-see-all-wrapper-{{ section.id }} svg g {
    fill: {{ see_color }};
  }

  .ingredients-see-{{ section.id }} {
    margin: 0;
    gap: 8px;
    align-items: center;
    font-size: {{ see_size_mobile }}px;
    color: {{ see_color }};
    line-height: {{ see_height }}%;
    text-transform: unset;
    word-break: break-word;
    font-weight: 700;
    cursor: pointer;
  }

  .ingredients-see-all-{{ section.id }} {
    display: flex;
  }

  .ingredients-see-all-{{ section.id }}.hide {
    display: none;
  }

  .ingredients-see-less-{{ section.id }} {
    display: none;
  }

  .ingredients-see-less-{{ section.id }}.active {
    display: flex;
  }

  .ingredients-see-all-wrapper-{{ section.id }}.active svg {
    transform: rotate(180deg);
    transition: all 0.25s ease 0s;
  }

  .ingredients-modal-{{ section.id }} {
    width: 100vw;
    height: 100vh;
    position: fixed;
    bottom: 0;
    right: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    opacity: 0;
    overflow: hidden;
    pointer-events: none;
    background: {{ modal_overlay_color | hex_to_rgba: 0.5}};
    padding-top: 20px;
    padding-bottom: 20px;
    transition: opacity 0.3s ease;
    z-index: -1;
  }

  .ingredients-modal-{{ section.id }}.active {
    opacity: 1;
    z-index: 100;
    pointer-events: all;
    transition: all 250ms ease-in 0s;
  }

  .ingredients-modal-inner-{{ section.id }} {
    position: relative;
    max-height: 70vh;
    width: 100%;
    height: 100%;
    padding: 0;
    overflow: hidden;
    margin: 0;
    display: flex;
    align-items: center;
    justify-content: center;
  }

   .ingredients-modal-slider-{{ section.id }} {
     max-height: 70vh;
   }

  .ingredients-modal-slider-wrapper-{{ section.id }} {
    align-items: stretch !important;
    max-height: 70vh;
  }

  .ingredients-modal-slide-{{ section.id }} {
    position: relative;
    height: auto !important;
    transform: scale(0.9) !important;
    border: {{ modal_slide_border_thickness }}px solid {{ modal_slide_border_color }} !important;
    border-radius: {{ modal_slide_radius }}px !important;
    background-color: {{ modal_slide_bg_color }} !important;
    transition: all 0.25s ease 0s !important;
    box-sizing: border-box !important;
    overflow: hidden;
  }

  .ingredients-modal-slide-content-{{ section.id }} {
    overflow-y: scroll;
    scrollbar-width: none;
    display: flex;
    flex-direction: column;
    height: 100% !important;
    padding: {{ modal_slide_padding_vertical_mobile }}px {{ modal_slide_padding_horizontal_mobile }}px !important;
    align-items: {{ modal_horizontal_align_mobile }};
  }

  .ingredients-modal-slide-image-{{ section.id }} {
    position: absolute;
    bottom: 0;
    right: 0;
    width: 50%;
    height: 50%;
  }

  .ingredients-modal-slide-image-{{ section.id }} img,
  .ingredients-modal-slide-image-{{ section.id }} svg {
    display: block;
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .ingredients-modal-slide-{{ section.id }}.swiper-slide-active {
    transform: scale(1) !important;
    transition: all 0.25s ease 0s !important;
  }

  .ingredients-modal-close-{{ section.id }} {
    width: {{ modal_close_size_mobile }}px;
    height: {{ modal_close_size_mobile }}px;
    position: absolute;
    top: 10px;
    right: 10px;
    cursor: pointer;
    z-index: 2;
  }

  .ingredients-modal-close-{{ section.id }} svg {
    display: block;
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .ingredients-modal-close-{{ section.id }} svg g {
    stroke: {{ modal_close_color }};
  }

  .ingredients-modal-slide-text-{{ section.id }} {
    margin-top: {{ slide_text_mt_mobile }}px;
    text-align: {{ modal_slide_align_mobile }};
    position: relative;
    z-index: 2;
  }

  .ingredients-modal-slide-text-{{ section.id }} * {
    margin: 0;
    font-size: {{ slide_text_size_mobile }}px;
    color: {{ slide_text_color }};
    line-height: {{ slide_text_height }}%;
    text-transform: unset;
    word-break: break-word;
  }

  .ingredients-modal-slide-title-{{ section.id }} {
    margin: 0;
    position: relative;
    z-index: 2;
    margin-top: {{ slide_title_mt_mobile }}px;
    text-align: {{ modal_slide_align_mobile }};
    font-size: {{ slide_title_size_mobile }}px;
    color: {{ slide_title_color }};
    line-height: {{ slide_title_height }}%;
    text-transform: unset;
    word-break: break-word;
  }

  .ingredients-modal-slide-sub-text-{{ section.id }} {
    margin-top: {{ slide_sub_text_mt_mobile }}px;
    text-align: {{ modal_slide_align_mobile }};
    position: relative;
    z-index: 2;
  }

  .ingredients-modal-slide-sub-text-{{ section.id }} * {
    margin: 0;
    font-size: {{ slide_sub_text_size_mobile }}px;
    color: {{ slide_sub_text_color }};
    line-height: {{ slide_sub_text_height }}%;
    text-transform: unset;
    word-break: break-word;
  }

  .ingredients-modal-button-{{ section.id }} {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    width: 100%;
    max-width: fit-content;
    margin: 0;
    position: relative;
    z-index: 2;
    font-size: {{ button_size_mobile }}px;
    color: {{ button_color }};
    line-height: {{ button_height }}%;
    text-align: center;
    text-transform: unset;
    text-decoration: none;
    padding: {{ button_padding_vertical_mobile }}px {{ button_padding_horizontal_mobile }}px;
    border-radius: {{ button_radius }}px;
    transition: all 0.25s ease 0s;
    background-color: transparent;
    border: 0px;
    cursor: pointer;
    margin-top: {{ button_mt_mobile }}px;
    font-weight: 700;
  }

  .ingredients-modal-button-inner-{{ section.id }} {
    margin: 0px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
  }

  .ingredients-modal-button-{{ section.id }}:hover {
    color: {{ button_hover_color }};
    transition: all 0.25s ease 0s;
  }

  .ingredients-modal-button-{{ section.id }} svg {
    width: 14px;
    height: 14px;
    border: none;
    background-color: transparent;
  }

  .ingredients-modal-button-{{ section.id }} svg path {
    fill: {{ button_color }};
    transition: all 0.25s ease 0s;
  }

  .ingredients-modal-button-{{ section.id }}:hover svg path {
    fill: {{ button_hover_color }};
    transition: all 0.25s ease 0s;
  }

  .ingredients-modal-pagination-{{ section.id }} {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 24px;
    margin-top: 16px;
  }

  .ingredients-modal-pagination-{{ section.id }} svg g {
    stroke: {{ pagination_arrow_color }};
  }

  .slide-count-{{ section.id }} {
    margin: 0;
    font-size: {{ pagination_size_mobile }}px;
    color: {{ pagination_color }};
    line-height: {{ pagination_height }}%;
    text-transform: unset;
    text-decoration: none;
    font-weight: 400;
    user-select: none
  }

  .slide-count-{{ section.id }} * {
    margin: 0;
    font-size: {{ pagination_size_mobile }}px;
    color: {{ pagination_color }};
    line-height: {{ pagination_height }}%;
    text-transform: unset;
    text-decoration: none;
    font-weight: 400;
  }

  .ingredients-modal-btn-prev-{{ section.id }},
  .ingredients-modal-btn-next-{{ section.id }} {
    cursor: pointer;
    transition: all 0.25s ease 0s;
  }

  .ingredients-modal-btn-prev-{{ section.id }}:hover,
  .ingredients-modal-btn-next-{{ section.id }}:hover {
    transform: scale(1.1);
    transition: all 0.25s ease 0s;
  }

  @media(min-width: 1024px) {

    .section-{{ section.id }} {
      margin-top: {{ margin_top }}px;
      margin-bottom: {{ margin_bottom }}px;
      margin-left: {{ margin_horizontal }}rem;
      margin-right: {{ margin_horizontal }}rem;
      border-radius: {{ section_radius }}px;
    }

    .section-{{ section.id }}-settings {
      padding: 0 5rem;
      padding-top: {{ padding_top }}px;
      padding-bottom: {{ padding_bottom }}px;
      padding-left: {{ padding_horizontal }}rem;
      padding-right: {{ padding_horizontal }}rem;
    }

    .ingredients-cards-{{ section.id }} {
      grid-template-columns: repeat({{ cards_view }}, 1fr);
      gap: {{ cards_gap }}px;
      margin-top: {{ cards_mt }}px;
    }

    .ingredients-card-{{ section.id }} {
      padding: {{ card_padding_vertical }}px {{ card_padding_horizontal }}px;
    }

    .ingredients-card-title-{{ section.id }} {
      text-align: {{ card_align }};
      font-size: {{ title_size }}px;
    }

    .ingredients-card-text-{{ section.id }} {
      margin-top: {{ text_mt }}px;
    }

    .ingredients-card-text-{{ section.id }} * {
      text-align: {{ card_align }};
      font-size: {{ text_size }}px;
    }

    .ingredients-source-{{ section.id }} {
      margin-top: {{ source_mt }}px;
      font-size: {{ source_size }}px;
    }

    .ingredients-plus-icon-{{ section.id }} {
      bottom: {{ card_padding_vertical }}px;
      right: {{ card_padding_horizontal }}px;
      width: {{ plus_icon_size }}px;
      height: {{ plus_icon_size }}px;
    }

    .ingredients-see-{{ section.id }} {
      font-size: {{ see_size }}px;
    }

    .ingredients-modal-slider-wrapper-{{ section.id }} {
      align-items: stretch !important;
    }

    .ingredients-modal-slide-{{ section.id }} {
      overflow-y: scroll !important;
      scrollbar-width: none;
    }

    .ingredients-modal-slide-content-{{ section.id }} {
      align-items: {{ modal_horizontal_align }};
      padding: {{ modal_slide_padding_vertical }}px {{ modal_slide_padding_horizontal }}px !important;
    }


    .ingredients-modal-close-{{ section.id }} {
      width: {{ modal_close_size }}px;
      height: {{ modal_close_size }}px;
      top: 16px;
      right: 16px;
    }

    .ingredients-modal-slide-text-{{ section.id }} {
      margin-top: {{ slide_text_mt }}px;
      text-align: {{ modal_slide_align }};
      width: 100%;
    }

    .ingredients-modal-slide-text-{{ section.id }} * {
      font-size: {{ slide_text_size }}px;
    }

    .ingredients-modal-slide-title-{{ section.id }} {
      margin-top: {{ slide_title_mt }}px;
      width: 100%;
      text-align: {{ modal_slide_align }};
      font-size: {{ slide_title_size }}px;
    }

    .ingredients-modal-slide-sub-text-{{ section.id }} {
      margin-top: {{ slide_sub_text_mt }}px;
      width: 100%;
      text-align: {{ modal_slide_align }};
    }

    .ingredients-modal-slide-sub-text-{{ section.id }} * {
      font-size: {{ slide_sub_text_size }}px;
    }

    .ingredients-modal-button-{{ section.id }} {
      margin-top: {{ button_mt }}px;
      padding: {{ button_padding_vertical }}px {{ button_padding_horizontal }}px;
      font-size: {{ button_size }}px;
    }

    .slide-count-{{ section.id }},
    .slide-count-{{ section.id }} * {
      font-size: {{ pagination_size }}px;
    }
  }
{%- endstyle -%}

{% unless full_width %}
  <style>
    .section-{{ section.id }}-settings {
      max-width: {{ content_width }}px;
    }
  </style>
{% endunless %}

{% if margin_horizontal_mobile > 0 %}
  <style>
    .section-{{ section.id }} {
      border-left: solid {{ border_color }} {{ border_thickness }}px;
      border-right: solid {{ border_color }} {{ border_thickness }}px;
    }

    @media(min-width: 1024px) {
      .section-{{ section.id }} {
        border-left: 0px;
        border-right: 0px;
      }
    }
  </style>
{% endif %}

{% if margin_horizontal > 0 %}
  <style>
    @media(min-width: 1024px) {
      .section-{{ section.id }} {
        border-left: solid {{ border_color }} {{ border_thickness }}px;
        border-right: solid {{ border_color }} {{ border_thickness }}px;
      }
    }
  </style>
{% endif %}

{% if title_custom %}
  <style>
    .ingredients-card-title-{{ section.id }} {
      font-family: {{ title_font.family }}, {{ title_font.fallback_families }};
      font-weight: {{ title_font.weight }};
      font-style: {{ title_font.style }};
    }
  </style>
{% endif %}

{% if text_custom %}
  <style>
    .ingredients-card-text-{{ section.id }} * {
      font-family: {{ text_font.family }}, {{ text_font.fallback_families }};
      font-weight: {{ text_font.weight }};
      font-style: {{ text_font.style }};
    }
  </style>
{% endif %}

{% if source_custom %}
  <style>
    .ingredients-source-{{ section.id }} {
      font-family: {{ source_font.family }}, {{ source_font.fallback_families }};
      font-weight: {{ source_font.weight }};
      font-style: {{ source_font.style }};
    }
  </style>
{% endif %}

{% if see_custom %}
  <style>
    .ingredients-see-{{ section.id }} {
      font-family: {{ see_font.family }}, {{ see_font.fallback_families }};
      font-weight: {{ see_font.weight }};
      font-style: {{ see_font.style }};
    }
  </style>
{% endif %}

{% if slide_text_custom %}
  <style>
    .ingredients-modal-slide-text-{{ section.id }} * {
      font-family: {{ slide_text_font.family }}, {{ slide_text_font.fallback_families }};
      font-weight: {{ slide_text_font.weight }};
      font-style: {{ slide_text_font.style }};
    }
  </style>
{% endif %}

{% if slide_title_custom %}
  <style>
    .ingredients-modal-slide-title-{{ section.id }} {
      font-family: {{ slide_title_font.family }}, {{ slide_title_font.fallback_families }};
      font-weight: {{ slide_title_font.weight }};
      font-style: {{ slide_title_font.style }};
    }
  </style>
{% endif %}

{% if slide_sub_text_custom %}
  <style>
    .ingredients-modal-slide-sub-text-{{ section.id }} * {
      font-family: {{ slide_sub_text_font.family }}, {{ slide_sub_text_font.fallback_families }};
      font-weight: {{ slide_sub_text_font.weight }};
      font-style: {{ slide_sub_text_font.style }};
    }
  </style>
{% endif %}

{% if button_custom %}
  <style>
    .ingredients-modal-button-{{ section.id }} {
      font-family: {{ button_font.family }}, {{ button_font.fallback_families }};
      font-weight: {{ button_font.weight }};
      font-style: {{ button_font.style }};
    }
  </style>
{% endif %}

{% if pagination_custom %}
  <style>
    .slide-count-{{ section.id }},
    .slide-count-{{ section.id }} * {
      font-family: {{ pagination_font.family }}, {{ pagination_font.fallback_families }};
      font-weight: {{ pagination_font.weight }};
      font-style: {{ pagination_font.style }};
    }
  </style>
{% endif %}

{% if button_style == 'non_outline' or button_style == 'non_outline_arrow' %}
  <style>
    .ingredients-modal-button-{{ section.id }} {
      background-color: {{ button_bg_color }};
    }

    .ingredients-modal-button-{{ section.id }}:hover {
      background-color: {{ button_bg_hover_color }};
    }
  </style>
{% elsif button_style == 'outline' or button_style == 'outline_arrow' %}
  <style>
    .ingredients-modal-button-{{ section.id }} {
      background-color: {{ button_bg_color }};
      border: {{ button_border_thickness }}px solid {{ button_border_color }};
    }

    .ingredients-modal-button-{{ section.id }}:hover {
      background-color: {{ button_bg_hover_color }};
      border: {{ button_border_thickness }}px solid {{ button_border_hover_color }};
    }
  </style>
{% elsif button_style == 'link' %}
  <style>
    .ingredients-modal-button-{{ section.id }} {
      position: relative;
      padding: 5px 0px !important;
    }

    .ingredients-modal-button-{{ section.id }}:after {
      content: "";
      position: absolute;
      width: 100%;
      height: 1px;
      left: 0;
      bottom: 0px;
      background: {{ button_color }};
      transition: all 0.25s ease 0s;
    }

    .ingredients-modal-button-{{ section.id }}:hover:after {
      background: {{ button_hover_color }};
      transition: all 0.25s ease 0s;
    }
  </style>
{% endif %}

{% for block in section.blocks %}
  {% case block.type %}
    {% when 'heading' %}
      <style>
        {{  block.settings.heading_font | font_face: font_display: 'swap' }}

        .ingredients-heading-{{ block.id }} {
          max-width: 100%;
          text-align: {{ content_align_mobile }};
          margin-top: {{ block.settings.heading_mt_mobile }}px;
        }

        .ingredients-heading-{{ block.id }} * {
          margin: 0;
          font-size: {{ block.settings.heading_size_mobile }}px;
          color: {{ block.settings.heading_color }};
          line-height: {{ block.settings.heading_height }}%;
          text-transform: unset;
          word-break: break-word;
          font-weight: 700;
        }

        @media(min-width: 1024px) {

          .ingredients-heading-{{ block.id }} {
            text-align: {{ content_align }};
            max-width: {{ content_maxwidth }}px;
            margin-top: {{ block.settings.heading_mt }}px;
          }

          .ingredients-heading-{{ block.id }} * {
            font-size: {{ block.settings.heading_size }}px;
          }
        }
      </style>

      {% if block.settings.heading_custom %}
        <style>
          .ingredients-heading-{{ block.id }} * {
            font-family: {{ block.settings.heading_font.family }}, {{ block.settings.heading_font.fallback_families }};
            font-weight: {{ block.settings.heading_font.weight }};
            font-style: {{ block.settings.heading_font.style }};
          }
        </style>
      {% endif %}

      {% if content_align == 'center' %}
        <style>
          @media(min-width: 1024px) {
           .ingredients-heading-{{ block.id }} {
              margin-left: auto !important;
              margin-right: auto !important;
            }
          }
        </style>
      {% elsif content_align == 'right' %}
        <style>
          @media(min-width: 1024px) {
           .ingredients-heading-{{ block.id }} {
              margin-left: auto !important;
              margin-right: 0px !important;
            }
          }
        </style>
      {% else %}
        <style>
          @media(min-width: 1024px) {
           .ingredients-heading-{{ block.id }} {
              margin-left: 0px !important;
              margin-right: 0px !important;
            }
          }
        </style>
      {% endif %}

    {% when 'text' %}
      <style>
        {{  block.settings.text_font | font_face: font_display: 'swap' }}

        .ingredients-text-{{ block.id }} {
          max-width: 1000%;
          text-align: {{ content_align_mobile }};
          margin-top: {{ block.settings.text_mt_mobile }}px;
        }

        .ingredients-text-{{ block.id }} * {
          margin: 0;
          font-size: {{ block.settings.text_size_mobile }}px;
          color: {{ block.settings.text_color }};
          line-height: {{ block.settings.text_height }}%;
          text-transform: unset;
        }

        @media(min-width: 1024px) {

          .ingredients-text-{{ block.id }} {
            max-width: {{ content_maxwidth }}px;
            text-align: {{ content_align }};
            margin-top: {{ block.settings.text_mt }}px;
          }

          .ingredients-text-{{ block.id }} * {
            font-size: {{ block.settings.text_size }}px;
          }

        }
      </style>

      {% if block.settings.text_custom %}
        <style>
          .ingredients-text-{{ block.id }} * {
            font-family: {{ block.settings.text_font.family }}, {{ block.settings.text_font.fallback_families }};
            font-weight: {{ block.settings.text_font.weight }};
            font-style: {{ block.settings.text_font.style }};
          }
        </style>
      {% endif %}

      {% if content_align == 'center' %}
        <style>
          @media(min-width: 1024px) {
           .ingredients-text-{{ block.id }} {
              margin-left: auto !important;
              margin-right: auto !important;
            }
          }
        </style>
      {% elsif content_align == 'right' %}
        <style>
          @media(min-width: 1024px) {
           .ingredients-text-{{ block.id }} {
              margin-left: auto !important;
              margin-right: 0px !important;
            }
          }
        </style>
      {% else %}
        <style>
          @media(min-width: 1024px) {
           .ingredients-text-{{ block.id }} {
              margin-left: 0px !important;
              margin-right: 0px !important;
            }
          }
        </style>
      {% endif %}
  {% endcase %}
{% endfor %}

<div
  class="section-{{ section.id }} ingredients-{{ section.id }}"
  style="background-color:{{ background_color }}; background-image: {{ background_gradient }};"
>
  <div class="section-{{ section.id }}-settings">
    {% for block in section.blocks %}
      {% case block.type %}
        {% when 'heading' %}
          {% if block.settings.heading != blank %}
            <div class="ingredients-heading-{{ block.id }}">
              {{ block.settings.heading }}
            </div>
          {% endif %}
        {% when 'text' %}
          {% if block.settings.text != blank %}
            <div class="ingredients-text-{{ block.id }}">
              {{ block.settings.text }}
            </div>
          {% endif %}
      {% endcase %}
    {% endfor %}

    <div class="ingredients-cards-{{ section.id }}">
      {% assign card_count = 0 %}
      {% for block in section.blocks %}
        {% if block.type == 'card' %}
          <div class="ingredients-card-{{ section.id }}">
            <div class="ingredients-card-image-{{ section.id }}">
              {% if block.settings.image != blank %}
                {{
                  block.settings.image
                  | image_url: width: 1000
                  | image_tag:
                    loading: loading_attribute,
                    sizes: '(min-width: 1024px) 100vw, 100vw',
                    widths: '375, 550, 750, 1100, 1500, 1780, 2000, 3000',
                    alt: block.settings.image.alt
                  | escape
                }}
              {% endif %}
            </div>
            {% if block.settings.title != blank %}
              <p class="ingredients-card-title-{{ section.id }}">{{ block.settings.title }}</p>
            {% endif %}
            {% if block.settings.text != blank %}
              <div class="ingredients-card-text-{{ section.id }}">{{ block.settings.text }}</div>
            {% endif %}
            <p class="ingredients-source-{{ section.id }}">{{ block.settings.source }}</p>
            <div class="ingredients-plus-icon-{{ section.id }}" id="{{ block.id }}">
              <svg
                data-element="PlusIconElement"
                data-source-file="IngredientCard.js"
                data-sentry-element="PlusIconElement"
                data-sentry-source-file="IngredientCard.js"
                width="24"
                height="24"
                version="1.1"
                viewBox="0 0 24 24"
              >
                <g fill="none" fill-rule="evenodd" stroke="none" stroke-width="1"><g fill-rule="nonzero"><g><g><g><g transform="translate(-153.000000, -497.000000) translate(153.000000, 188.000000) translate(0.000000, 132.000000) translate(0.000000, 177.000000)"><circl cx="12" cy="12" r="12" fill="#142B6F" fill-opacity=".51"></circl><g stroke="#FFF" stroke-linecap="square" stroke-width="2"><path d="M0,4 L8,4" transform="translate(8.000000, 8.000000)"></path><path d="M0,4 L8,4" transform="translate(8.000000, 8.000000) translate(4.000000, 4.000000) rotate(-270.000000) translate(-4.000000, -4.000000)"></path></g></g></g></g></g></g></g>
              </svg>
            </div>
          </div>

          {% assign card_count = card_count | plus: 1 %}
        {% endif %}
      {% endfor %}
    </div>

    {% if card_count > cards_limits %}
      <div class="ingredients-see-all-wrapper-{{ section.id }}">
        {% if see_all %}
          <p class="ingredients-see-{{ section.id }} ingredients-see-all-{{ section.id }}">
            {{ see_all }}
            {{ card_count }}
            <svg
              data-element="CaretDown"
              data-source-file="IngredientsOverview.tsx"
              data-sentry-element="CaretDown"
              data-sentry-source-file="IngredientsOverview.tsx"
              width="19"
              height="12"
              version="1.1"
              viewBox="0 0 19 12"
            >
              <g fill="none" fill-rule="evenodd" stroke="none" stroke-width="1"><g fill="#142B6F" fill-rule="nonzero"><g><g><path d="M328.181981,12 L335.386485,4.79549513 C335.825825,4.3561553 335.825825,3.6438447 335.386485,3.20450487 C334.947146,2.76516504 334.234835,2.76516504 333.795495,3.20450487 L325,12 L333.795495,20.7954951 C334.234835,21.234835 334.947146,21.234835 335.386485,20.7954951 C335.825825,20.3561553 335.825825,19.6438447 335.386485,19.2045049 L328.181981,12 L328.181981,12 Z" transform="translate(-456.000000, -284.000000) translate(135.000000, 160.000000) translate(0.000000, 118.000000) translate(330.357995, 12.000000) scale(-1, 1) rotate(-90.000000) translate(-330.357995, -12.000000)"></path></g></g></g></g>
            </svg>
          </p>
        {% endif %}
        {% if see_less %}
          <p class="ingredients-see-{{ section.id }} ingredients-see-less-{{ section.id }}">
            {{ see_less }}
            <svg
              data-element="CaretDown"
              data-source-file="IngredientsOverview.tsx"
              data-sentry-element="CaretDown"
              data-sentry-source-file="IngredientsOverview.tsx"
              width="19"
              height="12"
              version="1.1"
              viewBox="0 0 19 12"
            >
              <g fill="none" fill-rule="evenodd" stroke="none" stroke-width="1"><g fill="#142B6F" fill-rule="nonzero"><g><g><path d="M328.181981,12 L335.386485,4.79549513 C335.825825,4.3561553 335.825825,3.6438447 335.386485,3.20450487 C334.947146,2.76516504 334.234835,2.76516504 333.795495,3.20450487 L325,12 L333.795495,20.7954951 C334.234835,21.234835 334.947146,21.234835 335.386485,20.7954951 C335.825825,20.3561553 335.825825,19.6438447 335.386485,19.2045049 L328.181981,12 L328.181981,12 Z" transform="translate(-456.000000, -284.000000) translate(135.000000, 160.000000) translate(0.000000, 118.000000) translate(330.357995, 12.000000) scale(-1, 1) rotate(-90.000000) translate(-330.357995, -12.000000)"></path></g></g></g></g>
            </svg>
          </p>
        {% endif %}
      </div>
    {% endif %}
  </div>
</div>

<div class="ingredients-modal-{{ section.id }}">
  <div class="ingredients-modal-inner-{{ section.id }}">
    <div class="ingredients-modal-slider-{{ section.id }} swiper">
      <div class="ingredients-modal-slider-wrapper-{{ section.id }} swiper-wrapper">
        {% assign cards = 0 %}
        {% for block in section.blocks %}
          {% if block.type == 'card' %}
            <div class="ingredients-modal-slide-{{ section.id }} swiper-slide" data-id="{{ block.id }}">
              <div class="ingredients-modal-slide-content-{{ section.id }}">
                <div class="ingredients-modal-slide-image-{{ section.id }}">
                  {% if block.settings.modal_image != blank %}
                    {{
                      block.settings.modal_image
                      | image_url: width: 1000
                      | image_tag:
                        loading: loading_attribute,
                        sizes: '(min-width: 1024px) 100vw, 100vw',
                        widths: '375, 550, 750, 1100, 1500, 1780, 2000, 3000',
                        alt: block.settings.modal_image.alt
                      | escape
                    }}
                  {% endif %}
                </div>
                <div class="ingredients-modal-close-{{ section.id }}">
                  <svg
                    data-element="unknown"
                    data-source-file="Carousel.js"
                    data-sentry-element="unknown"
                    data-sentry-source-file="Carousel.js"
                    width="16"
                    height="16"
                    version="1.1"
                    viewBox="0 0 16 16"
                  >
                    <g fill="none" fill-rule="evenodd" stroke="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"><g stroke="#142B6F" stroke-width="2"><g><g><path d="M14,14 L2,2 L14,14 Z M14,2 L2,14 L14,2 Z" transform="translate(-284.000000, -22.000000) translate(284.000000, 22.000000)"></path></g></g></g></g>
                  </svg>
                </div>
                {% if block.settings.title != blank %}
                  <p class="ingredients-card-title-{{ section.id }}">{{ block.settings.title }}</p>
                {% endif %}
                {% if block.settings.modal_text != blank %}
                  <div class="ingredients-modal-slide-text-{{ section.id }}">{{ block.settings.modal_text }}</div>
                {% endif %}
                {% if block.settings.modal_subtitle != blank %}
                  <p class="ingredients-modal-slide-title-{{ section.id }}">{{ block.settings.modal_subtitle }}</p>
                {% endif %}
                {% if block.settings.modal_sub_text != blank %}
                  <div class="ingredients-modal-slide-sub-text-{{ section.id }}">
                    {{ block.settings.modal_sub_text }}
                  </div>
                {% endif %}
                {% if block.settings.url != blank %}
                  <a href="{{ block.settings.url }}" class="ingredients-modal-button-{{ section.id }}">
                    <p class="ingredients-modal-button-inner-{{ section.id }}">
                      {{ button }}
                      {% if button_style == 'non_outline_arrow' or button_style == 'outline_arrow' %}
                        <svg
                          width="192"
                          height="192"
                          viewBox="0 0 192 192"
                          fill="none"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path d="M187.827 98.7858C188.123 98.4749 188.123 98.4749 188.123 98.1761C188.419 97.8652 188.419 97.5663 188.704 97.5663C189 96.9566 189 96.6458 189 96.0361C189 95.4263 189 94.8166 188.704 94.5058C188.704 94.195 188.408 93.8961 188.123 93.5852C188.123 93.2744 187.827 93.2744 187.827 92.9755L103.287 4.21945C102.41 3.29889 101.533 3 100.668 3C99.791 3 98.6295 3.31083 98.0488 4.21945C97.1719 5.14 96.8872 6.06058 96.8872 6.96919C96.8872 7.88974 97.1833 9.10918 98.0488 9.7189L175.587 92.0539H6.79206C4.75371 92.0539 3 93.895 3 96.0351C3 98.1751 4.75365 100.016 6.79206 100.016H175.575L97.7543 182.042C96.2967 183.572 96.2967 186.322 97.7543 187.852C99.2119 189.383 101.831 189.383 103.288 187.852L187.827 98.7858Z" fill="black"></path>
                          <path fill-rule="evenodd" clip-rule="evenodd" d="M188.607 97.5657C188.432 97.5657 188.365 97.6788 188.27 97.8382C188.211 97.9378 188.141 98.0554 188.027 98.1748C188.027 98.4734 188.027 98.4734 187.731 98.7839L103.281 187.759C101.825 189.287 99.2085 189.287 97.7524 187.759C96.2963 186.23 96.2963 183.483 97.7524 181.954L175.492 100.013L6.88489 100.013C4.8486 100.013 3.09677 98.1739 3.09677 96.036C3.09677 93.8982 4.84866 92.059 6.88489 92.059L175.504 92.059L98.0465 9.80878C97.182 9.19968 96.8862 7.9815 96.8862 7.0619C96.8862 6.15422 97.1706 5.2346 98.0465 4.315C98.6267 3.40732 99.787 3.0968 100.663 3.0968C101.527 3.0968 102.403 3.39539 103.279 4.315L187.731 92.9796C187.731 93.1274 187.804 93.2021 187.877 93.2774C187.952 93.3543 188.027 93.4319 188.027 93.5887C188.046 93.6098 188.066 93.6308 188.085 93.6518C188.338 93.9267 188.584 94.1935 188.606 94.4689C188.607 94.482 188.607 94.4951 188.607 94.5083C188.903 94.8188 188.903 95.4279 188.903 96.037C188.903 96.6461 188.903 96.9566 188.607 97.5657ZM191.489 93.2767C191.79 93.8661 191.89 94.4204 191.934 94.7363C192.001 95.2226 192 95.7194 192 95.9856L192 96.037C192 96.0544 192 96.0729 192 96.0926C192 96.3523 192.001 96.8096 191.924 97.2931C191.828 97.8884 191.64 98.41 191.393 98.9184L190.546 100.663H190.212C190.127 100.759 190.038 100.852 189.988 100.905L189.974 100.92L105.527 189.891C102.85 192.701 98.1865 192.704 95.51 189.895C94.1476 188.464 93.5636 186.587 93.5636 184.857C93.5636 183.128 94.1468 181.252 95.5071 179.822M191.489 93.2767C191.316 92.7908 191.078 92.4357 190.938 92.2406C190.903 92.1912 190.866 92.142 190.828 92.0939V91.7408L105.522 2.17912C104.076 0.661813 102.397 0 100.663 0C99.3914 0 97.0569 0.401401 95.6212 2.37737C94.3151 3.83819 93.7895 5.45521 93.7895 7.0619C93.7895 8.26663 94.1183 10.6061 95.9608 12.111L168.333 88.9622L6.88489 88.9622C2.9981 88.9622 0 92.3316 0 96.036C0 99.7405 2.99801 103.11 6.88489 103.11L168.285 103.11" fill="black"></path>
                          <path d="M169.5 104L16.5 102.5V90H171L97.5 10.5V7L107.5 4.5C131.167 29.6667 180.2 81.5 187 87.5C193.8 93.5 191.5 99 189.5 101L105.5 184L94 181.5L169.5 104Z" fill="black"></path>
                        </svg>
                      {% endif %}
                    </p>
                  </a>
                {% endif %}
              </div>
            </div>
            {% assign card = card | plus: 1 %}
          {% endif %}
        {% endfor %}
      </div>
    </div>
  </div>
  <div class="ingredients-modal-pagination-{{ section.id }}">
    <div class="ingredients-modal-btn-prev-{{ section.id }}">
      <svg width="18" height="12" version="1.1" viewBox="0 0 18 12">
        <title></title><desc></desc><g fill="none" fill-rule="evenodd" stroke="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"><g stroke="#142B6F" stroke-width="2.25"><g><g><g><path d="M-2.1938007e-13,4.0010929 L14.8845268,4.0010929 L-2.1938007e-13,4.0010929 Z M14.8845268,4 L10.862069,0.0655737705 L14.8845268,4 Z M14.8845268,4 L10.862069,7.93442623 L14.8845268,4 Z" transform="translate(-776.000000, -654.000000) translate(135.000000, 530.000000) translate(0.000000, 118.000000) translate(649.500000, 12.000000) scale(-1, 1) translate(-649.500000, -12.000000) translate(642.000000, 8.000000)"></path></g></g></g></g></g>
      </svg>
    </div>
    <span class="slide-count-{{ section.id }}">
      <strong class="slide-current-{{ section.id }}">1</strong> OF
      <strong class="slide-total-{{ section.id }}">{{ card }}</strong>
    </span>
    <div class="ingredients-modal-btn-next-{{ section.id }}">
      <svg width="18" height="12" version="1.1" viewBox="0 0 18 12">
        <title></title><desc></desc><g fill="none" fill-rule="evenodd" stroke="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"><g stroke="#142B6F" stroke-width="2.25"><g><g><g><path d="M-2.1938007e-13,4.0010929 L14.8845268,4.0010929 L-2.1938007e-13,4.0010929 Z M14.8845268,4 L10.862069,0.0655737705 L14.8845268,4 Z M14.8845268,4 L10.862069,7.93442623 L14.8845268,4 Z" transform="translate(-820.000000, -654.000000) translate(135.000000, 530.000000) translate(0.000000, 118.000000) translate(687.000000, 8.000000)"></path></g></g></g></g></g>
      </svg>
    </div>
  </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/swiper@8/swiper-bundle.min.js"></script>

<script>
  function initProductIngredients6() {
    const slider = new Swiper(".ingredients-modal-slider-{{ section.id }}", {
      speed: 300,
      centeredSlides: true,    
      breakpoints: {
        320: { spaceBetween: 10, slidesPerView: 1.3 },
        768: { spaceBetween: 20, slidesPerView: 1.3 },
        1024: { spaceBetween: 30, slidesPerView: {{ slides_per_view }} }
      },
      navigation: {
        nextEl: `.ingredients-modal-btn-next-{{ section.id }}`,
        prevEl: `.ingredients-modal-btn-prev-{{ section.id }}`,
      },
      on: {
        init: function () {
          updateSlideCounter(this);
        },
        slideChange: function () {
          updateSlideCounter(this);
        }
      }
    });

    function updateSlideCounter(swiper) {
      const current = document.querySelector('.slide-current-{{ section.id }}');
      const total = document.querySelector('.slide-total-{{ section.id }}');
      if (current && total) {
        current.textContent = swiper.realIndex + 1;
        total.textContent = swiper.slides.length;
      }
    }

    let cardsLimits = document.querySelector('.ingredients-cards-{{ section.id }}');
    let seeAllWrapper = document.querySelector('.ingredients-see-all-wrapper-{{ section.id }}');

    let see = document.querySelectorAll('.ingredients-see-{{ section.id }}');
    let seeAll = document.querySelector('.ingredients-see-all-{{ section.id }}');
    let seeLess = document.querySelector('.ingredients-see-less-{{ section.id }}');

    see.forEach(item => {
      item.addEventListener('click', () => {
        seeAllWrapper.classList.toggle('active');
        seeAll.classList.toggle('hide');
        seeLess.classList.toggle('active');

        cardsLimits.classList.toggle('active');
        cardsFull.classList.toggle('active');
      });
    });

    const ingredientsWrapper = document.querySelectorAll('.ingredients-plus-icon-{{ section.id }}');
    const modal = document.querySelector(".ingredients-modal-{{ section.id }}");
    const close = document.querySelectorAll(".ingredients-modal-close-{{ section.id }}");

    ingredientsWrapper.forEach(item => {
      item.addEventListener('click', () => {
        if (modal) {
          modal.classList.add("active");           
          document.body.style.overflow = 'hidden';    
        }

        let clickedId = item.id;
        let slides = document.querySelectorAll(".ingredients-modal-slider-{{ section.id }} .swiper-slide");

        slides.forEach((slide, index) => {
          if (slide.getAttribute("data-id") === clickedId) {
            slider.slideTo(index);
          }
        });
      });

      close.forEach(item => {
        item.addEventListener('click', () => {
          if (modal) {
            modal.classList.remove("active");   
            document.body.style.overflow = '';           
          }
        });
      });

      document.addEventListener('click', (e) => {
        if (e.target == modal) {
          document.body.style.overflow = '';
          modal.classList.remove('active');
        }
      });
    });
  }

  document.addEventListener('DOMContentLoaded', initProductIngredients6);

  if (Shopify.designMode) {
    document.addEventListener('shopify:section:unload', initProductIngredients6);
    document.addEventListener('shopify:section:load', initProductIngredients6);
  }
</script>

{% schema %}
{
  "name": "SS - Ingredients #6",
  "settings": [
    {
      "type": "header",
      "content": "Content"
    },

    {
      "type": "text_alignment",
      "id": "content_align",
      "label": "Alignment",
      "default": "center"
    },
    {
      "type": "text_alignment",
      "id": "content_align_mobile",
      "label": "Alignment - mobile",
      "default": "center"
    },
    {
      "type": "range",
      "id": "content_maxwidth",
      "min": 200,
      "max": 700,
      "step": 10,
      "unit": "px",
      "label": "Max-width",
      "default": 700
    },
    {
      "type": "header",
      "content": "Cards"
    },
    {
      "type": "range",
      "id": "cards_view",
      "min": 1,
      "max": 4,
      "step": 1,
      "label": "Cards per row",
      "default": 3
    },
    {
      "type": "range",
      "id": "cards_view_mobile",
      "min": 1,
      "max": 3,
      "step": 1,
      "label": "Cards per row - mobile",
      "default": 1
    },
    {
      "type": "range",
      "id": "cards_gap",
      "min": 0,
      "max": 100,
      "step": 2,
      "unit": "px",
      "label": "Gap",
      "default": 30
    },
    {
      "type": "range",
      "id": "cards_gap_mobile",
      "min": 0,
      "max": 100,
      "step": 2,
      "unit": "px",
      "label": "Gap - mobile",
      "default": 16
    },
    {
      "type": "range",
      "id": "cards_mt",
      "min": 0,
      "max": 100,
      "step": 2,
      "unit": "px",
      "label": "Margin top",
      "default": 48
    },
    {
      "type": "range",
      "id": "cards_mt_mobile",
      "min": 0,
      "max": 100,
      "step": 2,
      "unit": "px",
      "label": "Margin top - mobile",
      "default": 36
    },
    {
      "type": "range",
      "id": "cards_limits",
      "min": 1,
      "max": 20,
      "step": 1,
      "label": "Limits",
      "default": 6
    },
    {
      "type": "header",
      "content": "Card"
    },
    {
      "type": "range",
      "id": "card_padding_horizontal",
      "min": 0,
      "max": 100,
      "step": 2,
      "unit": "px",
      "label": "Padding horizontal",
      "default": 24
    },
    {
      "type": "range",
      "id": "card_padding_horizontal_mobile",
      "min": 0,
      "max": 100,
      "step": 2,
      "unit": "px",
      "label": "Padding horizontal - mobile",
      "default": 24
    },
    {
      "type": "range",
      "id": "card_padding_vertical",
      "min": 0,
      "max": 100,
      "step": 2,
      "unit": "px",
      "label": "Padding vertical",
      "default": 24
    },
    {
      "type": "range",
      "id": "card_padding_vertical_mobile",
      "min": 0,
      "max": 100,
      "step": 2,
      "unit": "px",
      "label": "Padding vertical - mobile",
      "default": 24
    },
    {
      "type": "range",
      "id": "card_radius",
      "min": 0,
      "max": 100,
      "step": 2,
      "unit": "px",
      "label": "Roundness",
      "default": 0
    },
    {
      "type": "range",
      "id": "card_border_thickness",
      "min": 0,
      "max": 10,
      "step": 1,
      "unit": "px",
      "label": "Border thickness",
      "default": 0
    },
    {
      "type": "text_alignment",
      "id": "card_align",
      "label": "Alignment"
    },
    {
      "type": "text_alignment",
      "id": "card_align_mobile",
      "label": "Alignment - mobile"
    },
    {
      "type": "header",
      "content": "Title"
    },
    {
      "type": "checkbox",
      "id": "title_custom",
      "label": "Use custom font",
      "default": false
    },
    {
      "type": "font_picker",
      "id": "title_font",
      "label": "Font family",
      "default": "josefin_sans_n4"
    },
    {
      "type": "range",
      "id": "title_size",
      "min": 0,
      "max": 40,
      "step": 1,
      "unit": "px",
      "label": "Font size",
      "default": 26
    },
    {
      "type": "range",
      "id": "title_size_mobile",
      "min": 0,
      "max": 40,
      "step": 1,
      "unit": "px",
      "label": "Font size - mobile",
      "default": 22
    },
    {
      "type": "range",
      "id": "title_height",
      "min": 50,
      "max": 200,
      "step": 10,
      "unit": "%",
      "label": "Line height",
      "default": 130
    },
    {
      "type": "header",
      "content": "Text"
    },
    {
      "type": "checkbox",
      "id": "text_custom",
      "label": "Use custom font",
      "default": false
    },
    {
      "type": "font_picker",
      "id": "text_font",
      "label": "Font family",
      "default": "josefin_sans_n4"
    },
    {
      "type": "range",
      "id": "text_size",
      "min": 0,
      "max": 40,
      "step": 1,
      "unit": "px",
      "label": "Font size",
      "default": 16
    },
    {
      "type": "range",
      "id": "text_size_mobile",
      "min": 0,
      "max": 40,
      "step": 1,
      "unit": "px",
      "label": "Font size - mobile",
      "default": 14
    },
    {
      "type": "range",
      "id": "text_height",
      "min": 50,
      "max": 200,
      "step": 10,
      "unit": "%",
      "label": "Line height",
      "default": 130
    },
    {
      "type": "range",
      "id": "text_mt",
      "min": 0,
      "max": 50,
      "step": 2,
      "unit": "px",
      "label": "Margin top",
      "default": 8
    },
    {
      "type": "range",
      "id": "text_mt_mobile",
      "min": 0,
      "max": 50,
      "step": 2,
      "unit": "px",
      "label": "Margin top - mobile",
      "default": 8
    },
    {
      "type": "header",
      "content": "Source"
    },
    {
      "type": "checkbox",
      "id": "source_custom",
      "label": "Use custom font",
      "default": false
    },
    {
      "type": "font_picker",
      "id": "source_font",
      "label": "Font family",
      "default": "josefin_sans_n4"
    },
    {
      "type": "range",
      "id": "source_size",
      "min": 0,
      "max": 40,
      "step": 1,
      "unit": "px",
      "label": "Font size",
      "default": 14
    },
    {
      "type": "range",
      "id": "source_size_mobile",
      "min": 0,
      "max": 40,
      "step": 1,
      "unit": "px",
      "label": "Font size - mobile",
      "default": 14
    },
    {
      "type": "range",
      "id": "source_height",
      "min": 50,
      "max": 200,
      "step": 10,
      "unit": "%",
      "label": "Line height",
      "default": 130
    },
    {
      "type": "range",
      "id": "source_mt",
      "min": 0,
      "max": 100,
      "step": 2,
      "unit": "px",
      "label": "Margin top",
      "default": 48
    },
    {
      "type": "range",
      "id": "source_mt_mobile",
      "min": 0,
      "max": 100,
      "step": 2,
      "unit": "px",
      "label": "Margin top - mobile",
      "default": 40
    },
    {
      "type": "header",
      "content": "Plus icon"
    },
    {
      "type": "range",
      "id": "plus_icon_size",
      "min": 0,
      "max": 100,
      "step": 2,
      "unit": "px",
      "label": "Size",
      "default": 24
    },
    {
      "type": "range",
      "id": "plus_icon_size_mobile",
      "min": 0,
      "max": 100,
      "step": 2,
      "unit": "px",
      "label": "Size - mobile",
      "default": 24
    },
    {
      "type": "header",
      "content": "View options"
    },
    {
      "type": "text",
      "id": "see_all",
      "label": "View all",
      "default": "See all"
    },
    {
      "type": "text",
      "id": "see_less",
      "label": "View less",
      "default": "See less"
    },
    {
      "type": "checkbox",
      "id": "see_custom",
      "label": "Use custom font",
      "default": false
    },
    {
      "type": "font_picker",
      "id": "see_font",
      "label": "Font family",
      "default": "josefin_sans_n4"
    },
    {
      "type": "range",
      "id": "see_size",
      "min": 0,
      "max": 40,
      "step": 1,
      "unit": "px",
      "label": "Font size",
      "default": 14
    },
    {
      "type": "range",
      "id": "see_size_mobile",
      "min": 0,
      "max": 40,
      "step": 1,
      "unit": "px",
      "label": "Font size - mobile",
      "default": 14
    },
    {
      "type": "range",
      "id": "see_height",
      "min": 50,
      "max": 200,
      "step": 10,
      "unit": "%",
      "label": "Line height",
      "default": 130
    },
    {
      "type": "header",
      "content": "Modal layout"
    },
    {
      "type": "range",
      "id": "slides_per_view",
      "min": 1,
      "max": 5,
      "step": 1,
      "label": "Slides to show",
      "default": 2
    },
    {
      "type": "header",
      "content": "Modal slide"
    },
    {
      "type": "range",
      "id": "modal_slide_padding_horizontal",
      "min": 0,
      "max": 100,
      "step": 2,
      "unit": "px",
      "label": "Padding horizontal",
      "default": 56
    },
    {
      "type": "range",
      "id": "modal_slide_padding_horizontal_mobile",
      "min": 0,
      "max": 100,
      "step": 2,
      "unit": "px",
      "label": "Padding horizontal - mobile",
      "default": 24
    },
    {
      "type": "range",
      "id": "modal_slide_padding_vertical",
      "min": 0,
      "max": 100,
      "step": 2,
      "unit": "px",
      "label": "Padding vertical",
      "default": 56
    },
    {
      "type": "range",
      "id": "modal_slide_padding_vertical_mobile",
      "min": 0,
      "max": 100,
      "step": 2,
      "unit": "px",
      "label": "Padding vertical - mobile",
      "default": 24
    },
    {
      "type": "range",
      "id": "modal_slide_radius",
      "min": 0,
      "max": 50,
      "step": 2,
      "unit": "px",
      "label": "Roundness",
      "default": 0
    },
    {
      "type": "range",
      "id": "modal_slide_border_thickness",
      "min": 0,
      "max": 10,
      "step": 1,
      "unit": "px",
      "label": "Border thickness",
      "default": 0
    },
    {
      "type": "text_alignment",
      "id": "modal_slide_align",
      "label": "Alignment"
    },
    {
      "type": "text_alignment",
      "id": "modal_slide_align_mobile",
      "label": "Alignment - mobile"
    },
    {
      "type": "header",
      "content": "Modal close icon"
    },
    {
      "type": "range",
      "id": "modal_close_size",
      "min": 0,
      "max": 40,
      "step": 1,
      "unit": "px",
      "label": "Size",
      "default": 16
    },
    {
      "type": "range",
      "id": "modal_close_size_mobile",
      "min": 0,
      "max": 40,
      "step": 1,
      "unit": "px",
      "label": "Size - mobile",
      "default": 16
    },
    {
      "type": "header",
      "content": "Modal text"
    },
    {
      "type": "checkbox",
      "id": "slide_text_custom",
      "label": "Use custom font",
      "default": false
    },
    {
      "type": "font_picker",
      "id": "slide_text_font",
      "label": "Font family",
      "default": "josefin_sans_n4"
    },
    {
      "type": "range",
      "id": "slide_text_size",
      "min": 0,
      "max": 40,
      "step": 1,
      "unit": "px",
      "label": "Font size",
      "default": 14
    },
    {
      "type": "range",
      "id": "slide_text_size_mobile",
      "min": 0,
      "max": 40,
      "step": 1,
      "unit": "px",
      "label": "Font size - mobile",
      "default": 14
    },
    {
      "type": "range",
      "id": "slide_text_height",
      "min": 50,
      "max": 200,
      "step": 10,
      "unit": "%",
      "label": "Line height",
      "default": 150
    },
    {
      "type": "range",
      "id": "slide_text_mt",
      "min": 0,
      "max": 100,
      "step": 2,
      "unit": "px",
      "label": "Margin top",
      "default": 24
    },
    {
      "type": "range",
      "id": "slide_text_mt_mobile",
      "min": 0,
      "max": 100,
      "step": 2,
      "unit": "px",
      "label": "Margin top - mobile",
      "default": 16
    },
    {
      "type": "header",
      "content": "Modal subtitle"
    },
    {
      "type": "checkbox",
      "id": "slide_title_custom",
      "label": "Use custom font",
      "default": false
    },
    {
      "type": "font_picker",
      "id": "slide_title_font",
      "label": "Font family",
      "default": "josefin_sans_n4"
    },
    {
      "type": "range",
      "id": "slide_title_size",
      "min": 0,
      "max": 40,
      "step": 1,
      "unit": "px",
      "label": "Font size",
      "default": 12
    },
    {
      "type": "range",
      "id": "slide_title_size_mobile",
      "min": 0,
      "max": 40,
      "step": 1,
      "unit": "px",
      "label": "Font size - mobile",
      "default": 12
    },
    {
      "type": "range",
      "id": "slide_title_height",
      "min": 50,
      "max": 200,
      "step": 10,
      "unit": "%",
      "label": "Line height",
      "default": 150
    },
    {
      "type": "range",
      "id": "slide_title_mt",
      "min": 0,
      "max": 100,
      "step": 2,
      "unit": "px",
      "label": "Margin top",
      "default": 32
    },
    {
      "type": "range",
      "id": "slide_title_mt_mobile",
      "min": 0,
      "max": 100,
      "step": 2,
      "unit": "px",
      "label": "Margin top - mobile",
      "default": 24
    },
    {
      "type": "header",
      "content": "Modal subtext"
    },
    {
      "type": "checkbox",
      "id": "slide_sub_text_custom",
      "label": "Use custom font",
      "default": false
    },
    {
      "type": "font_picker",
      "id": "slide_sub_text_font",
      "label": "Font family",
      "default": "josefin_sans_n4"
    },
    {
      "type": "range",
      "id": "slide_sub_text_size",
      "min": 0,
      "max": 40,
      "step": 1,
      "unit": "px",
      "label": "Font size",
      "default": 14
    },
    {
      "type": "range",
      "id": "slide_sub_text_size_mobile",
      "min": 0,
      "max": 40,
      "step": 1,
      "unit": "px",
      "label": "Font size - mobile",
      "default": 14
    },
    {
      "type": "range",
      "id": "slide_sub_text_height",
      "min": 50,
      "max": 200,
      "step": 10,
      "unit": "%",
      "label": "Line height",
      "default": 150
    },
    {
      "type": "range",
      "id": "slide_sub_text_mt",
      "min": 0,
      "max": 100,
      "step": 2,
      "unit": "px",
      "label": "Margin top",
      "default": 8
    },
    {
      "type": "range",
      "id": "slide_sub_text_mt_mobile",
      "min": 0,
      "max": 100,
      "step": 2,
      "unit": "px",
      "label": "Margin top - mobile",
      "default": 8
    },
    {
      "type": "header",
      "content": "Modal button"
    },
    {
      "type": "text",
      "id": "button",
      "label": "Button label",
      "default": "Learn more"
    },
    {
      "type": "checkbox",
      "id": "button_custom",
      "label": "Use custom font",
      "default": false
    },
    {
      "type": "font_picker",
      "id": "button_font",
      "label": "Font family",
      "default": "josefin_sans_n4"
    },
    {
      "type": "range",
      "id": "button_size",
      "min": 0,
      "max": 30,
      "step": 1,
      "unit": "px",
      "label": "Font size",
      "default": 14
    },
    {
      "type": "range",
      "id": "button_size_mobile",
      "min": 0,
      "max": 30,
      "step": 1,
      "unit": "px",
      "label": "Font size - mobile",
      "default": 14
    },
    {
      "type": "range",
      "id": "button_height",
      "min": 50,
      "max": 200,
      "step": 10,
      "unit": "%",
      "label": "Line height",
      "default": 100
    },
    {
      "type": "range",
      "id": "button_padding_vertical",
      "min": 0,
      "max": 100,
      "step": 2,
      "unit": "px",
      "label": "Padding vertical",
      "default": 16
    },
    {
      "type": "range",
      "id": "button_padding_vertical_mobile",
      "min": 0,
      "max": 100,
      "step": 2,
      "unit": "px",
      "label": "Padding vertical - mobile",
      "default": 16
    },
    {
      "type": "range",
      "id": "button_padding_horizontal",
      "min": 0,
      "max": 100,
      "step": 2,
      "unit": "px",
      "label": "Padding horizontal",
      "default": 24
    },
    {
      "type": "range",
      "id": "button_padding_horizontal_mobile",
      "min": 0,
      "max": 100,
      "step": 2,
      "unit": "px",
      "label": "Padding horizontal - mobile",
      "default": 24
    },
    {
      "type": "range",
      "id": "button_radius",
      "min": 0,
      "max": 100,
      "step": 2,
      "unit": "px",
      "label": "Roundness",
      "default": 100
    },
    {
      "type": "range",
      "id": "button_border_thickness",
      "min": 0,
      "max": 10,
      "step": 1,
      "unit": "px",
      "label": "Border thickness",
      "default": 1
    },
    {
      "type": "range",
      "id": "button_mt",
      "min": 0,
      "max": 100,
      "step": 2,
      "unit": "px",
      "label": "Margin top",
      "default": 32
    },
    {
      "type": "range",
      "id": "button_mt_mobile",
      "min": 0,
      "max": 100,
      "step": 2,
      "unit": "px",
      "label": "Margin top - mobile",
      "default": 24
    },
    {
      "type": "select",
      "id": "button_style",
      "label": "Button style",
      "default": "link",
      "options": [
        {
          "label": "Link",
          "value": "link"
        },
        {
          "label": "Non-outline",
          "value": "non_outline"
        },
        {
          "label": "Non-outline & arrow",
          "value": "non_outline_arrow"
        },
        {
          "label": "Outline",
          "value": "outline"
        },
        {
          "label": "Outline & arrow",
          "value": "outline_arrow"
        }
      ]
    },
    {
      "type": "header",
      "content": "Pagination"
    },
    {
      "type": "checkbox",
      "id": "pagination_custom",
      "label": "Use custom font",
      "default": false
    },
    {
      "type": "font_picker",
      "id": "pagination_font",
      "label": "Font family",
      "default": "josefin_sans_n4"
    },
    {
      "type": "range",
      "id": "pagination_size",
      "min": 0,
      "max": 40,
      "step": 1,
      "unit": "px",
      "label": "Font size",
      "default": 14
    },
    {
      "type": "range",
      "id": "pagination_size_mobile",
      "min": 0,
      "max": 40,
      "step": 1,
      "unit": "px",
      "label": "Font size - mobile",
      "default": 14
    },
    {
      "type": "range",
      "id": "pagination_height",
      "min": 50,
      "max": 200,
      "step": 10,
      "unit": "%",
      "label": "Line height",
      "default": 130
    },
    {
      "type": "header",
      "content": "Card colors"
    },
    {
      "type": "color",
      "label": "Title",
      "id": "title_color",
      "default": "#121212"
    },
    {
      "type": "color",
      "label": "Text",
      "id": "text_color",
      "default": "#121212"
    },
    {
      "type": "color",
      "label": "Source",
      "id": "source_color",
      "default": "#121212"
    },
    {
      "type": "color",
      "label": "Plus icon",
      "id": "plus_icon_color",
      "default": "#FFFFFF"
    },
    {
      "type": "color",
      "label": "Plus icon background",
      "id": "plus_icon_bg_color",
      "default": "#AFAFAF"
    },
    {
      "type": "color",
      "label": "Background",
      "id": "card_bg_color",
      "default": "#f8f8fa"
    },
    {
      "type": "color",
      "label": "Border",
      "id": "card_border_color",
      "default": "#121212"
    },
    {
      "type": "color",
      "label": "View options",
      "id": "see_color",
      "default": "#121212"
    },
    {
      "type": "header",
      "content": "Modal slide colors"
    },
    {
      "type": "color",
      "label": "Text",
      "id": "slide_text_color",
      "default": "#121212"
    },
    {
      "type": "color",
      "label": "Subtitle",
      "id": "slide_title_color",
      "default": "#a7afc9"
    },
    {
      "type": "color",
      "label": "Subtext",
      "id": "slide_sub_text_color",
      "default": "#121212"
    },
    {
      "type": "color",
      "label": "Close icon",
      "id": "modal_close_color",
      "default": "#121212"
    },
    {
      "type": "color",
      "label": "Background",
      "id": "modal_slide_bg_color",
      "default": "#FFFFFF"
    },
    {
      "type": "color",
      "label": "Border",
      "id": "modal_slide_border_color",
      "default": "#121212"
    },
    {
      "type": "header",
      "content": "Modal button colors"
    },
    {
      "type": "color",
      "label": "Text",
      "id": "button_color",
      "default": "#121212"
    },
    {
      "type": "color",
      "label": "Hover text",
      "id": "button_hover_color",
      "default": "#121212"
    },
    {
      "type": "color",
      "label": "Background",
      "id": "button_bg_color",
      "default": "#F3F3F3"
    },
    {
      "type": "color",
      "label": "Background hover",
      "id": "button_bg_hover_color",
      "default": "#F3F3F3"
    },
    {
      "type": "color",
      "label": "Button border",
      "id": "button_border_color",
      "default": "#121212"
    },
    {
      "type": "color",
      "label": "Border hover",
      "id": "button_border_hover_color",
      "default": "#121212"
    },
    {
      "type": "header",
      "content": "Pagination colors"
    },
    {
      "type": "color",
      "label": "Text",
      "id": "pagination_color",
      "default": "#FFFFFF"
    },
    {
      "type": "color",
      "label": "Arrow",
      "id": "pagination_arrow_color",
      "default": "#FFFFFF"
    },
    {
      "type": "header",
      "content": "Section colors"
    },
    {
      "type": "color",
      "label": "Modal overlay",
      "id": "modal_overlay_color",
      "default": "#121212"
    },
    {
      "type": "color",
      "label": "Section background",
      "id": "background_color",
      "default": "#FFFFFF"
    },
    {
      "type": "color_background",
      "id": "background_gradient",
      "label": "Section background gradient",
      "info": "Remove gradient to replace with solid colors"
    },
    {
      "type": "color",
      "label": "Border",
      "id": "border_color",
      "default": "#121212"
    },
    {
      "type": "header",
      "content": "Section margin (outside)"
    },
    {
      "type": "range",
      "id": "margin_top",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "Margin top",
      "default": 0
    },
    {
      "type": "range",
      "id": "margin_bottom",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "Margin bottom",
      "default": 0
    },
    {
      "type": "range",
      "id": "margin_horizontal",
      "min": 0,
      "max": 30,
      "step": 1,
      "unit": "rem",
      "label": "Margin sides",
      "default": 0
    },
    {
      "type": "range",
      "id": "margin_horizontal_mobile",
      "min": 0,
      "max": 15,
      "step": 0.5,
      "unit": "rem",
      "label": "Margin sides mobile",
      "default": 0
    },
    {
      "type": "header",
      "content": "Section padding (inside)"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "Padding top",
      "default": 36
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "Padding bottom",
      "default": 36
    },
    {
      "type": "range",
      "id": "padding_horizontal",
      "min": 0,
      "max": 30,
      "step": 1,
      "unit": "rem",
      "label": "Padding sides",
      "default": 5
    },
    {
      "type": "range",
      "id": "padding_horizontal_mobile",
      "min": 0,
      "max": 15,
      "step": 0.5,
      "unit": "rem",
      "label": "Padding sides mobile",
      "default": 1.5
    },
    {
      "type": "header",
      "content": "Section"
    },
    {
      "type": "checkbox",
      "id": "full_width",
      "label": "Full width",
      "default": false
    },
    {
      "type": "range",
      "id": "content_width",
      "min": 800,
      "max": 2000,
      "step": 100,
      "unit": "px",
      "label": "Section content width",
      "default": 1200
    },
    {
      "type": "range",
      "id": "border_thickness",
      "min": 0,
      "max": 50,
      "step": 1,
      "unit": "px",
      "label": "Border thickness",
      "default": 0
    },
    {
      "type": "range",
      "id": "section_radius",
      "min": 0,
      "max": 100,
      "step": 2,
      "unit": "px",
      "label": "Section roundness",
      "default": 0
    },
    {
      "type": "checkbox",
      "id": "lazy",
      "label": "Lazy load",
      "info": "Lazy load images for speed optimisation",
      "default": true
    }
  ],
  "blocks": [
    {
      "type": "heading",
      "limit": 1,
      "name": "Heading",
      "settings": [
        {
          "type": "richtext",
          "id": "heading",
          "label": "Heading",
          "default": "<h2>Product ingredients #6</h2>"
        },
        {
          "type": "checkbox",
          "id": "heading_custom",
          "label": "Use custom font",
          "default": false
        },
        {
          "type": "font_picker",
          "id": "heading_font",
          "label": "Font family",
          "default": "josefin_sans_n4"
        },
        {
          "type": "range",
          "id": "heading_size",
          "min": 0,
          "max": 72,
          "step": 2,
          "unit": "px",
          "label": "Font size",
          "default": 48
        },
        {
          "type": "range",
          "id": "heading_size_mobile",
          "min": 0,
          "max": 72,
          "step": 2,
          "unit": "px",
          "label": "Font size - mobile",
          "default": 32
        },
        {
          "type": "range",
          "id": "heading_height",
          "min": 50,
          "max": 200,
          "step": 10,
          "unit": "%",
          "label": "Line height",
          "default": 130
        },
        {
          "type": "range",
          "id": "heading_mt",
          "min": 0,
          "max": 100,
          "step": 2,
          "unit": "px",
          "label": "Margin top",
          "default": 0
        },
        {
          "type": "range",
          "id": "heading_mt_mobile",
          "min": 0,
          "max": 100,
          "step": 2,
          "unit": "px",
          "label": "Margin top - mobile",
          "default": 0
        },
        {
          "type": "color",
          "label": "Color",
          "id": "heading_color",
          "default": "#121212"
        }
      ]
    },
    {
      "type": "text",
      "name": "Text",
      "settings": [
        {
          "type": "richtext",
          "id": "text",
          "label": "Text",
          "default": "<p>Add optional text</p>"
        },
        {
          "type": "checkbox",
          "id": "text_custom",
          "label": "Use custom font",
          "default": false
        },
        {
          "type": "font_picker",
          "id": "text_font",
          "label": "Font family",
          "default": "josefin_sans_n4"
        },
        {
          "type": "range",
          "id": "text_size",
          "min": 0,
          "max": 40,
          "step": 1,
          "unit": "px",
          "label": "Font size",
          "default": 16
        },
        {
          "type": "range",
          "id": "text_size_mobile",
          "min": 0,
          "max": 40,
          "step": 1,
          "unit": "px",
          "label": "Font size - mobile",
          "default": 13
        },
        {
          "type": "range",
          "id": "text_height",
          "min": 50,
          "max": 200,
          "step": 10,
          "unit": "%",
          "label": "Line height",
          "default": 150
        },
        {
          "type": "range",
          "id": "text_mt",
          "min": 0,
          "max": 100,
          "step": 2,
          "unit": "px",
          "label": "Margin top",
          "default": 16
        },
        {
          "type": "range",
          "id": "text_mt_mobile",
          "min": 0,
          "max": 100,
          "step": 2,
          "unit": "px",
          "label": "Margin top - mobile",
          "default": 12
        },
        {
          "type": "color",
          "label": "Color",
          "id": "text_color",
          "default": "#121212"
        }
      ]
    },
    {
      "type": "card",
      "name": "Card",
      "settings": [
        {
          "type": "image_picker",
          "id": "image",
          "label": "Image",
          "info": "Optional"
        },
        {
          "type": "text",
          "id": "title",
          "label": "Title",
          "default": "Title"
        },
        {
          "type": "richtext",
          "id": "text",
          "label": "Text",
          "default": "<p>Tell your story</p>"
        },
        {
          "type": "text",
          "id": "source",
          "label": "Source",
          "default": "Source"
        },
        {
          "type": "image_picker",
          "id": "modal_image",
          "label": "Modal image",
          "info": "Optional"
        },
        {
          "type": "richtext",
          "id": "modal_text",
          "label": "Modal text",
          "default": "<p>Tell your story</p>"
        },
        {
          "type": "text",
          "id": "modal_subtitle",
          "label": "Modal subtitle",
          "default": "MODAL SUBTITLE"
        },
        {
          "type": "richtext",
          "id": "modal_sub_text",
          "label": "Modal subtext",
          "default": "<p>Tell your story</p>"
        },
        {
          "type": "url",
          "id": "url",
          "label": "Link"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "SS - Product ingredients #6",
      "blocks": [
        {
          "type": "heading"
        },
        {
          "type": "text"
        },
        {
          "type": "card"
        },
        {
          "type": "card"
        },
        {
          "type": "card"
        },
        {
          "type": "card"
        },
        {
          "type": "card"
        },
        {
          "type": "card"
        },
        {
          "type": "card"
        },
        {
          "type": "card"
        },
        {
          "type": "card"
        }
      ]
    }
  ]
}
{% endschema %}
