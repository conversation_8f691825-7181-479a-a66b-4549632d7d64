{% case template.name %}
  {% when 'page' %}
    {% assign current_url = page.url %}
  {% when 'blog' %}
    {% assign current_url = blog.url %}
  {% when 'article' %}
    {% assign current_url = blog.url %}
  {% when 'collection' %}
    {% assign current_url = collection.url %}
  {% when 'product' %}
    {% assign current_url = product.url %}
{% endcase %}

<modal-content section="{{ section.id }}" id="quiz-popup--register" class="modal modal--quiz modal--register">

  <div class="modal__overlay"></div>

  <div class="modal__content">

    <button type="button" class="modal__close-button tap-area" data-action="close" title="{{ 'general.accessibility.close' | t | escape }}">
      {%- render 'icon' with 'close' -%}
    </button>

    <div class="quiz-modal-content">
      
      {%- if section.settings.image != blank -%}
        <img class="quiz-modal__image" loading="lazy" sizes="(max-width: 740px) 100vw, 500px" {% render 'image-attributes', image: section.settings.image, sizes: '300,400,500,600,700,800,900,1000' %}>
      {%- endif -%}

      <div class="newsletter-modal__content {% if section.settings.image != blank %}newsletter-modal__content--extra{% endif %} text-container text--center">

        {%- if section.settings.title != blank -%}
          <h2 class="heading h2">{{ section.settings.title | escape }}</h2>
        {%- endif -%}

        {%- if section.settings.content != blank -%}
          {{- section.settings.content | replace: "<p", "<p class='text--large'" -}}
        {%- endif -%}

        <div class="form__actions">

          <a href="{{ settings.quiz_bookmark_quiz_account }}" is="loader-button" class="button button--primary">
            <span class="button__text">{{ 'quiz.general.create_account' | t }}</span>
            <span class="button__icon">{%- render 'icon' with 'nav-arrow-right' -%}</span>
          </a>

          <div class="quiz-modal-footer">

            <div>
              {{ 'quiz.general.have_an_account' | t }}
              <button is="toggle-button" aria-controls="quiz-popup--login" aria-expanded="false" class="link">{{ 'quiz.general.login' | t }}</button>
            </div>

            {% comment %}
              <button href="button" is="loader-button" class="button button--link" data-action="close" title="{{ 'quiz.general.skip' | t | escape }}">
                <span class="button__text">{{ 'quiz.general.skip' | t }}</span>
                <span class="button__icon">{%- render 'icon' with 'nav-arrow-right' -%}</span>
              </button>
            {% endcomment %}

          </div>

        </div>

      </div>

    </div>
  </div>
</modal-content>

{% schema %}
{
  "name": "Popup - Register",
  "class": "shopify-section--popup quiz",
  "settings": [
    {
      "type": "image_picker",
      "id": "image",
      "label": "Image",
      "info": "1000 x 1000px .jpg recommended"
    },
    {
      "type": "text",
      "id": "title",
      "label": "Heading",
      "default": "Create An Account"
    },
    {
      "type": "richtext",
      "id": "content",
      "label": "Text",
      "default": "<p>If you’d like to save your recipe recommendations for later, please login or create an account.</p>"
    }
  ],
  "blocks": [
    {
      "type": "@app"
    },
    {
      "type": "liquid",
      "name": "Liquid",
      "settings": [
        {
          "type": "liquid",
          "id": "liquid",
          "label": "Liquid",
          "info": "Add app snippets or other Liquid code to create advanced customizations."
        }
      ]
    },
    {
      "type": "login",
      "name": "Login form",
      "limit": 1
    }
  ]
}
{% endschema %}