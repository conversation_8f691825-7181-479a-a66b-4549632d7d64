<style>
  #shopify-section-{{ section.id }} {
    
    {%- if section.settings.heading_color == 'rgba(0,0,0,0)' -%}
      {%- assign heading_color = settings.heading_color -%}
    {%- else -%}
      {%- assign heading_color = section.settings.heading_color -%}
    {%- endif -%}
    
    {%- if section.settings.text_color == 'rgba(0,0,0,0)' -%}
      {%- assign text_color = settings.text_color -%}
    {%- else -%}
      {%- assign text_color = section.settings.text_color -%}
    {%- endif -%}

    {%- if section.settings.background == 'rgba(0,0,0,0)' -%}
      {%- assign section_background = settings.background -%}
    {%- else -%}
      {%- assign section_background = section.settings.background -%}
    {%- endif -%}

    --section-background: {{ section_background.red }}, {{ section_background.green }}, {{ section_background.blue }};
    --heading-color: {{ heading_color.red }}, {{ heading_color.green }}, {{ heading_color.blue }};
    --text-color: {{ text_color.red }}, {{ text_color.green }}, {{ text_color.blue }};

  }
</style>

<vet-sticky-bar section="{{ section.id }}" class="vet-sticky-bar" id="{{ section.id }}--vet-sticky-bar">

  <button type="button" is="toggle-button" class="vet-sticky-bar__tag" aria-controls="{{ section.id }}--vet-sticky-bar" aria-expanded="false">
    <span class="vet-sticky-bar__text subheading heading heading--xsmall">{{ section.settings.tag_text }}</span>
  </button>

  <span class="vet-sticky-bar__overlay"></span>

  <div class="vet-sticky-bar__wrapper">
  
    <div class="container">

      <div class="vet-sticky-bar__inner">

        <div class="vet-sticky-bar__info">
  
          <div class="vet-sticky-bar__icon">
            {% render 'icon-whos-your-vet' %}
          </div>
  
          <div class="vet-sticky-bar__text">
            
            {%- if section.settings.title -%}
              <p class="subheading heading heading--xsmall hidden-pocket">{{ section.settings.title }}</p>
            {%- endif -%}
  
            {%- if section.settings.content -%}
              <div class="text--xxsmall">
                {{ section.settings.content }}
              </div>
            {%- endif -%}
  
          </div>
  
        </div>
    
        <div class="vet-sticky-bar__actions">
          {% render 'cart-vet-partner', section: section, auto_close_vet_text: true %}
        </div>

      </div>
  
    </div>

  </div>

</vet-sticky-bar>

{% render 'modal--vet-not-listed', section: section %}

{% schema %}
{
  "name": "🐕 Vet Sticky Bar",
  "class": "shopify-section--vet-sticky-bar",
  "settings": [
    {
      "type": "text",
      "id": "title",
      "label": "Title",
      "default": "Who's Your Vet?"
    },
    {
      "type": "richtext",
      "id": "content",
      "label": "Content",
      "default": "<p>For our prescription diets, we work closely with our vet partners to ensure the best care for your pup. Please select your vet from the dropdown to checkout with our prescription diets.</p>"
    },
    {
      "type": "text",
      "id": "tag_text",
      "label": "Tag Text",
      "default": "Who's Your Vet?"
    },
    {
      "type": "header",
      "content": "Colors"
    },
    {
      "type": "color",
      "id": "background",
      "label": "Background",
      "default": "#ffffff"
    },
    {
      "type": "color",
      "id": "heading_color",
      "label": "Heading color",
      "default": "rgba(0,0,0,0)"
    },
    {
      "type": "color",
      "id": "text_color",
      "label": "Text color",
      "default": "rgba(0,0,0,0)"
    }
  ]
}
{% endschema %}