{%- unless template.suffix contains 'quick-buy' -%}
  <style>
    #shopify-section-{{ section.id }} {
      {%- assign buy_buttons_block = section.blocks | where: 'type', 'buy_buttons' | first -%}

      {%- if buy_buttons_block.settings.show_payment_button -%}
        {%- if buy_buttons_block.settings.atc_button_background == 'rgba(0,0,0,0)' -%}
          {%- assign secondary_button_background = settings.secondary_button_background -%}
        {%- else -%}
          {%- assign secondary_button_background = buy_buttons_block.settings.atc_button_background -%}
        {%- endif -%}

        {%- if buy_buttons_block.settings.atc_button_text_color == 'rgba(0,0,0,0)' -%}
          {%- assign secondary_button_text_color = settings.secondary_button_text_color -%}
        {%- else -%}
          {%- assign secondary_button_text_color = buy_buttons_block.settings.atc_button_text_color -%}
        {%- endif -%}

        {%- if buy_buttons_block.settings.buy_now_button_background == 'rgba(0,0,0,0)' -%}
          {%- assign primary_button_background = settings.primary_button_background -%}
        {%- else -%}
          {%- assign primary_button_background = buy_buttons_block.settings.buy_now_button_background -%}
        {%- endif -%}

        {%- if buy_buttons_block.settings.buy_now_button_text_color == 'rgba(0,0,0,0)' -%}
          {%- assign primary_button_text_color = settings.primary_button_text_color -%}
        {%- else -%}
          {%- assign primary_button_text_color = buy_buttons_block.settings.buy_now_button_text_color -%}
        {%- endif -%}
      {%- else -%}
        {%- if buy_buttons_block.settings.atc_button_background == 'rgba(0,0,0,0)' -%}
          {%- assign primary_button_background = settings.primary_button_background -%}
        {%- else -%}
          {%- assign primary_button_background = buy_buttons_block.settings.atc_button_background -%}
        {%- endif -%}

        {%- if buy_buttons_block.settings.atc_button_text_color == 'rgba(0,0,0,0)' -%}
          {%- assign primary_button_text_color = settings.primary_button_text_color -%}
        {%- else -%}
          {%- assign primary_button_text_color = buy_buttons_block.settings.atc_button_text_color -%}
        {%- endif -%}
      {%- endif -%}

      --primary-button-background: {{ primary_button_background.red }}, {{ primary_button_background.green }}, {{ primary_button_background.blue }};
      --primary-button-text-color: {{ primary_button_text_color.red }}, {{ primary_button_text_color.green }}, {{ primary_button_text_color.blue }};
      --secondary-button-background: {{ secondary_button_background.red }}, {{ secondary_button_background.green }}, {{ secondary_button_background.blue }};
      --secondary-button-text-color: {{ secondary_button_text_color.red }}, {{ secondary_button_text_color.green }}, {{ secondary_button_text_color.blue }};
    }

    {%- if section.settings.hide_subscriptions_widget == true -%}
      #shopify-section-{{ section.id }} .awt-style-1 {
        display: none !important
      }
    {%- endif -%}
    
  </style>

  <section>
    {%- if section.settings.show_sticky_add_to_cart and product.available -%}
      {%- render 'product-sticky-form', product: product -%}
    {%- endif -%}

    <div class="container">
      <nav aria-label="{{ 'general.breadcrumb.title' | t }}" class="breadcrumb text--xsmall text--subdued hidden-phone">
        <ol class="breadcrumb__list" role="list">
          <li class="breadcrumb__item">
            <a class="breadcrumb__link" href="{{ routes.root_url }}">{{ 'general.breadcrumb.home' | t }}</a>
          </li>

          {%- if collection -%}
            <li class="breadcrumb__item">
              <a class="breadcrumb__link" href="{{ collection.url }}">{{- collection.title -}}</a>
            </li>
          {%- endif -%}

          <li class="breadcrumb__item">
            <span class="breadcrumb__link" aria-current="page">{{ product.title }}</span>
          </li>
        </ol>
      </nav>

      <!-- PRODUCT TOP PART -->
      <div class="product product--thumbnails-{{ section.settings.desktop_thumbnails_position }}">
        {%- render 'product-media', product: product -%}
        {%- render 'product-info', product: product, update_url: true -%}
      </div>
    </div>
  </section>
{%- else -%}
  {%- comment -%}
  The quick shop HTML being very different, we render it here. On mobile and tablet/desktop, the product renders also
  quite differently, as it is in a drawer on tablet/desktop, and a popover on mobile.
  {%- endcomment -%}

  {%- capture quick_buy_product_info -%}
    <div class="quick-buy-product">
      {%- assign featured_media = product.selected_or_first_available_variant.featured_media | default: product.featured_media -%}

      {%- if template.suffix == 'quick-buy-drawer' -%}
        {{- featured_media | image_url: width: featured_media.width | image_tag: loading: 'lazy', sizes: '114px', widths: '114,228,342', class: 'quick-buy-product__image' -}}
      {%- else -%}
        {{- featured_media | image_url: width: featured_media.width | image_tag: loading: 'lazy', sizes: '65px', widths: '65,130,195', class: 'quick-buy-product__image' -}}
      {%- endif -%}

      <div class="quick-buy-product__info {% if template.suffix == 'quick-buy-popover' %}text--small{% endif %}">
        <product-meta form-id="product-form-{{ section.id }}-{{ product.id }}" unit-price-class="text--xsmall" class="product-item-meta">
          {%- if section.settings.show_vendor -%}
            {%- assign vendor_handle = product.vendor | handle -%}
            {%- assign collection_for_vendor = collections[vendor_handle] -%}

            {%- unless collection_for_vendor.empty? -%}
              <a href="{{ collection_for_vendor.url }}" class="product-item-meta__vendor heading {% if template.suffix == 'quick-buy-drawer' %}heading--small{% else %}heading--xsmall{% endif %}">{{ product.vendor }}</a>
            {%- else -%}
              <a href="{{ product.vendor | url_for_vendor }}" class="product-item-meta__vendor heading {% if template.suffix == 'quick-buy-drawer' %}heading--small{% else %}heading--xsmall{% endif %}">{{ product.vendor }}</a>
            {%- endunless -%}
          {%- endif -%}

          <a href="{{ product.url }}" class="product-item-meta__title">{{ product.title }}</a>

          <div class="product-item-meta__price-list-container" role="region" aria-live="polite">
            <div class="price-list" data-product-price-list>
              {%- if product.selected_or_first_available_variant.compare_at_price > product.selected_or_first_available_variant.price -%}
                <span class="price price--highlight">
                  <span class="visually-hidden">{{ 'product.general.sale_price' | t }}</span>

                  {%- if settings.currency_code_enabled -%}
                    {{- product.selected_or_first_available_variant.price | money_with_currency -}}
                  {%- else -%}
                    {{- product.selected_or_first_available_variant.price | money -}}
                  {%- endif -%}
                </span>

                <span class="price price--compare">
                  <span class="visually-hidden">{{ 'product.general.regular_price' | t }}</span>

                  {%- if settings.currency_code_enabled -%}
                    {{- product.selected_or_first_available_variant.compare_at_price | money_with_currency -}}
                  {%- else -%}
                    {{- product.selected_or_first_available_variant.compare_at_price | money -}}
                  {%- endif -%}
                </span>
              {%- else -%}
                <span class="price">
                  <span class="visually-hidden">{{ 'product.general.sale_price' | t }}</span>

                  {%- if settings.currency_code_enabled -%}
                    {{- product.selected_or_first_available_variant.price | money_with_currency -}}
                  {%- else -%}
                    {{- product.selected_or_first_available_variant.price | money -}}
                  {%- endif -%}
                </span>
              {%- endif -%}

              {%- if product.selected_or_first_available_variant.unit_price_measurement -%}
                <div class="price text--subdued text--xsmall">
                  <div class="unit-price-measurement">
                    <span class="unit-price-measurement__price">{{ product.selected_or_first_available_variant.unit_price | money }}</span>
                    <span class="unit-price-measurement__separator">/</span>

                    {%- if product.selected_or_first_available_variant.unit_price_measurement.reference_value != 1 -%}
                      <span class="unit-price-measurement__reference-value">{{ product.selected_or_first_available_variant.unit_price_measurement.reference_value }}</span>
                    {%- endif -%}

                    <span class="unit-price-measurement__reference-unit">{{ product.selected_or_first_available_variant.unit_price_measurement.reference_unit }}</span>
                  </div>
                </div>
              {%- endif -%}
            </div>

            {%- if template.suffix == 'quick-buy-popover' -%}
              <a href="{{ product.url }}" class="link text--subdued">{{ 'product.general.view_details' | t }}</a>
            {%- endif -%}
          </div>

          {%- if section.settings.show_taxes_included -%}
            {%- if cart.taxes_included or shop.shipping_policy.body != blank -%}
              <p class="product-meta__taxes-included text--small">
                {%- if cart.taxes_included -%}
                  {{ 'product.general.include_taxes' | t }}
                {%- endif -%}

                {%- if shop.shipping_policy.body != blank -%}
                  {{ 'product.general.shipping_policy_html' | t: link: shop.shipping_policy.url }}
                {%- endif -%}
              </p>
            {%- endif -%}
          {%- endif -%}
        </product-meta>
      </div>
    </div>
  {%- endcapture -%}

  {%- if template.suffix == 'quick-buy-drawer' -%}
    <quick-buy-drawer class="drawer drawer--large drawer--quick-buy">
      <cart-notification hidden class="cart-notification cart-notification--drawer"></cart-notification>

      <span class="drawer__overlay"></span>

      <header class="drawer__header">
        <p class="drawer__title heading h4">{{ 'product.form.choose_options' | t }}</p>

        <button type="button" class="drawer__close-button tap-area" data-action="close" title="{{ 'general.accessibility.close' | t | escape }}">
          {%- render 'icon' with 'close' -%}
        </button>
      </header>

      <div class="drawer__content">
        {{- quick_buy_product_info -}}
        {%- render 'product-form', product: product -%}
      </div>
    </quick-buy-drawer>
  {%- elsif template.suffix == 'quick-buy-popover' -%}
    <quick-buy-popover class="popover popover--quick-buy">
      <span class="popover__overlay"></span>

      <header class="popover__header">
        {{- quick_buy_product_info -}}

        <button type="button" class="popover__close-button tap-area tap-area--large" data-action="close" title="{{ 'general.accessibility.close' | t | escape }}">
          {%- render 'icon' with 'close' -%}
        </button>
      </header>

      <div class="popover__content popover__content--no-padding">
        {%- render 'product-form', product: product -%}
      </div>
    </quick-buy-popover>
  {%- endif -%}
{%- endunless -%}

{% schema %}
{
  "name": "Product page",
  "class": "shopify-section--main-product",
  "blocks": [
    {
      "type": "@app"
    },
    {
      "type": "variant_picker",
      "name": "Variant picker",
      "limit": 1,
      "settings": [
        {
          "type": "checkbox",
          "id": "hide_sold_out_variants",
          "label": "Hide sold out variants",
          "default": false
        },
        {
          "type": "select",
          "id": "selector_mode",
          "label": "Selector type",
          "options": [
            {
              "value": "block",
              "label": "Block"
            },
            {
              "value": "dropdown",
              "label": "Dropdown"
            }
          ],
          "default": "block"
        },
        {
          "type": "select",
          "id": "color_mode",
          "label": "Color selector type",
          "info": "Enable [swatches](https://help.shopify.com/en/manual/online-store/themes/theme-structure/theme-settings#options-with-swatches) on product options.",
          "options": [
            {
              "value": "swatch",
              "label": "Swatch"
            },
            {
              "value": "none",
              "label": "None"
            }
          ],
          "default": "swatch"
        },
        {
          "type": "text",
          "id": "variant_image_options",
          "label": "Show variant image for options"
        },
        {
          "type": "page",
          "id": "size_chart_page",
          "label": "Size chart page",
          "info": "Feature a page for size option"
        }
      ]
    },
    {
      "type": "quantity_selector",
      "name": "Quantity selector",
      "limit": 1
    },
    {
      "type": "buy_buttons",
      "name": "Buy buttons",
      "limit": 1,
      "settings": [
        {
          "type": "checkbox",
          "id": "show_payment_button",
          "label": "Show dynamic checkout button",
          "info": "Each customer will see their preferred payment method from those available on your store, such as PayPal or Apple Pay. [Learn more](https://help.shopify.com/manual/using-themes/change-the-layout/dynamic-checkout)",
          "default": true
        },
        {
          "type": "checkbox",
          "id": "show_gift_card_recipient",
          "label": "Show recipient information form for gift card products",
          "info": "Gift card products can optionally be sent directly to a recipient along with a personal message. [Learn more](https://help.shopify.com/manual/online-store/themes/customizing-themes/add-gift-card-recipient-fields)",
          "default": false
        },
        {
          "type": "color",
          "id": "atc_button_background",
          "label": "Add to cart background",
          "default": "rgba(0,0,0,0)"
        },
        {
          "type": "color",
          "id": "atc_button_text_color",
          "label": "Add to cart color",
          "default": "rgba(0,0,0,0)"
        },
        {
          "type": "color",
          "id": "buy_now_button_background",
          "label": "Buy now background",
          "default": "rgba(0,0,0,0)"
        },
        {
          "type": "color",
          "id": "buy_now_button_text_color",
          "label": "Buy now color",
          "default": "rgba(0,0,0,0)"
        }
      ]
    },
    {
      "type": "description",
      "name": "Description",
      "limit": 1
    },
    {
      "type": "inventory",
      "name": "Inventory",
      "limit": 1,
      "settings": [
        {
          "type": "range",
          "id": "low_inventory_threshold",
          "label": "Low inventory threshold",
          "info": "Use low stock color when quantity is below the threshold. Choose 0 to always show in stock.",
          "min": 0,
          "max": 100,
          "step": 1,
          "default": 0
        }
      ]
    },
    {
      "type": "text",
      "name": "Text",
      "settings": [
        {
          "type": "richtext",
          "id": "text",
          "label": "Text"
        }
      ]
    },
    {
      "type": "image",
      "name": "Image",
      "settings": [
        {
          "type": "image_picker",
          "id": "image",
          "label": "Image"
        },
        {
          "type": "select",
          "id": "image_alignment",
          "label": "Alignment",
          "options": [
            {
              "value": "left",
              "label": "Left"
            },
            {
              "value": "center",
              "label": "Center"
            },
            {
              "value": "right",
              "label": "Right"
            }
          ],
          "default": "left"
        },
        {
          "type": "range",
          "id": "image_width",
          "min": 50,
          "max": 500,
          "step": 10,
          "unit": "px",
          "label": "Width",
          "default": 150
        }
      ]
    },
    {
      "type": "button",
      "name": "Button",
      "settings": [
        {
          "type": "paragraph",
          "content": "Create link to your contact page, external marketplace..."
        },
        {
          "type": "url",
          "id": "link",
          "label": "Link"
        },
        {
          "type": "text",
          "id": "text",
          "label": "Text"
        },
        {
          "type": "checkbox",
          "id": "stretch",
          "label": "Stretch button",
          "default": true
        },
        {
          "type": "color",
          "id": "background",
          "label": "Background",
          "default": "rgba(0,0,0,0)"
        },
        {
          "type": "color",
          "id": "text_color",
          "label": "Text color",
          "default": "rgba(0,0,0,0)"
        }
      ]
    },
    {
      "type": "liquid",
      "name": "Custom Liquid",
      "settings": [
        {
          "type": "liquid",
          "id": "liquid",
          "label": "Liquid",
          "info": "Add app snippets or other Liquid code to create advanced customizations."
        }
      ]
    },
    {
      "type": "line_item_property",
      "name": "Line item property",
      "settings": [
        {
          "type": "paragraph",
          "content": "Line item properties are used to collect customization information for an item added to the cart."
        },
        {
          "type": "text",
          "id": "label",
          "label": "Label",
          "default": "Your label"
        },
        {
          "type": "select",
          "id": "type",
          "label": "Type",
          "options": [
            {
              "value": "text",
              "label": "Text"
            },
            {
              "value": "checkbox",
              "label": "Checkbox"
            }
          ],
          "default": "text"
        },
        {
          "type": "header",
          "content": "Text",
          "info": "Only applicable for line item property of type Text."
        },
        {
          "type": "checkbox",
          "id": "required",
          "label": "Required",
          "default": false
        },
        {
          "type": "checkbox",
          "id": "allow_long_text",
          "label": "Allow long text",
          "default": false
        },
        {
          "type": "header",
          "content": "Checkbox",
          "info": "Only applicable for line item property of type Checkbox."
        },
        {
          "type": "text",
          "id": "checked_value",
          "label": "Checked value",
          "default": "Yes"
        },
        {
          "type": "text",
          "id": "unchecked_value",
          "label": "Unchecked value",
          "default": "No"
        }
      ]
    }
  ],
  "settings": [
    {
      "type": "header",
      "content": "🐕 Subscriptions"
    },
    {
      "type": "checkbox",
      "id": "hide_subscriptions_widget",
      "label": "Hide Subscriptions Widget",
      "default": true,
      "info": "Hides the Awtomic subscriptions widget, which makes it so that customers can only convert items to subscriptions in the cart, not add subscriptions to cart on the product page."
    },
    {
      "type": "header",
      "content": "Section Settings"
    },
    {
      "type": "checkbox",
      "id": "show_vendor",
      "label": "Show vendor",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "show_sku",
      "label": "Show SKU",
      "default": false
    },
    {
      "type": "checkbox",
      "id": "show_taxes_included",
      "label": "Show taxes included",
      "default": false
    },
    {
      "type": "checkbox",
      "id": "show_product_rating",
      "label": "Show product rating",
      "info": "To display a rating, add a product rating app. [Learn more](https://help.shopify.com/en/manual/products/product-reviews/installation)",
      "default": false
    },
    {
      "type": "checkbox",
      "id": "show_sticky_add_to_cart",
      "label": "Show sticky add to cart",
      "info": "Will be hidden if no Buy buttons block is added onto the page.",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "show_share_buttons",
      "label": "Show share buttons",
      "default": true
    },
    {
      "type": "page",
      "id": "help_page",
      "label": "Help page",
      "info": "Feature a page to help your customers"
    },
    {
      "type": "header",
      "content": "Media"
    },
    {
      "type": "paragraph",
      "content": "Learn more about [media types](https://help.shopify.com/en/manual/products/product-media)"
    },
    {
      "type": "checkbox",
      "id": "enable_video_autoplay",
      "label": "Enable video autoplay",
      "info": "Video are muted automatically to allow autoplay",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "enable_video_looping",
      "label": "Enable video looping",
      "default": false
    },
    {
      "type": "checkbox",
      "id": "enable_image_zoom",
      "label": "Enable image zoom",
      "info": "Zoom does not show video nor 3D models.",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "show_thumbnails_on_mobile",
      "label": "Show thumbnails on mobile",
      "default": false
    },
    {
      "type": "select",
      "id": "desktop_thumbnails_position",
      "label": "Desktop thumbnails position",
      "options": [
        {
          "value": "left",
          "label": "Left"
        },
        {
          "value": "bottom",
          "label": "Bottom"
        }
      ],
      "default": "bottom"
    },
    {
      "type": "select",
      "id": "transition_effect",
      "label": "Transition effect",
      "options": [
        {
          "value": "slide",
          "label": "Slide"
        },
        {
          "value": "fade",
          "label": "Fade"
        }
      ],
      "default": "slide"
    }
  ]
}
{% endschema %}
