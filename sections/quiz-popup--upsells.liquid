<modal-content section="{{ section.id }}" id="quiz-popup--upsells" class="modal modal--quiz modal--upsells">

  <div class="modal__overlay"></div>

  <div class="modal__content">

    <button type="button" class="modal__close-button tap-area" data-action="close" title="{{ 'general.accessibility.close' | t | escape }}">
      {%- render 'icon' with 'close' -%}
    </button>

    <div class="quiz-modal-content">

      <div class="newsletter-modal__content {% if section.settings.image != blank %}newsletter-modal__content--extra{% endif %} text-container text--center">

        <div class="quiz-modal-content__header">
          
          {%- if section.settings.title != blank -%}
            <h2 class="heading h2">{{ section.settings.title | escape }}</h2>
          {%- endif -%}

          {%- if section.settings.content != blank -%}
            {{- section.settings.content | replace: "<p", "<p class='text--large'" -}}
          {%- endif -%}

        </div>

        <quiz-results-products class="gallery">
          
          <scrollable-content {% unless section.settings.show_arrows %}draggable{% endunless %} class="gallery__list-wrapper {% if section.blocks.size >= 3 %}is-scrollable{% endif %} hide-scrollbar">
            <div class="container">
              <div class="gallery__list quiz-results__list">

                {% for block in section.blocks %}
                  {% if block.type == "upsell_product" %}
                    {% if block.settings.product != blank %}
                      {% render 'quiz-gallery-product', product: block.settings.product, show_price: true, hide_details: true %}
                    {% endif %}
                  {% endif %}
                {% endfor %}

              </div>
            </div>
          </scrollable-content>

        </quiz-results-products>

        <div class="quiz-modal-content__footer text-align--center">
          
          <form id="" data-quiz-checkout-form>
            
            <button type="button" is="loader-button" class="button-checkout button button--large button--highlight" data-quiz-button-checkout-upsells>
              Checkout
            </button>

          </form>

        </div>

      </div>

    </div>
  </div>
</modal-content>

<script>
  document.querySelector('[data-quiz-button-checkout-upsells]').addEventListener("click", document.querySelector("quiz-results").onClickCheckoutUpsells);
</script>

{% schema %}
{
  "name": "Popup - Upsells",
  "class": "shopify-section--popup quiz",
  "settings": [
    {
      "type": "text",
      "id": "title",
      "label": "Heading",
      "default": "Add Advanced Care Boosters for 50% off"
    },
    {
      "type": "richtext",
      "id": "content",
      "label": "Text",
      "default": "<p>Your pup may also benefit from some of our recommended supplements.</p>"
    }
  ],
  "blocks": [
    {
      "type": "upsell_product",
      "name": "Upsell Product",
      "settings": [
        {
          "id": "product",
          "type": "product",
          "label": "Product"
        }
      ]
    }
  ]
}
{% endschema %}