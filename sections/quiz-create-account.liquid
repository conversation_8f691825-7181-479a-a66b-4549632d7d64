{% render 'quiz-js-variables' %}

<section class="quiz-step quiz-step--home quiz-step--active">

  <div class="quiz-step__inner">

    <div class="quiz-step__body quiz-step__body--narrow">

      {% if section.settings.icon != blank %}
        <img class="quiz-step-logo" sizes="(max-width: 740px) calc(100vw - 96px), (max-width: 999px) calc(100vw - 160px), 600px" loading="lazy" {% render 'image-attributes', image: section.settings.icon, sizes: '600,700,800,1000,1200,1400' %}>
      {% endif %}

      {% if section.settings.title != blank %}
        <h1 class="h2 quiz-step-title">{{ section.settings.title }}</h1>
      {% endif %}
  
      {% if section.settings.description != blank %}
        <div class="quiz-step-description text--large">{{ section.settings.description }}</div>
      {% endif %}

      <div class="quiz-step-actions">

        <button type="button" class="button button--highlight" data-quiz-button-next>
          <span class="button__text">{{ 'quiz.general.create_account' | t }}</span>
          <span class="button__icon">{%- render 'icon' with 'nav-arrow-right' -%}</span>
        </button>

        <div class="quiz-step-actions-account">
          <div class="quiz-step-actions__inner">
            {% if customer %}
              {{ 'quiz.general.welcome_back_html' | t: customer_first_name: customer.first_name }}
            {% else %}
              {{ 'quiz.general.have_an_account' | t }}
            {% comment %} <a href="{{ routes.account_register_url }}" class="link">{{ 'quiz.general.login' | t }}</a> {% endcomment %}
            <button type="button" is="toggle-button" aria-controls="quiz-popup--login" aria-expanded="false" class="link">
              <span class="quiz-navigation-button__text">{{ 'quiz.general.login' | t  }}</span>
            </button>
            {% endif %}
          </div>
        </div>

        <button type="button" class="button button--link" data-quiz-button-skip>
          <span class="button__text">{{ 'quiz.inline-account.navigation.skip' | t }}</span>
          <span class="button__icon">{%- render 'icon' with 'nav-arrow-right' -%}</span>
        </button>
      
      </div>

    </div>

  </div>

  {% render 'quiz-decoration', classes: 'quiz-decoration--1' %}
  {% render 'quiz-decoration', classes: 'quiz-decoration--2' %}

</section>

<section class="quiz-step quiz-step--form">

  <div class="split-page">

    <div class="split-page__left">

      <div class="split-page__content-wrapper">

        <div class="split-page__header">
        </div>

        <div class="split-page__content">

          {% capture return_url %}{{ settings.quiz_bookmark_results }}{% endcapture %}

          {%- form 'create_customer', name: 'create', class: 'form', id: 'register-customer', return_to: return_url  -%}

            <input type="hidden" name="customer[email]" name="customer[email]" required="required" id="real-form-email">
            <input type="hidden" name="customer[password]" name="customer[password]" required="required" id="real-form-password">
            <input type="hidden" name="customer[first_name]" id="real-form-firstname">
            <input type="hidden" name="customer[last_name]" id="real-form-lastname">

            <input type="hidden" name="customer[tags]" id="real-form-kyc">
            <input type="hidden" name="customer[name]" id="real-form-note" data-hidden-note-input>

            <input type="hidden" name="return_to" value="{{ return_url }}" />

            {%- if form.errors -%}
              <div class="banner banner--error form__banner" id="login-form-error">
                <span class="banner__ribbon">{% render 'icon' with 'form-error' %}</span>
                <div class="banner__content">{{ form.errors | default_errors }}</div>
              </div>
            {%- endif -%}

          {%- endform -%}

          <form id="customer-register-form" method="post" action="#">

            <split-page-step class="quiz-page-content page-content page-content--small split-page-step--visible">

              <revealing-form class="revealing-form">
              
                <revealing-form-input class="input revealing-form-input revealing-form-input--visible">
                  <input type="text" placeholder="{{ 'quiz.register.full_name_placeholder' | t }}" id="customer[full_name]" class="input__field" name="customer[full_name]" required="required" autocomplete="email" {% if form.errors contains 'email' %}aria-invalid="true" aria-describedby="register-form-error"{% endif %} data-bound-split-name-input data-bound-first-name-input="real-form-firstname" data-bound-last-name-input="real-form-lastname">
                  <label for="customer[email]" class="input__label">{{ 'quiz.register.full_name' | t }}</label>
                </revealing-form-input>
                
                <revealing-form-input class="input revealing-form-input">
                  <input type="email" placeholder="{{ 'customer.register.email' | t }}" id="customer[email]" class="input__field" name="customer[email]" required="required" autocomplete="email" {% if form.errors contains 'email' %}aria-invalid="true" aria-describedby="register-form-error"{% endif %} data-bound-hidden-input="real-form-email">
                  <label for="customer[email]" class="input__label">{{ 'customer.register.email' | t }}</label>
                </revealing-form-input>

                <revealing-form-input class="input revealing-form-input">
                  <input type="password" placeholder="{{ 'customer.register.password_placeholder' | t }}" id="customer[password]" class="input__field" name="customer[password]" required="required" minlength="5" autocomplete="new-password" {% if form.errors contains 'password' %}aria-invalid="true" aria-describedby="register-form-error"{% endif %} data-bound-hidden-input="real-form-password">
                  <label for="customer[password]" class="input__label">{{ 'customer.register.password' | t }}</label>
                </revealing-form-input>

                <revealing-form-actions class="form__actions revealing-form__actions">

                  {% if section.settings.show_customer_info_step %}
                    <button type="button" class="form__submit button button--primary" data-split-page-next>
                      <span class="button__text">{{ 'quiz.general.continue' | t }}</span>
                      <span class="button__icon">{%- render 'icon' with 'nav-arrow-right' -%}</span>
                    </button>
                  {% else %}
                    <button type="submit" class="form__submit button button--primary" form="register-customer">
                      <span class="button__text">{{ 'quiz.general.continue' | t }}</span>
                      <span class="button__icon">{%- render 'icon' with 'nav-arrow-right' -%}</span>
                    </button>
                  {% endif %}

                </revealing-form-actions>

              </revealing-form>

            </split-page-step>

            <split-page-step class="quiz-page-content page-content page-content--small">

              <div class="split-page__step-navigation">
                <button class="quiz-navigation-button" type="button" data-split-page-prev>
                  <span class="quiz-navigation-button__icon">{%- render 'icon' with 'chevron-back' -%}</span>
                  <span class="quiz-navigation-button__text">Back</span>
                </button>
              </div>

              <div class="input">
                <p class="input__field">{{ 'quiz.inline-account.content.how_did_you_hear' | t }}</p>
                <label class="input__label">{{ 'quiz.general.required' | t }}</label>
              </div>

              {% for block in section.blocks %}

                {% if block.type == "kyc_option" %}

                  <div class="kyc-option-radio input input--radio">
                    <div class="checkbox-container">
                      <input 
                        type="radio" 
                        class="checkbox" 
                        name="customer[tags]"
                        id="customer[tags][{{ forloop.index }}]"
                        value="Referred By: {{ block.settings.title }}"
                        {% if section.settings.kyc_required == true %}required{% endif %}
                        data-bound-hidden-input="real-form-kyc" {% if block.settings.option_type != "" %}data-sub-option-container="{{ block.id }}-options"{% else %}data-sub-option-container{% endif %}>
                      <label for="customer[tags][{{ forloop.index }}]" class="">{{ block.settings.title }}</label>
                    </div>
                  </div>

                  {% if block.settings.option_type != "" %}

                    {% if block.settings.option_type == "select" %}

                      {% if block.settings.options != blank %}

                        {%- assign options_array = block.settings.options | newline_to_br | split: '<br />' -%}

                        <div class="input input--select input--sub-option" id="{{ block.id }}-options" style="display: none">

                          {%- capture option_name -%}
                            {%- if block.settings.option_name != blank -%}
                              {{- block.settings.option_name -}}
                            {%- else -%}
                              Option {{ forloop.index }} Details
                            {%- endif -%}
                          {%- endcapture -%}

                          <select name="customer[note][{{ option_name }}]" id="customer[note][{{ option_name }}]" data-sub-option-input>
                            <option value="" data-null-value>{{ block.settings.placeholder | default: "Choose your Option" }}</option>
                            {% for option in options_array %}
                              <option value="{{ option }}">{{ option }}</option>
                            {% endfor %}
                          </select>
                          <label for="customer[note][{{ option_name }}]" class="visually-hidden">{{ block.settings.title }}</label>

                        </div>

                      {% endif %}

                    {% elsif block.settings.option_type == "vets" %}

                      {% if settings.quiz_veterinarians != blank %}

                        {%- assign options_array = settings.quiz_veterinarians | newline_to_br | split: '<br />' -%}

                        <div class="input input--select input--sub-option" id="{{ block.id }}-options" style="display: none">

                          {%- capture option_name -%}
                            {%- if block.settings.option_name != blank -%}
                              {{- block.settings.option_name -}}
                            {%- else -%}
                              Option {{ forloop.index }} Details
                            {%- endif -%}
                          {%- endcapture -%}

                          <styled-select searchable observe> 
                            <div>

                              <select 
                                id="customer[note][{{ option_name }}]" 
                                name="customer[note][{{ option_name }}]" 
                                data-sub-option-input 
                                data-sub-option-input-reveal-value="Not Listed" 
                                data-sub-option-input-reveal-input="{{ block.id }}-other-input-container" 
                                data-bound-hidden-input="real-form-kyc">

                                <option value="" data-null-value>{{ block.settings.placeholder | default: "Choose your Option" }}</option>
                                {% for option in options_array %}
                                  <option value="{{ option }}">{{ option }}</option>
                                {% endfor %}
                              </select>
                            </div>
                          </styled-select>

                          <label for="customer[note][{{ option_name }}]" class="visually-hidden">{{ block.settings.title }}</label>

                          <div other-input id="{{ block.id }}-other-input-container" class="hidden">
                            <input type="text" name="customer[note][{{ option_name }}]" placeholder="{{ block.settings.placeholder | default: "Choose your Option" }}" data-sub-option-input id="{{ block.id }}-other-input" />
                            <label for="customer[note][{{ option_name }}]" class="visually-hidden">{{ block.settings.title }}</label>
                          </div>

                        </div>

                      {% endif %}

                    {% elsif block.settings.option_type == "text" %}

                      <div class="input input--text input--sub-option" id="{{ block.id }}-options" style="display: none">

                        {%- capture option_name -%}
                          {%- if block.settings.option_name != blank -%}
                            {{- block.settings.option_name -}}
                          {%- else -%}
                            Option {{ forloop.index }} Details
                          {%- endif -%}
                        {%- endcapture -%}

                        <input type="text" name="customer[note][{{ option_name }}]" id="customer[note][{{ option_name }}]" placeholder="{{ block.settings.placeholder | default: "Choose your Option" }}" data-sub-option-input />
                        <label for="customer[note][{{ option_name }}]" class="visually-hidden">{{ block.settings.title }}</label>

                      </div>

                    {% endif %}

                  {% endif %}

                {% endif %}

              {% endfor %}
              
              <div class="form__actions">

                <button type="submit" class="form__submit button button--highlight">
                  <span class="button__text">{{ 'quiz.general.create_account' | t }}</span>
                  <span class="button__icon">{%- render 'icon' with 'nav-arrow-right' -%}</span>
                </button>

                {% comment %} 
                <button type="button" class="button button--highlight" data-split-page-prev>
                  <span class="button__icon">{%- render 'icon' with 'nav-arrow-left' -%}</span>
                  <span class="button__text">Back</span>
                </button>
                {% endcomment %}

                <button type="submit" class="button button--primary">
                  <span class="button__text">{{ 'quiz.general.skip' | t }}</span>
                  <span class="button__icon">{%- render 'icon' with 'nav-arrow-right' -%}</span>
                </button>

              </div>

            </split-page-step>

          </form>

        </div>
          
        <div class="split-page__footer">

          {% if section.settings.footer_text != blank %}
            <div class="quiz-terms text--xsmall">{{ section.settings.footer_text }}</div>
          {% endif %}
          
        </div>
        
      </div>
      
    </div>
    
    <div class="split-page__right hidden-pocket">

      {% if section.settings.page_banner != blank %}
        <img class="split-page__image" sizes="(max-width: 740px) calc(100vw - 96px), (max-width: 999px) calc(100vw - 160px), 600px" loading="lazy" {% render 'image-attributes', image: section.settings.page_banner, sizes: '600,700,800,1000,1200,1400' %}>
      {% endif %}

    </div>

  </div>

</section>

<style>

  #shopify-section-{{ section.id }} {
    {%- if section.settings.text_background == 'rgba(0,0,0,0)' -%}
      {%- assign section_block_background = settings.background -%}
    {%- else -%}
      {%- assign section_block_background = section.settings.text_background -%}
    {%- endif -%}
  
    {%- if section.settings.text_color == 'rgba(0,0,0,0)' -%}
      {%- assign heading_color = settings.heading_color -%}
      {%- assign text_color = settings.text_color -%}
    {%- else -%}
      {%- assign heading_color = section.settings.text_color -%}
      {%- assign text_color = section.settings.text_color -%}
    {%- endif -%}
  
    {%- if section.settings.button_background == 'rgba(0,0,0,0)' -%}
      {%- assign button_background = settings.primary_button_background -%}
    {%- else -%}
      {%- assign button_background = section.settings.button_background -%}
    {%- endif -%}
  
    {%- if section.settings.button_text_color == 'rgba(0,0,0,0)' -%}
      {%- assign button_text_color = settings.primary_button_text_color -%}
    {%- else -%}
      {%- assign button_text_color = section.settings.button_text_color -%}
    {%- endif -%}
  
    --heading-color: {{ heading_color.red }}, {{ heading_color.green }}, {{ heading_color.blue }};
    --text-color: {{ text_color.red }}, {{ text_color.green }}, {{ text_color.blue }};
    --primary-button-background: {{ button_background.red }}, {{ button_background.green }}, {{ button_background.blue }};
    --primary-button-text-color: {{ button_text_color.red }}, {{ button_text_color.green }}, {{ button_text_color.blue }};
  
    --prev-next-button-background: {{ settings.background.red }}, {{ settings.background.green }}, {{ settings.background.blue }};
    --prev-next-button-color: {{ settings.text_color.red }}, {{ settings.text_color.green }}, {{ settings.text_color.blue }};
  
    --section-block-background: {{ section_block_background.red }}, {{ section_block_background.green }}, {{ section_block_background.blue }};
    
  }

</style>

{% schema %}
{
  "name": "Create Account",
  "class": "shopify-section--quiz-home",
  "settings": [
    {
      "type": "checkbox",
      "id": "show_customer_info_step",
      "label": "Show Customer Info Step",
      "default": false
    },
    {
      "type": "header",
      "content": "Home"
    },
    {
      "type": "image_picker",
      "id": "icon",
      "label": "Icon"
    },
    {
      "type": "text",
      "id": "title",
      "label": "Title",
      "default": "Save Your Pup Profiles"
    },
    {
      "type": "richtext",
      "id": "description",
      "label": "Description",
      "default": "<p>While we prepare your recommended recipes, you can save your pup’s profile by providing your contact details.</p>"
    },
    {
      "type": "richtext",
      "id": "hint",
      "label": "Hint",
      "default": "<p>This takes ~3 minutes per dog.</p>"
    },
    {
      "type": "header",
      "content": "Home"
    },
    {
      "type": "text",
      "id": "button_1_label",
      "label": "Label",
      "default": "Save Your Pup Profiles"
    },
    {
      "type": "header",
      "content": "Create Account"
    },
    {
      "type": "image_picker",
      "id": "page_banner",
      "info": "2160 x 1080px .jpg recommended, 1080 x 1080px .jpg recommended if split",
      "label": "Banner - Right (Desktop Only)"
    },
    {
      "type": "richtext",
      "id": "footer_text",
      "label": "Footer Text"
    },
    {
      "type": "header",
      "content": "Colors"
    },
    {
      "type": "color",
      "id": "text_background",
      "label": "Text background",
      "default": "rgba(0,0,0,0)"
    },
    {
      "type": "color",
      "id": "text_color",
      "label": "Text color",
      "default": "rgba(0,0,0,0)"
    },
    {
      "type": "color",
      "id": "button_background",
      "label": "Button background",
      "default": "rgba(0,0,0,0)"
    },
    {
      "type": "color",
      "id": "button_text_color",
      "label": "Button text",
      "default": "rgba(0,0,0,0)"
    },
    {
      "type": "header",
      "content": "How Did You Hear About Us?"
    },
    {
      "type": "checkbox",
      "id": "kyc_required",
      "label": "Required",
      "default": true
    }
  ],
  "blocks": [
    {
      "type": "kyc_option",
      "name": "KYC Option",
      "settings": [
        {
          "type": "text",
          "id": "title",
          "label": "Choice Text"
        },
        {
          "type": "header",
          "content": "Options"
        },
        {
          "type": "text",
          "id": "option_name",
          "label": "Option Name"
        },
        {
          "type": "select",
          "id": "option_type",
          "label": "Option Type",
          "options": [
            {
              "value": "",
              "label": "None"
            },
            {
              "value": "vets",
              "label": "Veterinarians (Select)"
            },
            {
              "value": "select",
              "label": "Select"
            },
            {
              "value": "text",
              "label": "Text"
            }
          ],
          "default": ""
        },
        {
          "type": "textarea",
          "id": "options",
          "label": "Dropdown Options",
          "placeholder": "Veterinarian 1\nVeterinarian 2\nVeterinarian 3",
          "info": "This will create a dropdown with options. Every line will be a different option in the dropdown."
        },
        {
          "type": "header",
          "content": "Default Values"
        },
        {
          "type": "text",
          "id": "placeholder",
          "label": "Placeholder"
        }
      ]
    },
    {
      "type": "register",
      "name": "Register form",
      "limit": 1
    }
  ]
}
{% endschema %}