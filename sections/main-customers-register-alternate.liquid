<section class="quiz quiz--account-create">

  <div class="split-page">

    <div class="split-page__left">

      <div class="split-page__content-wrapper">
        
        <div class="split-page__header">

        </div>

        <div class="split-page__content">

          <div class="quiz-page-header">
            <h1 class="heading h2">{{ 'customer.register.title' | t }}</h1>
            <p>{{ 'customer.register.instructions' | t }}</p>
          </div>

          <div class="quiz-page-content page-content page-content--small">
            
            <div class="account__block-list">
              
              {%- for block in section.blocks -%}
                <div class="account__block-item" {{ block.shopify_attributes }}>
                  {%- case block.type -%}
                    {%- when '@app' -%}
                      {%- render block -%}

                    {%- when 'liquid' -%}
                      {{- block.settings.liquid -}}

                    {%- when 'register' -%}
                      {%- form 'create_customer', name: 'create', class: 'form', id: 'register-customer' -%}
                        {%- if form.errors -%}
                          <div class="banner banner--error form__banner" id="login-form-error">
                            <span class="banner__ribbon">{% render 'icon' with 'form-error' %}</span>
                            <div class="banner__content">{{ form.errors | default_errors }}</div>
                          </div>
                        {%- endif -%}

                        {%- if request.locale.iso_code == 'ja' -%}
                          <div class="input">
                            <input type="text" placeholder="{{ 'customer.register.last_name' | t }}" id="customer[last_name]" class="input__field" name="customer[last_name]" required="required" autocomplete="family-name" {% if form.errors contains 'last_name' %}aria-invalid="true" aria-describedby="register-form-error"{% endif %}>
                            <label for="customer[last_name]" class="input__label">{{ 'customer.register.last_name' | t }}</label>
                          </div>

                          <div class="input">
                            <input type="text" placeholder="{{ 'customer.register.first_name' | t }}" id="customer[first_name]" class="input__field" name="customer[first_name]" required="required" autocomplete="given-name" {% if form.errors contains 'first_name' %}aria-invalid="true" aria-describedby="register-form-error"{% endif %}>
                            <label for="customer[first_name]" class="input__label">{{ 'customer.register.first_name' | t }}</label>
                          </div>
                        {%- else -%}
                          <div class="input">
                            <input type="text" placeholder="{{ 'customer.register.first_name' | t }}" id="customer[first_name]" class="input__field" name="customer[first_name]" required="required" autocomplete="given-name" {% if form.errors contains 'first_name' %}aria-invalid="true" aria-describedby="register-form-error"{% endif %}>
                            <label for="customer[first_name]" class="input__label">{{ 'customer.register.first_name' | t }}</label>
                          </div>

                          <div class="input">
                            <input type="text" placeholder="{{ 'customer.register.last_name' | t }}" id="customer[last_name]" class="input__field" name="customer[last_name]" required="required" autocomplete="family-name" {% if form.errors contains 'last_name' %}aria-invalid="true" aria-describedby="register-form-error"{% endif %}>
                            <label for="customer[last_name]" class="input__label">{{ 'customer.register.last_name' | t }}</label>
                          </div>
                        {%- endif -%}

                        <div class="input">
                          <input type="email" placeholder="{{ 'customer.register.email' | t }}" id="customer[email]" class="input__field" name="customer[email]" required="required" autocomplete="email" {% if form.errors contains 'email' %}aria-invalid="true" aria-describedby="register-form-error"{% endif %}>
                          <label for="customer[email]" class="input__label">{{ 'customer.register.email' | t }}</label>
                        </div>

                        <div class="input">
                          <input type="password" placeholder="{{ 'customer.register.password' | t }}" id="customer[password]" class="input__field" name="customer[password]" required="required" autocomplete="new-password" {% if form.errors contains 'password' %}aria-invalid="true" aria-describedby="register-form-error"{% endif %}>
                          <label for="customer[password]" class="input__label">{{ 'customer.register.password' | t }}</label>
                        </div>

                        <div class="input">
                          
                          <select placeholder="{{ 'customer.register.password' | t }}" id="customer[tags]" class="input__field" name="customer[tags]" autocomplete="new-password" {% if form.errors contains 'password' %}aria-invalid="true" aria-describedby="register-form-error"{% endif %}>
                            <option value="">{{ 'customer.register.how_did_you_hear' | t }}</option>
                            <option value="Veterinarian">Veterinarian</option>
                            <option value="Friend / Family">Friend / Family</option>
                            <option value="Google">Google</option>
                            <option value="Social Media">Social Media</option>
                            <option value="Walk-In">Walk-In</option>
                          </select>

                          <label for="customer[tags]" class="input__label">{{ 'customer.register.optional' | t }}</label>

                        </div>

                        <div class="form__actions">

                          <button type="submit" is="loader-button" class="form__submit button button--primary">
                            <span class="button__text">{{ 'customer.register.submit' | t }}</span>
                            <span class="button__icon">{%- render 'icon' with 'nav-arrow-right' -%}</span>
                          </button>

                          <span class="form__secondary-action text--subdued">
                            {{- 'customer.register.already_have_account' | t -}}
                            <a href="{{ routes.account_login_url }}" class="link">{{ 'customer.register.login' | t }}</a>
                          </span>

                        </div>
                        
                      {%- endform -%}
                  {%- endcase -%}
                </div>
              {%- endfor -%}

            </div>

          </div>

        </div>

        <div class="split-page__footer">

          {% if section.settings.footer_text != blank %}
            <div class="quiz-terms text--xsmall">{{ section.settings.footer_text }}</div>
          {% endif %}
          
        </div>
        
      </div>
      
    </div>
    
    <div class="split-page__right hidden-pocket">

      {% if section.settings.page_banner != blank %}
        <img class="split-page__image" sizes="(max-width: 740px) calc(100vw - 96px), (max-width: 999px) calc(100vw - 160px), 600px" loading="lazy" {% render 'image-attributes', image: section.settings.page_banner, sizes: '600,700,800,1000,1200,1400' %}>
      {% endif %}

    </div>

  </div>

</section>

<style>

  #shopify-section-{{ section.id }} {
    {%- if section.settings.text_background == 'rgba(0,0,0,0)' -%}
      {%- assign section_block_background = settings.background -%}
    {%- else -%}
      {%- assign section_block_background = section.settings.text_background -%}
    {%- endif -%}
  
    {%- if section.settings.text_color == 'rgba(0,0,0,0)' -%}
      {%- assign heading_color = settings.heading_color -%}
      {%- assign text_color = settings.text_color -%}
    {%- else -%}
      {%- assign heading_color = section.settings.text_color -%}
      {%- assign text_color = section.settings.text_color -%}
    {%- endif -%}
  
    {%- if section.settings.button_background == 'rgba(0,0,0,0)' -%}
      {%- assign button_background = settings.primary_button_background -%}
    {%- else -%}
      {%- assign button_background = section.settings.button_background -%}
    {%- endif -%}
  
    {%- if section.settings.button_text_color == 'rgba(0,0,0,0)' -%}
      {%- assign button_text_color = settings.primary_button_text_color -%}
    {%- else -%}
      {%- assign button_text_color = section.settings.button_text_color -%}
    {%- endif -%}
  
    --heading-color: {{ heading_color.red }}, {{ heading_color.green }}, {{ heading_color.blue }};
    --text-color: {{ text_color.red }}, {{ text_color.green }}, {{ text_color.blue }};
    --primary-button-background: {{ button_background.red }}, {{ button_background.green }}, {{ button_background.blue }};
    --primary-button-text-color: {{ button_text_color.red }}, {{ button_text_color.green }}, {{ button_text_color.blue }};
  
    --prev-next-button-background: {{ settings.background.red }}, {{ settings.background.green }}, {{ settings.background.blue }};
    --prev-next-button-color: {{ settings.text_color.red }}, {{ settings.text_color.green }}, {{ settings.text_color.blue }};
  
    --section-block-background: {{ section_block_background.red }}, {{ section_block_background.green }}, {{ section_block_background.blue }};
    
  }

</style>

{% schema %}
{
  "name": "Quiz - Create Account",
  "class": "shopify-section--quiz-create-account",
  "settings": [
    {
      "type": "image_picker",
      "id": "page_banner",
      "info": "2160 x 1080px .jpg recommended, 1080 x 1080px .jpg recommended if split",
      "label": "Banner - Right (Desktop Only)"
    },
    {
      "type": "richtext",
      "id": "footer_text",
      "label": "Footer Text"
    },
    {
      "type": "header",
      "content": "Colors"
    },
    {
      "type": "color",
      "id": "text_background",
      "label": "Text background",
      "default": "rgba(0,0,0,0)"
    },
    {
      "type": "color",
      "id": "text_color",
      "label": "Text color",
      "default": "rgba(0,0,0,0)"
    },
    {
      "type": "color",
      "id": "button_background",
      "label": "Button background",
      "default": "rgba(0,0,0,0)"
    },
    {
      "type": "color",
      "id": "button_text_color",
      "label": "Button text",
      "default": "rgba(0,0,0,0)"
    }
  ],
  "blocks": [
    {
      "type": "@app"
    },
    {
      "type": "liquid",
      "name": "Liquid",
      "settings": [
        {
          "type": "liquid",
          "id": "liquid",
          "label": "Liquid",
          "info": "Add app snippets or other Liquid code to create advanced customizations."
        }
      ]
    },
    {
      "type": "register",
      "name": "Register form",
      "limit": 1
    }
  ]
}
{% endschema %}
