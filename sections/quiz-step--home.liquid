  <div class="quiz-step__inner">

    <div class="quiz-step__body quiz-step__body--narrow">

      {% if section.settings.icon != blank %}
        <img class="quiz-step-icon" sizes="(max-width: 740px) calc(100vw - 96px), (max-width: 999px) calc(100vw - 160px), 600px" loading="lazy" {% render 'image-attributes', image: section.settings.icon, sizes: '600,700,800,1000,1200,1400' %}>
      {% endif %}

      {% if section.settings.title != blank %}
        <h1 class="heading heading--large quiz-step-title">{{ section.settings.title }}</h1>
      {% endif %}

      {% if section.settings.description != blank %}
        <div class="quiz-step-description text--large">{{ section.settings.description }}</div>
      {% endif %}

      {% if section.settings.hint != blank %}
        <div class="quiz-step-hint text--xsmall">{{ section.settings.hint }}</div>
      {% endif %}

      <div class="quiz-step-actions">

        <button type="button" is="loader-button" class="button button--highlight" data-quiz-button-next>
          <span class="button__text">Let's Go!</span>
          <span class="button__icon">{%- render 'icon' with 'nav-arrow-right' -%}</span>
        </button>

      </div>

    </div>

    <div class="quiz-step__footer">

      <div class="quiz-step-actions">

        {% comment %}
        <div class="quiz-step-actions__inner">
          {{ 'quiz.general.welcome_back_html' | t: customer_first_name: customer.first_name }}
        </div>
        {% endcomment %}

      </div>

    </div>

  </div>

  {% render 'quiz-decoration', classes: 'quiz-decoration--1' %}
  {% render 'quiz-decoration', classes: 'quiz-decoration--2' %}


{% schema %}
{
  "name": "Start",
  "class": "shopify-section--quiz-home",
  "settings": [
    {
      "type": "header",
      "content": "Home"
    },
    {
      "type": "image_picker",
      "id": "icon",
      "label": "Icon"
    },
    {
      "type": "text",
      "id": "title",
      "label": "Title",
      "default": "Fresh meals for your pup"
    },
    {
      "type": "richtext",
      "id": "description",
      "label": "Description",
      "default": "<p>Fill out our short form so we can provide you with a customized 10 day diet plan and feeding recommendation.</p>"
    },
    {
      "type": "richtext",
      "id": "hint",
      "label": "Hint",
      "default": "<p>This takes ~3 minutes per dog.</p>"
    },
    {
      "type": "header",
      "content": "Login/Register Form",
      "info": "Shown if customer is not logged in."
    },
    {
      "type": "text",
      "id": "login_title",
      "label": "Title",
      "default": "Fresh meals for your pup"
    },
    {
      "type": "text",
      "id": "login_hint",
      "label": "Hint",
      "default": "This takes ~2 minutes to complete."
    },
    {
      "type": "richtext",
      "id": "login_description",
      "label": "Description",
      "default": "<p>Fill out our short form so we can provide you with a customized 10 day diet plan and feeding recommendation.</p>"
    },
    {
      "type": "header",
      "content": "How Did You Hear About Us?"
    },
    {
      "type": "checkbox",
      "id": "kyc_required",
      "label": "Required",
      "default": true
    }
  ],
  "blocks": [
    {
      "type": "kyc_option",
      "name": "KYC Option",
      "settings": [
        {
          "type": "text",
          "id": "title",
          "label": "Choice Text"
        },
        {
          "type": "header",
          "content": "Options"
        },
        {
          "type": "text",
          "id": "option_name",
          "label": "Option Name"
        },
        {
          "type": "select",
          "id": "option_type",
          "label": "Option Type",
          "options": [
            {
              "value": "",
              "label": "None"
            },
            {
              "value": "select",
              "label": "Select"
            },
            {
              "value": "vets",
              "label": "Verterinarians (Select)"
            },
            {
              "value": "text",
              "label": "Text"
            }
          ],
          "default": ""
        },
        {
          "type": "textarea",
          "id": "options",
          "label": "Dropdown Options",
          "placeholder": "Veterinarian 1\nVeterinarian 2\nVeterinarian 3",
          "info": "This will create a dropdown with options. Every line will be a different option in the dropdown."
        },
        {
          "type": "header",
          "content": "Default Values"
        },
        {
          "type": "text",
          "id": "placeholder",
          "label": "Placeholder"
        }
      ]
    },
    {
      "type": "register",
      "name": "Register form",
      "limit": 1
    }
  ]
}
{% endschema %}