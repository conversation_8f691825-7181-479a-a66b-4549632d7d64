{%- liquid 
  
  comment 
  ------------------------------------------------------------------------------------------------------------------------ 
  Standalone Products 
  ------------------------------------------------------------------------------------------------------------------------ 
  endcomment
  
  assign standalone_products_in_cart = false
  
  for item in cart.items
    unless item.selling_plan_allocation
      assign standalone_products_in_cart = true
      break
    endunless
  endfor


  comment 
  ------------------------------------------------------------------------------------------------------------------------ 
  Subscriptions Products
  ------------------------------------------------------------------------------------------------------------------------ 
  endcomment

  assign subscription_products_in_cart = false

  for item in cart.items
    if item.selling_plan_allocation
      assign subscription_products_in_cart = true
      break
    endif
  endfor


  comment 
  ------------------------------------------------------------------------------------------------------------------------ 
  Prescription Products
  ------------------------------------------------------------------------------------------------------------------------ 
  endcomment

  assign prescription_product_in_cart = false

  for item in cart.items
    assign item_tags = item.product.tags | append: ""
    if item_tags contains settings.prescription_diet_tag
      assign prescription_product_in_cart = true
      break
    endif
  endfor



  comment
  ------------------------------------------------------------------------------------------------------------------------ 
  Vet Partners
  ------------------------------------------------------------------------------------------------------------------------
  endcomment

  
  
  
-%}


<cart-drawer section="{{ section.id }}" id="mini-cart" class="mini-cart drawer drawer--large">

  <span class="drawer__overlay"></span>

  <header class="drawer__header">

    <p class="drawer__title heading h3">
      {{- 'cart.general.title' | t -}}
    </p>

    <button type="button" class="drawer__close-button tap-area" data-action="close" title="{{ 'general.accessibility.close' | t | escape }}">
      {%- render 'icon' with 'close' -%}
    </button>
  </header>

  {%- if cart.item_count == 0 -%}
    <div class="drawer__content drawer__content--center">
      <p>{{ 'cart.general.empty' | t }}</p>

      <div class="button-wrapper">
        <a href="{{ section.settings.empty_button_link }}" class="button button--primary">{{ 'cart.general.start_shopping' | t }}</a>
      </div>
    </div>
  {%- else -%}
    <div class="drawer__content">

      <!--
      standalone_products_in_cart - {{ standalone_products_in_cart }}
      subscription_products_in_cart - {{ subscription_products_in_cart }}
      prescription_product_in_cart - {{ prescription_product_in_cart }}
      customer_vet - {{ customer_vet }}
      -->

      {%- if settings.cart_show_free_shipping_threshold and settings.cart_free_shipping_threshold != '' and cart.requires_shipping -%}
        {%- assign free_shipping_thresholds = settings.cart_free_shipping_threshold | remove: ' ' | split: ',' -%}
        {%- assign has_found_matching_threshold = false -%}

        {%- if free_shipping_thresholds.size > 1 -%}
          {%- for threshold in free_shipping_thresholds -%}
            {%- assign threshold_parts = threshold | split: ':' -%}
            {%- assign currency_code = threshold_parts | first | upcase -%}

            {%- if currency_code == cart.currency.iso_code -%}
              {%- assign free_shipping_calculated_threshold = threshold_parts | last -%}
              {%- assign has_found_matching_threshold = true -%}
              {%- break -%}
            {%- endif -%}
          {%- endfor -%}
        {%- else -%}
          {%- assign free_shipping_calculated_threshold = free_shipping_thresholds | last -%}
          {%- assign has_found_matching_threshold = true -%}
        {%- endif -%}

        {%- if has_found_matching_threshold -%}
          {%- assign threshold_in_cents = free_shipping_calculated_threshold | times: 100.0 -%}
          {%- assign calculated_total_price = 0 -%}

          {%- for line_item in cart.items -%}
            {%- if line_item.requires_shipping -%}
              {%- assign calculated_total_price = calculated_total_price | plus: line_item.final_line_price -%}
            {%- endif -%}
          {%- endfor -%}

          {% comment %}We have to remove the cart level discount from the calculated amount{% endcomment %}
          {%- assign total_cart_discount = 0 -%}

          {%- for discount_application in cart.cart_level_discount_applications -%}
            {%- assign total_cart_discount = total_cart_discount | plus: discount_application.total_allocated_amount -%}
          {%- endfor -%}

          {%- assign frozen_food_in_cart = false -%}
          {%- for line_item in cart.items -%}
            {%- if line_item.product.tags contains settings.frozen_food_tag -%}
              {%- assign frozen_food_in_cart = true -%}
              {%- break -%}
            {%- endif -%}
          {%- endfor -%}

          {%- assign frozen_food_enabled = false -%}
          {% if settings.frozen_food_enable == true and settings.frozen_food_tag != blank and settings.frozen_food_shipping_threshold != blank and frozen_food_in_cart == true %}
            {%- assign frozen_food_enabled = true -%}
          {% endif %}

          {% comment %} Frozen Food Minimum {% endcomment %}
          {%- assign frozen_food_minimum = settings.frozen_food_shipping_threshold | times: 1 -%}
          {%- assign calculated_total_price = calculated_total_price | minus: total_cart_discount -%}

          {% comment %} Shipping Information {% endcomment %}
           {%- assign more_info_link = true -%}

          <free-shipping-bar 
            threshold="{{ threshold_in_cents }}" 
            {% if frozen_food_minimum %}
              frozen-food-minimum="{{ frozen_food_minimum }}"
            {% endif %}
            class="shipping-bar {% if frozen_food_enabled == true %}shipping-bar--frozen-food{% endif %} {% if frozen_food_enabled and calculated_total_price < frozen_food_minimum %}shipping-bar--frozen-food--unmet{% endif %} " 
            style="
              --progress: {{ calculated_total_price | times: 1.0 | divided_by: threshold_in_cents | at_most: 1 }};
              {% if frozen_food_enabled and calculated_total_price < frozen_food_minimum %}
              --frozen-threshold: {{ frozen_food_minimum | times: 1.0 | divided_by: threshold_in_cents | at_most: 1 }};
              {% endif %}
              " 
            {% if section.settings.frozen_food_enable %}data-frozen-food{% endif %}>

              {%- if frozen_food_enabled == true -%}
                <span class="shipping-bar__icon">
                  {% render 'icon-frozen' %}
                </span>
              {%- endif -%}

              {%- capture shipping_bar_text -%}
                {%- if frozen_food_enabled and calculated_total_price < frozen_food_minimum -%}
                  {%- capture minimum -%}{{ frozen_food_minimum | money_without_trailing_zeros }}{%- endcapture -%}
                  {%- capture remaining -%}{{ calculated_total_price | minus: frozen_food_minimum | abs | money }}{%- endcapture -%}
                  {{ 'frozen_food.shipping_messages.threshold_not_met_html' | t: minimum: minimum, remaining: remaining }}
                {%- elsif calculated_total_price >= threshold_in_cents -%}
                  {{ 'cart.general.free_shipping_html' | t }}
                {%- else -%}
                  {%- capture remaining_amount -%}{{ calculated_total_price | minus: threshold_in_cents | abs | money }}{%- endcapture -%}
                  {{ 'cart.general.free_shipping_remaining_html' | t: remaining_amount: remaining_amount }}
                {%- endif -%}
              {%- endcapture -%}

              {%- capture shipping_details -%}

                {%- capture shipping_rates -%}

                {%- for block in section.blocks -%}
                  {%- if block.type == "shipping_rate" -%}
                    
                    <tr>
                      <td>
                        <span class="flex align-center gap-05">
                          <span>{{ block.settings.name }}</span>
                          {%- if block.settings.tooltip != blank -%}
                            <span class="flex align-center" data-tooltip="{{ block.settings.tooltip }}">
                              {% render 'icon-info' %}
                            </span>
                          {%- endif -%}
                        </span>
                      </td>
                      <td>{{ block.settings.eta_days }}</td>
                      <td>
                        {% if block.settings.over_threshold_rate != "0" %}
                          {{ block.settings.over_threshold_rate | times: 100 | money_without_trailing_zeros }}
                        {% else %}
                          {{ 'cart.general.free' | t }}
                        {% endif %}
                      </td>
                      <td>
                        {% if block.settings.under_threshold_rate != 0 %}
                          {{ block.settings.under_threshold_rate | times: 100 | money_without_trailing_zeros }}
                        {% else %}
                          {{ 'cart.general.free' | t }}
                        {% endif %}
                      </td>
                    </tr>
                    
                  {%- endif -%}
                {%- endfor -%}


                {%- endcapture -%}

                {%- if section.settings.show_shipping_details -%}

                  <collapsible-content id="cart-details-content" class="collapsible shipping-details">

                    <div class="shipping-details__inner">

                      <div class="shipping-details__header">
                        <p class="shipping-details__heading text--xsmall strong">Shipping Rates</p>
                        <button type="button" class="link text--xsmall" aria-controls="cart-details-content" data-action="close">Close</button>
                      </div>

                      <div class="shipping-details__body">

                        {%- assign minimum = frozen_food_minimum  -%}

                        {%- capture shipping_message -%}
                          {%- assign threshold = threshold_in_cents | money -%}
                          {%- assign minimum = minimum | money_without_trailing_zeros -%}
                          {{ 'frozen_food.shipping_messages.general_html' | t: threshold: threshold, minimum: minimum }}
                        {%- endcapture -%}

                        {%- if shipping_message != blank -%}
                          <div class="shipping-details__message text--xsmall">
                            {{ shipping_message }}
                          </div>
                        {%- endif -%}

                        {%- if shipping_rates != blank -%}

                          <table class="shipping-details__table">
                            <thead>
                              <tr>
                                <th>Zone</th>
                                <th>ETA (Days)</th>
                                <th>&gt; {{ threshold_in_cents | money_without_trailing_zeros }}</th>
                                <th>&lt; {{ threshold_in_cents | money_without_trailing_zeros }}</th>
                              </th>
                            </thead>
                            <tbody>
                              {{ shipping_rates }}
                            </tbody>
                          </table>
                          
                        {%- endif -%}

                        {%- if section.settings.shipping_rates_bottom_text != blank -%}
                          <div class="shipping-details__rates_text text--xxsmall padding-top--20">
                            {{ section.settings.shipping_rates_bottom_text }}
                          </div>
                        {%- endif -%}

                      </div>

                      {%- if section.settings.shipping_details_page_text != blank and section.settings.shipping_details_page != blank -%}
                        <div class="shipping-details__footer">
                          <a class="link text--xsmall" href="{{ section.settings.shipping_details_page }}" target="_blank">{{ section.settings.shipping_details_page_text }}</a>
                        </div>
                      {%- endif -%}
                    
                    </div>

                  </collapsible-content>
                  
                {%- endif -%}

              {%- endcapture -%}

              <span class="shipping-bar__text text--xsmall">
                
                {{ shipping_bar_text }}
                
                {%- if shipping_details != blank -%}
                  <button type="button" is="toggle-button" class="link text--xsmall" aria-controls="cart-details-content" aria-expanded="false">
                    More Info
                  </button>
                {%- endif -%}

              </span>

            <span class="shipping-bar__progress"></span>

            {%- if shipping_details != blank -%}
              {{ shipping_details }}
            {%- endif -%}

          </free-shipping-bar>

        {%- endif -%}
      {%- endif -%}

      <form id="mini-cart-form" action="{{ routes.cart_url }}" novalidate method="post">
        <input type="hidden" name="checkout">

        {%- for line_item in cart.items -%}

          {% assign bundle_group_id = blank %}

          {% for property in line_item.properties %}

            {%- if property contains "_boxID" -%}

              {% assign bundle_group_id = property.last %}
              {%- unless bundle_group_id == blank or bundle_group_ids contains bundle_group_id -%}
                {%- if bundle_group_ids == blank -%}
                  {% assign bundle_group_ids = bundle_group_id %}
                {%- else -%}
                  {% assign bundle_group_ids = bundle_group_ids | append: "," | append: bundle_group_id %}
                {%- endif -%}
              {%- endunless -%}

            {%- endif -%}

          {%- endfor -%}

        {%- endfor -%}

        {% assign bundle_group_ids_array = bundle_group_ids | split: "," %}

        {%- for bundle_id in bundle_group_ids_array -%}

          {%- for line_item in cart.items -%}
            {%- for property in line_item.properties -%}
              {%- if property contains bundle_id -%}
                {% assign first_product = line_item.product %}
                {% assign dog_name = line_item.properties['_dog'] %}
                {% assign dog_sex = line_item.properties['_dogSex'] %}
                {% assign dog_weight = line_item.properties['_dogWeight'] %}
                {% break %}
              {%- endif -%}
            {%- endfor -%}
          {%- endfor -%}

          <box-line-item class="box-line-item" data-dog="{{ dog_name }}" data-dog-weight="{{ dog_weight }}">

            {% assign meal_image = first_product.featured_image %}

            <div class="box-line-item__inner">

              <div class="box-line-item__body">

                <div class="box-line-item__image">
                  
                  <div class="line-item__image-inner">

                    <img class="line-item__image" loading="sizes" sizes="(max-width: 740px) 80px, 92px" {% render 'image-attributes', image: meal_image, sizes: '80,92,160,184,240,276' %}>

                    <span class="line-item__loader" hidden>
                      <span class="line-item__loader-spinner spinner" hidden>{% render 'icon' with 'spinner', width: 16, height: 16, stroke_width: 6 %}</span>
                      <span class="line-item__loader-mark" hidden>{% render 'icon' with 'check', width: 20, height: 20 %}</span>
                    </span>

                  </div>

                </div>

                <div class="box-line-item__contents">

                  <span class="product-item-meta__title heading heading--small">{{ dog_name }}'s Recipe Trial</span>

                  <ul class="product-item-meta__property list--unstyled text--subdued text--xxsmall" role="list">

                    <li>{{ settings.quiz_starter_boxes_days }} Days of Food</li>

                    {% assign total_price = 0 %}
                    {% assign variantIDs = '' %}

                    {%- assign box_has_frozen_food = false -%}
                    {%- assign box_has_prescription_food = false -%}

                    {%- assign box_has_selling_plan = false -%}
                    {%- assign box_selling_plan_title = blank -%}

                    {%- for line_item in cart.items -%}
                      {%- for property in line_item.properties -%}
                        {%- if property contains bundle_id -%}

                          {% assign total_price = total_price | plus: line_item.final_line_price %}
                          
                          {% assign variantIDs = variantIDs | append: line_item.key %}
                          {% unless forloop.last %}
                            {% assign variantIDs = variantIDs | append: ',' %}
                          {% endunless %}
                          {% assign variantIDsArray = variantIDs | split: ',' %}

                          {%- capture box_line_item_display -%}
                            {% unless line_item.variant.title contains 'Default Title' %}({{ line_item.quantity }}X {{ line_item.variant.title }}){% endunless %}
                          {%- endcapture -%}
                          
                          <li>{{ line_item.product.title }} {{ box_line_item_display }}</li>

                          {%- if box_has_selling_plan == false -%}
                            {%- if line_item.selling_plan_allocation -%}
                              {%- assign box_has_selling_plan = true -%}
                              {%- assign box_selling_plan_title = line_item.selling_plan_allocation.selling_plan.name -%}
                            {%- endif -%}
                          {%- endif -%}

                          {%- if line_item.product.tags contains settings.frozen_food_tag -%}
                            {%- assign box_has_frozen_food = true -%}
                          {%- endif -%}

                          {%- if line_item.product.tags contains settings.prescription_food_tag -%}
                            {%- assign box_has_prescription_food = true -%}
                          {%- endif -%}

                        {%- endif -%}
                      {%- endfor -%}
                    {%- endfor -%}
                    
                    {%- unless line_item.properties == blank -%}
                    
                      {%- for property in line_item.properties -%}
                        {%- assign first_character_in_key = property.first | truncate: 1, '' -%}

                        {%- if property.last == blank or first_character_in_key == '_' -%}
                          {%- continue -%}
                        {%- endif -%}

                        <li class="text--xxsmall">{{ property.first }}: {{ property.last }}</li>
                      {%- endfor -%}
                      
                    {%- endunless -%}

                  </ul>

                  {%- capture product_item_tags -%}

                    {%- if box_has_prescription_food -%}
                      {%- render 'product-item-tag--prescription' -%}
                    {%- endif -%}

                    {%- if box_has_frozen_food -%}
                      {%- render 'product-item-tag--frozen' -%}
                    {%- endif -%}

                    {%- if box_has_selling_plan -%}
                      {%- render 'product-item-tag--subscription', title: box_selling_plan_title -%}
                    {%- endif -%}

                  {%- endcapture -%}

                  {%- if product_item_tags != blank -%}
                    <div class="product-item-tags">
                      {{ product_item_tags }}
                    </div>
                  {%- endif -%}

                </div>

              </div>

              <div class="box-line-item__footer">

                <div class="box-line-item__price">
                  <span class="price text--small">{{ total_price | money }}</span>
                </div>

                <div class="box-line-item__actions">
                  
                  {%- capture remove_link_ids -%}
                    {%- for id in variantIDsArray -%}
                      {{ id }}{%- unless forloop.last -%},{%- endunless -%}
                    {%- endfor -%}
                  {%- endcapture -%}

                  <box-line-item-quantity data-ids="{{ remove_link_ids }}">
                    <button type="button" class="line-item__remove-button link text--subdued text--small" data-no-instant>{{ 'cart.general.remove' | t }}</button>
                  </box-line-item-quantity>

                </div>

              </div>
            
            </div>

          </box-line-item>

          {% comment %} Capturing dog data to use in subscriptions widget. {% endcomment %}

          {%- capture box_data_string -%}
            {%- if box_data_string -%}
              {{ box_data_string }},
            {%- endif -%}
            {{ dog_name }}|{{ dog_sex }}:{{ dog_weight }}
          {%- endcapture -%}

        {%- endfor -%}

        {%- for line_item in cart.items -%}

          {% assign is_bundle_item = false %}
          {% for property in line_item.properties %}
            {%- if property contains "Starter Box" -%}
              {% assign is_bundle_item = true %}
            {%- endif -%}
          {%- endfor -%}

          {%- if is_bundle_item == true -%}
            {% continue %}
          {%- endif -%}

          <line-item class="line-item">
            <div class="line-item__content-wrapper">
              <a href="{{ line_item.url }}" class="line-item__image-wrapper" tabindex="-1" aria-hidden="true">
                <span class="line-item__loader" hidden>
                  <span class="line-item__loader-spinner spinner" hidden>{% render 'icon' with 'spinner', width: 16, height: 16, stroke_width: 6 %}</span>
                  <span class="line-item__loader-mark" hidden>{% render 'icon' with 'check', width: 20, height: 20 %}</span>
                </span>

                {%- if line_item.image != blank -%}
                  {{- line_item.image | image_url: width: line_item.image.width | image_tag: loading: 'lazy', sizes: '(max-width: 740px) 80px, 92px', widths: '80,92,160,184,240,276', class: 'line-item__image' -}}
                {%- endif -%}
              </a>

              {%- capture unit_price -%}
                {%- if line_item.unit_price_measurement -%}
                  <div class="price text--subdued">
                    <div class="unit-price-measurement">
                      <span class="unit-price-measurement__price">{{ line_item.unit_price | money }}</span>
                      <span class="unit-price-measurement__separator">/</span>

                      {%- if line_item.unit_price_measurement.reference_value != 1 -%}
                        <span class="unit-price-measurement__reference-value">{{ line_item.unit_price_measurement.reference_value }}</span>
                      {%- endif -%}

                      <span class="unit-price-measurement__reference-unit">{{ line_item.unit_price_measurement.reference_unit }}</span>
                    </div>
                  </div>
                {%- endif -%}
              {%- endcapture -%}

              {%- capture line_price -%}
                {%- comment -%}
                IMPLEMENTATION NOTE: The designer wanted to show the "compare at price" on cart. In case an automatic discount is applied
                  to a line item though, the "real" discount takes precedence over the compare at price
                {%- endcomment -%}

                <span class="price {% if line_item.original_line_price > line_item.final_line_price or line_item.final_line_price == 0 or line_item.variant.compare_at_price > line_item.variant.price %}price--highlight{% endif %}">
                  <span class="visually-hidden">{{ 'product.general.sale_price' | t }}</span>

                  {%- if line_item.final_line_price == 0 -%}
                    {{- 'cart.general.free' | t -}}
                  {%- else -%}
                    {{- line_item.final_line_price | money -}}
                  {%- endif -%}
                </span>

                {%- if line_item.original_line_price > line_item.final_line_price or line_item.variant.compare_at_price > line_item.variant.price -%}
                  <span class="price price--compare">
                    <span class="visually-hidden">{{ 'product.general.regular_price' | t }}</span>

                    {%- if line_item.original_line_price > line_item.final_line_price -%}
                      {{- line_item.original_line_price | money -}}
                    {%- else -%}
                      {{- line_item.variant.compare_at_price | times: line_item.quantity | money -}}
                    {%- endif -%}
                  </span>
                {%- endif -%}
              {%- endcapture -%}

              <div class="line-item__info">
                <div class="product-item-meta">
                  {%- if settings.show_vendor -%}
                    {%- assign vendor_handle = line_item.vendor | handle -%}
                    {%- assign collection_for_vendor = collections[vendor_handle] -%}

                    {%- unless collection_for_vendor.empty? -%}
                      <a class="product-item-meta__vendor heading heading--xxsmall" href="{{ collection_for_vendor.url }}">{{ line_item.vendor }}</a>
                      {%- else -%}
                      <a class="product-item-meta__vendor heading heading--xxsmall" href="{{ line_item.vendor | url_for_vendor }}">{{ line_item.vendor }}</a>
                    {%- endunless -%}
                  {%- endif -%}

                  <a href="{{ line_item.url }}" class="product-item-meta__title heading heading--small">{{ line_item.product.title }}</a>

                  {%- capture line_item_properties -%}
                    {%- unless line_item.product.has_only_default_variant -%}
                      <span class="product-item-meta__property text--subdued text--xxsmall">{{ line_item.variant.title }}</span>
                    {%- endunless -%}

                    {%- comment -%}
                    <!-- Removing this, styling as tag. -->
                    
                    {%- if line_item.selling_plan_allocation -%}
                      <span class="product-item-meta__property text--subdued text--xsmall">{{ line_item.selling_plan_allocation.selling_plan.name }}</span>
                    {%- endif -%}

                    {%- endcomment -%}

                    {%- unless line_item.properties == blank -%}
                      <ul class="product-item-meta__property list--unstyled text--subdued text--xxsmall" role="list">
                        {%- for property in line_item.properties -%}
                          {%- assign first_character_in_key = property.first | truncate: 1, '' -%}

                          {%- if property.last == blank or first_character_in_key == '_' -%}
                            {%- continue -%}
                          {%- endif -%}

                          <li>{{ property.first }}: {{ property.last }}</li>
                        {%- endfor -%}
                      </ul>
                    {%- endunless -%}
                  {%- endcapture -%}

                  {%- if line_item_properties != blank -%}
                    <div class="product-item-meta__property-list">
                      {{- line_item_properties -}}
                    </div>
                  {%- endif -%}

                  
                  {%- capture product_item_tags -%}

                    {%- if line_item.product.tags contains settings.prescription_food_tag -%}
                      {%- render 'product-item-tag--prescription' -%}
                    {%- endif -%}
                    
                    {%- if line_item.product.tags contains settings.frozen_food_tag -%}
                      {%- render 'product-item-tag--frozen' -%}
                    {%- endif -%}

                    {%- if line_item.selling_plan_allocation -%}
                      {%- render 'product-item-tag--subscription', title: line_item.selling_plan_allocation.selling_plan.name -%}
                    {%- endif -%}

                  {%- endcapture -%}

                  {%- if product_item_tags != blank -%}
                    <div class="product-item-tags">
                      {{ product_item_tags }}
                    </div>
                  {%- endif -%}

                  <div class="product-item-meta__price-and-remove">
                    
                    <div class="product-item-meta__price-list-container text--small">
                      <div class="price-list">
                        {{- line_price -}}
                        {{- unit_price -}}
                      </div>
  
                      {%- if unit_price != blank -%}
                        <div class="price-list hidden-phone">
                          {{- unit_price -}}
                        </div>
                      {%- endif -%}
                    </div>

                  </div>
                  
                </div>

                {%- assign max_allowed_quantity = '' -%}
                {%- assign allow_more = true -%}

                {%- if line_item.variant.inventory_management == 'shopify' and line_item.variant.inventory_policy == 'deny' and line_item.variant.inventory_quantity <= line_item.quantity -%}
                  {%- assign max_allowed_quantity = line_item.variant.inventory_quantity -%}
                  {%- assign allow_more = false -%}
                {%- endif -%}

                <line-item-quantity class="line-item__quantity">
                  <div class="quantity-selector quantity-selector--small">
                    <a href="{{ routes.cart_change_url }}?quantity={{ line_item.quantity | minus: 1 }}&line={{ forloop.index }}" class="quantity-selector__button" aria-label="{{ 'cart.general.decrease_quantity' | t | escape }}" data-no-instant>
                      {%- render 'icon' with 'minus' -%}
                    </a>

                    <input is="input-number" class="quantity-selector__input text--xsmall" autocomplete="off" type="text" inputmode="numeric" name="updates[]" data-line="{{ forloop.index }}" value="{{ line_item.quantity }}" {% if max_allowed_quantity != '' %}max="{{ max_allowed_quantity }}"{% endif %} size="{{ line_item.quantity | append: '' | size | at_least: 2 }}" aria-label="{{ 'cart.general.change_quantity' | t | escape }}">

                    {%- if allow_more -%}
                      <a href="{{ routes.cart_change_url }}?quantity={{ line_item.quantity | plus: 1 }}&line={{ forloop.index }}" class="quantity-selector__button" aria-label="{{ 'cart.general.increase_quantity' | t | escape }}" data-no-instant>
                        {%- render 'icon' with 'plus' -%}
                      </a>
                    {%- else -%}
                      <span class="quantity-selector__button" aria-label="{{ 'cart.general.no_more_stock' | t | escape }}" data-tooltip="{{ 'cart.general.no_more_stock' | t | escape }}">
                        {%- render 'icon' with 'plus' -%}
                      </span>
                    {%- endif -%}
                  </div>

                  <a href="{{ line_item.url_to_remove }}" class="line-item__remove-button link text--subdued text--xxsmall" data-no-instant>{{ 'cart.general.remove' | t }}</a>

                </line-item-quantity>

                {%- if line_item.line_level_discount_allocations != blank -%}
                  <ul class="line-item__discount-list list--unstyled" role="list">
                    {%- for discount_allocation in line_item.line_level_discount_allocations -%}
                      <li class="line-item__discount-badge discount-badge">
                        {%- render 'icon' with 'discount-badge' -%}{{ discount_allocation.discount_application.title }} (-{{ discount_allocation.amount | money }})
                      </li>
                    {%- endfor -%}
                  </ul>
                {%- endif -%}

              </div>

              {%- comment -%}

              <!-- Removing this to improve cart layout, moving to above quantity selector. -->
              
              <div class="line-item__price-list-container text--small hidden-phone">
                {%- if settings.show_vendor -%}
                  {%- comment -%}
                    IMPLEMENTATION NOTE: in the design, the price is aligned in regards of the product title (not the brand). It was a
                    bit hard to do as we cannot set a fixed margin, so I am actually adding an empty vendor to simulate the same height
                  {%- endcomment -%}
                  <span class="product-item-meta__vendor heading heading--xxsmall" style="visibility: hidden">x</span>
                {%- endif -%}

                <div class="price-list price-list--stack">
                  {{- line_price -}}
                </div>
              </div>

              {%- endcomment -%}

            </div>
          </line-item>
        {%- endfor -%}
      </form>

      {%- if section.settings.show_recommendations == true and section.settings.upsell_collection != blank and section.settings.upsell_collection.products.size > 0 -%}

        <cart-drawer-recommendations section-id="{{ section.id }}" class="mini-cart__recommendations" static>

          <div class="mini-cart__recommendations-inner">
            {%- if section.settings.recommendations_title != blank -%}
              <p class="mini-cart__recommendations-heading heading heading--small hidden-pocket">{{ section.settings.recommendations_title | escape }}</p>
              <p class="mini-cart__recommendations-heading heading heading--xsmall text--subdued hidden-lap-and-up">{{ section.settings.recommendations_title | escape }}</p>
            {%- endif -%}

            <div class="scroller">
              <div class="scroller__inner">
                <div class="mini-cart__recommendations-list">

                  {%- for product in section.settings.upsell_collection.products -%}
                    {%- render 'product-item', product: product, reduced_content: true, reduced_font_size: true, hide_secondary_image: true, sizes_attribute: '(max-width: 740px) 65px, 92px' -%}
                  {%- endfor -%}

                </div>
              </div>
            </div>

          </div>

        </cart-drawer-recommendations>
        
      {%- elsif section.settings.show_recommendations and section.settings.upsell_collection == blank -%}

        <cart-drawer-recommendations section-id="{{ section.id }}" product-id="{{ cart.items.first.product_id }}" class="mini-cart__recommendations">
          {%- assign acceptable_recommendations_count = 0 -%}

          {%- for product in recommendations.products -%}
            {%- assign matching_product = cart.items | where: 'product_id', product.id | first -%}

            {%- if matching_product == blank -%}
              {%- assign acceptable_recommendations_count = acceptable_recommendations_count | plus: 1 -%}
            {%- endif -%}
          {%- endfor -%}

          {%- if recommendations.performed -%}
            {%- if acceptable_recommendations_count > 0 -%}
              <div class="mini-cart__recommendations-inner">
                {%- if section.settings.recommendations_title != blank -%}
                  <p class="mini-cart__recommendations-heading heading heading--small hidden-pocket">{{ section.settings.recommendations_title | escape }}</p>
                  <p class="mini-cart__recommendations-heading heading heading--xsmall text--subdued hidden-lap-and-up">{{ section.settings.recommendations_title | escape }}</p>
                {%- endif -%}

                <div class="scroller">
                  <div class="scroller__inner">
                    <div class="mini-cart__recommendations-list">
                      {%- assign shown_products_count = 0 -%}

                      {%- for product in recommendations.products -%}
                        {%- if shown_products_count >= 6 -%}
                          {%- break -%}
                        {%- endif -%}

                        {%- assign matching_product = cart.items | where: 'product_id', product.id -%}

                        {%- if matching_product.size == 0 -%}
                          {%- assign shown_products_count = shown_products_count | plus: 1 -%}
                          {%- render 'product-item', product: product, reduced_content: true, reduced_font_size: true, hide_secondary_image: true, sizes_attribute: '(max-width: 740px) 65px, 92px' -%}
                        {%- endif -%}
                      {%- endfor -%}
                    </div>
                  </div>
                </div>
              </div>
            {%- endif -%}
          {%- else -%}
            <div class="mini-cart__recommendations-inner">
              <div class="spinner">
                {%- render 'icon' with 'spinner', stroke_width: 3, width: 40, height: 40 -%}
              </div>
            </div>
          {%- endif -%}

        </cart-drawer-recommendations>
      
      {% else %}
      {% endif %}
      
    </div>

    {% if section.settings.cart_footer_enable == true %}
      <div class="mini-cart__drawer-prefooter">
        
        {% if section.settings.cart_footer_text != blank %}
          <div class="mini-cart__drawer-prefooter-message text--large">
            {{ section.settings.cart_footer_text }}
          </div>
        {% endif %}
        
        {% if section.settings.cart_footer_continue_shopping_enable == true %}
          <div class="mini-cart__drawer-prefooter-link">
            <a href="{{ section.settings.cart_continue_shopping_link }}" class="button button--link">
              <span class="button__text">{{ section.settings.cart_footer_link_text }}</span>
              <span class="button__icon">{%- render 'icon' with 'nav-arrow-right' -%}</span>
            </a>
          </div>
        {% endif %}

      </div>
    {% endif %}

    <footer class="mini-cart__drawer-footer drawer__footer drawer__footer--tight drawer__footer--bordered">
      {%- capture shipping_tax_note -%}{{ 'cart.general.shipping_tax_note' | t }}{%- endcapture -%}

      {%- if cart.cart_level_discount_applications != blank -%}
        <ul class="mini-cart__discount-list list--unstyled" role="list">
          {%- for discount_application in cart.cart_level_discount_applications -%}
            <li class="mini-cart__discount">
              <span class="mini-cart__discount-badge discount-badge">{%- render 'icon' with 'discount-badge' -%}{{ discount_application.title }}</span>
              <span class="mini-cart__discount-price text--xsmall text--subdued">-{{ discount_application.total_allocated_amount | money }}</span>
            </li>
          {%- endfor -%}
        </ul>
      {%- endif -%}

      {%- if section.settings.show_order_note or shipping_tax_note != '' -%}
        <div class="mini-cart__actions text--subdued text--xsmall">
          {%- if section.settings.show_order_note -%}
            <button type="button" is="toggle-button" id="order-note-toggle" class="link" data-action="toggle-order-note" aria-controls="mini-cart-note" aria-expanded="false">
              {%- if cart.note == blank -%}
                {{- 'cart.general.add_order_note' | t -}}
              {%- else -%}
                {{- 'cart.general.edit_order_note' | t -}}
              {%- endif -%}
            </button>
          {%- endif -%}

          {%- if shipping_tax_note != '' -%}
            <span>{{ shipping_tax_note }}</span>
          {%- endif -%}
        </div>
      {%- endif -%}

      {%- if section.settings.cart_subscriptions_enable and section.settings.cart_subscriptions_product -%}

        {%- assign selling_plan_group = all_products[section.settings.cart_subscriptions_product].selling_plan_groups[0] -%}
        {%- assign selling_plans = selling_plan_group.selling_plans -%}

        <!-- 
          {{ selling_plans | json }}
        -->

        <div class="mini-cart__footer-subscriptions">

          <cart-subscriptions-footer class="cart-subscriptions text-center">

            <button type="button" is="toggle-button" class="button button--full" aria-controls="cart-subscriptions-content" aria-expanded="false">
              {{ section.settings.cart_subscriptions_title }}
            </button>

            <collapsible-content id="cart-subscriptions-content" class="collapsible">

              <div class="cart-subscriptions__form">

                {%- assign box_data_array = box_data_string | split: "," -%}
                {%- assign number_of_boxes = box_data_array.size -%}

                {%- for box in box_data_array -%}
                {%- endfor -%}

                {%- if section.settings.cart_subscriptions_message -%}
                  <div class="cart-subscriptions__message text--xsmall">
                    {{ section.settings.cart_subscriptions_message }}
                  </div>
                {%- endif -%}

                {%- capture cart_subscriptions_notice -%}

                  {%- if number_of_boxes == 1 -%}

                    {%- assign box_dog = box_data_array[0] -%}
                    {%- assign box_dog_array = box_dog | split: ":" -%}
                    {%- assign box_dog_name = box_dog_array | first | split: "|" | first -%}
                    {%- assign box_dog_sex = box_dog_array | first | split: "|" | last -%}
                    {%- assign box_dog_weight = box_dog_array | last | plus: 0 -%}

                    {%- if box_dog_weight < 10 -%}

                      {% comment %} ONE DOG WEIGHING LESS THAN 10LBS {% endcomment %}
                      
                      {%- assign single_small_dog = true -%}

                      <p><strong data-dog-name>{{ box_dog_name }}</strong> weighs less than 10lbs, So {% if box_dog_sex == "Female" %}her{% else %}his{% endif %} subscription needs to be <strong>at least 4 weeks</strong>.
                      <button type="button" class="link" is="toggle-button" aria-controls="cart-subscriptions-notice" aria-expanded="false">Why is this?</button></p>

                    {%- endif -%}

                  {%- endif -%}

                {%- endcapture -%}

                {%- if cart_subscriptions_notice != blank -%}
                  <div class="cart-subscriptions__notice text--xsmall">
                    {{ cart_subscriptions_notice }}
                  </div>
                {%- endif -%}

                <form id="cart-subscriptions-form" class="" action="">

                  <div class="subscriptions-input text--xsmall">

                    {% if section.settings.cart_subscriptions_select_label %}
                      <label for="cart-subscriptions-interval">{{ section.settings.cart_subscriptions_select_label }}</label>
                    {% endif %}

                    {% comment %} Subscription Values {% endcomment %}

                    {%- capture subscription_values -%}

                      {%- for selling_plan in selling_plans -%}

                        {%- assign option = selling_plan.options[0] -%}
                        {%- assign option_number = option.value | first | times: 1 -%}
                        {%- assign option_disabled = false -%}

                        {%- if option_number == 0 -%}
                          {%- assign option_number = 1 -%}
                        {%- endif -%}

                        {% if option.value == "2 weeks" and single_small_dog == true %}
                          {%- assign option_disabled = true -%}
                        {% endif %}

                        {%- if option_disabled -%}
                          {%- continue -%}
                        {%- endif -%}

                        <option value="{{ selling_plan.id }}" 
                          {% if option_disabled %}disabled{% endif %}
                          data-selling-plan-name="{{ selling_plan.name }}" 
                          data-selling-plan-description="{{ selling_plan.description }}" 
                          data-selling-plan-options='{{ selling_plan.options | json }}' 
                          data-interval-multiplier="{{ option_number }}" 
                          {% if selected_set == false and option_disabled == false %}
                            {%- assign selected_set = true -%}
                            selected
                          {% endif %}
                        >{{ option.value }}</option>

                      {%- endfor -%}

                    {%- endcapture -%}

                    <select id="cart-subscriptions-interval" class="cart-subscriptions__interval cart-subscriptions-select" name="cart-subscriptions-interval" title="{{ block.settings.title | escape }}" required>
                      {{ subscription_values }}
                    </select>


                    {% comment %} Button {% endcomment %}

                    <button type="button" class="subscribe-button button button--primary button--tiny" data-button-subscribe>
                      Subscribe
                    </button>
                    
                  </div>

                  <div class="cart-subscriptions-form__actions">
                    
                    <button type="button" class="text--xsmall link color--danger" data-button-unsubscribe>
                      Unsubscribe
                    </button>

                  </div>

                </form>

              </div>
              
            </collapsible-content>

          </cart-subscriptions-footer>

        </div>
          
      {%- endif -%}

      {%- if section.settings.show_checkout_button -%}

        {%- assign checkout_disabled = false -%}
        {% if frozen_food_enabled and calculated_total_price < frozen_food_minimum %}
          {%- assign checkout_disabled = true -%}
        {%- endif -%}

        {% if prescription_product_in_cart == true and customer_vet == blank %}
          {%- assign checkout_disabled = true -%}
        {% endif %}

        <button form="mini-cart-form" type="submit" class="checkout-button button button--primary button--full" name="checkout" {% if checkout_disabled == true %}disabled{% endif %}>
          {% comment %} <span class="checkout-button__lock">{%- render 'icon' with 'lock' -%}</span> {% endcomment %}
          <span>{{- 'cart.general.checkout' | t -}}</span>
          <span>{{- cart.total_price | money_with_currency -}}</span>
        </button>

        {% if prescription_product_in_cart == true %}
          {% render 'cart-vet-partner', section: section %}
        {% endif %}

      {%- else -%}

        <a href="{{ routes.cart_url }}" class="button--primary button button--full" data-no-instant>{{ 'cart.general.go_to_cart' | t }}</a>

      {%- endif -%}
      
    </footer>
  {%- endif -%}

  {%- if section.settings.show_order_note -%}
    <openable-element id="mini-cart-note" class="mini-cart__order-note">
      <span class="openable__overlay"></span>
      <label for="cart[note]" class="mini-cart__order-note-title heading heading--xsmall">{{- 'cart.general.add_order_note' | t -}}</label>
      <textarea is="cart-note" name="note" id="cart[note]" rows="3" aria-owns="order-note-toggle" class="input__field input__field--textarea" placeholder="{{ 'cart.general.order_note_placeholder' | t }}">{{ cart.note }}</textarea>
      <button type="button" data-action="close" class="form__submit form__submit--closer button button--secondary">{{ 'cart.general.order_note_save' | t }}</button>
    </openable-element>
  {%- endif -%}

</cart-drawer>


{% comment %}
 ------------------------------------------------------------ 
 Modals 
 ------------------------------------------------------------
{% endcomment %}

{% comment %} Single Small Dog Modal {% endcomment %}
 
{%- capture cart_modal -%}
  {{ cart_modal }}
  {%- capture modal_content -%}
    <div class="text-center">
      <img style="max-width: 180px; display: inline; margin: 0;" sizes="(max-width: 740px) calc(100vw - 96px), (max-width: 999px) calc(100vw - 160px), 600px" loading="lazy" src="https://wynwooddogfood.com/cdn/shop/files/icon_260x.png?v=1670599341">
    </div>
    <div class="modal-content text--small">
      {{ 'cart.subscriptions.single_small_dog_notice_html' | t }}
    </div>
  {%- endcapture -%}
  {%- render 'modal', modal_id: 'cart-subscriptions-notice', modal_title: "Dogs Under 10lbs", modal_content: modal_content, show_modal_close: true -%}
{%- endcapture -%}

{%- if cart_modal -%}
  {{ cart_modal }}
{%- endif -%}

{% comment %} Prescription - "Not Listed" Vet Fields Modal {% endcomment %}

{% render 'modal--vet-not-listed', section: section %}

{% schema %}
{
  "name": "Cart drawer",
  "class": "shopify-section--mini-cart",
  "blocks": [
    {
      "type": "shipping_rate",
      "name": "Shipping Rate",
      "settings": [
        {
          "type": "paragraph",
          "content": "Note: These shipping rates are only for show and give the customer more information on expected shipping charges in the cart. The shipping rates that are actually applied are set up in the shipping rates in the Shopify admin."
        },
        {
          "type": "text",
          "id": "name",
          "label": "Name",
          "placeholder": "Local"
        },
        {
          "type": "text",
          "id": "tooltip",
          "label": "Tooltip",
          "info": "(Optional) Tooltip that shows when hovering info icon. Use to show state codes. (e.g. FL, NC, SC, etc.)"
        },
        {
          "type": "text",
          "id": "eta_days",
          "label": "ETA (Days)",
          "placeholder": "1"
        },
        {
          "type": "text",
          "id": "over_threshold_rate",
          "label": "Rate (Over Threshold)",
          "placeholder": "1"
        },
        {
          "type": "text",
          "id": "under_threshold_rate",
          "label": "Rate (Under Threshold)",
          "placeholder": "1"
        }
      ]
    }
  ],
  "settings": [
    {
      "type": "header",
      "content": "🩺 Vet \"Not Listed\" Fields popup"
    },
    {
      "type": "text",
      "id": "vet_notlisted_title",
      "label": "Title",
      "default": "Who's Your Vet?"
    },
    {
      "type": "richtext",
      "id": "vet_notlisted_content",
      "label": "Content",
      "default": "<p>Please give us some details about your vet for our records.</p>"
    },
    {
      "type": "header",
      "content": "🚚 Shipping Details"
    },
    { 
      "type": "checkbox",
      "id": "show_shipping_details",
      "label": "Enable Shipping Details",
      "default": true
    },
    {
      "type": "paragraph",
      "content": "Shipping Details Page"
    },
    { 
      "type": "url",
      "id": "shipping_details_page",
      "label": "Page"
    },
    { 
      "type": "text",
      "id": "shipping_details_page_text",
      "default": "Click Here for more Shipping Information",
      "label": "Link Text"
    },
    { 
      "type": "richtext",
      "id": "shipping_rates_bottom_text",
      "default": "<p>* Free shipping only available in some regions.</p>",
      "label": "Shipping Rates Bottom Text",
      "info": "Short text message that shows just under the shipping rates table."
    },
    {
      "type": "header",
      "content": "📦 Subscriptions"
    },
    { 
      "type": "checkbox",
      "id": "cart_subscriptions_enable",
      "label": "Enable Cart Subscriptions"
    },
    {
      "type": "text",
      "id": "cart_subscriptions_title",
      "label": "Title",
      "default": "Subscribe and save 10%!"
    },
    {
      "type": "richtext",
      "id": "cart_subscriptions_message",
      "label": "Subscriptions Message"
    },
    {
      "type": "product",
      "id": "cart_subscriptions_product",
      "label": "Cart Subscriptions Product",
      "info": "Select a product to pull in subscription plans from. IMPORTANT: We assume that the subscription plans are applied to all products in the cart."
    },
    {
      "type": "text",
      "id": "cart_subscriptions_select_label",
      "label": "Frequency Select Label",
      "default": "Deliver every"
    },
    {
      "type": "header",
      "content": "Cart Settings"
    },
    {
      "type": "paragraph",
      "content": "Free shipping notice can be configured in global cart settings."
    },
    {
      "type": "checkbox",
      "id": "show_order_note",
      "label": "Show order note",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "show_checkout_button",
      "label": "Show checkout button",
      "default": true
    },
    {
      "type": "url",
      "id": "empty_button_link",
      "label": "Empty button link",
      "default": "/collections/all"
    },
    {
      "type": "header",
      "content": "Cart Footer Message",
      "info": "Text shown below the cart contents but above the Checkout button"
    },
    {
      "type": "checkbox",
      "id": "cart_footer_enable",
      "label": "Show Cart Footer Message",
      "default": true
    },
    {
      "type": "richtext",
      "id": "cart_footer_text",
      "label": "Text"
    },
    {
      "type": "text",
      "id": "cart_footer_link_text",
      "label": "Link Text",
      "default": "Continue Shopping"
    },
    {
      "type": "url",
      "id": "cart_empty_button_link",
      "label": "Empty Cart Link"
    },
    {
      "type": "checkbox",
      "id": "cart_footer_continue_shopping_enable",
      "label": "Show Continue Shopping link",
      "default": true
    },
    {
      "type": "url",
      "id": "cart_continue_shopping_link",
      "label": "Continue Shopping Link"
    },
    {
      "type": "header",
      "content": "Cross-sell",
      "info": "Dynamic recommendations are based on the items in your cart. They change and improve with time. [Learn more](https://help.shopify.com/en/themes/development/recommended-products)"
    },
    {
      "type": "checkbox",
      "id": "show_recommendations",
      "label": "Show cart recommendations",
      "default": true
    },
    {
      "type": "text",
      "id": "recommendations_title",
      "label": "Recommendations heading",
      "default": "You may also like"
    },
    {
      "type": "collection",
      "id": "upsell_collection",
      "label": "Upsell Collection",
      "info": "Show a collection instead for a simple upsell. (Overrides dynamic recommendations)"
    }
  ]
}
{% endschema %}