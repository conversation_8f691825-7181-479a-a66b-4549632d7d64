<style>
  #shopify-section-{{ section.id }} .password {
    --heading-color: {{ section.settings.text_color.red }}, {{ section.settings.text_color.green }}, {{ section.settings.text_color.blue }};
    --text-color: {{ section.settings.text_color.red }}, {{ section.settings.text_color.green }}, {{ section.settings.text_color.blue }};
    --primary-button-background: {{ section.settings.button_background.red }}, {{ section.settings.button_background.green }}, {{ section.settings.button_background.blue }};
    --primary-button-text-color: {{ section.settings.button_text_color.red }}, {{ section.settings.button_text_color.green }}, {{ section.settings.button_text_color.blue }};

    --section-background: {{ section.settings.background.red }}, {{ section.settings.background.green }}, {{ section.settings.background.blue }};
  }

  #shopify-section-{{ section.id }} .password__logo-image {
    max-width: {{ section.settings.mobile_logo_max_width }}px;
  }

  @media screen and (min-width: 741px) {
    #shopify-section-{{ section.id }} .password__logo-image {
      max-width: {{ section.settings.logo_max_width }}px;
    }
  }
</style>

<section>
  <div class="password">
    <div class="password__main vertical-breather vertical-breather--tight">
      <h1 class="password__logo">
        {%- if section.settings.logo != blank -%}
          {%- assign logo_size = section.settings.logo_max_width | times: 2 -%}
          <span class="visually-hidden">{{ shop.name }}</span>
          <img class="password__logo-image" width="{{ section.settings.logo.width }}" height="{{ section.settings.logo.height }}" src="{{ section.settings.logo | image_url: width: logo_size }}" alt="{{ section.settings.logo.alt | escape }}">
        {%- else -%}
          <span class="heading h5">{{ shop.name }}</span>
        {%- endif -%}
      </h1>

      <div class="password__content">
        {%- if section.settings.title != blank -%}
          <h2 class="heading h3">{{ section.settings.title | escape }}</h2>
        {%- endif -%}

        {%- if shop.password_message != blank -%}
          {{- shop.password_message -}}
        {%- endif -%}

        {%- if section.settings.show_newsletter -%}
          <div class="password__newsletter">
            {%- form 'customer', class: 'form password__newsletter-form' -%}
              {%- if form.posted_successfully? -%}
                <div class="form__banner banner banner--success">
                  <span class="banner__ribbon">{% render 'icon' with 'form-success' %}</span>
                  <p class="banner__content">{{ 'password.newsletter.success' | t }}</p>
                </div>
              {%- else -%}
                {%- if form.errors -%}
                  <div class="form__banner banner banner--error">
                    <span class="banner__ribbon">{% render 'icon' with 'form-error' %}</span>
                    <p class="banner__content">{{ form.errors.translated_fields['email'] }} {{ form.errors.messages['email'] }}</p>
                  </div>
                {%- endif -%}

                <input type="hidden" name="contact[tags]" value="newsletter">

                <div class="input">
                  <input type="email" id="password[contact][email]" name="contact[email]" class="input__field input__field--transparent input__field--text" required>
                  <label for="password[contact][email]" class="input__label">{{ 'password.newsletter.email' | t }}</label>
                </div>

                <button type="submit" class="form__submit form__submit--closer button button--primary button--full">{{ 'password.newsletter.submit' | t }}</button>
              {%- endif -%}
            {%- endform -%}
          </div>
        {%- endif -%}

        <span class="password__storefront-login">
          {%- render 'icon' with 'lock', width: 18, height: 18, inline: true -%}

          <button type="button" is="toggle-button" class="link hidden-tablet-and-up" aria-controls="password-popover" aria-expanded="false">{{- 'password.general.enter_password' | t -}}</button><!--
          --><button type="button" is="toggle-button" class="link hidden-phone" aria-controls="password-drawer" aria-expanded="false">{{- 'password.general.enter_password' | t -}}</button>
        </span>
      </div>

      <div class="password__copyright">
        <span class="password__powered-by text--subdued text--xsmall">
          {{- 'password.general.powered_by' | t -}}
          <a class="password__shopify-logo" href="//www.shopify.com" target="_blank" rel="noreferrer" aria-label="Shopify">
            {%- render 'icon' with 'shopify-logo' -%}
          </a>
        </span>

        <span class="password__admin-link text--xsmall">
          <span class="text--subdued">{{ 'password.general.store_owner' | t }}</span>
          <a href="/admin" class="link">{{ 'password.general.login' | t }}</a>
        </span>
      </div>
    </div>

    {%- if section.settings.image != blank -%}
      {{- section.settings.image | image_url: width: section.settings.image.width | image_tag: loading: 'lazy', sizes: '50vw', widths: '400,500,600,700,800,900,1000,1100,1200,1300,1400', class: 'password__image' -}}
    {%- endif -%}
  </div>

  {%- comment -%}Storefront password drawer (desktop) and popover (mobile){%- endcomment -%}
  <drawer-content initial-focus-selector="[type='password']" id="password-drawer" class="drawer drawer--large">
    <span class="drawer__overlay"></span>

    <button type="button" class="drawer__close-button tap-area" data-action="close" title="{{ 'general.accessibility.close' | t | escape }}">
      {%- render 'icon' with 'close' -%}
    </button>

    <div class="drawer__content drawer__content--center">
      {%- form 'storefront_password', id: 'storefront-password-drawer', name: 'password', class: 'password__storefront-form form' -%}
        {%- if form.errors -%}
          <div class="form__banner banner banner--error">
            <span class="banner__ribbon">{% render 'icon' with 'form-error' %}</span>
            <p class="banner__content">{{ form.errors.messages['form'] }}</p>
          </div>

          <script>
            window.addEventListener('DOMContentLoaded', () => {
              if (window.matchMedia('(min-width: 741px)').matches) {
                document.getElementById('password-drawer').open = true;
              }
            });
          </script>
        {%- else -%}
          <p class="form__info text--center">{{ 'password.password.label' | t }}</p>
        {%- endif -%}

        <div class="input">
          <input type="password" id="password[drawer][password]" name="password" class="input__field input__field--text" required="required">
          <label for="password[drawer][password]" class="input__label">{{ 'password.password.input' | t }}</label>
        </div>

        <button type="submit" class="form__submit form__submit--closer button button--primary button--full">{{ 'password.password.submit' | t }}</button>
      {%- endform -%}
    </div>
  </drawer-content>

  <popover-content id="password-popover" class="popover hidden-tablet-and-up">
    <span class="popover__overlay"></span>

    <button type="button" class="popover__close-button tap-area tap-area--large" data-action="close" title="{{ 'general.accessibility.close' | t | escape }}">
      {%- render 'icon' with 'close' -%}
    </button>

    {%- form 'storefront_password', id: 'storefront-password-popover', name: 'password', class: 'password__storefront-form form' -%}
      {%- if form.errors -%}
        <div class="form__banner banner banner--error">
          <span class="banner__ribbon">{% render 'icon' with 'form-error' %}</span>
          <p class="banner__content">{{ form.errors.messages['form'] }}</p>
        </div>

        <script>
          window.addEventListener('DOMContentLoaded', () => {
            if (window.matchMedia('(max-width: 740px)').matches) {
              document.getElementById('password-popover').open = true;
            }
          });
        </script>
      {%- endif -%}

      <p class="form__info text--center">{{ 'password.password.label' | t }}</p>

      <div class="input">
        <input type="password" id="password[picker][password]" name="password" class="input__field input__field--text" required="required">
        <label for="password[picker][password]" class="input__label">{{ 'password.password.input' | t }}</label>
      </div>

      <button type="submit" class="form__submit form__submit--closer button button--primary button--full">{{ 'password.password.submit' | t }}</button>
    {%- endform -%}
  </popover-content>
</section>

{% schema %}
{
  "name": "Password",
  "class": "shopify-section--main-password",
  "settings": [
    {
      "type": "header",
      "content": "Logo"
    },
    {
      "type": "image_picker",
      "id": "logo",
      "label": "Image",
      "info": "280 x 80px .png recommended"
    },
    {
      "type": "range",
      "id": "logo_max_width",
      "min": 50,
      "max": 300,
      "step": 5,
      "unit": "px",
      "label": "Image width",
      "default": 140
    },
    {
      "type": "range",
      "id": "mobile_logo_max_width",
      "min": 50,
      "max": 170,
      "step": 5,
      "unit": "px",
      "label": "Mobile image width",
      "default": 100
    },
    {
      "type": "header",
      "content": "Content",
      "info": "[Configure more content](/admin/online_store/preferences)"
    },
    {
      "type": "image_picker",
      "id": "image",
      "label": "Image",
      "info": "1400 x 1800px .jpg recommended"
    },
    {
      "type": "text",
      "id": "title",
      "label": "Heading",
      "default": "Opening soon"
    },
    {
      "type": "checkbox",
      "id": "show_newsletter",
      "label": "Show newsletter form",
      "info": "Customers who subscribe will have their email address added to the \"accepts marketing\" [customer list](/admin/customers?query=&accepts_marketing=1).",
      "default": true
    },
    {
      "type": "header",
      "content": "Colors"
    },
    {
      "type": "color",
      "id": "background",
      "label": "Background",
      "default": "#282828"
    },
    {
      "type": "color",
      "id": "text_color",
      "label": "Text",
      "default": "#ffffff"
    },
    {
      "type": "color",
      "id": "button_background",
      "label": "Button background",
      "default": "#f3ff34"
    },
    {
      "type": "color",
      "id": "button_text_color",
      "label": "Button text",
      "default": "#282828"
    }
  ]
}
{% endschema %}