  <quiz-step-dog-number class="quiz-step__inner">

    <div class="quiz-step__body">

      {% if section.settings.icon != blank %}
        <img class="quiz-step-icon" sizes="(max-width: 740px) calc(100vw - 96px), (max-width: 999px) calc(100vw - 160px), 600px" loading="lazy" {% render 'image-attributes', image: section.settings.icon, sizes: '600,700,800,1000,1200,1400' %}>
      {% endif %}

      {% if section.settings.title != blank %}
        <h1 class="h1 quiz-step-title">{{ section.settings.title }}</h1>
      {% endif %}
  
      {% if section.settings.description != blank %}
        <div class="quiz-step-description text--large">{{ section.settings.description }}</div>
      {% endif %}
  
      <div class="quiz-step-form">

        <div id="step-form">

          <quiz-step-line class="quiz-step-line">
            
            <span class="quiz-step-line__text">I have</span>
            
            <expanding-input>
              <span class="expanding-input__display quiz-step-line__input quiz-input" role="textbox" contenteditable data-type="number" data-default="0"></span>
              <input type="number" class="expanding-input__input" name="dogs-number" id="dog-number" max="10" min="1">
            </expanding-input>

            <span class="quiz-step-line__text">dog(s)</span>

          </quiz-step-line>

          <quiz-step-line class="quiz-step-line quiz-step-line--hidden" id="dog-names">

            <span class="quiz-step-line__text">named</span>
            
          </quiz-step-line>

        </div>

      </div>

    </div>

    <div class="quiz-step__footer">

      <div class="container">

        <div class="quiz-step-hint">{{ 'quiz.steps.general.hint' | t }}</div>

        <div class="quiz-step-actions">

          <button type="button" is="loader-button" class="button button--highlight" form="step-form" data-quiz-button-next data-quiz-button-populate-dogs disabled="disabled">
            <span class="button__text">{{ 'quiz.general.continue' | t }}</span>
            <span class="button__icon">{%- render 'icon' with 'nav-arrow-right' -%}</span>
          </button>

        </div>
      
      </div>

    </div>

  </quiz-step-dog-number>



{% schema %}
{
  "name": "Number of Dogs",
  "class": "shopify-section--dogs-number",
  "settings": [
    {
      "type": "header",
      "content": "Home"
    },
    {
      "type": "image_picker",
      "id": "icon",
      "label": "Icon"
    },
    {
      "type": "text",
      "id": "title",
      "label": "Title"
    },
    {
      "type": "richtext",
      "id": "description",
      "label": "Description"
    },
    {
      "type": "richtext",
      "id": "hint",
      "label": "Hint",
      "default": "<p>We’ll recommend recipes for each of your pups!</p>"
    }
  ]
}
{% endschema %}