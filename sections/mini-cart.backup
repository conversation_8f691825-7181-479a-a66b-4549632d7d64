<cart-drawer section="{{ section.id }}" id="mini-cart" class="mini-cart drawer drawer--large">
  <span class="drawer__overlay"></span>

  <header class="drawer__header">
    <p class="drawer__title heading h4">
      {%- case settings.cart_icon -%}
        {%- when 'shopping_bag' -%}
          {%- render 'icon' with 'header-cart' -%}

        {%- when 'shopping_cart' -%}
          {%- render 'icon' with 'header-shopping-cart' -%}

        {%- when 'tote_bag' -%}
          {%- render 'icon' with 'header-tote-bag' -%}
      {%- endcase -%}

      {%- if cart.item_count == 0 -%}
        {{- 'cart.general.title' | t -}}
      {%- else -%}
        {{- 'cart.general.item_count' | t: count: cart.item_count -}}
      {%- endif -%}
    </p>

    <button type="button" class="drawer__close-button tap-area" data-action="close" title="{{ 'general.accessibility.close' | t | escape }}">
      {%- render 'icon' with 'close' -%}
    </button>
  </header>

  {%- if cart.item_count == 0 -%}
    <div class="drawer__content drawer__content--center">
      <p>{{ 'cart.general.empty' | t }}</p>

      <div class="button-wrapper">
        <a href="{{ section.settings.empty_button_link }}" class="button button--primary">{{ 'cart.general.start_shopping' | t }}</a>
      </div>
    </div>
  {%- else -%}
    <div class="drawer__content">
      {%- if settings.cart_show_free_shipping_threshold and settings.cart_free_shipping_threshold != '' -%}
        {%- assign free_shipping_thresholds = settings.cart_free_shipping_threshold | remove: ' ' | split: ',' -%}
        {%- assign has_found_matching_threshold = false -%}

        {%- if free_shipping_thresholds.size > 1 -%}
          {%- for threshold in free_shipping_thresholds -%}
            {%- assign threshold_parts = threshold | split: ':' -%}
            {%- assign currency_code = threshold_parts | first | upcase -%}

            {%- if currency_code == cart.currency.iso_code -%}
              {%- assign free_shipping_calculated_threshold = threshold_parts | last -%}
              {%- assign has_found_matching_threshold = true -%}
              {%- break -%}
            {%- endif -%}
          {%- endfor -%}
        {%- else -%}
          {%- assign free_shipping_calculated_threshold = free_shipping_thresholds | last -%}
          {%- assign has_found_matching_threshold = true -%}
        {%- endif -%}

        {%- if has_found_matching_threshold -%}
          {%- assign threshold_in_cents = free_shipping_calculated_threshold | times: 100 -%}

          {%- assign calculated_total_price = cart.total_price -%}

          {%- for line_item in cart.items -%}
            {%- unless line_item.requires_shipping -%}
              {%- assign calculated_total_price = calculated_total_price | minus: line_item.final_line_price -%}
            {%- endunless -%}
          {%- endfor -%}

          <free-shipping-bar threshold="{{ threshold_in_cents }}" class="shipping-bar" style="--progress: {{ cart.total_price | times: 1.0 | divided_by: threshold_in_cents | at_most: 1 }}">
            {%- if calculated_total_price >= threshold_in_cents -%}
              <span class="shipping-bar__text text--small">{{ 'cart.general.free_shipping' | t }}</span>
            {%- else -%}
              {%- capture remaining_amount -%}{{ calculated_total_price | minus: threshold_in_cents | abs | money }}{%- endcapture -%}
              <span class="shipping-bar__text text--small">{{ 'cart.general.free_shipping_remaining_html' | t: remaining_amount: remaining_amount }}</span>
            {%- endif -%}

            <span class="shipping-bar__progress"></span>
          </free-shipping-bar>
        {%- endif -%}
      {%- endif -%}

      <form id="mini-cart-form" action="{{ routes.cart_url }}" novalidate method="post">
        <input type="hidden" name="checkout">

        {%- for line_item in cart.items -%}
          <line-item class="line-item">
            <div class="line-item__content-wrapper">
              <a href="{{ line_item.url }}" class="line-item__image-wrapper" tabindex="-1" aria-hidden="true">
                <span class="line-item__loader" hidden>
                  <span class="line-item__loader-spinner spinner" hidden>{% render 'icon' with 'spinner', width: 16, height: 16, stroke_width: 6 %}</span>
                  <span class="line-item__loader-mark" hidden>{% render 'icon' with 'check', width: 20, height: 20 %}</span>
                </span>

                <img class="line-item__image" loading="sizes" sizes="(max-width: 740px) 80px, 92px" {% render 'image-attributes', image: line_item.image, sizes: '80,92,160,184,240,276' %}>
              </a>

              {%- capture unit_price -%}
                {%- if line_item.unit_price_measurement -%}
                  <div class="price text--subdued">
                    <div class="unit-price-measurement">
                      <span class="unit-price-measurement__price">{{ line_item.unit_price | money }}</span>
                      <span class="unit-price-measurement__separator">/</span>

                      {%- if line_item.unit_price_measurement.reference_value != 1 -%}
                        <span class="unit-price-measurement__reference-value">{{ line_item.unit_price_measurement.reference_value }}</span>
                      {%- endif -%}

                      <span class="unit-price-measurement__reference-unit">{{ line_item.unit_price_measurement.reference_unit }}</span>
                    </div>
                  </div>
                {%- endif -%}
              {%- endcapture -%}

              {%- capture line_price -%}
                {%- comment -%}
                IMPLEMENTATION NOTE: The designer wanted to show the "compare at price" on cart. In case an automatic discount is applied
                  to a line item though, the "real" discount takes precedence over the compare at price
                {%- endcomment -%}

                <span class="price {% if line_item.original_line_price > line_item.final_line_price or line_item.final_line_price == 0 or line_item.variant.compare_at_price > line_item.variant.price %}price--highlight{% endif %}">
                  <span class="visually-hidden">{{ 'product.general.sale_price' | t }}</span>

                  {%- if line_item.final_line_price == 0 -%}
                    {{- 'cart.general.free' | t -}}
                  {%- else -%}
                    {{- line_item.final_line_price | money -}}
                  {%- endif -%}
                </span>

                {%- if line_item.original_line_price > line_item.final_line_price or line_item.variant.compare_at_price > line_item.variant.price -%}
                  <span class="price price--compare">
                    <span class="visually-hidden">{{ 'product.general.regular_price' | t }}</span>

                    {%- if line_item.original_line_price > line_item.final_line_price -%}
                      {{- line_item.original_line_price | money -}}
                    {%- else -%}
                      {{- line_item.variant.compare_at_price | times: line_item.quantity | money -}}
                    {%- endif -%}
                  </span>
                {%- endif -%}
              {%- endcapture -%}

              <div class="line-item__info">
                <div class="product-item-meta">
                  {%- if settings.show_vendor -%}
                    {%- assign vendor_handle = line_item.vendor | handle -%}
                    {%- assign collection_for_vendor = collections[vendor_handle] -%}

                    {%- unless collection_for_vendor.empty? -%}
                      <a class="product-item-meta__vendor heading heading--xxsmall" href="{{ collection_for_vendor.url }}">{{ line_item.vendor }}</a>
                      {%- else -%}
                      <a class="product-item-meta__vendor heading heading--xxsmall" href="{{ line_item.vendor | url_for_vendor }}">{{ line_item.vendor }}</a>
                    {%- endunless -%}
                  {%- endif -%}

                  <a href="{{ line_item.url }}" class="product-item-meta__title text--small">{{ line_item.product.title }}</a>

                  {%- capture line_item_properties -%}
                    {%- unless line_item.product.has_only_default_variant -%}
                      <span class="product-item-meta__property text--subdued text--xsmall">{{ line_item.variant.title }}</span>
                    {%- endunless -%}

                    {%- if line_item.selling_plan_allocation -%}
                      <span class="product-item-meta__property text--subdued text--xsmall">{{ line_item.selling_plan_allocation.selling_plan.name }}</span>
                    {%- endif -%}

                    {%- unless line_item.properties == blank -%}
                      <ul class="product-item-meta__property list--unstyled text--subdued text--xsmall" role="list">
                        {%- for property in line_item.properties -%}
                          {%- assign first_character_in_key = property.first | truncate: 1, '' -%}

                          {%- if property.last == blank or first_character_in_key == '_' -%}
                            {%- continue -%}
                          {%- endif -%}

                          <li>{{ property.first }}: {{ property.last }}</li>
                        {%- endfor -%}
                      </ul>
                    {%- endunless -%}
                  {%- endcapture -%}

                  {%- if line_item_properties != blank -%}
                    <div class="product-item-meta__property-list">
                      {{- line_item_properties -}}
                    </div>
                  {%- endif -%}

                  <div class="product-item-meta__price-list-container text--small">
                    <div class="price-list hidden-tablet-and-up">
                      {{- line_price -}}
                      {{- unit_price -}}
                    </div>

                    {%- if unit_price != blank -%}
                      <div class="price-list hidden-phone">
                        {{- unit_price -}}
                      </div>
                    {%- endif -%}
                  </div>
                </div>

                {%- if line_item.line_level_discount_allocations != blank -%}
                  <ul class="line-item__discount-list list--unstyled" role="list">
                    {%- for discount_allocation in line_item.line_level_discount_allocations -%}
                      <li class="line-item__discount-badge discount-badge">
                        {%- render 'icon' with 'discount-badge' -%}{{ discount_allocation.discount_application.title }} (-{{ discount_allocation.amount | money }})
                      </li>
                    {%- endfor -%}
                  </ul>
                {%- endif -%}

                {%- assign max_allowed_quantity = '' -%}
                {%- assign allow_more = true -%}

                {%- if line_item.variant.inventory_management == 'shopify' and line_item.variant.inventory_policy == 'deny' and line_item.variant.inventory_quantity <= line_item.quantity -%}
                  {%- assign max_allowed_quantity = line_item.variant.inventory_quantity -%}
                  {%- assign allow_more = false -%}
                {%- endif -%}

                <line-item-quantity class="line-item__quantity">
                  <div class="quantity-selector quantity-selector--small">
                    <a href="{{ routes.cart_change_url }}?quantity={{ line_item.quantity | minus: 1 }}&line={{ forloop.index }}" class="quantity-selector__button" aria-label="{{ 'cart.general.decrease_quantity' | t | escape }}" data-no-instant>
                      {%- render 'icon' with 'minus' -%}
                    </a>

                    <input is="input-number" class="quantity-selector__input text--xsmall" autocomplete="off" type="text" inputmode="numeric" name="updates[]" data-line="{{ forloop.index }}" value="{{ line_item.quantity }}" {% if max_allowed_quantity != '' %}max="{{ max_allowed_quantity }}"{% endif %} size="{{ line_item.quantity | append: '' | size | at_least: 2 }}" aria-label="{{ 'cart.general.change_quantity' | t | escape }}">

                    {%- if allow_more -%}
                      <a href="{{ routes.cart_change_url }}?quantity={{ line_item.quantity | plus: 1 }}&line={{ forloop.index }}" class="quantity-selector__button" aria-label="{{ 'cart.general.increase_quantity' | t | escape }}" data-no-instant>
                        {%- render 'icon' with 'plus' -%}
                      </a>
                    {%- else -%}
                      <span class="quantity-selector__button" aria-label="{{ 'cart.general.no_more_stock' | t | escape }}" data-tooltip="{{ 'cart.general.no_more_stock' | t | escape }}">
                        {%- render 'icon' with 'plus' -%}
                      </span>
                    {%- endif -%}
                  </div>

                  <a href="{{ line_item.url_to_remove }}" class="line-item__remove-button link text--subdued text--xxsmall" data-no-instant>{{ 'cart.general.remove' | t }}</a>
                </line-item-quantity>
              </div>

              <div class="line-item__price-list-container text--small hidden-phone">
                {%- if settings.show_vendor -%}
                  {%- comment -%}
                    IMPLEMENTATION NOTE: in the design, the price is aligned in regards of the product title (not the brand). It was a
                    bit hard to do as we cannot set a fixed margin, so I am actually adding an empty vendor to simulate the same height
                  {%- endcomment -%}
                  <span class="product-item-meta__vendor heading heading--xxsmall" style="visibility: hidden">x</span>
                {%- endif -%}

                <div class="price-list price-list--stack">
                  {{- line_price -}}
                </div>
              </div>
            </div>
          </line-item>
        {%- endfor -%}
      </form>

      {%- if section.settings.show_recommendations -%}
        <cart-drawer-recommendations section-id="{{ section.id }}" product-id="{{ cart.items.first.product_id }}" class="mini-cart__recommendations">
          {%- assign acceptable_recommendations_count = 0 -%}

          {%- for product in recommendations.products -%}
            {%- assign matching_product = cart.items | where: 'product_id', product.id | first -%}

            {%- if matching_product == blank -%}
              {%- assign acceptable_recommendations_count = acceptable_recommendations_count | plus: 1 -%}
            {%- endif -%}
          {%- endfor -%}

          {%- if recommendations.performed -%}
            {%- if acceptable_recommendations_count > 0 -%}
              <div class="mini-cart__recommendations-inner">
                {%- if section.settings.recommendations_title != blank -%}
                  <p class="mini-cart__recommendations-heading heading heading--small hidden-pocket">{{ section.settings.recommendations_title | escape }}</p>
                  <p class="mini-cart__recommendations-heading heading heading--xsmall text--subdued hidden-lap-and-up">{{ section.settings.recommendations_title | escape }}</p>
                {%- endif -%}

                <div class="scroller">
                  <div class="scroller__inner">
                    <div class="mini-cart__recommendations-list">
                      {%- assign shown_products_count = 0 -%}

                      {%- for product in recommendations.products -%}
                        {%- if shown_products_count >= 6 -%}
                          {%- break -%}
                        {%- endif -%}

                        {%- assign matching_product = cart.items | where: 'product_id', product.id -%}

                        {%- if matching_product.size == 0 -%}
                          {%- assign shown_products_count = shown_products_count | plus: 1 -%}
                          {%- render 'product-item', product: product, reduced_content: true, reduced_font_size: true, hide_secondary_image: true, sizes_attribute: '(max-width: 740px) 65px, 92px' -%}
                        {%- endif -%}
                      {%- endfor -%}
                    </div>
                  </div>
                </div>
              </div>
            {%- endif -%}
          {%- else -%}
            <div class="mini-cart__recommendations-inner">
              <div class="spinner">
                {%- render 'icon' with 'spinner', stroke_width: 3, width: 40, height: 40 -%}
              </div>
            </div>
          {%- endif -%}
        </cart-drawer-recommendations>
      {%- endif -%}
    </div>

    <footer class="mini-cart__drawer-footer drawer__footer drawer__footer--tight drawer__footer--bordered">
      {%- capture shipping_tax_note -%}{{ 'cart.general.shipping_tax_note' | t }}{%- endcapture -%}

      {%- if cart.cart_level_discount_applications != blank -%}
        <ul class="mini-cart__discount-list list--unstyled" role="list">
          {%- for discount_application in cart.cart_level_discount_applications -%}
            <li class="mini-cart__discount">
              <span class="mini-cart__discount-badge discount-badge">{%- render 'icon' with 'discount-badge' -%}{{ discount_application.title }}</span>
              <span class="mini-cart__discount-price text--xsmall text--subdued">-{{ discount_application.total_allocated_amount | money }}</span>
            </li>
          {%- endfor -%}
        </ul>
      {%- endif -%}

      {%- if section.settings.show_order_note or shipping_tax_note != '' -%}
        <div class="mini-cart__actions text--subdued text--xsmall">
          {%- if section.settings.show_order_note -%}
            <button type="button" is="toggle-button" id="order-note-toggle" class="link" data-action="toggle-order-note" aria-controls="mini-cart-note" aria-expanded="false">
              {%- if cart.note == blank -%}
                {{- 'cart.general.add_order_note' | t -}}
              {%- else -%}
                {{- 'cart.general.edit_order_note' | t -}}
              {%- endif -%}
            </button>
          {%- endif -%}

          {%- if shipping_tax_note != '' -%}
            <span>{{ shipping_tax_note }}</span>
          {%- endif -%}
        </div>
      {%- endif -%}

      {%- if section.settings.show_checkout_button -%}
        <button form="mini-cart-form" type="submit" class="checkout-button button button--primary button--full" name="checkout">
          <span class="checkout-button__lock">{%- render 'icon' with 'lock' -%}</span>

          {{- 'cart.general.checkout' | t -}}
          <span class="square-separator"></span>
          {{- cart.total_price | money_with_currency -}}
        </button>
      {%- else -%}
        <a href="{{ routes.cart_url }}" class="button button--primary button--full" data-no-instant>{{ 'cart.general.go_to_cart' | t }}</a>
      {%- endif -%}
    </footer>
  {%- endif -%}

  {%- if section.settings.show_order_note -%}
    <openable-element id="mini-cart-note" class="mini-cart__order-note">
      <span class="openable__overlay"></span>
      <label for="cart[note]" class="mini-cart__order-note-title heading heading--xsmall">{{- 'cart.general.add_order_note' | t -}}</label>
      <textarea is="cart-note" name="note" id="cart[note]" rows="3" aria-owns="order-note-toggle" class="input__field input__field--textarea" placeholder="{{ 'cart.general.order_note_placeholder' | t }}">{{ cart.note }}</textarea>
      <button type="button" data-action="close" class="form__submit form__submit--closer button button--secondary">{{ 'cart.general.order_note_save' | t }}</button>
    </openable-element>
  {%- endif -%}
</cart-drawer>

{% schema %}
{
  "name": "Cart drawer",
  "class": "shopify-section--mini-cart",
  "settings": [
    {
      "type": "paragraph",
      "content": "Free shipping notice can be configured in global cart settings."
    },
    {
      "type": "checkbox",
      "id": "show_order_note",
      "label": "Show order note",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "show_checkout_button",
      "label": "Show checkout button",
      "default": true
    },
    {
      "type": "url",
      "id": "empty_button_link",
      "label": "Empty button link",
      "default": "/collections/all"
    },
    {
      "type": "header",
      "content": "Cross-sell",
      "info": "Dynamic recommendations are based on the items in your cart. They change and improve with time. [Learn more](https://help.shopify.com/en/themes/development/recommended-products)"
    },
    {
      "type": "checkbox",
      "id": "show_recommendations",
      "label": "Show cart recommendations",
      "default": true
    },
    {
      "type": "text",
      "id": "recommendations_title",
      "label": "Recommendations heading",
      "default": "You may also like"
    }
  ]
}
{% endschema %}