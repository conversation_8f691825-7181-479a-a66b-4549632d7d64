{%- if section.settings.background != settings.background and section.settings.background != 'rgba(0,0,0,0)' -%}
  {%- assign blends_with_background = false -%}
{%- else -%}
  {%- assign blends_with_background = true -%}
{%- endif -%}

<style>
  #shopify-section-{{ section.id }} {
    {%- if section.settings.background == 'rgba(0,0,0,0)' -%}
      {%- assign section_background = settings.background -%}
    {%- else -%}
      {%- assign section_background = section.settings.background -%}
    {%- endif -%}

    {%- if section.settings.text_color == 'rgba(0,0,0,0)' -%}
      {%- assign heading_color = settings.heading_color -%}
      {%- assign text_color = settings.text_color -%}
    {%- else -%}
      {%- assign heading_color = section.settings.text_color -%}
      {%- assign text_color = section.settings.text_color -%}
    {%- endif -%}

    {%- if section.settings.button_background == 'rgba(0,0,0,0)' -%}
      {%- assign button_background = settings.primary_button_background -%}
    {%- else -%}
      {%- assign button_background = section.settings.button_background -%}
    {%- endif -%}

    {%- if section.settings.button_text_color == 'rgba(0,0,0,0)' -%}
      {%- assign button_text_color = settings.primary_button_text_color -%}
    {%- else -%}
      {%- assign button_text_color = section.settings.button_text_color -%}
    {%- endif -%}

    --heading-color: {{ heading_color.red }}, {{ heading_color.green }}, {{ heading_color.blue }};
    --text-color: {{ text_color.red }}, {{ text_color.green }}, {{ text_color.blue }};
    --prev-next-button-background: {{ settings.background.red }}, {{ settings.background.green }}, {{ settings.background.blue }};
    --prev-next-button-color: {{ settings.text_color.red }}, {{ settings.text_color.green }}, {{ settings.text_color.blue }};

    --primary-button-background: {{ button_background.red }}, {{ button_background.green }}, {{ button_background.blue }};
    --primary-button-text-color: {{ button_text_color.red }}, {{ button_text_color.green }}, {{ button_text_color.blue }};

    --section-background: {{ section_background.red }}, {{ section_background.green }}, {{ section_background.blue }};
  }

  {%- if section.settings.title == blank and section.settings.subheading == blank and section.settings.content == blank -%}
    #shopify-section-{{ section.id }} {
      --vertical-breather: 40px; /* Only on multi-column section, due to its specific usage we reduce spacing when no content */
    }

    {%- if blends_with_background or section.settings.mobile_item_size == 'small' -%}
      /* Reduce the margin on small devices to create a slightly better layout */
      @media screen and (max-width: 999px) {
        #shopify-section-{{ section.id }} {
          --vertical-breather: var(--container-gutter);
        }
      }
    {%- endif -%}
  {%- endif -%}

  {%- for block in section.blocks -%}
    {%- if block.settings.image_border != 'rgba(0,0,0,0)' -%}
      #block-{{ section.id }}-{{ block.id }} .multi-column__image-wrapper {
        border: 1px solid {{ block.settings.image_border }};
      }
    {%- endif -%}
  {%- endfor -%}
</style>

<section class="section {% unless blends_with_background %}section--flush{% endunless %}">
  <div class="section__color-wrapper">
    <div class="container {% unless blends_with_background %}vertical-breather{% endunless %}">
      {%- if section.settings.title != blank or section.settings.subheading != blank or section.settings.content != blank -%}
        <header class="section__header {% if section.settings.content != blank %}section__header--tight{% endif %} section__header--{{ section.settings.column_alignment }}">
          <div class="text-container">
            {%- if section.settings.subheading != blank -%}
              <h2 class="heading heading--small">{{ section.settings.subheading }}</h2>
            {%- endif -%}

            {%- if section.settings.title != blank -%}
              <h3 class="heading h2">{{ section.settings.title }}</h3>
            {%- endif -%}

            {%- if section.settings.content != blank -%}
              {{- section.settings.content -}}
            {%- endif -%}
          </div>
        </header>
      {%- endif -%}

      {%- if section.blocks.size > 0 -%}
        <multi-column {% if section.settings.stack_items %}stack{% endif %} {% if section.settings.reveal_on_scroll %}stagger-apparition{% endif %} class="multi-column multi-column--pocket-{{ section.settings.mobile_item_size }} multi-column--{{ section.settings.desktop_item_size }} multi-column--spacing-{{ section.settings.spacing }}">
          {%- assign align_arrows_on_image = true -%}
          {%- assign smallest_image_aspect_ratio = 0 -%}

          <div {% unless section.settings.stack_items %}class="scroller"{% endunless %}>
            <scrollable-content class="multi-column__inner multi-column__inner--{{ section.settings.column_alignment }} {% unless section.settings.stack_items %}multi-column__inner--scroller{% endunless %}">
              {%- case section.settings.desktop_item_size -%}
                {%- when 'pico' -%}
                  {%- assign desktop_items_per_row = 8 -%}

                {%- when 'small' -%}
                  {%- assign desktop_items_per_row = 7 -%}

                {%- when 'medium' -%}
                  {%- assign desktop_items_per_row = 5 -%}

                {%- when 'large' -%}
                  {%- assign desktop_items_per_row = 3 -%}
              {%- endcase -%}

              {%- if section.settings.stack_items -%}
                {%- case section.settings.mobile_item_size -%}
                  {%- when 'small' -%}
                    {%- assign mobile_calculated_size = 'calc(50vw - 48px)' -%}
                    {%- assign tablet_calculated_size = 'calc(20vw - 80px)' -%}

                  {%- when 'medium' -%}
                    {%- assign mobile_calculated_size = 'calc(100vw - 48px)' -%}
                    {%- assign tablet_calculated_size = 'calc(25vw - 80px)' -%}

                  {%- when 'large' -%}
                    {%- assign mobile_calculated_size = 'calc(100vw - 48px)' -%}
                    {%- assign tablet_calculated_size = 'calc(33vw - 80px)' -%}
                {%- endcase -%}
              {%- else -%}
                {%- case section.settings.mobile_item_size -%}
                  {%- when 'small' -%}
                    {%- assign mobile_calculated_size = '25vw' -%}
                    {%- assign tablet_calculated_size = '20vw' -%}

                  {%- when 'medium' -%}
                    {%- assign mobile_calculated_size = '35vw' -%}
                    {%- assign tablet_calculated_size = '26vw' -%}

                  {%- when 'large' -%}
                    {%- assign mobile_calculated_size = '56vw' -%}
                    {%- assign tablet_calculated_size = '36vw' -%}
                {%- endcase -%}
              {%- endif -%}

              {%- case section.settings.spacing -%}
                {%- when 'tight' -%}
                  {%- assign desktop_item_gap = 24 -%}

                {%- when 'normal' -%}
                  {%- assign desktop_item_gap = 40 -%}

                {%- when 'loose' -%}
                  {%- assign desktop_item_gap = 60 -%}
              {%- endcase -%}

              {%- assign desktop_items_per_row_minus_one = desktop_items_per_row | minus: 1 -%}
              {%- assign gap_width = desktop_item_gap | divided_by: desktop_items_per_row | times: desktop_items_per_row_minus_one -%}

              {%- for block in section.blocks -%}
                {%- capture block_content -%}
                  {%- if block.settings.image != blank -%}
                    {%- assign smallest_image_aspect_ratio = smallest_image_aspect_ratio | at_least: block.settings.image.aspect_ratio -%}
                    {%- capture sizes_attribute -%}(max-width: 740px) {{ mobile_calculated_size }}, (max-width: 999px) {{ tablet_calculated_size }}, {{ 1520.0 | divided_by: desktop_items_per_row | minus: gap_width | ceil }}px{%- endcapture -%}

                    {%- if block.settings.link_url != blank -%}
                      <a href="{{ block.settings.link_url }}" class="multi-column__image-wrapper" style="max-width: {{ block.settings.image_width }}%; width: {{ block.settings.image.width }}px">
                        {{- block.settings.image | image_url: width: block.settings.image.width | image_tag: loading: 'lazy', sizes: sizes_attribute, widths: '200,300,400,500,600,700,800,900,1000,1100,1200', class: 'multi-column__image' -}}
                      </a>
                    {%- else -%}
                      <div class="multi-column__image-wrapper" style="max-width: {{ block.settings.image_width }}%; width: {{ block.settings.image.width }}px">
                        {{- block.settings.image | image_url: width: block.settings.image.width | image_tag: loading: 'lazy', sizes: sizes_attribute, widths: '200,300,400,500,600,700,800,900,1000,1100,1200', class: 'multi-column__image' -}}
                      </div>
                    {%- endif -%}
                  {%- else -%}
                    {%- assign align_arrows_on_image = false -%}
                  {%- endif -%}

                  {%- if block.settings.title != blank or block.settings.content != blank or block.settings.link_text != blank -%}
                    <div class="multi-column__text-container text--{{ block.settings.text_alignment }} text-container">
                      {%- if block.settings.subheading != blank -%}
                        <p class="heading heading--small">{{ block.settings.subheading | escape }}</p>
                      {%- endif -%}
                      
                      {%- if block.settings.title != blank -%}
                        <p class="heading {{ section.settings.column_title_style }}">{{ block.settings.title | escape }}</p>
                      {%- endif -%}

                      {%- if block.settings.content != blank -%}
                        {{- block.settings.content -}}
                      {%- endif -%}

                      {%- if block.settings.link_text != blank -%}
                        {%- if block.settings.link_style == 'link' -%}
                          <a href="{{ block.settings.link_url }}" class="multi-column__link heading heading--small link">{{ block.settings.link_text | escape }}</a>
                        {%- else -%}
                          <div class="button-wrapper">
                            <a href="{{ block.settings.link_url }}" class="multi-column__button button button--small button--primary">{{ block.settings.link_text | escape }}</a>
                          </div>
                        {%- endif -%}
                      {%- endif -%}
                    </div>
                  {%- endif -%}
                {%- endcapture -%}

                {%- if block_content != blank -%}
                  <div id="block-{{ section.id }}-{{ block.id }}" class="multi-column__item multi-column__item--align-{{ block.settings.vertical_alignment }} {% if block.settings.link_url != blank %}image-zoom{% endif %}" {% if section.settings.reveal_on_scroll %}reveal{% endif %} {{ block.shopify_attributes }}>
                    {{- block_content -}}
                  </div>
                {%- endif -%}
              {%- endfor -%}
            </scrollable-content>

            {%- unless section.settings.stack_items -%}
              <prev-next-buttons class="multi-column__prev-next {% unless align_arrows_on_image %}multi-column__prev-next--no-image{% endunless %} hidden-pocket" {% if align_arrows_on_image %}style="--smallest-image-aspect-ratio: {{ smallest_image_aspect_ratio }}"{% endif %}>
                <button class="multi-column__arrow prev-next-button prev-next-button--prev" disabled>
                  <span class="visually-hidden">{{ 'general.accessibility.previous' | t }}</span>
                  {%- render 'icon' with 'nav-arrow-left', block: true, direction_aware: true -%}
                </button>

                <button class="multi-column__arrow prev-next-button prev-next-button--next">
                  <span class="visually-hidden">{{ 'general.accessibility.next' | t }}</span>
                  {%- render 'icon' with 'nav-arrow-right', block: true, direction_aware: true -%}
                </button>
              </prev-next-buttons>
            {%- endunless -%}
          </div>
        </multi-column>
      {%- endif -%}

      {%- if section.settings.button_text != blank -%}
        <div class="section__footer">
          <div class="button-wrapper">
            <a href="{{ section.settings.button_link }}" class="button button--large button--primary">{{ section.settings.button_text | escape }}</a>
          </div>
        </div>
      {%- endif -%}

    </div>
  </div>
</section>

{% schema %}
{
  "name": "🐕 Multi-column",
  "class": "shopify-section--multi-column",
  "disabled_on": {
    "groups": ["header", "custom.overlay"]
  },
  "blocks": [
    {
      "name": "Column",
      "type": "item",
      "settings": [
        {
          "type": "image_picker",
          "id": "image",
          "label": "Image",
          "info": "1200 x 1200px .jpg recommended"
        },
        {
          "type": "range",
          "id": "image_width",
          "min": 10,
          "max": 100,
          "unit": "%",
          "step": 1,
          "label": "Image width",
          "default": 100
        },
        {
          "type": "color",
          "id": "image_border",
          "label": "Image border",
          "default": "rgba(0,0,0,0)"
        },
        {
          "type": "text",
          "id": "subheading",
          "label": "Subheading"
        },
        {
          "type": "text",
          "id": "title",
          "label": "Heading",
          "default": "Your content"
        },
        {
          "type": "richtext",
          "id": "content",
          "label": "Content",
          "default": "<p>Pair text with an image to focus on your chosen product, collection, or blog post. Add details on availability, style, or even provide a review.</p>"
        },
        {
          "type": "text",
          "id": "link_text",
          "label": "Link text"
        },
        {
          "type": "url",
          "id": "link_url",
          "label": "Link"
        },
        {
          "type": "select",
          "id": "link_style",
          "label": "Link style",
          "options": [
            {
              "value": "link",
              "label": "Link"
            },
            {
              "value": "button",
              "label": "Button"
            }
          ],
          "default": "link"
        },
        {
          "type": "select",
          "id": "text_alignment",
          "label": "Text alignment",
          "options": [
            {
              "value": "left",
              "label": "Left"
            },
            {
              "value": "center",
              "label": "Center"
            },
            {
              "value": "right",
              "label": "Right"
            }
          ],
          "default": "center"
        },
        {
          "type": "select",
          "id": "vertical_alignment",
          "label": "Vertical alignment",
          "options": [
            {
              "value": "start",
              "label": "Top"
            },
            {
              "value": "center",
              "label": "Center"
            },
            {
              "value": "end",
              "label": "Bottom"
            }
          ],
          "default": "start"
        }
      ]
    }
  ],
  "settings": [
    {
      "type": "checkbox",
      "id": "reveal_on_scroll",
      "label": "Reveal on scroll",
      "info": "Show animation when section becomes visible.",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "stack_items",
      "label": "Stack items",
      "default": false
    },
    {
      "type": "text",
      "id": "subheading",
      "label": "Subheading",
      "default": "Subheading"
    },
    {
      "type": "text",
      "id": "title",
      "label": "Heading",
      "default": "Multi-column"
    },
    {
      "type": "richtext",
      "id": "content",
      "label": "Content"
    },
    {
      "type": "text",
      "id": "button_text",
      "label": "Button text"
    },
    {
      "type": "url",
      "id": "button_link",
      "label": "Button link"
    },
    {
      "type": "select",
      "id": "column_title_style",
      "label": "Column Title Style",
      "default": "h3",
      "options": [
        {
          "value": "",
          "label": "Default"
        },
        {
          "value": "h1",
          "label": "Heading 1"
        },
        {
          "value": "h2",
          "label": "Heading 2"
        },
        {
          "value": "h3",
          "label": "Heading 3"
        },
        {
          "value": "h4",
          "label": "Heading 4"
        },
        {
          "value": "h5",
          "label": "Heading 5"
        }
      ]
    },
    {
      "type": "select",
      "id": "column_alignment",
      "label": "Column/header alignment",
      "options": [
        {
          "value": "left",
          "label": "Left"
        },
        {
          "value": "center",
          "label": "Center"
        },
        {
          "value": "right",
          "label": "Right"
        }
      ],
      "default": "center"
    },
    {
      "type": "select",
      "id": "mobile_item_size",
      "label": "Mobile/tablet column size",
      "options": [
        {
          "value": "small",
          "label": "Small"
        },
        {
          "value": "medium",
          "label": "Medium"
        },
        {
          "value": "large",
          "label": "Large"
        }
      ],
      "default": "medium"
    },
    {
      "type": "select",
      "id": "desktop_item_size",
      "label": "Desktop column size",
      "options": [
        {
          "value": "pico",
          "label": "X-Small"
        },
        {
          "value": "small",
          "label": "Small"
        },
        {
          "value": "medium",
          "label": "Medium"
        },
        {
          "value": "large",
          "label": "Large"
        }
      ],
      "default": "medium"
    },
    {
      "type": "select",
      "id": "spacing",
      "label": "Spacing",
      "options": [
        {
          "value": "tight",
          "label": "Small"
        },
        {
          "value": "normal",
          "label": "Medium"
        },
        {
          "value": "loose",
          "label": "Large"
        }
      ],
      "default": "normal"
    },
    {
      "type": "header",
      "content": "Colors"
    },
    {
      "type": "color",
      "id": "background",
      "label": "Background",
      "default": "rgba(0,0,0,0)"
    },
    {
      "type": "color",
      "id": "text_color",
      "label": "Text color",
      "default": "rgba(0,0,0,0)"
    },
    {
      "type": "color",
      "id": "button_background",
      "label": "Button background",
      "default": "rgba(0,0,0,0)"
    },
    {
      "type": "color",
      "id": "button_text_color",
      "label": "Button text",
      "default": "rgba(0,0,0,0)"
    }
  ],
  "presets": [
    {
      "name": "🐕 Multi-column",
      "blocks": [
        {
          "type": "item",
          "settings": {
            "title": "Column 1"
          }
        },
        {
          "type": "item",
          "settings": {
            "title": "Column 1"
          }
        },
        {
          "type": "item",
          "settings": {
            "title": "Column 1"
          }
        },
        {
          "type": "item",
          "settings": {
            "title": "Column 1"
          }
        }
      ]
    }
  ]
}
{% endschema %}