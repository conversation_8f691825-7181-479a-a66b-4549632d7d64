<div class="split-page">

  <div class="split-page__left">

    <div class="split-page__content-wrapper">
      
      <div class="split-page__header">
      </div>

      <div class="split-page__content">

        <div class="quiz-page-content page-content page-content--small" data-step="1">
          
          <div class="input">
            <input type="email" placeholder="{{ 'quiz.register.full_name_placeholder' | t }}" id="customer[full_name]" class="input__field" name="customer[full_name]" required="required" autocomplete="email" {% if form.errors contains 'email' %}aria-invalid="true" aria-describedby="register-form-error"{% endif %}>
            <label for="customer[email]" class="input__label">{{ 'quiz.register.full_name' | t }}</label>
          </div>
          
          <div class="input">
            <input type="email" placeholder="{{ 'customer.register.email' | t }}" id="customer[email]" class="input__field" name="customer[email]" required="required" autocomplete="email" {% if form.errors contains 'email' %}aria-invalid="true" aria-describedby="register-form-error"{% endif %}>
            <label for="customer[email]" class="input__label">{{ 'customer.register.email' | t }}</label>
          </div>

          <div class="input">
            <input type="password" placeholder="{{ 'customer.register.password' | t }}" id="customer[password]" class="input__field" name="customer[password]" required="required" autocomplete="new-password" {% if form.errors contains 'password' %}aria-invalid="true" aria-describedby="register-form-error"{% endif %}>
            <label for="customer[password]" class="input__label">{{ 'customer.register.password' | t }}</label>
          </div>

          <div class="form__actions">

            <button type="submit" is="loader-button" class="form__submit button button--primary" data-action="nextStep">
              <span class="button__text">{{ 'quiz.general.continue' | t }}</span>
              <span class="button__icon">{%- render 'icon' with 'nav-arrow-right' -%}</span>
            </button>

          </div>

        </div>

        <div class="quiz-page-content page-content page-content--small hidden" data-step="2">

          <div class="input">
            <p class="input__field">{{ 'quiz.inline-account.content.how_did_you_hear' | t }}</p>
            <label class="input__label">{{ 'quiz.general.optional' | t }}</label>
          </div>

          {% for block in section.blocks %}

            {% if block.type == "kyc_option" %}

              <div class="input input--radio">
                <div class="checkbox-container">
                  <input type="radio" class="checkbox" name="customer[properties][kyc]" id="customer[properties][kyc][{{ forloop.index }}]" value="{{ block.settings.title | handle }}" {% if block.settings.options != blank %}data-options="{{ block.id }}-options"{% endif%}>
                  <label for="customer[properties][kyc][{{ forloop.index }}]" class="">{{ block.settings.title }}</label>
                </div>
              </div>

              <div class="input input--select sub-option" id="{{ block.id }}-options" style="display: none">
                <select class="expanding-input__input" required>
                  <option value="">Select a Veterinarian</option>
                  <option value="She">She</option>
                  <option value="He">He</option>
                </select>
              </div>
              
            {% endif %}

          {% endfor %}
          
          <div class="form__actions">

            <button type="submit" is="loader-button" class="form__submit button button--primary">
              <span class="button__text">{{ 'quiz.general.create_account' | t }}</span>
              <span class="button__icon">{%- render 'icon' with 'nav-arrow-right' -%}</span>
            </button>

            <button type="submit" is="loader-button" class="button button--link">
              <span class="button__text">{{ 'quiz.general.skip' | t }}</span>
              <span class="button__icon">{%- render 'icon' with 'nav-arrow-right' -%}</span>
            </button>

          </div>

        </div>

      </div>

      <div class="split-page__footer">

        {% if section.settings.footer_text != blank %}
          <div class="quiz-terms text--xsmall">{{ section.settings.footer_text }}</div>
        {% endif %}
        
      </div>
      
    </div>
    
  </div>
  
  <div class="split-page__right hidden-pocket">

    {% if section.settings.page_banner != blank %}
      <img class="split-page__image" sizes="(max-width: 740px) calc(100vw - 96px), (max-width: 999px) calc(100vw - 160px), 600px" loading="lazy" {% render 'image-attributes', image: section.settings.page_banner, sizes: '600,700,800,1000,1200,1400' %}>
    {% endif %}

  </div>

</div>

<style>

  #shopify-section-{{ section.id }} {
    {%- if section.settings.text_background == 'rgba(0,0,0,0)' -%}
      {%- assign section_block_background = settings.background -%}
    {%- else -%}
      {%- assign section_block_background = section.settings.text_background -%}
    {%- endif -%}
  
    {%- if section.settings.text_color == 'rgba(0,0,0,0)' -%}
      {%- assign heading_color = settings.heading_color -%}
      {%- assign text_color = settings.text_color -%}
    {%- else -%}
      {%- assign heading_color = section.settings.text_color -%}
      {%- assign text_color = section.settings.text_color -%}
    {%- endif -%}
  
    {%- if section.settings.button_background == 'rgba(0,0,0,0)' -%}
      {%- assign button_background = settings.primary_button_background -%}
    {%- else -%}
      {%- assign button_background = section.settings.button_background -%}
    {%- endif -%}
  
    {%- if section.settings.button_text_color == 'rgba(0,0,0,0)' -%}
      {%- assign button_text_color = settings.primary_button_text_color -%}
    {%- else -%}
      {%- assign button_text_color = section.settings.button_text_color -%}
    {%- endif -%}
  
    --heading-color: {{ heading_color.red }}, {{ heading_color.green }}, {{ heading_color.blue }};
    --text-color: {{ text_color.red }}, {{ text_color.green }}, {{ text_color.blue }};
    --primary-button-background: {{ button_background.red }}, {{ button_background.green }}, {{ button_background.blue }};
    --primary-button-text-color: {{ button_text_color.red }}, {{ button_text_color.green }}, {{ button_text_color.blue }};
  
    --prev-next-button-background: {{ settings.background.red }}, {{ settings.background.green }}, {{ settings.background.blue }};
    --prev-next-button-color: {{ settings.text_color.red }}, {{ settings.text_color.green }}, {{ settings.text_color.blue }};
  
    --section-block-background: {{ section_block_background.red }}, {{ section_block_background.green }}, {{ section_block_background.blue }};
    
  }

</style>

<script>

  document.querySelectorAll('[data-action="prevStep"]').forEach(button => {
    button.addEventListener("click", e => {
      
      const button = e.currentTarget;
      const currentStep = button.closest("[data-step]");
      const prevStep = currentStep.previousElementSibling;
      
      if (prevStep != null) {
        currentStep.classList.add("hidden");
        prevStep.classList.remove("hidden");
      }

    })
  });

  document.querySelectorAll('[data-action="nextStep"]').forEach(button => {
    button.addEventListener("click", e => {
      
      const button = e.currentTarget;
      const currentStep = button.closest("[data-step]");
      const nextStep = currentStep.nextElementSibling;
      
      console.log(button);
      console.log(currentStep);
      console.log(nextStep);

      if (nextStep != null) {
        currentStep.classList.add("hidden");
        nextStep.classList.remove("hidden");
      }

    })
  });

  document.querySelectorAll('[data-action="back"]').forEach(button => {
    button.addEventListener("click", e => {

      const button = e.currentTarget;
      
      const steps = document.querySelectorAll('[data-step]');
      const currentStep = [...steps].filter(step => step.classList.contains("hidden") == false )[0]

      console.log(currentStep);
      const prevStep = currentStep.previousElementSibling;
      
      if (prevStep != null) {
        currentStep.classList.add("hidden");
        prevStep.classList.remove("hidden");
      }

    })
  });

</script>

{% schema %}
{
  "name": "Quiz - Create Account",
  "class": "shopify-section--quiz-create-account",
  "settings": [
    {
      "type": "image_picker",
      "id": "page_banner",
      "info": "2160 x 1080px .jpg recommended, 1080 x 1080px .jpg recommended if split",
      "label": "Banner - Right (Desktop Only)"
    },
    {
      "type": "richtext",
      "id": "footer_text",
      "label": "Footer Text"
    },
    {
      "type": "header",
      "content": "Colors"
    },
    {
      "type": "color",
      "id": "text_background",
      "label": "Text background",
      "default": "rgba(0,0,0,0)"
    },
    {
      "type": "color",
      "id": "text_color",
      "label": "Text color",
      "default": "rgba(0,0,0,0)"
    },
    {
      "type": "color",
      "id": "button_background",
      "label": "Button background",
      "default": "rgba(0,0,0,0)"
    },
    {
      "type": "color",
      "id": "button_text_color",
      "label": "Button text",
      "default": "rgba(0,0,0,0)"
    }
  ],
  "blocks": [
    {
      "type": "kyc_option",
      "name": "KYC Option",
      "settings": [
        {
          "type": "text",
          "id": "title",
          "label": "Choice Text"
        }
      ]
    },
    {
      "type": "register",
      "name": "Register form",
      "limit": 1
    }
  ]
}
{% endschema %}