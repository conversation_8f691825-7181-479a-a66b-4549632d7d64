{%- render 'raven-js-variables' -%}
{%- render 'quiz-js-variables' -%}

{%- assign text = 'quiz.loading_overlay.text_saving' | t -%}
{%- render 'quiz-loading-overlay', text: text -%}

<script src="{{ 'quiz-post-data.js' | asset_url }}"></script> 

<style>

  #shopify-section-{{ section.id }} {
    {%- if section.settings.background == 'rgba(0,0,0,0)' -%}
      {%- assign section_background = settings.background -%}
    {%- else -%}
      {%- assign section_background = section.settings.background -%}
    {%- endif -%}

    {%- if section.settings.text_color == 'rgba(0,0,0,0)' -%}
      {%- assign heading_color = settings.heading_color -%}
      {%- assign text_color = settings.text_color -%}
    {%- else -%}
      {%- assign heading_color = section.settings.text_color -%}
      {%- assign text_color = section.settings.text_color -%}
    {%- endif -%}

    --heading-color: {{ heading_color.red }}, {{ heading_color.green }}, {{ heading_color.blue }};
    --text-color: {{ text_color.red }}, {{ text_color.green }}, {{ text_color.blue }};
    --prev-next-button-background: var(--text-color);
    --prev-next-button-color: var(--section-background);

    --section-background: {{ section_background.red }}, {{ section_background.green }}, {{ section_background.blue }};
  }

</style>

{% schema %}
{
  "name": "Quiz - Saving Data",
  "class": "shopify-section--gallery quiz",
  "settings": [
    {
      "type": "checkbox",
      "id": "show_arrows",
      "label": "Show navigation arrows",
      "default": true
    },
    {
      "type": "header",
      "content": "Colors"
    },
    {
      "type": "color",
      "id": "background",
      "label": "Background",
      "default": "rgba(0,0,0,0)"
    },
    {
      "type": "color",
      "id": "text_color",
      "label": "Color",
      "default": "rgba(0,0,0,0)"
    }
  ],
  "presets": [
    {
      "name": "Gallery",
      "settings": {}
    }
  ]
}
{% endschema %}