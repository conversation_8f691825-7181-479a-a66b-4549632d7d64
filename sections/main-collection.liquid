{%- assign collection_sort_by = collection.sort_by | default: collection.default_sort_by -%}
{%- assign active_filters_count = 0 -%}

{%- for filter in collection.filters -%}
  {%- if filter.type == 'list' -%}
    {%- assign active_filters_count = active_filters_count | plus: filter.active_values.size -%}
  {%- elsif filter.type == 'price_range' and filter.min_value.value or filter.max_value.value -%}
    {%- assign active_filters_count = active_filters_count | plus: 1 -%}
  {%- endif -%}
{%- endfor -%}

<style>
  #shopify-section-{{ section.id }} {
    --section-products-per-row: {{ section.settings.mobile_products_per_row | times: 1 }};
  }

  @media screen and (min-width: 741px) {
    #shopify-section-{{ section.id }} {
      {%- assign remainder_for_two = section.settings.products_per_page | modulo: 2 -%}
      {%- assign remainder_for_desktop_count = section.settings.products_per_page | modulo: section.settings.desktop_products_per_row -%}

      --section-products-per-row: {% if remainder_for_two == 0 and remainder_for_desktop_count == 0 %}2{% else %}3{% endif %};
    }
  }

  {%- if section.settings.filter_position == 'drawer' -%}
    @media screen and (min-width: 1200px) {
      #shopify-section-{{ section.id }} {
        --section-products-per-row: {{ section.settings.desktop_products_per_row }};
      }
    }
  {%- else -%}
    @media screen and (min-width: 1400px) {
      #shopify-section-{{ section.id }} {
        --section-products-per-row: {{ section.settings.desktop_products_per_row }};
      }
    }
  {%- endif -%}

  {%- for block in section.blocks -%}
    #block-{{ section.id }}-{{ block.id }} {
      --heading-color: {{ block.settings.text_color.red }}, {{ block.settings.text_color.green }}, {{ block.settings.text_color.blue }};
      --text-color: {{ block.settings.text_color.red }}, {{ block.settings.text_color.green }}, {{ block.settings.text_color.blue }};
      --section-block-background: {{ block.settings.background.red }}, {{ block.settings.background.green }}, {{ block.settings.background.blue }};
      --primary-button-background: {{ block.settings.button_background.red }}, {{ block.settings.button_background.green }}, {{ block.settings.button_background.blue }};
      --primary-button-text-color: {{ block.settings.button_text_color.red }}, {{ block.settings.button_text_color.green }}, {{ block.settings.button_text_color.blue }};

      {%- assign text_position = block.settings.text_position | split: '_' | first -%}

      {%- case text_position -%}
        {%- when 'top' -%}
          {%- assign section_items_alignment = 'flex-start' -%}
        {%- when 'middle' -%}
          {%- assign section_items_alignment = 'center' -%}
        {%- when 'bottom' -%}
          {%- assign section_items_alignment = 'flex-end' -%}
      {%- endcase -%}

      --section-blocks-alignment: {{ section_items_alignment }};
    }
  {%- endfor -%}

  /*
    IMPLEMENTATION NOTE: due to design requirements, the mobile toolbar (with filters and sort by) had to be moved to the
    layout file. However as section settings cannot be accessed outside the section itself, we simply hide them in CSS.
   */

  {%- if section.settings.show_filters and collection.filters != empty and collection.products_count > 0 -%}
    .mobile-toolbar__item--filters {
      display: flex !important;
    }
  {%- endif -%}

  {%- if section.settings.show_sort_by and collection.products_count > 0 -%}
    .mobile-toolbar__item--sort {
      display: flex !important;
    }
  {%- endif -%}

  {%- if section.settings.show_sort_by or section.settings.show_filters and collection.filters != empty -%}
    @media screen and (max-width: 999px) {
      :root {
        --anchor-offset: 60px;
      }
    }
  {%- endif -%}
</style>

<section>
  <div class="container">
    <product-facet section-id="{{ section.id }}" class="product-facet">
      {%- if section.settings.show_filters and collection.filters != empty -%}
        {%- if section.settings.filter_position == 'always_visible' -%}
          <div class="product-facet__aside">
            <safe-sticky offset="30" class="product-facet__aside-inner">
              <div class="product-facet__filters-header hidden-pocket">
                <p class="heading {% if settings.heading_text_transform == 'uppercase' %}h6{% else %}h5{% endif %}">{{ 'collection.general.filters' | t }}</p>
              </div>

              {%- render 'facet-filters', filters: collection.filters, filters_position: section.settings.filter_position -%}
            </safe-sticky>
          </div>
        {%- else -%}
          {%- render 'facet-filters', filters: collection.filters, filters_position: section.settings.filter_position -%}
        {%- endif -%}
      {%- endif -%}

      <div id="facet-main" class="product-facet__main anchor" role="region" aria-live="polite">
        {%- if section.blocks.size > 0 -%}
          {%- capture promotion_blocks -%}
            <div class="promotion-block-list promotion-block-list--{{ section.settings.promotion_position }}">
              {%- for block in section.blocks -%}
                {%- capture block_content -%}
                  {%- if block.settings.image != blank -%}
                    {%- capture sizes_attribute -%}(max-width: 740px) calc(100vw - 12px * 2), calc(min(100vw, 1560px) / {{ section.blocks.size }} - 20px * {{ section.blocks.size | minus: 1 }}){%- endcapture -%}
                    {{- block.settings.image | image_url: width: block.settings.image.width | image_tag: loading: 'lazy', sizes: sizes_attribute, widths: '200,300,400,500,600,700,800,900,1000,1100,1200,1300,1400,1500,1600', class: 'promotion-block__image' -}}
                  {%- endif -%}

                  <div class="promotion-block__content-wrapper text-container" style="{% if block.settings.text_position contains 'center' %}text-align: center{% elsif block.settings.text_position contains 'right' %}text-align: end{% endif %}">
                    {%- if block.settings.subheading != blank -%}
                      <p class="heading heading--small">{{ block.settings.subheading | escape }}</p>
                    {%- endif -%}

                    {%- if block.settings.title != blank -%}
                      <p class="heading h4">{{ block.settings.title | escape | newline_to_br }}</p>
                    {%- endif -%}

                    {%- if block.settings.link_text != blank -%}
                      {%- if block.settings.link_style == 'link' -%}
                        <span class="heading heading--small link">{{ block.settings.link_text | escape }}</span>
                      {%- else -%}
                        <div class="button-wrapper">
                          <span class="button button--small button--primary">{{ block.settings.link_text | escape }}</span>
                        </div>
                      {%- endif -%}
                    {%- endif -%}
                  </div>
                {%- endcapture -%}

                {%- if block.settings.link_url != blank -%}
                  <a id="block-{{ section.id }}-{{ block.id }}" href="{{ block.settings.link_url }}" class="promotion-block promotion-block--{{ section.settings.promotion_height }} promotion-block--expand image-zoom" {{ block.shopify_attributes }}>
                    {{- block_content -}}
                  </a>
                {%- else -%}
                  <div id="block-{{ section.id }}-{{ block.id }}" class="promotion-block promotion-block--{{ section.settings.promotion_height }} promotion-block--expand image-zoom" {{ block.shopify_attributes }}>
                    {{- block_content -}}
                  </div>
                {%- endif -%}
              {%- endfor -%}
            </div>
          {%- endcapture -%}
        {%- endif -%}

        {%- if section.settings.promotion_position == 'top' -%}
          {{- promotion_blocks -}}
        {%- endif -%}

        {%- if collection.products_count > 0 -%}
          <div class="product-facet__meta-bar anchor">
            {%- if section.settings.filter_position == 'drawer' and section.settings.show_filters and collection.filters != empty -%}
              <button type="submit" is="toggle-button" class="product-facet__meta-bar-item product-facet__meta-bar-item--filter hidden-pocket" aria-controls="facet-filters" aria-expanded="false">
                {%- render 'icon' with 'filters', inline: true -%} {{- 'collection.general.show_filters' | t -}} {% if active_filters_count > 0 %}<span class="product-facet__active-count bubble-count">{{ active_filters_count }}</span>{% endif %}
              </button>
            {%- endif -%}

            <span class="product-facet__meta-bar-item product-facet__meta-bar-item--count" role="status">{{ 'collection.general.products_count' | t: count: collection.products_count }}</span>

            {%- if section.settings.show_sort_by -%}
              <div class="product-facet__meta-bar-item product-facet__meta-bar-item--sort">
                <span class="product-facet__sort-by-title text--subdued hidden-pocket">{{ 'collection.general.sort_by' | t }}</span>

                <div class="popover-container">
                  {%- for option in collection.sort_options -%}
                    {%- if option.value == collection_sort_by -%}
                      {%- assign collection_sort_by_name = option.name -%}
                      {%- break -%}
                    {%- endif -%}
                  {%- endfor -%}

                  <button type="button" is="toggle-button" class="popover-button hidden-pocket" aria-expanded="false" aria-controls="sort-by-popover">
                    <span id="sort-by-selected-value" style="pointer-events: none">{{- collection_sort_by_name -}}</span>
                    {%- render 'icon' with 'chevron', inline: true -%}
                  </button>

                  <sort-by-popover id="sort-by-popover" class="popover">
                    <span class="popover__overlay"></span>

                    <header class="popover__header">
                      <span class="popover__title heading h6">{{- 'collection.general.sort_by' | t -}}</span>

                      <button type="button" class="popover__close-button tap-area tap-area--large" data-action="close" title="{{ 'general.accessibility.close' | t | escape }}">
                        {%- render 'icon' with 'close' -%}
                      </button>
                    </header>

                    <div class="popover__content">
                      <div class="popover__choice-list">
                        {%- for sort_option in collection.sort_options -%}
                          <label class="popover__choice-item">
                            <input type="radio" data-bind-value="sort-by-selected-value" class="visually-hidden" {% if sort_option.value == collection_sort_by %}checked="checked"{% endif %} name="sort_by" value="{{ sort_option.value }}" title="{{ sort_option.name }}">
                            <span class="popover__choice-label">{{ sort_option.name }}</span>
                          </label>
                        {%- endfor -%}
                      </div>
                    </div>
                  </sort-by-popover>
                </div>
              </div>
            {%- endif -%}
          </div>

          {%- if active_filters_count > 0 -%}
            <div class="product-facet__active-list tag-list hidden-tablet-and-up">
              {%- render 'facet-active-filters', filters: collection.filters -%}
            </div>
          {%- endif -%}

          {%- paginate collection.products by section.settings.products_per_page -%}
            <product-list {% if settings.stagger_products_apparition %}stagger-apparition{% endif %} class="product-facet__product-list product-list anchor">
              {%- if section.settings.filter_position == 'always_visible' and section.settings.show_filters and collection.filters != empty -%}
                {%- assign sidebar_width = 305 -%}
              {% else %}
                {%- assign sidebar_width = 0 -%}
              {%- endif -%}

              <div class="product-list__inner">
                {%- assign desktop_number_of_products_minus_one = section.settings.desktop_products_per_row | minus: 1 -%}
                {%- assign gap_width = 24.0 | divided_by: section.settings.desktop_products_per_row | times: desktop_number_of_products_minus_one -%}
                {%- capture sizes_attribute -%}(max-width: 740px) calc({{ 100.0 | divided_by: section.settings.mobile_products_per_row | ceil }}vw - 24px), calc((min(100vw - 80px, 1520px) - {{ sidebar_width }}px) / {{ section.settings.desktop_products_per_row }} - {{ gap_width | ceil }}px){%- endcapture -%}

                {%- for product in collection.products -%}
                  {%- render 'product-item', product: product, collection: collection, sizes_attribute: sizes_attribute, reveal: settings.stagger_products_apparition -%}
                {%- endfor -%}
              </div>
            </product-list>

            {%- render 'pagination', paginate: paginate, use_ajax: true -%}
          {%- endpaginate -%}

          {%- if section.settings.promotion_position == 'bottom' -%}
            {{- promotion_blocks -}}
          {%- endif -%}
        {%- else -%}
          <div class="empty-state">
            {%- if collection.all_products_count == 0 -%}
              <h3 class="heading h4">{{ 'collection.general.empty_title' | t }}</h3>
              <p>{{ 'collection.general.empty_label' | t }}</p>

              <div class="button-wrapper">
                <a href="{{ routes.all_products_collection_url }}" class="button button--primary">{{ 'collection.general.empty_button' | t }}</a>
              </div>
            {%- else -%}
              <h3 class="heading h4">{{ 'collection.general.no_results_title' | t }}</h3>
              <p>{{ 'collection.general.no_results_label' | t }}</p>

              <div class="button-wrapper">
                <a href="{{ collection.url }}?sort_by={{ collection_sort_by }}" data-action="clear-filters" class="button button--primary">{{ 'collection.general.no_results_button' | t }}</a>
              </div>
            {%- endif -%}
          </div>
        {%- endif -%}
      </div>
    </product-facet>
  </div>
</section>

{% schema %}
{
  "name": "Collection page",
  "class": "shopify-section--main-collection",
  "max_blocks": 2,
  "blocks": [
    {
      "type": "image",
      "name": "Promotion block",
      "settings": [
        {
          "type": "image_picker",
          "id": "image",
          "label": "Image",
          "info": "1200 x 600px .jpg recommended (if two blocks), 2400 x 800px .jpg recommended (if one block)"
        },
        {
          "type": "text",
          "id": "subheading",
          "label": "Subheading",
          "default": "Your subheading"
        },
        {
          "type": "text",
          "id": "title",
          "label": "Heading",
          "default": "Your heading"
        },
        {
          "type": "text",
          "id": "link_text",
          "label": "Link text"
        },
        {
          "type": "url",
          "id": "link_url",
          "label": "Link URL"
        },
        {
          "type": "select",
          "id": "link_style",
          "label": "Link style",
          "options": [
            {
              "value": "link",
              "label": "Link"
            },
            {
              "value": "button",
              "label": "Button"
            }
          ],
          "default": "link"
        },
        {
          "type": "select",
          "id": "text_position",
          "label": "Text position",
          "options": [
            {
              "value": "top_left",
              "label": "Top left"
            },
            {
              "value": "top_center",
              "label": "Top center"
            },
            {
              "value": "top_right",
              "label": "Top right"
            },
            {
              "value": "middle_left",
              "label": "Middle left"
            },
            {
              "value": "middle_center",
              "label": "Middle center"
            },
            {
              "value": "middle_right",
              "label": "Middle right"
            },
            {
              "value": "bottom_left",
              "label": "Bottom left"
            },
            {
              "value": "bottom_center",
              "label": "Bottom center"
            },
            {
              "value": "bottom_right",
              "label": "Bottom right"
            }
          ],
          "default": "middle_left"
        },
        {
          "type": "color",
          "id": "background",
          "label": "Background",
          "default": "#f7f8fd",
          "info": "Only used when no image is uploaded."
        },
        {
          "type": "color",
          "id": "text_color",
          "label": "Text color",
          "default": "#1e316a"
        },
        {
          "type": "color",
          "id": "button_background",
          "label": "Button background",
          "default": "#ffffff"
        },
        {
          "type": "color",
          "id": "button_text_color",
          "label": "Button text color",
          "default": "#1e316a"
        }
      ]
    }
  ],
  "settings": [
    {
      "type": "checkbox",
      "id": "show_sort_by",
      "label": "Show sort by",
      "default": true
    },
    {
      "type": "range",
      "id": "products_per_page",
      "label": "Products per page",
      "min": 8,
      "max": 50,
      "step": 1,
      "default": 24
    },
    {
      "type": "select",
      "id": "mobile_products_per_row",
      "label": "Products per row (mobile)",
      "options": [
        {
          "value": "1",
          "label": "1"
        },
        {
          "value": "2",
          "label": "2"
        }
      ],
      "default": "2"
    },
    {
      "type": "range",
      "min": 3,
      "max": 5,
      "id": "desktop_products_per_row",
      "label": "Products per row (desktop)",
      "default": 4
    },
    {
      "type": "header",
      "content": "Filters"
    },
    {
      "type": "checkbox",
      "id": "show_filters",
      "label": "Show filters",
      "info": "[Customize filters](/admin/menus)",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "show_filter_group_name",
      "label": "Show group name",
      "info": "Group name will be shown inside selected filters.",
      "default": false
    },
    {
      "type": "checkbox",
      "id": "show_color_swatch",
      "label": "Show filter color swatch",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "open_first_filter_group",
      "label": "Open first group by default",
      "default": false
    },
    {
      "type": "select",
      "id": "filter_position",
      "label": "Position",
      "options": [
        {
          "value": "always_visible",
          "label": "Always visible"
        },
        {
          "value": "drawer",
          "label": "Drawer"
        }
      ],
      "default": "always_visible"
    },
    {
      "type": "header",
      "content": "Promotion blocks",
      "info": "Add extra information to push other collections or products."
    },
    {
      "type": "select",
      "id": "promotion_position",
      "label": "Position",
      "options": [
        {
          "value": "top",
          "label": "Top"
        },
        {
          "value": "bottom",
          "label": "Bottom"
        }
      ],
      "default": "top"
    },
    {
      "type": "select",
      "id": "promotion_height",
      "label": "Block height",
      "options": [
        {
          "value": "small",
          "label": "Small"
        },
        {
          "value": "medium",
          "label": "Medium"
        },
        {
          "value": "large",
          "label": "Large"
        }
      ],
      "default": "small"
    }
  ]
}
{% endschema %}