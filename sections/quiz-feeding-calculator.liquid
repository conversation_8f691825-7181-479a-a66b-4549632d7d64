<a id="quiz-feeding-calculator" name="quiz-feeding-calculator"></a>

<style>

  #shopify-section-{{ section.id }} {
    {%- if section.settings.background == 'rgba(0,0,0,0)' -%}
      {%- assign section_background = settings.background -%}
    {%- else -%}
      {%- assign section_background = section.settings.background -%}
    {%- endif -%}

    {%- if section.settings.heading_color == 'rgba(0,0,0,0)' -%}
      {%- assign heading_color = settings.heading_color -%}
    {%- else -%}
      {%- assign heading_color = section.settings.heading_color -%}
    {%- endif -%}

    {%- if section.settings.text_color == 'rgba(0,0,0,0)' -%}
      {%- assign text_color = settings.text_color -%}
    {%- else -%}
      {%- assign text_color = section.settings.text_color -%}
    {%- endif -%}

    --heading-color: {{ heading_color.red }}, {{ heading_color.green }}, {{ heading_color.blue }};
    --text-color: {{ text_color.red }}, {{ text_color.green }}, {{ text_color.blue }};
    --prev-next-button-background: var(--text-color);
    --prev-next-button-color: var(--section-background);

    --section-background: {{ section_background.red }}, {{ section_background.green }}, {{ section_background.blue }};
  }

</style>

<quiz-feeding-calculator class="quiz-feeding-calculator">

  <section class="section section--flush quiz">

    <div class="section__color-wrapper section section--use-padding vertical-breather">
    
      <div class="container">

        <div class="section__header">

          <h2 class="heading heading--small">How much <span data-calculator-product-name>Beef + Potato</span> should you feed <span data-calculator-dog-name>Ripley</span> per day?</h2>

          {%- if section.settings.title != blank -%}
            <h3 class="heading h1">{{ section.settings.title | escape }}</h3>
          {%- endif -%}

          <p class="text--large"><span data-calculator-dog-name></span> weighs <span><span data-calculator-dog-weight>20</span>lbs</span>, is <span><span data-calculator-dog-age-number>6</span> <span data-calculator-dog-age-type>months</span></span> old, is <span data-calculator-dog-activity>moderately active</span> and has <span data-calculator-condition>no health conditions</span>.</p>

          <p class="text--large">Based on this, you should feed <span data-calculator-dog-pronoun-possessive>him</span>:</p>
        
        </div>

        <div class="section__body">

          <div class="feeding-calculator">
          
            <div class="feeding-calculator__inner">

              <div class="feeding-calculator__header">

                <span class="feeding-calculator-perday">

                  <span class="feeding-calculator-perday__quantity">
                    <span data-calculator-total-ounces>16</span>
                  </span>

                  <span class="feeding-calculator-perday__label heading h4">
                    Ounces Per Day
                  </span>

                </span>

                <span class="feeding-calculator-separator heading heading--regular text--subdued">which is</span>

                <span class="feeding-calculator-perday">

                  <span class="feeding-calculator-perday__quantity" data-calculator-perday-cups>
                    <span data-calculator-total-cups>16</span>
                  </span>

                  <span class="feeding-calculator-perday__label heading h4">
                    Cups Per Day
                  </span>

                </span>
              
              </div>

              <div class="feeding-calculator__body">

                <hr>

                <h3 class="feeding-calculator-perday__label heading h4 text-center">
                  To transition your pup:
                </h3>
              
                <div class="feeding-calculator-days">

                  <div class="feeding-calculator-day" data-calculator-period-1>

                    <div class="feeding-calculator-day__title">
                      <h4 class="heading heading--small text-center">Day 1-3</h4>
                    </div>

                    <div class="feeding-calculator-day__image">
                      <img src="https://cdn.shopify.com/s/files/1/1683/1605/files/days-period-1.png?v=1695255200" alt="Day 1-3">
                    </div>

                    <div class="feeding-calculator-day__details">
                      <div class="text--large text--strong">25%</div>
                      <div class="feeding-calculator-day__amounts">
                        <div class="text text--small"><span data-ounces>4<span class="fraction">1/2</span></span> Ounces Per Day</div>
                        <div class="text text--small text--subdued"><span data-cups>0.5</span> Cups Per Day</div>
                      </div>
                    </div>
                  
                  </div>

                  <div class="feeding-calculator-day" data-calculator-period-2>

                    <div class="feeding-calculator-day__title">
                      <h4 class="heading heading--small text-center">Day 4-6</h4>
                    </div>

                    <div class="feeding-calculator-day__image">
                      <img src="https://cdn.shopify.com/s/files/1/1683/1605/files/days-period-2.png?v=1695255200" alt="Day 4-6">
                    </div>

                    <div class="feeding-calculator-day__details">
                      <div class="text--large text--strong">50%</div>
                      <div class="feeding-calculator-day__amounts">
                        <div class="text text--small"><span data-ounces>4</span> Ounces Per Day</div>
                        <div class="text text--small text--subdued"><span data-cups>0.5</span> Cups Per Day</div>
                      </div>
                    </div>
                  
                  </div>

                  <div class="feeding-calculator-day" data-calculator-period-3>

                    <div class="feeding-calculator-day__title">
                      <h4 class="heading heading--small text-center">Day 7-9</h4>
                    </div>

                    <div class="feeding-calculator-day__image">
                      <img src="https://cdn.shopify.com/s/files/1/1683/1605/files/days-period-3.png?v=1695255200" alt="Day 7-9">
                    </div>

                    <div class="feeding-calculator-day__details">
                      <div class="text--large text--strong">75%</div>
                      <div class="feeding-calculator-day__amounts">
                        <div class="text text--small"><span data-ounces>4</span> Ounces Per Day</div>
                        <div class="text text--small text--subdued"><span data-cups>0.5</span> Cups Per Day</div>
                      </div>
                    </div>
                  
                  </div>

                  <div class="feeding-calculator-day" data-calculator-period-4>

                    <div class="feeding-calculator-day__title">
                      <h4 class="heading heading--small text-center">Day 10+</h4>
                    </div>

                    <div class="feeding-calculator-day__image">
                      <img src="https://cdn.shopify.com/s/files/1/1683/1605/files/days-period-4.png?v=1695255200" alt="Day 10+">
                    </div>

                    <div class="feeding-calculator-day__details">
                      <div class="text--large text--strong">100%</div>
                      <div class="feeding-calculator-day__amounts">
                        <div class="text text--small"><span data-ounces>4</span> Ounces Per Day</div>
                        <div class="text text--small text--subdued"><span data-cups>0.5</span> Cups Per Day</div>
                      </div>
                    </div>
                  
                  </div>

                </div>

              </div>

              {% if section.settings.footer_text != blank %}
                <div class="feeding-calculator__footer">
                  <div class="text text--large">{{ section.settings.footer_text }}</div>
                </div>
              {% endif %}


            </div>

          </div>
        
        </div>
        
        {% if section.settings.button_text != blank %}
          
          <div class="section__footer">
          
            <div class="button-wrapper text-center">
              <a href="{{ section.settings.button_link }}" class="button button--highlight" target="_blank">{{ section.settings.button_text | escape }}</a>
            </div>

          </div>

        {% endif %}

      </div>
      
    </div>
    
  </section>

</quiz-feeding-calculator>

{% schema %}
{
  "name": "Quiz Feeding Calculator",
  "class": "shopify-section--calculator quiz",
  "settings": [
    {
      "type": "text",
      "id": "title",
      "label": "Heading",
      "default": "Talk about your brand"
    },
    {
      "type": "text",
      "id": "button_text",
      "label": "Button Text",
      "default": "Read our Transition Guide"
    },
    {
      "type": "richtext",
      "id": "footer_text",
      "label": "Footer Text",
      "default": "<p><strong>Tip:</strong> Screenshot this for easy reference!</p>"
    },
    {
      "type": "url",
      "id": "button_link",
      "label": "Button Link"
    },
    {
      "type": "header",
      "content": "Colors"
    },
    {
      "type": "color",
      "id": "background",
      "label": "Background",
      "default": "rgba(0,0,0,0)"
    },
    {
      "type": "color",
      "id": "heading_color",
      "label": "Heading Color",
      "default": "rgba(0,0,0,0)"
    },
    {
      "type": "color",
      "id": "text_color",
      "label": "Color",
      "default": "rgba(0,0,0,0)"
    }
  ],
  "presets": [
    {
      "name": "Feeding Calculator",
      "settings": {}
    }
  ]
}
{% endschema %}