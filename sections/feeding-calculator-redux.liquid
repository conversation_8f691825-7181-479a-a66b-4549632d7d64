{%- if section.settings.background_type == 'boxed' -%}
  {%- assign is_boxed = true -%}
{%- else -%}
  {%- assign is_boxed = false -%}
{%- endif -%}

{%- if section.settings.background != settings.background and section.settings.background != 'rgba(0,0,0,0)' -%}
  {%- assign blends_with_background = false -%}
{%- else -%}
  {%- assign blends_with_background = true -%}
{%- endif -%}

<style>
  #shopify-section-{{ section.id }} {
    
    {%- if section.settings.background == 'rgba(0,0,0,0)' -%}
      {%- assign section_background = settings.background -%}
    {%- else -%}
      {%- assign section_background = section.settings.background -%}
    {%- endif -%}

    {%- if section.settings.text_color == 'rgba(0,0,0,0)' -%}
      {%- assign heading_color = settings.heading_color -%}
      {%- assign text_color = settings.text_color -%}
    {%- else -%}
      {%- assign heading_color = section.settings.text_color -%}
      {%- assign text_color = section.settings.text_color -%}
    {%- endif -%}

    {%- if section.settings.subheading_color == 'rgba(0,0,0,0)' -%}
      {%- assign subheading_color = settings.subheading_color -%}
    {%- else -%}
      {%- assign subheading_color = section.settings.subheading_color -%}
    {%- endif -%}

    {%- if section.settings.button_background == 'rgba(0,0,0,0)' -%}
      {%- assign button_background = settings.primary_button_background -%}
    {%- else -%}
      {%- assign button_background = section.settings.button_background -%}
    {%- endif -%}

    --heading-color: {{ heading_color.red }}, {{ heading_color.green }}, {{ heading_color.blue }};
    --subheading-color: {{ subheading_color.red }}, {{ subheading_color.green }}, {{ subheading_color.blue }};
    --text-color: {{ text_color.red }}, {{ text_color.green }}, {{ text_color.blue }};
    --primary-button-background: {{ button_background.red }}, {{ button_background.green }}, {{ button_background.blue }};

    --section-background: {{ section_background.red }}, {{ section_background.green }}, {{ section_background.blue }};
  }
</style>

<section class="section section--feeding-calculator">

  <div class="container {% if section.settings.container_width %}container--{{ section.settings.container_width }}{% endif %} {% if section.settings.container_padding == false %}container--no-padding{% endif %}">

    <feeding-calculator 
      class="feeding-calculator" 
      id="{{ section.id }}--feeding-calculator" 
      data-results="{{ section.id }}--feeding-calculator-results" 
      data-calorie-multiplier="{{ section.settings.calorie_multiplier }}" 
      aria-controls="{{ section.id }}--feeding-calculator-results" 
      {% if section.settings.show_add_to_cart %}add-to-cart-enabled{% endif %}
      {% if section.settings.collection_complete %}data-collection-complete-id="{{ section.settings.collection_complete.id }}"{% endif %}
      {% if section.settings.collection_condition %}data-collection-condition-id="{{ section.settings.collection_condition.id }}"{% endif %}
    > 
    
      <div class="section__color-wrapper section__color-wrapper--boxed">
        <div class="container vertical-breather--tight">

          {%- assign text_alignment = section.settings.text_alignment -%}

          <div class="text-align--{{ text_alignment }}">

            {% if section.settings.calculator_icon != blank %}
              {%- capture sizes_attribute -%}(max-width: 740px) 120px, (max-width: 999px) 150px, 185px{%- endcapture -%}
              {{- section.settings.calculator_icon | image_url: width: section.settings.calculator_icon.width | image_tag: loading: 'lazy', sizes: sizes_attribute, widths: '600,700,800,1000,1200,1400', class: 'feeding-calculator__icon' -}}
            {% endif %}

            {%- if section.settings.subheading != blank -%}
              <h2 class="heading heading--small">{{ section.settings.subheading | escape }}</h2>
            {%- endif -%}

            {%- if section.settings.title != blank -%}
              <h3 class="heading h3 no-margin--bottom">{{ section.settings.title | escape }}</h3>
            {%- endif -%}

            {%- if section.settings.content != blank -%}
              {{- section.settings.content -}}
            {%- endif -%}

          </div>

          <form data-calculator-form>
  
            <div class="fieldset">

              {%- capture calculator_weight_title -%}
                {%- if section.settings.weight_title != blank -%}
                  {{- section.settings.weight_title -}}
                {%- else -%}
                  {{- 'calculator.weight.title' | t -}}
                {%- endif -%}
              {%- endcapture -%}
  
              <h5 class="heading heading--small text-center">{{ calculator_weight_title }}</h5>
  
              {%- assign min_value = section.settings.weight_min -%}
              {%- assign max_value = section.settings.weight_max -%}
              {%- assign start_value = max_value | divided_by: 2 | ceil -%}
  
              {%- assign lower_bound_progress = min_value | divided_by: max_value | times: 100.0 -%}
              {%- assign higher_bound_progress = max_value | divided_by: max_value | times: 100.0 -%}
              {%- assign start_progress = 0.5 | times: 100 -%}
  
              {%- assign unit = section.settings.unit -%}
  
              <weight-range class="weight-range" style="--range-position: {{ start_progress }}%;">
  
                <div class="weight-range__inner">
  
                  <div class="weight-range__thumb">
                    <div class="weight-range__thumb-icon"></div>
                    <div class="weight-range__thumb-value heading heading--xsmall">
                      <span data-weight-value>{{ start_value }}</span>
                      <span data-weight-unit>{{ unit }}</span>
                    </div>
                  </div>
  
                  <input type="range" class="weight-range__range range" aria-label="{{ section.settings.label }}" min="{{ min_value | ceil }}" max="{{ max_value | ceil }}" name="Weight" value="{{ start_value | ceil }}">
  
                  <div class="weight-range__track"></div>
  
                </div>
  
                <div class="weight-range__labels">
                  <div class="weight-range__label weight-range__label-lower heading heading--xsmall">
                    <span data-weight-min>{{ min_value }}</span>
                    <span data-weight-unit>{{ section.settings.unit }}</span>
                  </div>
                  <div class="weight-range__label weight-range__label-upper heading heading--xsmall">
                    <span data-weight-max>{{ max_value }}</span>
                    <span data-weight-unit>{{ section.settings.unit }}</span>
                  </div>
                </div>
  
              </weight-range>
  
              <p class="subheading heading heading--xsmall text-center">{{ 'calculator.weight.tip' | t }}</p>
  
            </div>
  
            <div class="fieldset">
  
              <p class="heading heading--small text-center">{{ 'calculator.details.title' | t }}</p>
  
              <div class="input">
  
                {%- assign field_name = 'Neutered' -%}
                {%- assign field_label = 'calculator.details.neutered' | t -%}
                {%- capture field_values -%}
                  Yes, 
                  No
                {%- endcapture -%}
                {%- assign field_values_array = field_values | split: "," -%}
  
                <div class="select-wrapper">
                  <select id="calculator-{{ field_name | handle }}" class="select" name="{{ field_name | escape }}" title="{{ field_name | escape }}" required>
                    <option value="" disabled selected></option>
                    {%- for item in field_values_array -%}
                      <option value="{{ item | split: "-" | first | strip }}">{{ item | split: "-" | last | strip }}</option>
                    {%- endfor -%}
                  </select>
                  {%- render 'icon' with 'chevron' -%}
                </div>
  
                <label for="calculator-{{ field_name | handle }}" class="input__label">{{ field_label }}</label>
  
              </div>
  
              <div class="input">
  
                {%- assign field_name = 'Activity Level' -%}
                {%- assign field_label = 'calculator.details.activity_level' | t -%}
                {%- capture field_values -%}
                  Low, 
                  Normal, 
                  High
                {%- endcapture -%}
                {%- assign field_values_array = field_values | split: "," -%}
  
                <div class="select-wrapper">
                  <select id="calculator-{{ field_name | handle }}" class="select" name="{{ field_name | escape }}" title="{{ field_name | escape }}" required>
                    <option value="" disabled selected></option>
                    {%- for item in field_values_array -%}
                      <option value="{{ item | split: "-" | first | strip }}">{{ item | split: "-" | last | strip }}</option>
                    {%- endfor -%}
                  </select>
                  {%- render 'icon' with 'chevron' -%}
                </div>
  
                <label for="calculator-{{ field_name | handle }}" class="input__label">{{ field_label }}</label>
  
              </div>
  
              <div class="input">
  
                {%- assign field_name = 'Age' -%}
                {%- assign field_label = 'calculator.details.age' | t -%}
                {%- capture field_values -%}
                  1.5 - Puppies (2 — 6 Months),
                  1.25 - Puppies (6 — 12 Months),
                  1 - Adult (1 — 6 Years),
                  0.9 - Senior (7+ Years)
                {%- endcapture -%}
                {%- assign field_values_array = field_values | split: "," -%}
  
                <div class="select-wrapper">
                  <select id="calculator-{{ field_name | handle }}" class="select" name="{{ field_name | escape }}" title="{{ field_name | escape }}" required>
                    <option value="" disabled selected></option>
                    {%- for item in field_values_array -%}
                      <option value="{{ item | split: "-" | first | strip }}">{{ item | split: "-" | last | strip }}</option>
                    {%- endfor -%}
                  </select>
                  {%- render 'icon' with 'chevron' -%}
                </div>
  
                <label for="calculator-{{ field_name | handle }}" class="input__label">{{ field_label }}</label>
  
              </div>

              {%- assign field_name = 'Diet Type' -%}
              {%- assign field_label = 'calculator.details.diet_type' | t -%}

              {% if section.settings.collections.count > 1 %}

                <div class="input">

                  <div class="select-wrapper">
                    <select id="calculator-{{ field_name | handle }}" class="select" name="{{ field_name | escape }}" title="{{ field_name | escape }}" required>
                      <option value="" disabled selected></option>

                      {%- for collection in section.settings.collections -%}
                        <option value="{{ collection.id }}">{{ collection.title }}</option>
                      {%- endfor -%}

                    </select>
                    {%- render 'icon' with 'chevron' -%}
                  </div>

                  <label for="calculator-{{ field_name | handle }}" class="input__label">{{ field_label }}</label>

                </div>
                
              {% else %}

                {%- assign collection = section.settings.collections | first -%}

                <input type="hidden" value="{{ collection.id }}" id="calculator-{{ field_name | handle }}" name="{{ field_name | escape }}" title="{{ field_name | escape }}" required>

              {% endif %}
  
            </div>
  
            <div class="fieldset">
  
              <h5 class="heading heading--small text-center">{{ 'calculator.feeding.title' | t }}</h5>
  
              <div class="tile-radios">
  
                {%- capture icon -%}{{ 'days-period-1.png' | file_url }}{%- endcapture -%}
  
                {%
                  render 'tile-radio',
                  required: true,
                  title: 'Topper',
                  icon: icon,
                  description: '25% of Total Food',
                  radio_value: 0.25,
                  radio_name: 'Feeding Portion',
                  radio_index: 1
                %}
  
                {%- capture icon -%}{{ 'days-period-2.png' | file_url }}{%- endcapture -%}
  
                {%
                  render 'tile-radio',
                  required: true,
                  title: 'Half and Half',
                  icon: icon,
                  description: '50% of Total Food',
                  radio_value: 0.5,
                  radio_name: 'Feeding Portion',
                  radio_index: 2
                %}
  
                {%- capture icon -%}{{ 'days-period-4.png' | file_url }}{%- endcapture -%}
  
                {%
                  render 'tile-radio',
                  required: true,
                  title: 'All Wynwood',
                  icon: icon,
                  description: '100% of Total Food',
                  radio_value: 1,
                  radio_name: 'Feeding Portion',
                  radio_index: 3
                %}
  
              </div>
  
              <p class="subheading heading heading--xsmall text-center">{{ 'calculator.feeding.tip' | t }}</p>
  
            </div>
  
            <div class="fieldset">
  
              <div class="text-center">
                
                <button type="submit" class="button button--large button--highlight" data-calculator-submit-button>
                  <span class="button__text">{{ 'calculator.submit' | t }}</span>
                  <span class="button__icon">{%- render 'icon' with 'nav-arrow-right' -%}</span>
                </button>
  
              </div>
  
            </div>
          
          </form>

        </div>

      </div>

    </feeding-calculator>

  </div>

</section>

<section class="section section--feeding-calculator">

  <div class="container {% if section.settings.container_width %}container--{{ section.settings.container_width }}{% endif %} {% if section.settings.container_padding == false %}container--no-padding{% endif %}">

    <feeding-calculator-results class="feeding-calculator-results" id="{{ section.id }}--feeding-calculator-results" data-calculator="{{ section.id }}--feeding-calculator" hidden>

      <div class="section__color-wrapper section__color-wrapper--boxed">
        <div class="container vertical-breather--tight">

          {%- assign text_alignment = section.settings.text_alignment -%}

          <div class="text-align--{{ text_alignment }}">

            {%- if section.settings.results_subheading != blank -%}
              <h2 class="heading heading--small">{{ section.settings.results_subheading | escape }}</h2>
            {%- endif -%}

            {%- if section.settings.results_title != blank -%}
              <h3 class="heading h3 no-margin--bottom">{{ section.settings.results_title | escape }}</h3>
            {%- endif -%}

            {%- if section.settings.results_content != blank -%}
              {{- section.settings.results_content -}}
            {%- endif -%}

          </div>

          <div class="fieldset">

            <table class="table table--1 table--auto calculator-results-table">
              <thead>
                <tr>
                  <th>Diet</th>
                  <th>
                    Oz
                    <span class="nowrap">per Day</span>
                  </th>
                  <th>
                    Cups 
                    <span class="nowrap">per Day</span>
                  </th>
                </tr>
              </thead>
              <tbody data-results-table>

                {%- comment -%}
                  Results populated by Javascript
                {%- endcomment -%}

                {%- comment -%}

                  <tr class="results-row text--small" data-details="[object Object]" data-result-row="" data-total-price="undefined">
                    <td class="results-row__details">
                      <a class="results-row__external-link" target="_blank" href="https://wynwooddogfood.com/products/beef-potato"><svg focusable="false" width="12" height="12" viewBox="0 0 12 12" role="presentation">
                    <title>
                      External Link
                    </title>
                    <path fill="currentColor" d="M6 1h5v5L8.86 3.85 4.7 8 4 7.3l4.15-4.16zM2 3h2v1H2v6h6V8h1v2a1 1 0 0 1-1 1H2a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1"></path>
                  </svg></a>
                      <button type="button" class="results-row__button link" target="_blank" data-result-title="" data-link="https://wynwooddogfood.com/products/beef-potato">
                        <span class="">Beef + Potato</span>
                        <span class="label label--custom">Selected</span>
                      </button>
                    </td>
                    <td data-unrounded="24.288673718967093">24<span class="fraction">1/4</span></td>
                    <td data-unrounded="3.0360842148708866">3</td>
                  </tr>

                {%- endcomment -%}
                 
              </tbody>
            </table>

          </div>

          <div class="fieldset text-center">

            <div class="button-wrapper button-wrapper--vertical">

              {%- if section.settings.show_add_to_cart -%}
                
                {%- capture add_form_id -%}{{ section.id }}--add-form{%- endcapture -%}
                {%- capture add_form_classes -%}feeding-calculator-results-add-form{%- endcapture -%}

                {%- form 'cart', cart, id: add_form_id, data-add-form: "", class: add_form_classes -%}

                  {% comment %} Customizations and Parent Product get added here. {% endcomment %} 

                  <input class="input" type="hidden" name="items[0]id" value="" data-add-form-16-id>
                  <input class="input" type="hidden" name="items[0]quantity" value="" data-add-form-16-quantity>

                  <input class="input" type="hidden" name="items[1]id" value="" data-add-form-64-id>
                  <input class="input" type="hidden" name="items[1]quantity" value="" data-add-form-64-quantity>

                  <button type="button" is="loader-button" class="button button--highlight" data-add-to-cart>
                    <span class="button__icon">{%- render 'icon' with 'header-shopping-cart' -%}</span>
                    <span class="button__text">Add Enough <span data-product-name>Food</span> for One Week - <span data-product-total>$100.00</span></span>
                  </button>

                {%- endform -%}

              {%- endif -%}

              {%- if section.settings.nutrition_enable == true -%}
                
                <button type="button" is="loader-button" class="button button--primary" data-show-nutrition>
                  <span class="button__text">Nutritional Info for <span data-product-name>this product</span></span>
                </button>

              {%- endif -%}

              {%- if section.settings.button_text -%}
                
                {%- capture button_class -%}
                  {% if section.settings.nutrition_enable %}
                    button--link
                  {% else %}
                    button--primary
                  {% endif %}
                {%- endcapture -%}

                <a href="{{ section.settings.button_link }}" class="button {{ button_class }}" target="_blank">
                  <span class="button__text">{{ section.settings.button_text }}</span>
                </a>

              {%- endif -%}

            </div>

          
          </div>

        </div>

      </div>

    </feeding-calculator-results>

  </div>

</section>

{%- if section.settings.nutrition_enable == true -%}

  {%- assign product = all_products['beef-potato'] -%}

  {% comment %} =========== CONSTANTS =========== {% endcomment %}

  {% comment %} Other {% endcomment %}
  {%- assign ratio_cups_to_ounces = 0.125 -%}
  {%- assign ratio_ounces_to_cups = 8 -%}

  {%- assign nutrition_calories_per_cup = product.metafields.nutrition.calories_cup -%}
  {%- assign nutrition_calories_per_oz = nutrition_calories_per_cup | times: ratio_cups_to_ounces -%}

  {% comment %} Calculator {% endcomment %}
  {%- assign dog_calories_per_day = 381 -%}
  {%- assign dog_cups_per_day = dog_calories_per_day | divided_by: nutrition_calories_per_cup -%}
  
  {%- assign dog_cups_per_day_full = dog_cups_per_day | floor -%}
  {%- assign dog_cups_per_day_frac = dog_cups_per_day | minus: dog_cups_per_day_full -%}
  {%- assign dog_cups_per_day_quarters = dog_cups_per_day_frac | times: 100 | divided_by: 25 | ceil | times: 25 | divided_by: 100.0 -%}
  {%- assign dog_cups_per_day_rounded = dog_cups_per_day_full | plus: dog_cups_per_day_quarters -%}

  {%- assign nutrition_calories_per_kg = product.metafields.nutrition.calories_per_kg -%}
  {%- assign nutrition_calories_per_gram = nutrition_calories_per_kg | divided_by: 1000 -%}
  {%- assign nutrition_grams_per_calorie = 1 | divided_by: nutrition_calories_per_gram -%}
  {%- assign nutrition_grams_per_1000_calories = nutrition_grams_per_calorie | times: 1000 | round -%}

  {%- assign multiplier_perday = dog_calories_per_day | divided_by: nutrition_calories_per_kg -%}

  {%- assign nutrition_calories_per_day = nutrition_calories_per_cup | times: dog_cups_per_day -%}

  {% comment %} =========== DATA SOURCES =========== {% endcomment %}

  {% comment %} Product Details {% endcomment %}

  {%- assign product_title = product.title -%}
  {%- assign product_url = product.url -%}
  
  {%- assign product_color = product.metafields.quiz.product_color.value | default: "#2E2E2E" -%}
  {%- assign product_image_url = product.metafields.custom.quiz_results_image -%}
  
  {%- assign product_protein_rating = product.metafields.product.protein_rating.value -%}
  {%- assign product_fat_rating = product.metafields.product.fat_rating.value -%}
  {%- assign product_carbohydrate_rating = product.metafields.product.carbohydrate_rating.value -%}
  
  {%- assign product_summary_feature_1_title = product.metafields.product.summary_feature_1_title -%}
  {%- assign product_summary_feature_1_description = product.metafields.product.summary_feature_1_description | metafield_tag -%}
  {%- assign product_summary_feature_2_title = product.metafields.product.summary_feature_2_title -%}
  {%- assign product_summary_feature_2_description = product.metafields.product.summary_feature_2_description | metafield_tag -%}
  {%- assign product_summary_feature_3_title = product.metafields.product.summary_feature_3_title -%}
  {%- assign product_summary_feature_3_description = product.metafields.product.summary_feature_3_description | metafield_tag -%}
  
  {%- assign product_ingredients = product.metafields.product.ingredients | metafield_tag -%}
  {%- assign product_description = product.metafields.product.description | metafield_tag -%}
  {%- assign product_indications = product.metafields.product.indications | metafield_tag -%}
  {%- assign product_contra_indications = product.metafields.product.contra_indications | metafield_tag -%}


  {% comment %} Nutrition {% endcomment %}

  {%- assign nutrition_protein = product.metafields.nutrition.protein -%}
  {%- assign nutrition_fat = product.metafields.nutrition.fat -%}
  {%- assign nutrition_carbohydrate = product.metafields.nutrition.carbohydrate -%}

  {% comment %} {%- assign calories_from_protein = nutrition_protein | times: 3.5 -%} {% endcomment %}
  {% comment %} {%- assign calories_from_carbohydrates = nutrition_carbohydrate | times: 3.5 -%} {% endcomment %}
  {% comment %} {%- assign calories_from_fat = nutrition_fat | times: 8.5 -%} {% endcomment %}
  
  {%- assign nutrition_calories_from_protein = product.metafields.nutrition.calories_from_protein | round -%}
  {%- assign nutrition_calories_from_fat = product.metafields.nutrition.calories_from_fat | round -%}
  {%- assign nutrition_calories_from_carbohydrates = product.metafields.nutrition.calories_from_carbohydrates | round -%}

  {%- assign nutrition_fiber = product.metafields.nutrition.dietary_fiber -%}
  {%- assign nutrition_ash = product.metafields.nutrition.ash -%}
  {%- assign nutrition_calcium = product.metafields.nutrition.calcium -%}
  {%- assign nutrition_phosphorus = product.metafields.nutrition.phosphorus -%}
  {%- assign nutrition_sodium = product.metafields.nutrition.sodium -%}
  {%- assign nutrition_taurine = product.metafields.nutrition.taurine -%}
  {%- assign nutrition_epa = product.metafields.nutrition.epa -%}
  {%- assign nutrition_dha = product.metafields.nutrition.dha -%}
  {%- assign nutrition_omega3 = product.metafields.nutrition.omega3 -%}
  {%- assign nutrition_omega6 = product.metafields.nutrition.omega6 -%}
  
  {%- assign nutrition_vitamin_a = product.metafields.nutrition.vitamin_a -%}
  {%- assign nutrition_vitamin_b1 = product.metafields.nutrition.vitamin_b1 -%}
  {%- assign nutrition_vitamin_b2 = product.metafields.nutrition.vitamin_b2 -%}
  {%- assign nutrition_vitamin_b3 = product.metafields.nutrition.vitamin_b3 -%}
  {%- assign nutrition_vitamin_b6 = product.metafields.nutrition.vitamin_b6 -%}
  {%- assign nutrition_vitamin_b12 = product.metafields.nutrition.vitamin_b12 -%}
  {%- assign nutrition_vitamin_d = product.metafields.nutrition.vitamin_d -%}
  {%- assign nutrition_folic_acid = product.metafields.nutrition.folic_acid -%}
  {%- assign nutrition_vitamin_e = product.metafields.nutrition.vitamin_e -%}
  
  {%- assign nutrition_magnesium = product.metafields.nutrition.magnesium -%}
  {%- assign nutrition_potassium = product.metafields.nutrition.potassium -%}
  {%- assign nutrition_iron = product.metafields.nutrition.iron -%}
  {%- assign nutrition_zinc = product.metafields.nutrition.zinc -%}
  {%- assign nutrition_copper = product.metafields.nutrition.copper -%}
  {%- assign nutrition_manganese = product.metafields.nutrition.manganese -%}
  {%- assign nutrition_iodine = product.metafields.nutrition.iodine -%}
  {%- assign nutrition_selenium = product.metafields.nutrition.selenium -%}
  {%- assign nutrition_aginine = product.metafields.nutrition.aginine -%}
  {%- assign nutrition_cystine = product.metafields.nutrition.cystine -%}
  {%- assign nutrition_histidine = product.metafields.nutrition.histidine -%}
  {%- assign nutrition_isoleucine = product.metafields.nutrition.isoleucine -%}
  {%- assign nutrition_leucine = product.metafields.nutrition.leucine -%}
  {%- assign nutrition_lysine = product.metafields.nutrition.lysine -%}
  {%- assign nutrition_methionone = product.metafields.nutrition.methionone -%}
  {%- assign nutrition_phenylalanine = product.metafields.nutrition.phenylalanine -%}
  {%- assign nutrition_tryptophan = product.metafields.nutrition.tryptophan -%}
  {%- assign nutrition_tyrosine = product.metafields.nutrition.tyrosine -%}
  {%- assign nutrition_valine = product.metafields.nutrition.valine -%}
  {%- assign nutrition_threonine = product.metafields.nutrition.threonine -%}


  {% comment %} Calculated Values {% endcomment %}

  {%- assign nutrition_protein_perday = product.metafields.nutrition.protein | times: multiplier_perday -%}
  {%- assign nutrition_fat_perday = product.metafields.nutrition.fat | times: multiplier_perday -%}
  {%- assign nutrition_carbohydrate_perday = product.metafields.nutrition.carbohydrate | times: multiplier_perday -%}
  
  {%- assign nutrition_calories_from_protein_perday = nutrition_calories_from_protein | times: multiplier_perday -%}
  {%- assign nutrition_calories_from_fat_perday = nutrition_calories_from_fat | times: multiplier_perday -%}
  {%- assign nutrition_calories_from_carbohydrates_perday = nutrition_calories_from_carbohydrates | times: multiplier_perday -%}

  {%- assign nutrition_fiber_perday = nutrition_fiber | times: multiplier_perday -%}
  {%- assign nutrition_ash_perday = nutrition_ash | times: multiplier_perday -%}
  {%- assign nutrition_calcium_perday = nutrition_calcium | times: multiplier_perday -%}
  {%- assign nutrition_phosphorus_perday = nutrition_phosphorus | times: multiplier_perday -%}
  {%- assign nutrition_sodium_perday = nutrition_sodium | times: multiplier_perday -%}
  {%- assign nutrition_taurine_perday = nutrition_taurine | times: multiplier_perday -%}
  {%- assign nutrition_epa_perday = nutrition_epa | times: multiplier_perday -%}
  {%- assign nutrition_dha_perday = nutrition_dha | times: multiplier_perday -%}
  {%- assign nutrition_omega3_perday = nutrition_omega3 | times: multiplier_perday -%}
  {%- assign nutrition_omega6_perday = nutrition_omega6 | times: multiplier_perday -%}

  {%- assign nutrition_vitamin_a_perday = nutrition_vitamin_a | times: multiplier_perday -%}
  {%- assign nutrition_vitamin_b1_perday = nutrition_vitamin_b1 | times: multiplier_perday -%}
  {%- assign nutrition_vitamin_b2_perday = nutrition_vitamin_b2 | times: multiplier_perday -%}
  {%- assign nutrition_vitamin_b3_perday = nutrition_vitamin_b3 | times: multiplier_perday -%}
  {%- assign nutrition_vitamin_b6_perday = nutrition_vitamin_b6 | times: multiplier_perday -%}
  {%- assign nutrition_vitamin_b12_perday = nutrition_vitamin_b12 | times: multiplier_perday -%}
  {%- assign nutrition_vitamin_d_perday = nutrition_vitamin_d | times: multiplier_perday -%}
  {%- assign nutrition_folic_acid_perday = nutrition_folic_acid | times: multiplier_perday -%}
  {%- assign nutrition_vitamin_e_perday = nutrition_vitamin_e | times: multiplier_perday -%}

  {%- assign nutrition_magnesium_perday = nutrition_magnesium | times: multiplier_perday -%}
  {%- assign nutrition_potassium_perday = nutrition_potassium | times: multiplier_perday -%}
  {%- assign nutrition_iron_perday = nutrition_iron | times: multiplier_perday -%}
  {%- assign nutrition_zinc_perday = nutrition_zinc | times: multiplier_perday -%}
  {%- assign nutrition_copper_perday = nutrition_copper | times: multiplier_perday -%}
  {%- assign nutrition_manganese_perday = nutrition_manganese | times: multiplier_perday -%}
  {%- assign nutrition_iodine_perday = nutrition_iodine | times: multiplier_perday -%}
  {%- assign nutrition_selenium_perday = nutrition_selenium | times: multiplier_perday -%}
  {%- assign nutrition_aginine_perday = nutrition_aginine | times: multiplier_perday -%}
  {%- assign nutrition_cystine_perday = nutrition_cystine | times: multiplier_perday -%}
  {%- assign nutrition_histidine_perday = nutrition_histidine | times: multiplier_perday -%}
  {%- assign nutrition_isoleucine_perday = nutrition_isoleucine | times: multiplier_perday -%}
  {%- assign nutrition_leucine_perday = nutrition_leucine | times: multiplier_perday -%}
  {%- assign nutrition_lysine_perday = nutrition_lysine | times: multiplier_perday -%}
  {%- assign nutrition_methionone_perday = nutrition_methionone | times: multiplier_perday -%}
  {%- assign nutrition_phenylalanine_perday = nutrition_phenylalanine | times: multiplier_perday -%}
  {%- assign nutrition_tryptophan_perday = nutrition_tryptophan | times: multiplier_perday -%}
  {%- assign nutrition_tyrosine_perday = nutrition_tyrosine | times: multiplier_perday -%}
  {%- assign nutrition_valine_perday = nutrition_valine | times: multiplier_perday -%}
  {%- assign nutrition_threonine_perday = nutrition_threonine | times: multiplier_perday -%}
  

  {% comment %} Display Values {% endcomment %}

  <section class="section section--feeding-calculator">

    <div class="container {% if section.settings.container_width %}container--{{ section.settings.container_width }}{% endif %} {% if section.settings.container_padding == false %}container--no-padding{% endif %}">

      <feeding-calculator-nutrition hidden class="feeding-calculator-nutrition" id="{{ section.id }}--feeding-calculator-nutrition" data-calculator="{{ section.id }}--feeding-nutrition" style="--product-color: {{ product_color | color_extract: 'red' }}, {{ product_color | color_extract: 'green' }}, {{ product_color | color_extract: 'blue' }}">

        <div class="section__color-wrapper section__color-wrapper--boxed">

          <div class="feeding-calculator-nutrition__header">
            <div class="feeding-calculator-nutrition__header-image">
              <img src="{{ product_image_url | image_url: width: 150, height: 150 }}" alt="{{ product_title }}" width="120" height="120" loading="lazy" data-product-image>
            </div>
          </div>

          <div class="feeding-calculator-nutrition__content">

            <div class="container vertical-breather--tight spaced-content">

              <div class="nutrition-header spaced-content spaced-content--tight">
                <p class="heading h6 text--subdued">{{ 'nutrition.content.nutritional_info' | t }}</p>
                <h3 class="heading h3 no-margin--bottom" data-product-title>{{ product.title }}</h3>
                <div class="nutrition-ratings">
                  <div class="nutrition-rating heading h6 product-color"><span data-product-protein-rating>{{ product_protein_rating }}</span> Protein</div>
                  <div class="nutrition-rating-separator"></div>
                  <div class="nutrition-rating heading h6 product-color"><span data-product-fat-rating>{{ product_fat_rating }}</span> Fat</div>
                  <div class="nutrition-rating-separator"></div>
                  <div class="nutrition-rating heading h6 product-color"><span data-product-carbohydrate-rating>{{ product_carbohydrate_rating }}</span> Carb</div>
                </div>
              </div>

              <hr class="hr--narrow">

              <div class="spaced-content nutrition-analysis">

                <h4 class="heading h4">Brief Summary</h4>

              </div>

              <div class="nutrition-summary">
                <div class="nutrition-summary-item">
                  <h4 class="nutrition-summary-item__title product-color h6" data-product-feature-1-title>{{ product_summary_feature_1_title }}</h4>
                  <div class="nutrition-summary-item__description text--xsmall" data-product-feature-1-description>
                    {{ product_summary_feature_1_description }}
                  </div>
                </div>
                <div class="nutrition-summary-item">
                  <h4 class="nutrition-summary-item__title product-color h6" data-product-feature-2-title>{{ product_summary_feature_2_title }}</h4>
                  <div class="nutrition-summary-item__description text--xsmall" data-product-feature-2-description>
                    {{ product_summary_feature_2_description }}
                  </div>
                </div>
                <div class="nutrition-summary-item">
                  <h4 class="nutrition-summary-item__title product-color h6" data-product-feature-3-title>{{ product_summary_feature_3_title }}</h4>
                  <div class="nutrition-summary-item__description text--xsmall" data-product-feature-3-description>
                    {{ product_summary_feature_3_description }}
                  </div>
                </div>
              </div>

              <hr class="hr--narrow">
  
              <div class="spaced-content nutrition-analysis">

                <h4 class="heading h4">{{ 'nutrition.analysis.analysis' | t }}</h4>

                <div class="nutritional-analysis">

                  <div class="nutritional-analysis__content">

                    <!-- CALORIC INFO -->

                    <analysis-header class="analysis-header">
                      <span class="analysis-header__title heading h6">Caloric Info</span>
                    </analysis-header>

                    <!-- Calories per Serving Size -->

                    <analysis-category class="analysis-category">

                      <button class="analysis-category__header" is="toggle-button" aria-expanded="true" aria-controls="{{ section.id }}--analysis-category--caloric-info">
                        <div class="analysis-category__button">
                          <span class="visually-hidden">Toggle</span>
                        </div>
                        <div class="analysis-category__header-row">
                          <span class="analysis-category__title">Calories per Serving Size</span>
                        </div>
                      </button>

                      <div class="analysis-category__content" id="{{ section.id }}--analysis-category--caloric-info">

                        {% comment %}
                        <analysis-row class="analysis-row analysis-table-row">
                          <span>
                            <strong>per 1000cal</strong>
                          </span>
                          <span>
                            <span class="analysis-value" data-analysis-value="calories_per_1000cal">
                              {% render 'significant', places: 1, value: nutrition_grams_per_1000_calories %}
                            </span>
                            <span class="analysis-unit">kcal</span>
                          </span>
                        </analysis-row>
                        {% endcomment %}

                        <analysis-row class="analysis-row analysis-table-row">
                          <span>
                            <strong>per kg</strong>
                          </span>
                          <span>
                            <span class="analysis-value" data-analysis-value="calories_per_kg">
                              {% render 'significant', places: 1, value: nutrition_calories_per_kg %}
                            </span>
                            <span class="analysis-unit">kcal</span>
                          </span>
                        </analysis-row>

                        <analysis-row class="analysis-row analysis-table-row">
                          <span>
                            <strong>per cup</strong>
                          </span>
                          <span>
                            <span class="analysis-value" data-analysis-value="calories_per_cup">
                              {% render 'significant', places: 1, value: nutrition_calories_per_cup %}
                            </span>
                            <span class="analysis-unit">kcal</span>
                          </span>
                        </analysis-row>

                        <analysis-row class="analysis-row analysis-table-row">
                          <span>
                            <strong>per oz</strong>
                          </span>
                          <span>
                            <span class="analysis-value" data-analysis-value="calories_per_oz">
                              {% render 'significant', places: 1, value: nutrition_calories_per_oz %}
                            </span>
                            <span class="analysis-unit">kcal</span>
                          </span>
                        </analysis-row>

                        <analysis-row class="analysis-row analysis-table-row">
                          <span>
                            <strong>per day</strong>
                          </span>
                          <span>
                            <span class="analysis-value" data-analysis-value="calories_per_day">
                              {% render 'significant', places: 1, value: nutrition_calories_per_day %}
                            </span>
                            <span class="analysis-unit">kcal</span>
                          </span>
                        </analysis-row>

                      </div>

                    </analysis-category>

                    <!-- Calories from Nutrients -->

                    <analysis-category class="analysis-category">

                      <button class="analysis-category__header" is="toggle-button" aria-expanded="true" aria-controls="{{ section.id }}--analysis-category--calories-from-nutrients">
                        <div class="analysis-category__button">
                          <span class="visually-hidden">Toggle</span>
                        </div>
                        <div class="analysis-table-row">
                          <span><strong>Calories from Nutrients</strong></span>
                        </div>
                      </button>

                      <div class="analysis-category__content" id="{{ section.id }}--analysis-category--calories-from-nutrients">

                        <analysis-row class="analysis-row analysis-table-row">
                          <span></span>
                          <span>
                            <strong>per 1000cal</strong>
                          </span>
                          <span>
                            <strong>per day</strong>
                          </span>
                        </analysis-row>

                        <analysis-row class="analysis-row analysis-table-row">
                          <span>From Protein</span>
                          <span>
                            <span class="analysis-value" data-analysis-value="calories_from_protein">
                              {% render 'significant', places: 1, value: nutrition_calories_from_protein %}
                            </span>
                            <span class="analysis-unit">kcal</span>
                          </span>
                          <span>
                            <span class="analysis-value" data-analysis-value="calories_from_protein_perday">
                              {% render 'significant', places: 1, value: nutrition_calories_from_protein_perday %}
                            </span>
                            <span class="analysis-unit">kcal</span>
                          </span>
                        </analysis-row>

                        <analysis-row class="analysis-row analysis-table-row">
                          <span>From Fat</span>
                          <span>
                            <span class="analysis-value" data-analysis-value="calories_from_fat">
                              {% render 'significant', places: 1, value: nutrition_calories_from_fat %}
                            </span>
                            <span class="analysis-unit">kcal</span>
                          </span>
                          <span>
                            <span class="analysis-value" data-analysis-value="calories_from_fat_perday">
                              {% render 'significant', places: 1, value: nutrition_calories_from_fat_perday %}
                            </span>
                            <span class="analysis-unit">kcal</span>
                          </span>
                        </analysis-row>

                        <analysis-row class="analysis-row analysis-table-row">
                          <span>From Carbohydrate</span>
                          <span>
                            <span class="analysis-value" data-analysis-value="calories_from_carbohydrates">
                              {% render 'significant', places: 1, value: nutrition_calories_from_carbohydrates %}
                            </span>
                            <span class="analysis-unit">kcal</span>
                          </span>
                          <span>
                            <span class="analysis-value" data-analysis-value="calories_from_carbohydrates_perday">
                              {% render 'significant', places: 1, value: nutrition_calories_from_carbohydrates_perday %}
                            </span>
                            <span class="analysis-unit">kcal</span>
                          </span>
                        </analysis-row>

                      </div>

                    </analysis-category>

                    <!-- BASIC COMPONENTS -->

                    <analysis-header class="analysis-header">
                      <span class="analysis-header__title heading h6">{{ 'nutrition.analysis.basic_components' | t }}</span>
                    </analysis-header>

                    <!-- Macronutrients -->

                    <div class="analysis-table">

                      <analysis-row class="analysis-row analysis-table-row">
                        <span></span>
                        <span>
                          <strong>per 1000cal</strong>
                        </span>
                        <span>
                          <strong>per day</strong>
                        </span>
                      </analysis-row>
  
                      <analysis-row class="analysis-row analysis-table-row">
                        <span>
                          <strong>Protein</strong>
                          <span class="text--subdued">(min)</span>
                        </span>
                        <span>
                          <span>
                            <span class="analysis-value" data-analysis-value="protein">
                              {% render 'significant', places: 1, value: protein %}
                            </span>
                            <span class="analysis-unit">g</span>
                          </span>
                          <br>
                          <span class="text--subdued">({{ product_protein_rating }})</span>
                        </span>
                        <span>
                          <span>
                            <span class="analysis-value" data-analysis-value="protein_perday">
                              {% render 'significant', places: 1, value: protein_perday %}
                            </span>
                            <span class="analysis-unit">g</span>
                          </span>
                          <br>
                          <span class="text--subdued">({{ product_protein_rating }})</span>
                        </span>
                      </analysis-row>
  
                      <analysis-row class="analysis-row analysis-table-row">
                        <span>
                          <strong>Fat</strong>
                          <span class="text--subdued">(min)</span>
                        </span>
                        <span>
                          <span>
                            <span class="analysis-value" data-analysis-value="fat">
                              {% render 'significant', places: 1, value: fat %}
                            </span>
                            <span class="analysis-unit">g</span>
                          </span>
                          <br>
                          <span class="text--subdued">({{ product_fat_rating }})</span>
                        </span>
                        <span>
                          <span>
                            <span class="analysis-value" data-analysis-value="fat_perday">
                              {% render 'significant', places: 1, value: fat_perday %}
                            </span>
                            <span class="analysis-unit">g</span>
                          </span>
                          <br>
                          <span class="text--subdued">({{ product_fat_rating }})</span>
                        </span>
                      </analysis-row>
  
                      <analysis-row class="analysis-row analysis-table-row">
                        <span>
                          <strong>Carbohydrate <sup>†</sup></strong>
                        </span>
                        <span>
                          <span>
                            <span class="analysis-value" data-analysis-value="carbohydrate">
                              {% render 'significant', places: 1, value: nutrition_carbohydrate %}
                            </span>
                            <span class="analysis-unit">g</span>
                          </span>
                          <br>
                          <span class="text--subdued">({{ product_carbohydrate_rating }})</span>
                        </span>
                        <span>
                          <span>
                            <span class="analysis-value" data-analysis-value="carbohydrate_perday">
                              {% render 'significant', places: 1, value: nutrition_carbohydrate_perday %}
                            </span>
                            <span class="analysis-unit">g</span>
                          </span>
                          <br>
                          <span class="text--subdued">({{ product_carbohydrate_rating }})</span>
                        </span>
                      </analysis-row>

                    </div>


                    <!-- Nutrients -->

                    <analysis-category class="analysis-category">

                      <button class="analysis-category__header" is="toggle-button" aria-expanded="false" aria-controls="{{ section.id }}--analysis-category--calories-from-nutrients">
                        <div class="analysis-category__button">
                          <span class="visually-hidden">Toggle</span>
                        </div>
                        <div class="analysis-table-row">
                          <span><strong>{{ 'nutrition.analysis.nutrients' | t }}</strong></span>
                        </div>
                      </button>

                      <div class="analysis-category__content" id="{{ section.id }}--analysis-category--calories-from-nutrients">

                        <analysis-row class="analysis-row analysis-table-row">
                          <span>
                            <span>Dietary Fiber</span>
                          </span>
                          <span>
                            <span class="analysis-value" data-analysis-value="fiber">
                              {% render 'significant', places: 2, value: nutrition_fiber %}
                            </span>
                            <span class="analysis-unit">g</span>
                          </span>
                          <span>
                            <span class="analysis-value" data-analysis-value="fiber_perday">
                              {% render 'significant', places: 2, value: nutrition_fiber_perday %}
                            </span>
                            <span class="analysis-unit">g</span>
                          </span>
                        </analysis-row>

                        <analysis-row class="analysis-row analysis-table-row">
                          <span>
                            <span>Ash</span>
                          </span>
                          <span>
                            <span class="analysis-value" data-analysis-value="ash">
                              {% render 'significant', places: 2, value: nutrition_ash %}
                            </span>
                            <span class="analysis-unit">g</span>
                          </span>
                          <span>
                            <span>
                            <span class="analysis-value" data-analysis-value="ash_perday">
                              {% render 'significant', places: 2, value: nutrition_ash_perday %}
                            </span>
                            <span class="analysis-unit">g</span>
                          </span>
                          </span>
                        </analysis-row>

                        <analysis-row class="analysis-row analysis-table-row">
                          <span>
                            <span>Calcium</span>
                          </span>
                          <span>
                            <span class="analysis-value" data-analysis-value="calcium">
                              {% render 'significant', places: 2, value: nutrition_calcium %}
                            </span>
                            <span class="analysis-unit">g</span>
                          </span>
                          <span>
                            <span class="analysis-value" data-analysis-value="calcium_perday">
                              {% render 'significant', places: 2, value: nutrition_calcium_perday %}
                            </span>
                            <span class="analysis-unit">g</span>
                          </span>
                        </analysis-row>

                        <analysis-row class="analysis-row analysis-table-row">
                          <span>
                            <span>Phosphorus</span>
                          </span>
                          <span>
                            <span class="analysis-value" data-analysis-value="phosphorus">
                              {% render 'significant', places: 2, value: nutrition_phosphorus %}
                            </span>
                            <span class="analysis-unit">g</span>
                          </span>
                          <span>
                            <span class="analysis-value" data-analysis-value="phosphorus_perday">
                              {% render 'significant', places: 2, value: nutrition_phosphorus_perday %}
                            </span>
                            <span class="analysis-unit">g</span>
                          </span>
                        </analysis-row>

                        <analysis-row class="analysis-row analysis-table-row">
                          <span>
                            <span>Sodium</span>
                          </span>
                          <span>
                            <span class="analysis-value" data-analysis-value="sodium">
                              {% render 'significant', places: 2, value: nutrition_sodium %}
                            </span>
                            <span class="analysis-unit">g</span>
                          </span>
                          <span>
                            <span class="analysis-value" data-analysis-value="sodium_perday">
                              {% render 'significant', places: 2, value: nutrition_sodium_perday %}
                            </span>
                            <span class="analysis-unit">g</span>
                          </span>
                        </analysis-row>

                        <analysis-row class="analysis-row analysis-table-row">
                          <span>
                            <span>Taurine</span>
                          </span>
                          <span>
                            <span class="analysis-value" data-analysis-value="taurine">
                              {% render 'significant', places: 2, value: nutrition_taurine %}
                            </span>
                            <span class="analysis-unit">g</span>
                          </span>
                          <span>
                            <span class="analysis-value" data-analysis-value="taurine_perday">
                              {% render 'significant', places: 2, value: nutrition_taurine_perday %}
                            </span>
                            <span class="analysis-unit">g</span>
                          </span>
                        </analysis-row>

                        <analysis-row class="analysis-row analysis-table-row">
                          <span>
                            <span>EPA</span>
                          </span>
                          <span>
                            <span class="analysis-value" data-analysis-value="epa">
                              {% render 'significant', places: 2, value: nutrition_epa %}
                            </span>
                            <span class="analysis-unit">g</span>
                          </span>
                          <span>
                            <span class="analysis-value" data-analysis-value="epa_perday">
                              {% render 'significant', places: 2, value: nutrition_epa_perday %}
                            </span>
                            <span class="analysis-unit">g</span>
                          </span>
                        </analysis-row>

                        <analysis-row class="analysis-row analysis-table-row">
                          <span>
                            <span>DHA</span>
                          </span>
                          <span>
                            <span class="analysis-value" data-analysis-value="dha">
                              {% render 'significant', places: 2, value: nutrition_dha %}
                            </span>
                            <span class="analysis-unit">g</span>
                          </span>
                          <span>
                            <span class="analysis-value" data-analysis-value="dha_perday">
                              {% render 'significant', places: 2, value: nutrition_dha_perday %}
                            </span>
                            <span class="analysis-unit">g</span>
                          </span>
                        </analysis-row>

                        <analysis-row class="analysis-row analysis-table-row">
                          <span>
                            <span>Omega-6</span>
                          </span>
                          <span>
                            <span class="analysis-value" data-analysis-value="omega6">
                              {% render 'significant', places: 2, value: nutrition_omega6 %}
                            </span>
                            <span class="analysis-unit">g</span>
                          </span>
                          <span>
                            <span class="analysis-value" data-analysis-value="omega6_perday">
                              {% render 'significant', places: 2, value: nutrition_omega6_perday %}
                            </span>
                            <span class="analysis-unit">g</span>
                          </span>
                        </analysis-row>

                      </div>

                    </analysis-category>

                    
                    <!-- Vitamins -->

                    <analysis-category class="analysis-category">

                      <button class="analysis-category__header" is="toggle-button" aria-expanded="false" aria-controls="{{ section.id }}--analysis-category--calories-from-nutrients">
                        <div class="analysis-category__button">
                          <span class="visually-hidden">Toggle</span>
                        </div>
                        <div class="analysis-table-row">
                          <span><strong>Vitamins</strong></span>
                        </div>
                      </button>

                      <div class="analysis-category__content" id="{{ section.id }}--analysis-category--calories-from-nutrients">

                        <analysis-row class="analysis-row analysis-table-row">
                          <span>
                            <span>Vitamin A</span>
                          </span>
                          <span>
                            <span class="analysis-value" data-analysis-value="vitamin_a">
                              {% render 'significant', places: 2, value: nutrition_vitamin_a %}
                            </span>
                            <span class="analysis-unit">IU</span>
                          </span>
                          <span>
                            <span class="analysis-value" data-analysis-value="vitamin_a_perday">
                              {% render 'significant', places: 2, value: nutrition_vitamin_a_perday %}
                            </span>
                            <span class="analysis-unit">IU</span>
                          </span>
                        </analysis-row>

                        <analysis-row class="analysis-row analysis-table-row">
                          <span>
                            <span>Vitamin B1</span>
                          </span>
                          <span>
                            <span class="analysis-value" data-analysis-value="vitamin_b1">
                              {% render 'significant', places: 2, value: nutrition_vitamin_b1 %}
                            </span>
                            <span class="analysis-unit">mg</span>
                          </span>
                          <span>
                            <span class="analysis-value" data-analysis-value="vitamin_b1_perday">
                              {% render 'significant', places: 2, value: nutrition_vitamin_b1_perday %}
                            </span>
                            <span class="analysis-unit">mg</span>
                          </span>
                        </analysis-row>

                        <analysis-row class="analysis-row analysis-table-row">
                          <span>
                            <span>Vitamin B2</span>
                          </span>
                          <span>
                            <span class="analysis-value" data-analysis-value="vitamin_b2">
                              {% render 'significant', places: 2, value: nutrition_vitamin_b2 %}
                            </span>
                            <span class="analysis-unit">mg</span>
                          </span>
                          <span>
                            <span class="analysis-value" data-analysis-value="vitamin_b2_perday">
                              {% render 'significant', places: 2, value: nutrition_vitamin_b2_perday %}
                            </span>
                            <span class="analysis-unit">mg</span>
                          </span>
                        </analysis-row>

                        <analysis-row class="analysis-row analysis-table-row">
                          <span>
                            <span>Vitamin B3</span>
                          </span>
                          <span>
                            <span class="analysis-value" data-analysis-value="vitamin_b3">
                              {% render 'significant', places: 2, value: nutrition_vitamin_b3 %}
                            </span>
                            <span class="analysis-unit">mg</span>
                          </span>
                          <span>
                            <span class="analysis-value" data-analysis-value="vitamin_b3_perday">
                              {% render 'significant', places: 2, value: nutrition_vitamin_b3_perday %}
                            </span>
                            <span class="analysis-unit">mg</span>
                          </span>
                        </analysis-row>

                        <analysis-row class="analysis-row analysis-table-row">
                          <span>
                            <span>Vitamin B6</span>
                          </span>
                          <span>
                            <span class="analysis-value" data-analysis-value="vitamin_b6">
                              {% render 'significant', places: 2, value: nutrition_vitamin_b6 %}
                            </span>
                            <span class="analysis-unit">mg</span>
                          </span>
                          <span>
                            <span class="analysis-value" data-analysis-value="vitamin_b6_perday">
                              {% render 'significant', places: 2, value: nutrition_vitamin_b6_perday %}
                            </span>
                            <span class="analysis-unit">mg</span>
                          </span>
                        </analysis-row>

                        <analysis-row class="analysis-row analysis-table-row">
                          <span>
                            <span>Vitamin B12</span>
                          </span>
                          <span>
                            <span class="analysis-value" data-analysis-value="vitamin_b12">
                              {% render 'significant', places: 2, value: nutrition_vitamin_b12 %}
                            </span> 
                            <span class="analysis-unit">mcg</span>
                          </span>
                          <span>
                            <span class="analysis-value" data-analysis-value="vitamin_b12_perday">
                              {% render 'significant', places: 2, value: nutrition_vitamin_b12_perday %}
                            </span> 
                            <span class="analysis-unit">mcg</span>
                          </span>
                        </analysis-row>

                        <analysis-row class="analysis-row analysis-table-row">
                          <span>
                            <span>Vitamin D</span>
                          </span>
                          <span>
                            <span class="analysis-value" data-analysis-value="vitamin_d">
                              {% render 'significant', places: 2, value: nutrition_vitamin_d %}
                            </span>
                            <span class="analysis-unit">IU</span>
                          </span>
                          <span>
                            <span class="analysis-value" data-analysis-value="vitamin_d_perday">
                              {% render 'significant', places: 2, value: nutrition_vitamin_d_perday %}
                            </span>
                            <span class="analysis-unit">IU</span>
                          </span>
                        </analysis-row>

                        <analysis-row class="analysis-row analysis-table-row">
                          <span>
                            <span>Folic Acid</span>
                          </span>
                          <span>
                            <span class="analysis-value" data-analysis-value="folic_acid">
                              {% render 'significant', places: 2, value: nutrition_folic_acid %}
                            </span> 
                            <span class="analysis-unit">mcg</span>
                          </span>
                          <span>
                            <span class="analysis-value" data-analysis-value="folic_acid_perday">
                              {% render 'significant', places: 2, value: nutrition_folic_acid_perday %}
                            </span> 
                            <span class="analysis-unit">mcg</span>
                          </span>
                        </analysis-row>

                        <analysis-row class="analysis-row analysis-table-row">
                          <span>
                            <span>Vitamin E</span>
                          </span>
                          <span>
                            <span class="analysis-value" data-analysis-value="vitamin_e">
                              {% render 'significant', places: 2, value: nutrition_vitamin_e %}
                            </span>
                            <span class="analysis-unit">IU</span>
                          </span>
                          <span>
                            <span class="analysis-value" data-analysis-value="vitamin_e_perday">
                              {% render 'significant', places: 2, value: nutrition_vitamin_e_perday %}
                            </span>
                            <span class="analysis-unit">IU</span>
                          </span>
                        </analysis-row>

                      </div>

                    </analysis-category>


                    <!-- Minerals -->

                    <analysis-category class="analysis-category">

                      <button class="analysis-category__header" is="toggle-button" aria-expanded="false" aria-controls="{{ section.id }}--analysis-category--calories-from-nutrients">
                        <div class="analysis-category__button">
                          <span class="visually-hidden">Toggle</span>
                        </div>
                        <div class="analysis-table-row">
                          <span><strong>Minerals</strong></span>
                        </div>
                      </button>

                      <div class="analysis-category__content" id="{{ section.id }}--analysis-category--calories-from-nutrients">

                        <analysis-row class="analysis-row analysis-table-row">
                          <span>
                            <span>Magnesium</span>
                          </span>
                          <span>
                            <span class="analysis-value" data-analysis-value="magnesium">
                              {% render 'significant', places: 2, value: nutrition_magnesium %}
                            </span>
                            <span class="analysis-unit">mg</span>
                          </span>
                          <span>
                            <span class="analysis-value" data-analysis-value="magnesium_perday">
                              {% render 'significant', places: 2, value: nutrition_magnesium_perday %}
                            </span>
                            <span class="analysis-unit">mg</span>
                          </span>
                        </analysis-row>

                        <analysis-row class="analysis-row analysis-table-row">
                          <span>
                            <span>Potassium</span>
                          </span>
                          <span>
                            <span class="analysis-value" data-analysis-value="potassium">
                              {% render 'significant', places: 2, value: nutrition_potassium %}
                            </span>
                            <span class="analysis-unit">mg</span>
                          </span>
                          <span>
                            <span class="analysis-value" data-analysis-value="potassium_perday">
                              {% render 'significant', places: 2, value: nutrition_potassium_perday %}
                            </span>
                            <span class="analysis-unit">mg</span>
                          </span>
                        </analysis-row>

                        <analysis-row class="analysis-row analysis-table-row">
                          <span>
                            <span>Iron</span>
                          </span>
                          <span>
                            <span class="analysis-value" data-analysis-value="iron">
                              {% render 'significant', places: 2, value: nutrition_iron %}
                            </span>
                            <span class="analysis-unit">mg</span>
                          </span>
                          <span>
                            <span class="analysis-value" data-analysis-value="iron_perday">
                              {% render 'significant', places: 2, value: nutrition_iron_perday %}
                            </span>
                            <span class="analysis-unit">mg</span>
                          </span>
                        </analysis-row>

                        <analysis-row class="analysis-row analysis-table-row">
                          <span>
                            <span>Zinc</span>
                          </span>
                          <span>
                            <span class="analysis-value" data-analysis-value="zinc">
                              {% render 'significant', places: 2, value: nutrition_zinc %}
                            </span>
                            <span class="analysis-unit">mg</span>
                          </span>
                          <span>
                            <span class="analysis-value" data-analysis-value="zinc_perday">
                              {% render 'significant', places: 2, value: nutrition_zinc_perday %}
                            </span>
                            <span class="analysis-unit">mg</span>
                          </span>
                        </analysis-row>

                        <analysis-row class="analysis-row analysis-table-row">
                          <span>
                            <span>Copper</span>
                          </span>
                          <span>
                            <span class="analysis-value" data-analysis-value="copper">
                              {% render 'significant', places: 2, value: nutrition_copper %}
                            </span>
                            <span class="analysis-unit">mg</span>
                          </span>
                          <span>
                            <span class="analysis-value" data-analysis-value="copper_perday">
                              {% render 'significant', places: 2, value: nutrition_copper_perday %}
                            </span>
                            <span class="analysis-unit">mg</span>
                          </span>
                        </analysis-row>

                        <analysis-row class="analysis-row analysis-table-row">
                          <span>
                            <span>Manganese</span>
                          </span>
                          <span>
                            <span class="analysis-value" data-analysis-value="manganese">
                              {% render 'significant', places: 2, value: nutrition_manganese %}
                            </span>
                            <span class="analysis-unit">mg</span>
                          </span>
                          <span>
                            <span class="analysis-value" data-analysis-value="manganese_perday">
                              {% render 'significant', places: 2, value: nutrition_manganese_perday %}
                            </span>
                            <span class="analysis-unit">mg</span>
                          </span>
                        </analysis-row>

                        <analysis-row class="analysis-row analysis-table-row">
                          <span>
                            <span>Iodine</span>
                          </span>
                          <span>
                            <span class="analysis-value" data-analysis-value="iodine">
                              {% render 'significant', places: 2, value: nutrition_iodine %}
                            </span> 
                            <span class="analysis-unit">mcg</span>
                          </span>
                          <span>
                            <span class="analysis-value" data-analysis-value="iodine_perday">
                              {% render 'significant', places: 2, value: nutrition_iodine_perday %}
                            </span> 
                            <span class="analysis-unit">mcg</span>
                          </span>
                        </analysis-row>

                        <analysis-row class="analysis-row analysis-table-row">
                          <span>
                            <span>Selenium</span>
                          </span>
                          <span>
                            <span class="analysis-value" data-analysis-value="selenium">
                              {% render 'significant', places: 2, value: nutrition_selenium %}
                            </span> 
                            <span class="analysis-unit">mcg</span>
                          </span>
                          <span>
                            <span class="analysis-value" data-analysis-value="selenium_perday">
                              {% render 'significant', places: 2, value: nutrition_selenium_perday %}
                            </span> 
                            <span class="analysis-unit">mcg</span>
                          </span>
                        </analysis-row>

                      </div>

                    </analysis-category>


                    <!-- Amino Acids -->

                    <analysis-category class="analysis-category">

                      <button class="analysis-category__header" is="toggle-button" aria-expanded="false" aria-controls="{{ section.id }}--analysis-category--calories-from-nutrients">
                        <div class="analysis-category__button">
                          <span class="visually-hidden">Toggle</span>
                        </div>
                        <div class="analysis-table-row">
                          <span><strong>Amino Acids</strong></span>
                        </div>
                      </button>

                      <div class="analysis-category__content" id="{{ section.id }}--analysis-category--calories-from-nutrients">

                        <analysis-row class="analysis-row analysis-table-row">
                          <span>
                            <span>Aginine</span>
                          </span>
                          <span>
                            <span class="analysis-value" data-analysis-value="aginine">
                              {% render 'significant', places: 2, value: nutrition_aginine %}
                            </span>
                            <span class="analysis-unit">g</span>
                          </span>
                          <span>
                            <span class="analysis-value" data-analysis-value="aginine_perday">
                              {% render 'significant', places: 2, value: nutrition_aginine_perday %}
                            </span>
                            <span class="analysis-unit">g</span>
                          </span>
                        </analysis-row>

                        <analysis-row class="analysis-row analysis-table-row">
                          <span>
                            <span>Cystine</span>
                          </span>
                          <span>
                            <span class="analysis-value" data-analysis-value="cystine">
                              {% render 'significant', places: 2, value: nutrition_cystine %}
                            </span>
                            <span class="analysis-unit">g</span>
                          </span>
                          <span>
                            <span class="analysis-value" data-analysis-value="cystine_perday">
                              {% render 'significant', places: 2, value: nutrition_cystine_perday %}
                            </span>
                            <span class="analysis-unit">g</span>
                          </span>
                        </analysis-row>

                        <analysis-row class="analysis-row analysis-table-row">
                          <span>
                            <span>Histidine</span>
                          </span>
                          <span>
                            <span class="analysis-value" data-analysis-value="histidine">
                              {% render 'significant', places: 2, value: nutrition_histidine %}
                            </span>
                            <span class="analysis-unit">g</span>
                          </span>
                          <span>
                            <span class="analysis-value" data-analysis-value="histidine_perday">
                              {% render 'significant', places: 2, value: nutrition_histidine_perday %}
                            </span>
                            <span class="analysis-unit">g</span>
                          </span>
                        </analysis-row>

                        <analysis-row class="analysis-row analysis-table-row">
                          <span>
                            <span>Isoleucine</span>
                          </span>
                          <span>
                            <span class="analysis-value" data-analysis-value="isoleucine">
                              {% render 'significant', places: 2, value: nutrition_isoleucine %}
                            </span>
                            <span class="analysis-unit">g</span>
                          </span>
                          <span>
                            <span class="analysis-value" data-analysis-value="isoleucine_perday">
                              {% render 'significant', places: 2, value: nutrition_isoleucine_perday %}
                            </span>
                            <span class="analysis-unit">g</span>
                          </span>
                        </analysis-row>

                        <analysis-row class="analysis-row analysis-table-row">
                          <span>
                            <span>Leucine</span>
                          </span>
                          <span>
                            <span class="analysis-value" data-analysis-value="leucine">
                              {% render 'significant', places: 2, value: nutrition_leucine %}
                            </span>
                            <span class="analysis-unit">g</span>
                          </span>
                          <span>
                            <span class="analysis-value" data-analysis-value="leucine_perday">
                              {% render 'significant', places: 2, value: nutrition_leucine_perday %}
                            </span>
                            <span class="analysis-unit">g</span>
                          </span>
                        </analysis-row>

                        <analysis-row class="analysis-row analysis-table-row">
                          <span>
                            <span>Lysine</span>
                          </span>
                          <span>
                            <span class="analysis-value" data-analysis-value="lysine">
                              {% render 'significant', places: 2, value: nutrition_lysine %}
                            </span>
                            <span class="analysis-unit">g</span>
                          </span>
                          <span>
                            <span class="analysis-value" data-analysis-value="lysine_perday">
                              {% render 'significant', places: 2, value: nutrition_lysine_perday %}
                            </span>
                            <span class="analysis-unit">g</span>
                          </span>
                        </analysis-row>

                        <analysis-row class="analysis-row analysis-table-row">
                          <span>
                            <span>Methionone</span>
                          </span>
                          <span>
                            <span class="analysis-value" data-analysis-value="methionone">
                              {% render 'significant', places: 2, value: nutrition_methionone %}
                            </span>
                            <span class="analysis-unit">g</span>
                          </span>
                          <span>
                            <span class="analysis-value" data-analysis-value="methionone_perday">
                              {% render 'significant', places: 2, value: nutrition_methionone_perday %}
                            </span>
                            <span class="analysis-unit">g</span>
                          </span>
                        </analysis-row>

                        <analysis-row class="analysis-row analysis-table-row">
                          <span>
                            <span>Phenylalanine</span>
                          </span>
                          <span>
                            <span class="analysis-value" data-analysis-value="phenylalanine">
                              {% render 'significant', places: 2, value: nutrition_phenylalanine %}
                            </span>
                            <span class="analysis-unit">g</span>
                          </span>
                          <span>
                            <span class="analysis-value" data-analysis-value="phenylalanine_perday">
                              {% render 'significant', places: 2, value: nutrition_phenylalanine_perday %}
                            </span>
                            <span class="analysis-unit">g</span>
                          </span>
                        </analysis-row>

                        <analysis-row class="analysis-row analysis-table-row">
                          <span>
                            <span>Threonine</span>
                          </span>
                          <span>
                            <span class="analysis-value" data-analysis-value="threonine">
                              {% render 'significant', places: 2, value: nutrition_threonine %}
                            </span>
                            <span class="analysis-unit">g</span>
                          </span>
                          <span>
                            <span class="analysis-value" data-analysis-value="threonine_perday">
                              {% render 'significant', places: 2, value: nutrition_threonine_perday %}
                            </span>
                            <span class="analysis-unit">g</span>
                          </span>
                        </analysis-row>

                        <analysis-row class="analysis-row analysis-table-row">
                          <span>
                            <span>Tryptophan</span>
                          </span>
                          <span>
                            <span class="analysis-value" data-analysis-value="tryptophan">
                              {% render 'significant', places: 2, value: nutrition_tryptophan %}
                            </span>
                            <span class="analysis-unit">g</span>
                          </span>
                          <span>
                            <span class="analysis-value" data-analysis-value="tryptophan_perday">
                              {% render 'significant', places: 2, value: nutrition_tryptophan_perday %}
                            </span>
                            <span class="analysis-unit">g</span>
                          </span>
                        </analysis-row>

                        <analysis-row class="analysis-row analysis-table-row">
                          <span>
                            <span>Tyrosine</span>
                          </span>
                          <span>
                            <span class="analysis-value" data-analysis-value="tyrosine">
                              {% render 'significant', places: 2, value: nutrition_tyrosine %}
                            </span>
                            <span class="analysis-unit">g</span>
                          </span>
                          <span>
                            <span class="analysis-value" data-analysis-value="tyrosine_perday">
                              {% render 'significant', places: 2, value: nutrition_tyrosine_perday %}
                            </span>
                            <span class="analysis-unit">g</span>
                          </span>
                        </analysis-row>

                        <analysis-row class="analysis-row analysis-table-row">
                          <span>
                            <span>Valine</span>
                          </span>
                          <span>
                            <span class="analysis-value" data-analysis-value="valine">
                              {% render 'significant', places: 2, value: nutrition_valine %}
                            </span>
                            <span class="analysis-unit">g</span>
                          </span>
                          <span>
                            <span class="analysis-value" data-analysis-value="valine_perday">
                              {% render 'significant', places: 2, value: nutrition_valine_perday %}
                            </span>
                            <span class="analysis-unit">g</span>
                          </span>
                        </analysis-row>

                      </div>

                    </analysis-category>

                  </div>

                  <div class="nutritional-analysis__footer">

                    <p>† calculated value</p>
                    <p><strong data-product-title>Beef + Potato</strong> is formulated to meet the nutritional levels established by the AAFCO Dog Nutrient Profiles for all stages of life including growth. For additional diet information, visit <a href="http://wynwooddogfood.com" target="_blank">wynwooddogfood.com</a></p>

                  </div>

                </div>
  
              </div>

              <hr class="hr--narrow">

              <div class="spaced-content text--xsmall">
  
                <div class="spaced-content spaced-content--tight nutrition-ingredients">
                  <h4 class="heading h4">{{ 'nutrition.content.ingredients_heading' | t }}</h4>
                  <div data-product-ingredients>{{ product_ingredients }}</div>
                </div>
  
                <hr class="hr--narrow">

                <div class="spaced-content spaced-content--tight nutrition-description">
                  <h4 class="heading h4">{{ 'nutrition.content.description_heading' | t }}</h4>
                  <div data-product-description>{{ product_description }}</div>
                </div>

                <div class="spaced-content spaced-content--tight nutrition-indications">
                  <h5 class="heading h5">{{ 'nutrition.content.indications' | t }}</h5>
                  <div data-product-indications>{{ product_indications | replace: "<ul>", '<ul class="list--unstyled">' }}</div>
                </div>

                <div class="spaced-content spaced-content--tight nutrition-contraindications">
                  <h5 class="heading h5">{{ 'nutrition.content.contraindications' | t }}</h5>
                  <div data-product-contraindications>{{ product_contra_indications | replace: "<ul>", '<ul class="list--unstyled">' }}</div>
                </div>
  
                <div class="flex justify-content-center">
                  <a class="button button--primary" href="{{ product_url }}" target="_blank" data-product-view-link>View Product</a>
                </div>
  
              </div>
  
            </div>

          </div>

        </div>

      </feeding-calculator-nutrition>

    </div>

  </section>

{%- endif -%}

{% schema %}
{
  "name": "🐕 Feeding Calculator",
  "class": "shopify-section--feeding-calculator",
  "disabled_on": {
    "groups": ["header", "custom.overlay"]
  },
  "settings": [
    {
      "type": "header",
      "content": "🐕 Nutrition"
    },
    {
      "type": "text",
      "id": "nutrition_subheading",
      "label": "Subheading",
      "default": "Nutritional Info"
    },
    {
      "type": "paragraph",
      "content": "Summary"
    },
    {
      "type": "text",
      "id": "summary_heading",
      "label": "Heading",
      "default": "Brief Summary"
    },
    {
      "type": "header",
      "content": "🐕 Layout"
    },
    {
      "type": "select",
      "id": "container_width",
      "label": "Container Width",
      "options": [
        {
          "value": "smaller",
          "label": "Smaller"
        },
        {
          "value": "small",
          "label": "Small"
        },
        {
          "value": "",
          "label": "Default"
        }
      ],
      "default": ""
    },
    {
      "type": "checkbox",
      "id": "container_padding",
      "label": "Container Padding",
      "default": false
    },
    {
      "type": "select",
      "id": "text_alignment",
      "label": "Text alignment",
      "options": [
        {
          "value": "left",
          "label": "Left"
        },
        {
          "value": "center",
          "label": "Center"
        },
        {
          "value": "right",
          "label": "Right"
        }
      ],
      "default": "center"
    },
    {
      "type": "header",
      "content": "🐕 Calculator"
    },
    {
      "type": "collection_list",
      "id": "collections",
      "label": "Collections"
    },
    {
      "type": "image_picker",
      "id": "calculator_icon",
      "label": "Icon"
    },
    {
      "type": "text",
      "id": "subheading",
      "label": "Subheading",
      "default": "Subheading"
    },
    {
      "type": "text",
      "id": "title",
      "label": "Heading",
      "default": "Talk about your brand"
    },
    {
      "type": "richtext",
      "id": "content",
      "label": "Content",
      "default": "<p>Use this text to share information about your brand with your customers. Describe a product, share announcements, or welcome customers to your store.</p>"
    },
    {
      "type": "paragraph",
      "content": "Weight Range"
    },
    {
      "type": "text",
      "id": "label",
      "label": "Label",
      "default": "Dog Weight"
    },
    {
      "type": "text",
      "id": "weight_title",
      "label": "Title"
    },
    {
      "type": "number",
      "id": "weight_min",
      "label": "Minimum Weight",
      "default": 1
    },
    {
      "type": "number",
      "id": "weight_max",
      "label": "Maximum Weight",
      "default": 180
    },
    {
      "type": "text",
      "id": "unit",
      "label": "Unit",
      "default": "lbs"
    },
    {
      "type": "checkbox",
      "id": "show_add_to_cart",
      "label": "Show Add to Cart",
      "default": false,
      "info": "Show an add to cart button that lets you add a week's worth of food to cart."
    },
    {
      "type": "header",
      "content": "🐕 Results"
    },
    {
      "type": "text",
      "id": "results_subheading",
      "label": "Subheading"
    },
    {
      "type": "text",
      "id": "results_title",
      "label": "Heading",
      "default": "Your Results"
    },
    {
      "type": "richtext",
      "id": "results_content",
      "label": "Content",
      "default": "<p>Use this text to share information about your brand with your customers. Describe a product, share announcements, or welcome customers to your store.</p>"
    },
    {
      "type": "url",
      "id": "button_link",
      "label": "Button Link"
    },
    {
      "type": "text",
      "id": "button_text",
      "label": "Button Text"
    },
    {
      "type": "header",
      "content": "🐕 Nutrition"
    },
    {
      "type": "checkbox",
      "id": "nutrition_enable",
      "label": "Enable Nutrition Details",
      "default": true,
      "info": "Shows a button in the calculator results that retrieves nutrition information for the selected product."
    },
    {
      "type": "header",
      "content": "🐕 ADVANCED"
    },
    {
      "type": "text",
      "id": "calorie_multiplier",
      "label": "Calorie Multiplier",
      "default": "1",
      "info": "This multiplies the calorie calculation by the specified amount. (Default is 1. Used for weight loss calculations where we use a value of 0.75)"
    },
    {
      "type": "header",
      "content": "🐕 Colors"
    },
    {
      "type": "color",
      "id": "background",
      "label": "Background",
      "default": "rgba(0,0,0,0)"
    },
    {
      "type": "color",
      "id": "text_color",
      "label": "Text color",
      "default": "rgba(0,0,0,0)"
    },
    {
      "type": "color",
      "id": "subheading_color",
      "label": "Subheading color",
      "default": "rgba(0,0,0,0)"
    },
    {
      "type": "color",
      "id": "button_background",
      "label": "Button background",
      "default": "rgba(0,0,0,0)"
    }
  ],
  "presets": [
    {
      "name": "🐕 Feeding Calculator",
      "settings": {}
    }
  ]
}
{% endschema %}