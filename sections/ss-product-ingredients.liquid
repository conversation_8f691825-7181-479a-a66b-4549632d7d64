{% comment %}
---------------------------------------------------------
Copyright © 2023 Section Store. All rights reserved.
Unauthorized copying, modification, distribution, or use
of this code or any portion of it, is strictly prohibited.
Violators will be prosecuted to the fullest extent of the law.
For inquiries or permissions, contact <EMAIL>
---------------------------------------------------------
{% endcomment %}

{%- liquid 
  assign padding_horizontal = section.settings.padding_horizontal
  assign padding_horizontal_mobile = section.settings.padding_horizontal_mobile
  assign padding_top = section.settings.padding_top
  assign padding_bottom = section.settings.padding_bottom
  assign border_color = section.settings.border_color
  assign border_thickness = section.settings.border_thickness
  assign margin_top = section.settings.margin_top
  assign margin_bottom = section.settings.margin_bottom
  assign background_color = section.settings.background_color
  assign background_gradient = section.settings.background_gradient
  assign full_width = section.settings.full_width
  assign content_width = section.settings.content_width
  assign lazy = section.settings.lazy

  assign heading = section.settings.heading
  assign heading_size = section.settings.heading_size
  assign heading_size_mobile = section.settings.heading_size_mobile
  assign heading_color = section.settings.heading_color
  assign heading_custom = section.settings.heading_custom
  assign heading_font = section.settings.heading_font
  assign heading_height = section.settings.heading_height
  assign heading_align = section.settings.heading_align
  assign heading_align_mobile = section.settings.heading_align_mobile
  assign heading_position = section.settings.heading_position

  assign grid_gap = section.settings.grid_gap
  assign grid_gap_mobile = section.settings.grid_gap_mobile
  assign grid_bg_color = section.settings.grid_bg_color
  assign grid_bg_gradient_color = section.settings.grid_bg_gradient_color
  assign grid_border_color = section.settings.grid_border_color
  assign grid_padding_vertical = section.settings.grid_padding_vertical
  assign grid_padding_horizontal = section.settings.grid_padding_horizontal
  assign grid_radius = section.settings.grid_radius
  assign grid_border_thickness = section.settings.grid_border_thickness
  assign grid_mt = section.settings.grid_mt

  assign image = section.settings.image
  assign image_width_mobile = section.settings.image_width_mobile
  assign image_width = section.settings.image_width
  assign image_ratio = section.settings.image_ratio
  assign image_radius = section.settings.image_radius
  assign image_border_thickness = section.settings.image_border_thickness
  assign image_border_color = section.settings.image_border_color
  assign image_shadow = section.settings.image_shadow
  assign image_shadow_color = section.settings.image_shadow_color
  assign image_hide_desktop = section.settings.image_hide_desktop
  assign image_hide_mobile = section.settings.image_hide_mobile
  assign image_position_mobile = section.settings.image_position_mobile
  assign image_position = section.settings.image_position

  assign content_bg_color = section.settings.content_bg_color
  assign content_border_color = section.settings.content_border_color
  assign content_padding_vertical = section.settings.content_padding_vertical
  assign content_padding_horizontal = section.settings.content_padding_horizontal
  assign content_radius = section.settings.content_radius
  assign content_border_thickness = section.settings.content_border_thickness

  assign ingredients_title_size = section.settings.ingredients_title_size
  assign ingredients_title_size_mobile = section.settings.ingredients_title_size_mobile
  assign ingredients_title_color = section.settings.ingredients_title_color
  assign ingredients_title_custom = section.settings.ingredients_title_custom
  assign ingredients_title_font = section.settings.ingredients_title_font
  assign ingredients_title_height = section.settings.ingredients_title_height
  assign ingredients_title_align = section.settings.ingredients_title_align
  assign ingredients_title_align_mobile = section.settings.ingredients_title_align_mobile
  assign ingredients_title_position = section.settings.ingredients_title_position

  assign list_gap_mobile = section.settings.list_gap_mobile
  assign list_gap = section.settings.list_gap
  assign list_mt = section.settings.list_mt

  assign ingredient_size = section.settings.ingredient_size
  assign ingredient_size_mobile = section.settings.ingredient_size_mobile
  assign ingredient_color = section.settings.ingredient_color
  assign ingredient_hover_color = section.settings.ingredient_hover_color
  assign ingredient_custom = section.settings.ingredient_custom
  assign ingredient_font = section.settings.ingredient_font
  assign ingredient_height = section.settings.ingredient_height
  assign ingredient_underline = section.settings.ingredient_underline

  assign other_ingredients_title = section.settings.other_ingredients_title
  assign other_ingredients_title_size = section.settings.other_ingredients_title_size
  assign other_ingredients_title_size_mobile = section.settings.other_ingredients_title_size_mobile
  assign other_ingredients_title_color = section.settings.other_ingredients_title_color
  assign other_ingredients_title_custom = section.settings.other_ingredients_title_custom
  assign other_ingredients_title_font = section.settings.other_ingredients_title_font
  assign other_ingredients_title_height = section.settings.other_ingredients_title_height
  assign other_ingredients_title_align = section.settings.other_ingredients_title_align
  assign other_ingredients_title_align_mobile = section.settings.other_ingredients_title_align_mobile
  assign other_ingredients_title_mt = section.settings.other_ingredients_title_mt

  assign other_ingredients_text = section.settings.other_ingredients_text
  assign other_ingredients_text_size = section.settings.other_ingredients_text_size
  assign other_ingredients_text_size_mobile = section.settings.other_ingredients_text_size_mobile
  assign other_ingredients_text_color = section.settings.other_ingredients_text_color
  assign other_ingredients_text_custom = section.settings.other_ingredients_text_custom
  assign other_ingredients_text_font = section.settings.other_ingredients_text_font
  assign other_ingredients_text_height = section.settings.other_ingredients_text_height
  assign other_ingredients_text_align = section.settings.other_ingredients_text_align
  assign other_ingredients_text_align_mobile = section.settings.other_ingredients_text_align_mobile
  assign other_ingredients_text_mt = section.settings.other_ingredients_text_mt

  assign tooltip_bg_color = section.settings.tooltip_bg_color
  assign tooltip_border_color = section.settings.tooltip_border_color
  assign tooltip_padding_vertical = section.settings.tooltip_padding_vertical
  assign tooltip_padding_horizontal = section.settings.tooltip_padding_horizontal
  assign tooltip_radius = section.settings.tooltip_radius
  assign tooltip_border_thickness = section.settings.tooltip_border_thickness
  assign tooltip_shadow = section.settings.tooltip_shadow
  assign tooltip_shadow_color = section.settings.tooltip_shadow_color

  assign tooltip_text_size = section.settings.tooltip_text_size
  assign tooltip_text_size_mobile = section.settings.tooltip_text_size_mobile
  assign tooltip_text_color = section.settings.tooltip_text_color
  assign tooltip_text_custom = section.settings.tooltip_text_custom
  assign tooltip_text_font = section.settings.tooltip_text_font
  assign tooltip_text_height = section.settings.other_ingredients_text_height
  
-%}

{%- style -%}

  {{  heading_font | font_face: font_display: 'swap' }}
  {{  ingredients_title_font | font_face: font_display: 'swap' }}
  {{  ingredient_font | font_face: font_display: 'swap' }}
  {{  other_ingredients_title_font | font_face: font_display: 'swap' }}
  {{  other_ingredients_text_font | font_face: font_display: 'swap' }}
  {{  tooltip_text_font | font_face: font_display: 'swap' }}
  
  .section-{{ section.id }} {
    border-top: solid {{ border_color }} {{ border_thickness }}px;
    border-bottom: solid {{ border_color }} {{ border_thickness }}px;
    margin-top: {{ margin_top | times: 0.75 | round: 0 }}px;
    margin-bottom: {{ margin_bottom | times: 0.75 | round: 0 }}px;
  }
  
  .section-{{ section.id }}-settings {
    margin: 0 auto;
    padding-top: {{ padding_top | times: 0.75 | round: 0 }}px;
    padding-bottom: {{ padding_bottom | times: 0.75 | round: 0 }}px;
    padding-left: {{ padding_horizontal_mobile }}rem;
    padding-right: {{ padding_horizontal_mobile }}rem;
  }

  .ingredients-heading-{{ section.id }} {
    text-align: {{ heading_align_mobile }};
  }

  .ingredients-heading-{{ section.id }} * {
    margin: 0;
    font-size: {{ heading_size_mobile }}px;
    color: {{ heading_color }};
    line-height: {{ heading_height }}%;
    text-transform: unset;
  }

  .ingredients-grid-wrapper-{{ section.id }} {
    padding: {{ grid_padding_vertical | times: 0.75 | round: 0 }}px {{ grid_padding_horizontal | times: 0.75 | round: 0 }}px;
    border-radius: {{ grid_radius }}px;
    border: {{ grid_border_thickness }}px solid {{ grid_border_color }};
    background-color: {{ grid_bg_color }};
    background-image: {{ grid_bg_gradient_color }};
  }

  .ingredients-grid-{{ section.id }} {
    display: grid;
    grid-template-columns: repeat(1, 1fr);
    gap: {{ grid_gap_mobile }}px;
    align-items: center;
  }

  .ingredients-image-inner-{{ section.id }} {
    width: 100%;
    height: 100%;
    max-width: {{ image_width_mobile }}%;
    border-radius: {{ image_radius }}px;
    border:  {{ image_border_thickness }}px solid {{ image_border_color }};
  }

  .ingredients-image-inner-{{ section.id }} img,
  .ingredients-image-inner-{{ section.id }} svg {
    display: block;
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: {{ image_radius }}px;
  }

  .ingredients-image-inner-{{ section.id }} svg {
    background-color: #DDDDDD;
  }

  .ingredients-content-{{ section.id }} {
    padding: {{ content_padding_vertical | times: 0.75 | round: 0 }}px {{ content_padding_horizontal | times: 0.75 | round: 0 }}px;
    border-radius: {{ content_radius }}px;
    border: {{ content_border_thickness }}px solid {{ content_border_color }};
    background-color: {{ content_bg_color }};
  }

  .ingredients-title-{{ section.id }} {
    margin: 0;
    display: block;
    width: 100%;
    font-size: {{ ingredients_title_size_mobile }}px;
    color: {{ ingredients_title_color }};
    line-height: {{ ingredients_title_height }}%;
    text-transform: unset;
    text-align: {{ ingredients_title_align_mobile }};
    font-weight: 700;
  }

  .ingredients-list-{{ section.id }} {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: {{ list_gap_mobile }}px;
  }

  .ingredients-item-{{ section.id }} {
    margin: 0px;
    display: block;
    font-size: {{ ingredient_size_mobile }}px;
    color: {{ ingredient_color }};
    line-height: {{ ingredient_height }}%;
    cursor: pointer;
    transition: all 0.25s ease 0s;
  }

  .ingredients-item-{{ section.id }}:hover {
    transition: all 0.25s ease 0s;
    color: {{ ingredient_hover_color }};
  }

  .ingredients-other-title-{{ section.id }} {
    margin: 0;
    display: block;
    width: 100%;
    margin-top: {{ other_ingredients_title_mt | times: 0.75 | round: 0 }}px;
    font-size: {{ other_ingredients_title_size_mobile }}px;
    color: {{ other_ingredients_title_color }};
    line-height: {{ other_ingredients_title_height }}%;
    text-transform: unset;
    text-align: {{ other_ingredients_title_align_mobile }};
    font-weight: 700;
  }

  .ingredients-other-text-{{ section.id }} {
    margin: 0;
    margin-top: {{ other_ingredients_text_mt | times: 0.75 | round: 0 }}px;
    font-size: {{ other_ingredients_text_size_mobile }}px;
    color: {{ other_ingredients_text_color }};
    line-height: {{ other_ingredients_text_height }}%;
    text-transform: unset;
    text-align: {{ other_ingredients_text_align_mobile }};
  }

  .ingredients-tooltip-{{ section.id }} {
    display: none;
    position: fixed;
    max-width: 420px;
    padding: {{ tooltip_padding_vertical | times: 0.75 | round: 0 }}px {{ tooltip_padding_horizontal | times: 0.75 | round: 0 }}px;
    border-radius: {{ tooltip_radius }}px;
    border: {{ tooltip_border_thickness }}px solid {{ tooltip_border_color }};
    background-color: {{ tooltip_bg_color }};
  }

  .ingredients-tooltip-{{ section.id }} * {
    margin: 0px;
    font-size: {{ tooltip_text_size_mobile }}px;
    color: {{ tooltip_text_color }};
    line-height: {{ tooltip_text_height }}%;
    text-transform: unset;
  }
  
  @media(min-width: 1024px) {

    .section-{{ section.id }} {
      margin-top: {{ margin_top }}px;
      margin-bottom: {{ margin_bottom }}px;
    }
    
    .section-{{ section.id }}-settings {
      padding: 0 5rem;
      padding-top: {{ padding_top }}px;
      padding-bottom: {{ padding_bottom }}px;
      padding-left: {{ padding_horizontal }}rem;
      padding-right: {{ padding_horizontal }}rem;
    }

    .ingredients-heading-{{ section.id }} {
      text-align: {{ heading_align }};
    }

    .ingredients-heading-{{ section.id }} * {
      font-size: {{ heading_size }}px;
    }

    .ingredients-grid-wrapper-{{ section.id }} {
      padding: {{ grid_padding_vertical }}px {{ grid_padding_horizontal }}px;
    }

    .ingredients-grid-{{ section.id }} {
      gap: {{ grid_gap }}px;
    }

    .ingredients-image-inner-{{ section.id }} {
      max-width: 100%;
    }

    .ingredients-content-{{ section.id }} {
      padding: {{ content_padding_vertical }}px {{ content_padding_horizontal }}px;
    }

    .ingredients-title-{{ section.id }} {
      font-size: {{ ingredients_title_size }}px;
      text-align: {{ ingredients_title_align }};
    }

    .ingredients-list-{{ section.id }} {
      gap: {{ list_gap }}px;
    }

    .ingredients-item-{{ section.id }} {
      font-size: {{ ingredient_size }}px;
    }

    .ingredients-other-title-{{ section.id }} {
      margin-top: {{ other_ingredients_title_mt }}px;
      font-size: {{ other_ingredients_title_size }}px;
      text-align: {{ other_ingredients_title_align }};
    }

    .ingredients-other-text-{{ section.id }} {
      margin-top: {{ other_ingredients_text_mt }}px;
      font-size: {{ other_ingredients_text_size }}px;
      text-align: {{ other_ingredients_text_align }};
    }

    .ingredients-tooltip-{{ section.id }} {
      padding: {{ tooltip_padding_vertical }}px {{ tooltip_padding_horizontal }}px;
    }
  
    .ingredients-tooltip-{{ section.id }} * {
      font-size: {{ tooltip_text_size }}px;
    }
  }
  
{%- endstyle -%}

{% unless full_width %}
  <style>
    .section-{{ section.id }}-settings {
      max-width: {{ content_width }}rem;
    }
  </style>
{% endunless %}

{% if heading_custom %}
  <style>
    .ingredients-heading-{{ section.id }} * {
      font-family: {{ heading_font.family }}, {{ heading_font.fallback_families }};
      font-weight: {{ heading_font.weight }};
      font-style: {{ heading_font.style }};
    }
  </style>
{% endif %}

{% if ingredients_title_custom %}
  <style>
    .ingredients-title-{{ section.id }} {
      font-family: {{ ingredients_title_font.family }}, {{ ingredients_title_font.fallback_families }};
      font-weight: {{ ingredients_title_font.weight }};
      font-style: {{ ingredients_title_font.style }};
    }
  </style>
{% endif %}

{% if ingredient_custom %}
  <style>
    .ingredients-item-{{ section.id }} {
      font-family: {{ ingredient_font.family }}, {{ ingredient_font.fallback_families }};
      font-weight: {{ ingredient_font.weight }};
      font-style: {{ ingredient_font.style }};
    }
  </style>
{% endif %}

{% if other_ingredients_title_custom %}
  <style>
    .ingredients-other-title-{{ section.id }} {
      font-family: {{ other_ingredients_title_font.family }}, {{ other_ingredients_title_font.fallback_families }};
      font-weight: {{ other_ingredients_title_font.weight }};
      font-style: {{ other_ingredients_title_font.style }};
    }
  </style>
{% endif %}

{% if other_ingredients_text_custom %}
  <style>
    .ingredients-other-text-{{ section.id }} {
      font-family: {{ other_ingredients_text_font.family }}, {{ other_ingredients_text_font.fallback_families }};
      font-weight: {{ other_ingredients_text_font.weight }};
      font-style: {{ other_ingredients_text_font.style }};
    }
  </style>
{% endif %}

{% if tooltip_text_custom %}
  <style>
    .ingredients-tooltip-{{ section.id }} * {
      font-family: {{ tooltip_text_font.family }}, {{ tooltip_text_font.fallback_families }};
      font-weight: {{ tooltip_text_font.weight }};
      font-style: {{ tooltip_text_font.style }};
    }
  </style>
{% endif %}

{% if ingredient_underline %}
  <style>
    .ingredients-item-{{ section.id }} {
      text-decoration: underline;
    }
  </style>
{% endif %}

{% if ingredients_title_position == "side" %}
  <style>
    .ingredients-title-{{ section.id }} {
      max-width: fit-content;
    }
  </style>
{% else %}
  <style>
    .ingredients-list-{{ section.id }} {
      margin-top: {{ list_mt | times: 0.75 | round: 0 }}px;
    }

    @media(min-width: 1024px) {
      .ingredients-list-{{ section.id }} {
        margin-top: {{ list_mt }}px;
      }
    }
  </style>
{% endif %}


{% if heading_position == "inside" %}
  <style>
    .ingredients-grid-{{ section.id }} {
      margin-top: {{ grid_mt | times: 0.75 | round: 0 }}px;
    }
    @media(min-width: 1024px) {
      .ingredients-grid-{{ section.id }} {
        margin-top: {{ grid_mt }}px;
      }
    }
  </style>
{% else %}
  <style>
    .ingredients-grid-wrapper-{{ section.id }} {
      margin-top: {{ grid_mt | times: 0.75 | round: 0 }}px;
    }
    @media(min-width: 1024px) {
      .ingredients-grid-wrapper-{{ section.id }} {
        margin-top: {{ grid_mt }}px;
      }
    }
  </style>
{% endif %}

{% if image_position_mobile == "top" %}
  <style>
    .ingredients-image-{{ section.id }} {
      order: 1;
    }
    .ingredients-content-{{ section.id }} {
      order: 2;
    }
  </style>
{% else %}
  <style>
    .ingredients-image-{{ section.id }} {
      order: 2;
    }
    .ingredients-content-{{ section.id }} {
      order: 1;
    }
  </style>
{% endif %}

{% if image_position == "left" %}
  <style>
    @media(min-width: 1024px) {
      .ingredients-grid-{{ section.id }} {
        grid-template-columns: calc({{ image_width }}% - {{ grid_gap | times: 0.5 | round: 0 }}px) calc((100% - {{ image_width }}%) - {{ grid_gap | times: 0.5 | round: 0 }}px);
      }
      
      .ingredients-image-{{ section.id }} {
        order: 1;
      }
      .ingredients-content-{{ section.id }} {
        order: 2;
      } 
    }
  </style>
{% else %}
  <style>
    @media(min-width: 1024px) {
      .ingredients-grid-{{ section.id }} {
        grid-template-columns: calc((100% - {{ image_width }}%) - {{ grid_gap | times: 0.5 | round: 0 }}px) calc({{ image_width }}% - {{ grid_gap | times: 0.5 | round: 0 }}px);
      }
      
      .ingredients-image-{{ section.id }} {
        order: 2;
      }
      .ingredients-content-{{ section.id }} {
        order: 1;
      } 
    }
  </style>
{% endif %}

{% if image_ratio == "portrait" %}
  <style>
    .ingredients-image-inner-{{ section.id }} {
     aspect-ratio: 9.6/12; 
    }
  </style>
{% elsif image_ratio == "landscape" %}
  <style>
    .ingredients-image-inner-{{ section.id }} {
     aspect-ratio: 12/8; 
    }
  </style>
{% elsif image_ratio == "square" %}
  <style>
    .ingredients-image-inner-{{ section.id }} {
     aspect-ratio: 12/12; 
    }
  </style>
{% endif %}

{% if image_shadow %}
  <style>
    .ingredients-image-inner-{{ section.id }} {
      box-shadow: 0px 0px 10px 0px {{ image_shadow_color | hex_to_rgba: 0.3 }};
    }
  </style>
{% endif %}

{% if image_hide_mobile %}
  <style>
    .ingredients-image-{{ section.id }} {
      display: none;
    }
    @media(min-width: 1024px) {
      .ingredients-image-{{ section.id }} {
        display: block;
      }
    }
  </style>
{% endif %}

{% if image_hide_desktop %}
  <style>
    @media(min-width: 1024px) {
      .ingredients-image-{{ section.id }} {
        display: none !important;
      }
      
      .ingredients-grid-{{ section.id }} {
        grid-template-columns: repeat(1, 1fr);
      }
    }
  </style>
{% else %}
  <style>
    @media(min-width: 1024px) {
      .ingredients-image-{{ section.id }} {
        display: block !important;
      }
    }
  </style>
{% endif %}

{% if tooltip_shadow %}
  <style>
    .ingredients-tooltip-{{ section.id }} {
      box-shadow: 0px 0px 70px 5px {{ tooltip_shadow_color | hex_to_rgba: 0.2 }};
    }
  </style>
{% endif %}

<div class="section-{{ section.id }} ingredients-{{ section.id }}" style="background-color:{{ background_color }}; background-image: {{ background_gradient }};">
  <div class="section-{{ section.id }}-settings">
    {% if heading != blank and heading_position == "outside" %}
      <div class="ingredients-heading-{{ section.id }}">{{ heading }}</div>
    {% endif %}
    <div class="ingredients-grid-wrapper-{{ section.id }}">
      {% if heading != blank and heading_position == "inside" %}
        <div class="ingredients-heading-{{ section.id }}">{{ heading }}</div>
      {% endif %}
      <div class="ingredients-grid-{{ section.id }}">
        <div class="ingredients-image-{{ section.id }}">
          <div class="ingredients-image-inner-{{ section.id }}">
            {% if image != blank %}
              <img src="{{ image | image_url }}" alt="{{ image.alt }}" {% if lazy %}loading="lazy"{% endif %}>
            {% else %}
              {{ 'image' | placeholder_svg_tag }}
            {% endif %}
          </div>
        </div>
        <div class="ingredients-content-{{ section.id }}">
          {% if ingredients_title_position == "top" %}
            {% for block in section.blocks %}
              {% if block.type == "ingredients_title" %}
                <p class="ingredients-title-{{ section.id }}">{{ block.settings.title }}</p>
              {% endif %}
            {% endfor %}
          {% endif %}
          <div class="ingredients-list-{{ section.id }}">
            {% if ingredients_title_position == "side" %}
              {% for block in section.blocks %}
                {% if block.type == "ingredients_title" %}
                  <p class="ingredients-title-{{ section.id }}">{{ block.settings.title }}</p>
                {% endif %}
              {% endfor %}
            {% endif %}
            {% for block in section.blocks %}
              {% if block.type == "ingredient" %}
                <p class="ingredients-item-{{ section.id }}" data-text="{{ block.settings.text }}">{{ block.settings.title }}{% unless forloop.last %},{% endunless %}</p>
              {% endif %}
            {% endfor %}
          </div>
          {% if other_ingredients_title != blank %}
            <p class="ingredients-other-title-{{ section.id }}">{{ other_ingredients_title }}</p>
          {% endif %}
          {% if other_ingredients_text != blank %}
            <p class="ingredients-other-text-{{ section.id }}">{{ other_ingredients_text }}</p>
          {% endif %}
          <div class="ingredients-tooltip-{{ section.id }}"></div>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
  function initProductIngredients() {
    var ingredientItems = document.querySelectorAll('.ingredients-item-{{ section.id }}');
    var tooltip = document.querySelector('.ingredients-tooltip-{{ section.id }}');
    var tooltipTimeout;

    ingredientItems.forEach(function (ingredientItem) {
        ingredientItem.addEventListener('mouseover', function (event) {
            var tooltipText = ingredientItem.getAttribute('data-text');
            tooltip.innerHTML = tooltipText;
            tooltip.style.display = 'block';
            clearTimeout(tooltipTimeout);
        });

        ingredientItem.addEventListener('mouseout', function () {
            tooltipTimeout = setTimeout(function () {
                tooltip.style.display = 'none';
            }, 100);
        });
    });

    document.addEventListener('mousemove', function (event) {
        var mouseX = event.clientX + 10;
        var mouseY = event.clientY + 10;
        tooltip.style.left = mouseX + 'px';
        tooltip.style.top = mouseY + 'px';
    });
  }
  
  document.addEventListener('DOMContentLoaded', initProductIngredients);
  
  if (Shopify.designMode) {
     document.addEventListener('shopify:section:unload', initProductIngredients);
     document.addEventListener('shopify:section:load', initProductIngredients);
  }
  
</script>

{% schema %}
  {
    "name": "SS - Product Ingredients",
    "settings": [
      {
        "type": "header",
        "content": "Heading"
      },
      {
        "type": "richtext",
        "id": "heading",
        "label": "Heading",
        "default": "<h2>Product Ingredients</h2>"
      },
      {
        "type": "checkbox",
        "id": "heading_custom",
        "label": "Use Custom Font",
        "default": false
      },
      {
        "type": "font_picker",
        "id": "heading_font",
        "label": "Heading Font Family",
        "default": "josefin_sans_n4"
      },
      {
        "type": "range",
        "id": "heading_size",
        "min": 0,
        "max": 72,
        "step": 2,
        "unit": "px",
        "label": "Heading Size",
        "default": 40
      },
      {
        "type": "range",
        "id": "heading_size_mobile",
        "min": 0,
        "max": 72,
        "step": 2,
        "unit": "px",
        "label": "Heading Size - Mobile",
        "default": 26
      },
      {
        "type": "range",
        "id": "heading_height",
        "min": 50,
        "max": 200,
        "step": 10,
        "unit": "%",
        "label": "Heading Line Height",
        "default": 130
      },
      {
        "type": "select",
        "id": "heading_align",
        "label": "Heading Text Align",
        "default": "left",
        "options": [
          {
            "label": "Left",
            "value": "left"
          },
          {
            "label": "Center",
            "value": "center"
          },
          {
            "label": "Right",
            "value": "right"
          }
        ]
      },
      {
        "type": "select",
        "id": "heading_align_mobile",
        "label": "Heading Text Align - Mobile",
        "default": "center",
        "options": [
          {
            "label": "Left",
            "value": "left"
          },
          {
            "label": "Center",
            "value": "center"
          },
          {
            "label": "Right",
            "value": "right"
          }
        ]
      },
      {
        "type": "select",
        "id": "heading_position",
        "label": "Heading Position",
        "default": "outside",
        "options": [
          {
            "label": "Inside the Grid",
            "value": "inside"
          },
          {
            "label": "Outside the Grid",
            "value": "outside"
          }
        ]
      },
      {
        "type": "header",
        "content": "Grid Settings"
      },
      {
        "type": "range",
        "id": "grid_gap",
        "min": 0,
        "max": 200,
        "step": 2,
        "unit": "px",
        "label": "Grid Gap",
        "default": 32
      },
      {
        "type": "range",
        "id": "grid_gap_mobile",
        "min": 0,
        "max": 200,
        "step": 2,
        "unit": "px",
        "label": "Grid Gap - Mobile",
        "default": 32
      },
      {
        "type": "range",
        "id": "grid_padding_vertical",
        "min": 0,
        "max": 200,
        "step": 2,
        "unit": "px",
        "label": "Grid Padding Vertical",
        "default": 24
      },
      {
         "type": "range",
         "id": "grid_padding_horizontal",
         "min": 0,
         "max": 200,
         "step": 2,
         "unit": "px",
         "label": "Grid Padding Horizontal",
         "default": 24
      },
      {
         "type": "range",
         "id": "grid_radius",
         "min": 0,
         "max": 100,
         "step": 2,
         "unit": "px",
         "label": "Grid Border Radius",
         "default": 16
      },
      {
         "type": "range",
         "id": "grid_border_thickness",
         "min": 0,
         "max": 10,
         "step": 1,
         "unit": "px",
         "label": "Grid Border Thickness",
         "default": 0
      },
      {
        "type": "range",
        "id": "grid_mt",
        "min": 0,
        "max": 100,
        "step": 2,
        "unit": "px",
        "label": "Grid Margin Top",
        "default": 16
      },
      {
        "type": "header",
        "content": "Image Settings"
      },
      {
        "type": "image_picker",
        "id": "image",
        "label": "Image"
      },
      {
         "type": "range",
         "id": "image_width",
         "min": 10,
         "max": 50,
         "step": 1,
         "unit": "%",
         "label": "Image Width - Desktop",
         "default": 35
      },
      {
         "type": "range",
         "id": "image_width_mobile",
         "min": 10,
         "max": 100,
         "step": 1,
         "unit": "%",
         "label": "Image Width - Mobile",
         "default": 100
      },
      {
        "type": "select",
        "id": "image_ratio",
        "label": "Image Aspect Ratio",
        "default": "portrait",
        "options": [
          {
            "label": "Original",
            "value": "original"
          },
          {
            "label": "Portrait",
            "value": "portrait"
          },
          {
            "label": "Square",
            "value": "square"
          },
          {
            "label": "Landscape",
            "value": "landscape"
          }
        ]
      },
      {
        "type": "select",
        "id": "image_position",
        "label": "Image Position",
        "default": "left",
        "options": [
          {
            "label": "Left",
            "value": "left"
          },
          {
            "label": "Right",
            "value": "right"
          }
        ]
      },
      {
        "type": "select",
        "id": "image_position_mobile",
        "label": "Image Position - Mobile",
        "default": "top",
        "options": [
          {
            "label": "Top",
            "value": "top"
          },
          {
            "label": "Bottom",
            "value": "bottom"
          }
        ]
      },
      {
        "type": "range",
        "id": "image_radius",
        "min": 0,
        "max": 100,
        "step": 2,
        "unit": "px",
        "label": "Image Radius",
        "default": 16
      },
      {
        "type": "range",
        "id": "image_border_thickness",
        "min": 0,
        "max": 10,
        "step": 1,
        "unit": "px",
        "label": "Image Border Thickness",
        "default": 0
      },
      {
        "type": "checkbox",
        "label": "Use Image Shadow",
        "id": "image_shadow",
        "default": false
      },
      {
        "type": "checkbox",
        "label": "Hide Image on Desktop",
        "id": "image_hide_desktop",
        "default": false
      },
      {
        "type": "checkbox",
        "label": "Hide Image on Mobile",
        "id": "image_hide_mobile",
        "default": false
      },
      {
        "type": "header",
        "content": "Content Settings"
      },
      {
        "type": "range",
        "id": "content_padding_vertical",
        "min": 0,
        "max": 100,
        "step": 4,
        "unit": "px",
        "label": "Content Padding Vertical",
        "default": 0
      },
      {
         "type": "range",
         "id": "content_padding_horizontal",
         "min": 0,
         "max": 100,
         "step": 4,
         "unit": "px",
         "label": "Content Padding Horizontal",
         "default": 0
      },
      {
         "type": "range",
         "id": "content_radius",
         "min": 0,
         "max": 100,
         "step": 2,
         "unit": "px",
         "label": "Content Border Radius",
         "default": 0
      },
      {
         "type": "range",
         "id": "content_border_thickness",
         "min": 0,
         "max": 10,
         "step": 1,
         "unit": "px",
         "label": "Content Border Thickness",
         "default": 0
      },
      {
        "type": "header",
        "content": "Ingredients Title Settings"
      },
      {
        "type": "checkbox",
        "id": "ingredients_title_custom",
        "label": "Use Custom Font",
        "default": false
      },
      {
        "type": "font_picker",
        "id": "ingredients_title_font",
        "label": "Ingredients Title Font Family",
        "default": "josefin_sans_n4"
      },
      {
        "type": "range",
        "id": "ingredients_title_size",
        "min": 0,
        "max": 72,
        "step": 2,
        "unit": "px",
        "label": "Ingredients Title Size",
        "default": 34
      },
      {
        "type": "range",
        "id": "ingredients_title_size_mobile",
        "min": 0,
        "max": 72,
        "step": 2,
        "unit": "px",
        "label": "Ingredients Title Size - Mobile",
        "default": 24
      },
      {
        "type": "range",
        "id": "ingredients_title_height",
        "min": 50,
        "max": 200,
        "step": 10,
        "unit": "%",
        "label": "Ingredients Title Line Height",
        "default": 130
      },
      {
        "type": "select",
        "id": "ingredients_title_align",
        "label": "Ingredients Title Text Align",
        "info": "If 'Ingredients Title Position' == 'Top' ",
        "default": "left",
        "options": [
          {
            "label": "Left",
            "value": "left"
          },
          {
            "label": "Center",
            "value": "center"
          },
          {
            "label": "Right",
            "value": "right"
          }
        ]
      },
      {
        "type": "select",
        "id": "ingredients_title_align_mobile",
        "label": "Ingredients Title Text Align - Mobile",
        "info": "If 'Ingredients Title Position' == 'Top' ",
        "default": "left",
        "options": [
          {
            "label": "Left",
            "value": "left"
          },
          {
            "label": "Center",
            "value": "center"
          },
          {
            "label": "Right",
            "value": "right"
          }
        ]
      },
      {
        "type": "select",
        "id": "ingredients_title_position",
        "label": "Ingredients Title Position",
        "default": "side",
        "options": [
          {
            "label": "Top",
            "value": "top"
          },
          {
            "label": "Side",
            "value": "side"
          }
        ]
      },
      {
        "type": "header",
        "content": "Ingredients List Settings"
      },
      {
        "type": "range",
        "id": "list_gap",
        "min": 0,
        "max": 100,
        "step": 2,
        "unit": "px",
        "label": "List Gap",
        "default": 10
      },
      {
        "type": "range",
        "id": "list_gap_mobile",
        "min": 0,
        "max": 100,
        "step": 2,
        "unit": "px",
        "label": "List Gap - Mobile",
        "default": 10
      },
      {
        "type": "range",
        "id": "list_mt",
        "min": 0,
        "max": 100,
        "step": 2,
        "unit": "px",
        "label": "List Margin Top",
        "info": "If 'Ingredients Title Position' == 'Top' ",
        "default": 16
      },
      {
        "type": "header",
        "content": "Ingredient Settings"
      },
      {
        "type": "checkbox",
        "id": "ingredient_custom",
        "label": "Use Custom Font",
        "default": false
      },
      {
        "type": "font_picker",
        "id": "ingredient_font",
        "label": "Ingredient Title Font Family",
        "default": "josefin_sans_n4"
      },
      {
        "type": "range",
        "id": "ingredient_size",
        "min": 0,
        "max": 72,
        "step": 2,
        "unit": "px",
        "label": "Ingredient Title Size",
        "default": 34
      },
      {
        "type": "range",
        "id": "ingredient_size_mobile",
        "min": 0,
        "max": 72,
        "step": 2,
        "unit": "px",
        "label": "Ingredient Title Size - Mobile",
        "default": 24
      },
      {
        "type": "range",
        "id": "ingredient_height",
        "min": 50,
        "max": 200,
        "step": 10,
        "unit": "%",
        "label": "Ingredient Title Line Height",
        "default": 130
      },
      {
        "type": "checkbox",
        "label": "Use Ingredient Underline",
        "id": "ingredient_underline",
        "default": true
      },
      {
        "type": "header",
        "content": "Other Ingredients Title Settings"
      },
      {
        "type": "text",
        "id": "other_ingredients_title",
        "label": "Other Ingredients Title",
        "default": "Other Ingredients:"
      },
      {
        "type": "checkbox",
        "id": "other_ingredients_title_custom",
        "label": "Use Custom Font",
        "default": false
      },
      {
        "type": "font_picker",
        "id": "other_ingredients_title_font",
        "label": "Other Ingredients Title Font Family",
        "default": "josefin_sans_n4"
      },
      {
        "type": "range",
        "id": "other_ingredients_title_size",
        "min": 0,
        "max": 72,
        "step": 2,
        "unit": "px",
        "label": "Other Ingredients Title Size",
        "default": 26
      },
      {
        "type": "range",
        "id": "other_ingredients_title_size_mobile",
        "min": 0,
        "max": 72,
        "step": 2,
        "unit": "px",
        "label": "Other Ingredients Title Size - Mobile",
        "default": 18
      },
      {
        "type": "range",
        "id": "other_ingredients_title_height",
        "min": 50,
        "max": 200,
        "step": 10,
        "unit": "%",
        "label": "Other Ingredients Title Line Height",
        "default": 130
      },
      {
        "type": "select",
        "id": "other_ingredients_title_align",
        "label": "Other Ingredients Title Text Align",
        "default": "left",
        "options": [
          {
            "label": "Left",
            "value": "left"
          },
          {
            "label": "Center",
            "value": "center"
          },
          {
            "label": "Right",
            "value": "right"
          }
        ]
      },
      {
        "type": "select",
        "id": "other_ingredients_title_align_mobile",
        "label": "Other Ingredients Title Text Align - Mobile",
        "default": "left",
        "options": [
          {
            "label": "Left",
            "value": "left"
          },
          {
            "label": "Center",
            "value": "center"
          },
          {
            "label": "Right",
            "value": "right"
          }
        ]
      },
      {
        "type": "range",
        "id": "other_ingredients_title_mt",
        "min": 0,
        "max": 100,
        "step": 2,
        "unit": "px",
        "label": "Other Ingredients Title Margin Top",
        "default": 40
      },
      {
        "type": "header",
        "content": "Other Ingredients Text Settings"
      },
      {
        "type": "text",
        "id": "other_ingredients_text",
        "label": "Other Ingredients Text",
        "default": "Hyaluronic Acid, Salicylic Acid, Glycolic Acid, Benzoyl Peroxide, Ceramides, Peptides, Alpha Hydroxy Acids (AHAs), Beta Hydroxy Acids (BHAs), Vitamin C, Vitamin E, Retinol, Squalane, Zinc Oxide, Titanium Dioxide, Tea Tree Oil, Aloe Vera, Green Tea Extract, Glycerin, Shea Butter, Jojoba Oil."
      },
      {
        "type": "checkbox",
        "id": "other_ingredients_text_custom",
        "label": "Use Custom Font",
        "default": false
      },
      {
        "type": "font_picker",
        "id": "other_ingredients_text_font",
        "label": "Other Ingredients Text Font Family",
        "default": "josefin_sans_n4"
      },
      {
        "type": "range",
        "id": "other_ingredients_text_size",
        "min": 0,
        "max": 72,
        "step": 2,
        "unit": "px",
        "label": "Other Ingredients Text Size",
        "default": 18
      },
      {
        "type": "range",
        "id": "other_ingredients_text_size_mobile",
        "min": 0,
        "max": 72,
        "step": 2,
        "unit": "px",
        "label": "Other Ingredients Text Size - Mobile",
        "default": 16
      },
      {
        "type": "range",
        "id": "other_ingredients_text_height",
        "min": 50,
        "max": 200,
        "step": 10,
        "unit": "%",
        "label": "Other Ingredients Text Line Height",
        "default": 150
      },
      {
        "type": "select",
        "id": "other_ingredients_text_align",
        "label": "Other Ingredients Text Align",
        "default": "left",
        "options": [
          {
            "label": "Left",
            "value": "left"
          },
          {
            "label": "Center",
            "value": "center"
          },
          {
            "label": "Right",
            "value": "right"
          }
        ]
      },
      {
        "type": "select",
        "id": "other_ingredients_text_align_mobile",
        "label": "Other Ingredients Text Align - Mobile",
        "default": "left",
        "options": [
          {
            "label": "Left",
            "value": "left"
          },
          {
            "label": "Center",
            "value": "center"
          },
          {
            "label": "Right",
            "value": "right"
          }
        ]
      },
      {
        "type": "range",
        "id": "other_ingredients_text_mt",
        "min": 0,
        "max": 100,
        "step": 2,
        "unit": "px",
        "label": "Other Ingredients Text Margin Top",
        "default": 10
      },
      {
        "type": "header",
        "content": "Tooltip Settings"
      },
      {
        "type": "range",
        "id": "tooltip_padding_vertical",
        "min": 0,
        "max": 100,
        "step": 4,
        "unit": "px",
        "label": "Tooltip Padding Vertical",
        "default": 16
      },
      {
         "type": "range",
         "id": "tooltip_padding_horizontal",
         "min": 0,
         "max": 100,
         "step": 4,
         "unit": "px",
         "label": "Tooltip Padding Horizontal",
         "default": 16
      },
      {
         "type": "range",
         "id": "tooltip_radius",
         "min": 0,
         "max": 100,
         "step": 2,
         "unit": "px",
         "label": "Tooltip Border Radius",
         "default": 16
      },
      {
         "type": "range",
         "id": "tooltip_border_thickness",
         "min": 0,
         "max": 10,
         "step": 1,
         "unit": "px",
         "label": "Tooltip Border Thickness",
         "default": 0
      },
      {
        "type": "checkbox",
        "id": "tooltip_shadow",
        "label": "Tooltip Shadow",
        "default": true
      },
       {
        "type": "header",
        "content": "Tooltip Text Settings"
      },
      {
        "type": "checkbox",
        "id": "tooltip_text_custom",
        "label": "Use Custom Font",
        "default": false
      },
      {
        "type": "font_picker",
        "id": "tooltip_text_font",
        "label": "Tooltip Text Font Family",
        "default": "josefin_sans_n4"
      },
      {
        "type": "range",
        "id": "tooltip_text_size",
        "min": 0,
        "max": 72,
        "step": 2,
        "unit": "px",
        "label": "Tooltip Text Size",
        "default": 18
      },
      {
        "type": "range",
        "id": "tooltip_text_size_mobile",
        "min": 0,
        "max": 72,
        "step": 2,
        "unit": "px",
        "label": "Tooltip Text Size - Mobile",
        "default": 16
      },
      {
        "type": "range",
        "id": "tooltip_text_height",
        "min": 50,
        "max": 200,
        "step": 10,
        "unit": "%",
        "label": "Tooltip Text Line Height",
        "default": 130
      },
      {
        "type": "header",
        "content": "Section Colors"
      },
      {
        "type": "color",
        "label": "Heading Color",
        "id": "heading_color",
        "default": "#663969"
      },
      {
        "type": "color",
        "label": "Grid Background",
        "id": "grid_bg_color",
        "default": "#806AC6"
      },
      {
        "type": "color_background",
        "label": "Grid Background Gradient",
        "id": "grid_bg_gradient_color"
      },
      {
        "type": "color",
        "label": "Grid Border Color",
        "id": "grid_border_color",
        "default": "#000000"
      },
      {
        "type": "color",
        "label": "Image Border Color",
        "id": "image_border_color",
        "default": "#000000"
      },
      {
        "type": "color",
        "label": "Image Shadow Color",
        "id": "image_shadow_color",
        "default": "#000000"
      },
      {
        "type": "color",
        "label": "Content Background",
        "id": "content_bg_color"
      },
      {
        "type": "color",
        "label": "Content Border Color",
        "id": "content_border_color",
        "default": "#000000"
      },
      {
        "type": "color",
        "label": "Ingredients Title Color",
        "id": "ingredients_title_color",
        "default": "#d3b5d5"
      },
      {
        "type": "color",
        "label": "Ingredient Color",
        "id": "ingredient_color",
        "default": "#fdfbf7"
      },
      {
        "type": "color",
        "label": "Ingredient Hover Color",
        "id": "ingredient_hover_color",
        "default": "#d3b5d5"
      },
      {
        "type": "color",
        "label": "Other Ingredients Title Color",
        "id": "other_ingredients_title_color",
        "default": "#d3b5d5"
      },
      {
        "type": "color",
        "label": "Other Ingredients Text Color",
        "id": "other_ingredients_text_color",
        "default": "#fdfbf7"
      },
      {
        "type": "color",
        "label": "Tooltip Background",
        "id": "tooltip_bg_color",
        "default": "#FFFFFF"
      },
      {
        "type": "color",
        "label": "Tooltip Border Color",
        "id": "tooltip_border_color",
        "default": "#000000"
      },
      {
        "type": "color",
        "label": "Tooltip Shadow Color",
        "id": "tooltip_shadow_color",
        "default": "#000000"
      },
      {
        "type": "color",
        "label": "Tooltip Text Color",
        "id": "tooltip_text_color",
        "default": "#000000"
      },
      {
        "type": "color",
        "label": "Section background",
        "id": "background_color",
        "default": "#FFFFFF"
      },
      {
        "type": "color_background",
        "id": "background_gradient",
        "label": "Section background gradient"
      },
      {
        "type": "color",
        "label": "Border",
        "id": "border_color",
        "default": "#000000"
      },
      {
        "type": "header",
        "content": "Section margin (outside)"
      },
      {
        "type": "range",
        "id": "margin_top",
        "min": 0,
        "max": 100,
        "step": 4,
        "unit": "px",
        "label": "Margin top",
        "default": 0
      },
      {
        "type": "range",
        "id": "margin_bottom",
        "min": 0,
        "max": 100,
        "step": 4,
        "unit": "px",
        "label": "Margin bottom",
        "default": 0
      },
      {
        "type": "header",
        "content": "Section padding (inside)"
      },
      {
        "type": "range",
        "id": "padding_top",
        "min": 0,
        "max": 100,
        "step": 4,
        "unit": "px",
        "label": "Padding top",
        "default": 36
      },
      {
         "type": "range",
         "id": "padding_bottom",
         "min": 0,
         "max": 100,
         "step": 4,
         "unit": "px",
         "label": "Padding bottom",
         "default": 36
      },
      {
        "type": "range",
        "id": "padding_horizontal",
        "min": 0,
        "max": 30,
        "step": 1,
        "unit": "rem",
        "label": "Padding sides",
        "default": 5
      },
      {
        "type": "range",
        "id": "padding_horizontal_mobile",
        "min": 0,
        "max": 15,
        "step": 0.5,
        "unit": "rem",
        "label": "Padding sides mobile",
        "default": 1.5
      },
      {
        "type": "header",
        "content": "Section Settings"
      },
      {
        "type": "checkbox",
        "id": "full_width",
        "label": "Full Width",
        "default": false
      },
      {
        "type": "range",
        "id": "content_width",
        "min": 0,
        "max": 400,
        "step": 10,
        "unit": "rem",
        "label": "Section content width",
        "default": 130
      },
      {
        "type": "range",
        "id": "border_thickness",
        "min": 0,
        "max": 50,
        "step": 1,
        "unit": "px",
        "label": "Border thickness",
        "default": 0
      },
      {
        "type": "checkbox",
        "id": "lazy",
        "label": "Lazy load",
        "info": "Lazy load images for speed optimisation",
        "default": true
      }
    ],
    "blocks": [
      {
        "type": "ingredients_title",
        "name": "Ingredients Title",
        "limit": 1,
        "settings": [
          {
            "type": "text",
            "id": "title",
            "label": "Ingredients Title",
            "default": "Active Ingredients:"
          }
        ]
      },
      {
        "type": "ingredient",
        "name": "Ingredient",
        "settings": [
          {
            "type": "text",
            "id": "title",
            "label": "Ingredient Title",
            "default": "Ingredient"
          },
          {
            "type": "richtext",
            "id": "text",
            "label": "Ingredient Text Block",
            "default": "<p>Text</p>"
          }
        ]
      }
    ],
    "presets": [
      {
        "name": "SS - Product Ingredients",
        "blocks": [
          {
            "type": "ingredients_title"
          },
          {
            "type": "ingredient",
            "settings": {
              "title": "Tretinoin",
              "text": "<p>A Vitamin A derivative that helps reduce wrinkles and acne by promoting skin cell turnover.</p>"
            }
          },
          {
            "type": "ingredient",
            "settings": {
              "title": "Niacinamide",
              "text": "<p>Also known as Vitamin B3, it reduces inflammation, improves skin barrier function, and helps with hyperpigmentation.</p>"
            }
          },
          {
            "type": "ingredient",
            "settings": {
              "title": "Azelaic Acid",
              "text": "<p>Effective for acne and rosacea, it has antibacterial properties and helps reduce redness and skin discoloration.</p>"
            }
          },
          {
            "type": "ingredient",
            "settings": {
              "title": "Clindamycin",
              "text": "<p>An antibiotic that targets acne-causing bacteria and reduces skin inflammation.</p>"
            }
          }
        ]
      }
    ]
  }
{% endschema %}