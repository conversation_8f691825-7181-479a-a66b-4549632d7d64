{% if template == "page.quiz-flow-account" %}

  {%- capture return_url -%}{{- settings.quiz_bookmark_results -}}{%- endcapture -%}

{% else %}

  {% case template.name %}
    {% when 'page' %}
      {%- capture return_url -%}{{- page.url -}}{%- endcapture -%}
    {% when 'blog' %}
      {%- capture return_url -%}{{- blog.url -}}{%- endcapture -%}
    {% when 'article' %}
      {%- capture return_url -%}{{- article.url -}}{%- endcapture -%}
    {% when 'collection' %}
      {%- capture return_url -%}{{- collection.url -}}{%- endcapture -%}
    {% when 'product' %}
      {%- capture return_url -%}{{- product.url -}}{%- endcapture -%}
  {% endcase %}

{% endif %}

<modal-content section="{{ section.id }}" id="quiz-popup--login" class="modal modal--quiz modal--login">

  <div class="modal__overlay"></div>

  <div class="modal__content">

    <button type="button" class="modal__close-button tap-area" data-action="close" title="{{ 'general.accessibility.close' | t | escape }}">
      {%- render 'icon' with 'close' -%}
    </button>

    <div class="modal__header">
      <div class="modal__title">
        <span class="heading heading--small">{{ 'quiz.general.login' | t }}</span>
      </div>
    </div>

    <div class="quiz-modal-content">

      <div class="newsletter-modal__content {% if section.settings.image != blank %}newsletter-modal__content--extra{% endif %} text-container text--center">

        {%- form 'customer_login', name: 'login', class: 'form', return_to: return_url -%}
          
          <div class="input">
            <input type="email" id="customer[email]" autocomplete="email" class="input__field" name="customer[email]" placeholder="{{ 'customer.login.email' | t }}" required="required" {% if form.errors %}aria-invalid="true" aria-describedby="login-form-error"{% endif %}>
            <label for="customer[email]" class="input__label">{{ 'customer.login.email' | t }}</label>
          </div>

          <div class="input">
            <input type="password" id="customer[password]" class="input__field" name="customer[password]" placeholder="{{ 'customer.login.password' | t }}" required="required" autocomplete="current-password" {% if form.errors %}aria-invalid="true" aria-describedby="login-form-error"{% endif %}>
            <label for="customer[password]" class="input__label">{{ 'customer.login.password' | t }}</label>
          </div>

          <div class="form__actions">

            <button type="submit" is="loader-button" class="button button--primary">
              <span class="button__text">{{ 'customer.login.submit' | t }}</span>
              <span class="button__icon">{%- render 'icon' with 'nav-arrow-right' -%}</span>
            </button>

          </div>

        {%- endform -%}

      </div>

      <div class="quiz-modal-footer">

        {{ 'customer.login.new_customer' | t }}
        <button is="toggle-button" class="link" aria-controls="quiz-popup--register" aria-expanded="false">{{ 'customer.login.create_account' | t }}</button>

      </div>

    </div>
  </div>
</modal-content>

{% schema %}
{
  "name": "Popup - Login",
  "class": "shopify-section--popup quiz",
  "settings": [
    {
      "type": "text",
      "id": "title",
      "label": "Heading",
      "default": "Get 10% off"
    },
    {
      "type": "richtext",
      "id": "content",
      "label": "Text",
      "default": "<p>Promotions, new products and sales. Directly to your inbox.</p>"
    }
  ],
  "blocks": [
    {
      "type": "@app"
    },
    {
      "type": "liquid",
      "name": "Liquid",
      "settings": [
        {
          "type": "liquid",
          "id": "liquid",
          "label": "Liquid",
          "info": "Add app snippets or other Liquid code to create advanced customizations."
        }
      ]
    },
    {
      "type": "login",
      "name": "Login form",
      "limit": 1
    }
  ]
}
{% endschema %}