<section>
  
  {% render 'account-link-bar' %}
  {% render 'account-popover' %}

  <div class="account account--order-list section section--use-padding">
    
    <div class="container container--medium">
      {%- if customer.orders.size == 0 -%}
        <div class="page-header page-header--small {% if section.blocks.size == 1 %}page-header--alone{% endif %}">
          <div class="page-header__text-wrapper text-container">
            <h1 class="heading h4">{{ 'customer.orders.title' | t }} <span class="bubble-count bubble-count--top">{{ customer.orders.size }}</span></h1>
            <p class="text--subdued">{{ 'customer.orders.no_orders' | t }}</p>

            <div class="button-wrapper">
              <a href="{{ routes.all_products_collection_url }}" class="button button--primary">{{ 'customer.orders.start_shopping' | t }}</a>
            </div>
          </div>
        </div>

        <div class="page-content page-content--fluid">
          <div class="account__block-list">
            {%- for block in section.blocks -%}
              <div class="account__block-item" {{ block.shopify_attributes }}>
                {%- case block.type -%}
                  {%- when '@app' -%}
                    {%- render block -%}

                  {%- when 'liquid' -%}
                    {{- block.settings.liquid -}}
                {%- endcase -%}
              </div>
            {%- endfor -%}
          </div>
        </div>
      {%- else -%}
        <div class="page-header page-header--small">
          <div class="page-header__text-wrapper text-container">
            <h1 class="heading h4">{{ 'customer.orders.title' | t }} <span class="bubble-count bubble-count--top">{{ customer.orders.size }}</span></h1>
          </div>
        </div>

        <div class="page-content page-content--fluid">
          <div class="account__block-list">
            {%- for block in section.blocks -%}
              <div class="account__block-item" {{ block.shopify_attributes }}>
                {%- case block.type -%}
                  {%- when '@app' -%}
                    {%- render block -%}

                  {%- when 'liquid' -%}
                    {{- block.settings.liquid -}}

                  {%- when 'orders' -%}
                    {%- paginate customer.orders by 5 -%}
                      <table class="account__orders-table table table--bordered hidden-phone">
                        <thead>
                          <tr>
                            <th><span class="heading heading--xsmall text--subdued">{{ 'customer.orders.number' | t }}</span></th>
                            <th><span class="heading heading--xsmall text--subdued">{{ 'customer.orders.date' | t }}</span></th>
                            <th><span class="heading heading--xsmall text--subdued">{{ 'customer.orders.payment_status' | t }}</span></th>
                            <th><span class="heading heading--xsmall text--subdued">{{ 'customer.orders.fulfillment_status' | t }}</span></th>
                            {% if section.settings.reorder_buttons_enable %}
                              <th><span class="heading heading--xsmall text--subdued">{{ 'customer.orders.total' | t }}</span></th>
                              <th class="text--right"><span class="heading heading--xsmall text--subdued">{{ 'customer.orders.reorder' | t }}</span></th>
                              {% else %}
                              <th class="text--right"><span class="heading heading--xsmall text--subdued">{{ 'customer.orders.total' | t }}</span></th>
                            {% endif %}
                          </tr>
                        </thead>

                        <tbody>
                          {%- for order in customer.orders -%}

                            {% if section.settings.reorder_buttons_enable %}
                              
                            {% endif %}

                            {% liquid
                              assign reorder_url = ''
                              for line_item in order.line_items
                                assign reorder_url = reorder_url | append: line_item.variant_id | append: ':' | append: line_item.quantity | append: ','
                              endfor
                            -%}

                            <tr class="account__order-table-item">
                              <td><a href="{{ order.customer_url }}" class="link--animated">{{ order.name }}</a></td>
                              <td>{{ order.created_at | date: format: 'date' }}</td>
                              <td>{{ order.financial_status_label }}</td>
                              <td>{{ order.fulfillment_status_label }}</td>

                              {% if section.settings.reorder_buttons_enable %}
                                <td class="">{{ order.total_net_amount | money }}</td>
                                <td class="text--right"><a href="/cart/{{ reorder_url }}" class="reorder-button button button--tiny">{{ 'customer.orders.reorder' | t }}</a></td>
                              {% else %}
                                <td class="text--right">{{ order.total_net_amount | money }}</td>
                              {% endif %}
                            </tr>
                          {%- endfor -%}
                        </tbody>
                      </table>

                      {%- comment -%}On mobile we render them a bit differently{%- endcomment -%}
                      <div class="account__orders-list hidden-tablet-and-up">
                        {%- for order in customer.orders -%}
                          <div class="account__order-list-item">
                            <h2 class="account__order-item-name heading h6">{{ 'customer.orders.order_name' | t: name: order.name }}</h2>

                            <div class="account__order-item-info">
                              <div class="account__order-item-block">
                                <h3 class="heading heading--xsmall text--subdued">{{ 'customer.orders.date' | t }}</h3>
                                <span>{{ order.created_at | date: format: 'date' }}</span>
                              </div>

                              <div class="account__order-item-block">
                                <h3 class="heading heading--xsmall text--subdued">{{ 'customer.orders.fulfillment_status' | t }}</h3>
                                <span>{{ order.fulfillment_status_label }}</span>
                              </div>

                              <div class="account__order-item-block">
                                <h3 class="heading heading--xsmall text--subdued">{{ 'customer.orders.payment_status' | t }}</h3>
                                <span>{{ order.financial_status_label }}</span>
                              </div>

                              <div class="account__order-item-block">
                                <h3 class="heading heading--xsmall text--subdued">{{ 'customer.orders.total' | t }}</h3>
                                <span>{{ order.total_net_amount | money }}</span>
                              </div>
                            </div>

                            <div class="account__order-item-actions">
                              <a class="button button--small button--text button--outline button--full" href="{{ order.customer_url }}">{{ 'customer.orders.view_details' | t }}</a>
  
                              {% if section.settings.reorder_buttons_enable %}
                                <a href="/cart/{{ reorder_url }}" class="reorder-button button button--full button--small button--highlight">{{ 'customer.orders.reorder' | t }}</a>
                              {% endif %}
                            </div>

                          </div>
                        {%- endfor -%}
                      </div>

                      {%- render 'pagination', paginate: paginate -%}
                    {%- endpaginate -%}
                {%- endcase -%}
              </div>
            {%- endfor -%}
          </div>
        </div>
      {%- endif -%}
    </div>

  </div>

</section>

{% schema %}
{
  "name": "Customer account",
  "class": "shopify-section--main-customers-account",
  "settings": [
    {
      "type": "header",
      "content": "Reorder Buttons",
      "info": "Show buttons next to each order giving customers the option to order the same order again."
    },
    {
      "type": "checkbox",
      "id": "reorder_buttons_enable",
      "label": "Enable",
      "default": true
    }
  ],
  "blocks": [
    {
      "type": "@app"
    },
    {
      "type": "liquid",
      "name": "Liquid",
      "settings": [
        {
          "type": "liquid",
          "id": "liquid",
          "label": "Liquid",
          "info": "Add app snippets or other Liquid code to create advanced customizations."
        }
      ]
    },
    {
      "type": "orders",
      "name": "Order list",
      "limit": 1
    }
  ]
}
{% endschema %}