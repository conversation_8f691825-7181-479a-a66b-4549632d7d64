  <div class="quiz-step__inner">

    <div class="quiz-step__body quiz-step__body--forms">

      {% if section.settings.icon != blank %}
        <img class="quiz-step-icon" sizes="(max-width: 740px) calc(100vw - 96px), (max-width: 999px) calc(100vw - 160px), 600px" loading="lazy" {% render 'image-attributes', image: section.settings.icon, sizes: '600,700,800,1000,1200,1400' %}>
      {% endif %}

      {% if section.settings.title != blank %}
        <h1 class="h1 quiz-step-title">{{ section.settings.title }}</h1>
      {% endif %}

      {% if section.settings.description != blank %}
        <div class="quiz-step-description text--large">{{ section.settings.description }}</div>
      {% endif %}

      <quiz-customer-forms class="quiz-customer-forms">

        <quiz-customer-email-form class="">

          <form id="request-email-form" action="#">

            <quiz-step-line class="quiz-step-line">

              <span class="quiz-step-line__text">My e-mail is </span>
              
              <expanding-input>
                <span class="expanding-input__display quiz-step-line__input quiz-input" role="textbox" contenteditable data-type="email" data-default="{{ 'quiz.login.email_placeholder' | t }}"></span>
                <input type="email" class="expanding-input__input" name="request_email" id="request_email" autocomplete="email" required="required" data-dog-age>
              </expanding-input>

              <span class="quiz-step-line__text">.</span>

            </quiz-step-line>

            <div class="input"> 

              <button type="submit" class="button button--highlight" data-quiz-button-next>
                <span class="button__text">{{ 'quiz.general.continue' | t }}</span>
                <span class="button__icon">{%- render 'icon' with 'nav-arrow-right' -%}</span>
              </button>

            </div>

            {% comment %}
            <div class="quiz-customer-form__skip minor">
              {{ 'quiz.request_email.skip_this_step_html' | t }}
            </div>
          {% endcomment %}

          </form>
          
        </quiz-customer-email-form>

      </quiz-customer-forms>

      {% comment %}

      <div class="quiz-step__footer">

        <div class="quiz-step-hint">
          {% comment %} {{ 'quiz.request_email.use_email_access' | t }} {% endcomment %}
          {{ 'quiz.request_email.skip_this_step_html' | t }}
        </div>

      </div>

      {% endcomment %}

    </div>

  </div>

{% schema %} 
{
  "name": "Request Email",
  "class": "shopify-section--quiz-request-email",
  "settings": [
    {
      "type": "header",
      "content": "Home"
    },
    {
      "type": "image_picker",
      "id": "icon",
      "label": "Icon"
    },
    {
      "type": "text",
      "id": "title",
      "label": "Title",
      "default": "Fresh meals for your pup"
    },
    {
      "type": "richtext",
      "id": "description",
      "label": "Description",
      "default": "<p>Fill out our short form so we can provide you with a customized 10 day diet plan and feeding recommendation.</p>"
    }
  ]
}
{% endschema %}