{% render 'quiz-js-variables' %}

<style>

  #shopify-section-{{ section.id }} {
    {%- if section.settings.background == 'rgba(0,0,0,0)' -%}
      {%- assign section_background = settings.background -%}
    {%- else -%}
      {%- assign section_background = section.settings.background -%}
    {%- endif -%}

    {%- if section.settings.heading_color == 'rgba(0,0,0,0)' -%}
      {%- assign heading_color = settings.heading_color -%}
    {%- else -%}
      {%- assign heading_color = section.settings.heading_color -%}
    {%- endif -%}

    {%- if section.settings.text_color == 'rgba(0,0,0,0)' -%}
      {%- assign text_color = settings.text_color -%}
    {%- else -%}
      {%- assign text_color = section.settings.text_color -%}
    {%- endif -%}

    --heading-color: {{ heading_color.red }}, {{ heading_color.green }}, {{ heading_color.blue }};
    --text-color: {{ text_color.red }}, {{ text_color.green }}, {{ text_color.blue }};
    --prev-next-button-background: var(--text-color);
    --prev-next-button-color: var(--section-background);

    --section-background: {{ section_background.red }}, {{ section_background.green }}, {{ section_background.blue }};
  }

</style>

<quiz-results class="quiz-results">

  {% render 'quiz-loading-overlay' %}
  
  {% assign back_button_label = 'quiz.general.start_over' | t %}
  {% render 'quiz-navigation', starting_progress: 1, back_button_label: back_button_label %}

  <section class="section section--flush quiz">
    
    <div class="section__color-wrapper vertical-breather">
      
      <header class="section__header container text-container no-margin--bottom">

        <quiz-sub-navigation class="quiz-sub-navigation quiz-sub-navigation--hidden">
        
          <div class="quiz-step__tabs">

            <div class="container">

              <quiz-dog-navigation class="quiz-step__tabs-inner">

                {% comment %}
                  Dog names are dynamically loaded here.
                {% endcomment%} 
                
              </quiz-dog-navigation>
            
            </div>

          </div>

        </quiz-sub-navigation>

      </header>

      <quiz-results-dogs>

        {% comment %}
          Dog Product Recommendations are dynamically loaded here.
        {% endcomment %}
        
      </quiz-results-dogs>

    </div>

  </section>

  <quiz-results-modals class="quiz-results-modals">

    {% comment %}
      Dog Product Modals are dynamically loaded here.
    {% endcomment %}

  </quiz-results-modals>

  {% render 'quiz-sticky-form' %}
  
</quiz-results>

<script>
  {% if section.settings.quiz_starter_box_freebie != blank %}
    const StarterBoxFreebie = {{ all_products[section.settings.quiz_starter_box_freebie] | json }};
  {% endif %}
</script>

{% schema %}
{
  "name": "Quiz Results",
  "class": "shopify-section--gallery quiz",
  "settings": [
    {
      "type": "header",
      "content": "Colors"
    },
    {
      "type": "product",
      "id": "quiz_starter_box_freebie",
      "label": "Starter Box Freebie",
      "info": "This product will automatically added to the cart in a starter box, if set."
    },
    {
      "type": "checkbox",
      "id": "show_arrows",
      "label": "Show navigation arrows",
      "default": true
    },
    {
      "type": "header",
      "content": "Colors"
    },
    {
      "type": "color",
      "id": "background",
      "label": "Background",
      "default": "rgba(0,0,0,0)"
    },
    {
      "type": "color",
      "id": "text_color",
      "label": "Color",
      "default": "rgba(0,0,0,0)"
    }
  ],
  "presets": [
    {
      "name": "Gallery",
      "settings": {}
    }
  ]
}
{% endschema %}