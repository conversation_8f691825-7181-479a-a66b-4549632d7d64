{% comment %}
---------------------------------------------------------
Copyright © 2023 Section Store. All rights reserved.
Unauthorized copying, modification, distribution, or use
of this code or any portion of it, is strictly prohibited.
Violators will be prosecuted to the fullest extent of the law.
For inquiries or permissions, contact <EMAIL>
---------------------------------------------------------
{% endcomment %}

{%- liquid 
  assign padding_horizontal = section.settings.padding_horizontal
  assign padding_horizontal_mobile = section.settings.padding_horizontal_mobile
  assign padding_top = section.settings.padding_top
  assign padding_bottom = section.settings.padding_bottom
  assign border_color = section.settings.border_color
  assign border_thickness = section.settings.border_thickness
  assign margin_top = section.settings.margin_top
  assign margin_bottom = section.settings.margin_bottom
  assign background_color = section.settings.background_color
  assign background_gradient = section.settings.background_gradient
  assign full_width = section.settings.full_width
  assign content_width = section.settings.content_width
  assign lazy = section.settings.lazy

  assign body_gap_mobile = section.settings.body_gap_mobile
  assign body_gap = section.settings.body_gap

  assign content_horizontal_align_mobile = section.settings.content_horizontal_align_mobile
  assign content_horizontal_align = section.settings.content_horizontal_align

  assign text_align = ""
  if content_horizontal_align == "start"
    assign text_align = "left"
  elsif content_horizontal_align == "center"
    assign text_align = "center"
  else
    assign text_align = "right"
  endif

  assign text_align_mobile = ""
  if content_horizontal_align_mobile == "start"
    assign text_align_mobile = "left"
  elsif content_horizontal_align_mobile == "center"
    assign text_align_mobile = "center"
  else
    assign text_align_mobile = "right"
  endif

  assign heading = section.settings.heading
  assign heading_size = section.settings.heading_size
  assign heading_size_mobile = section.settings.heading_size_mobile
  assign heading_color = section.settings.heading_color
  assign heading_custom = section.settings.heading_custom
  assign heading_font = section.settings.heading_font
  assign heading_height = section.settings.heading_height

  assign text = section.settings.text
  assign text_size = section.settings.text_size
  assign text_size_mobile = section.settings.text_size_mobile
  assign text_color = section.settings.text_color
  assign text_custom = section.settings.text_custom
  assign text_font = section.settings.text_font
  assign text_height = section.settings.text_height
  assign text_mt = section.settings.text_mt

  assign list_gap_mobile = section.settings.list_gap_mobile
  assign list_gap = section.settings.list_gap
  assign list_mt_mobile = section.settings.list_mt_mobile
  assign list_mt = section.settings.list_mt
  assign list_hide_mobile = section.settings.list_hide_mobile

  assign icon = section.settings.icon
  assign icon_size = section.settings.icon_size
  assign icon_size_mobile = section.settings.icon_size_mobile
  assign icon_margin_right = section.settings.icon_margin_right
  assign icon_color = section.settings.icon_color

  assign list_text_size = section.settings.list_text_size
  assign list_text_size_mobile = section.settings.list_text_size_mobile
  assign list_text_color = section.settings.list_text_color
  assign list_text_custom = section.settings.list_text_custom
  assign list_text_font = section.settings.list_text_font
  assign list_text_height = section.settings.list_text_height

  assign button = section.settings.button
  assign button_url = section.settings.button_url
  assign button_size = section.settings.button_size
  assign button_size_mobile = section.settings.button_size_mobile
  assign button_color = section.settings.button_color
  assign button_hover_color = section.settings.button_hover_color
  assign button_custom = section.settings.button_custom
  assign button_font = section.settings.button_font
  assign button_height = section.settings.button_height
  assign button_mt = section.settings.button_mt
  assign button_mt_mobile = section.settings.button_mt_mobile
  assign button_width = section.settings.button_width
  assign button_padding_vertical = section.settings.button_padding_vertical
  assign button_padding_vertical_mobile = section.settings.button_padding_vertical_mobile
  assign button_padding_horizontal = section.settings.button_padding_horizontal
  assign button_padding_horizontal_mobile = section.settings.button_padding_horizontal_mobile
  assign button_radius = section.settings.button_radius
  assign button_border_thickness = section.settings.button_border_thickness
  assign button_border_color = section.settings.button_border_color
  assign button_border_hover_color = section.settings.button_border_hover_color
  assign button_bg_color = section.settings.button_bg_color
  assign button_bg_hover_color = section.settings.button_bg_hover_color
  assign button_style = section.settings.button_style
  assign button_position_mobile = section.settings.button_position_mobile

  assign table_columns = section.settings.table_columns
  assign table_column_active = section.settings.table_column_active
  assign table_column_active_bg_color = section.settings.table_column_active_bg_color
  assign table_radius = section.settings.table_radius
  assign table_border_thickness = section.settings.table_border_thickness
  assign table_border_color = section.settings.table_border_color
  assign table_active_border_color = section.settings.table_active_border_color

  assign first_heading = section.settings.first_heading
  assign first_heading_image = section.settings.first_heading_image
  assign second_heading = section.settings.second_heading
  assign second_heading_image = section.settings.second_heading_image
  assign third_heading = section.settings.third_heading
  assign third_heading_image = section.settings.third_heading_image
  assign four_heading = section.settings.four_heading
  assign four_heading_image = section.settings.four_heading_image

  assign table_heading_image_size_mobile = section.settings.table_heading_image_size_mobile
  assign table_heading_image_size = section.settings.table_heading_image_size 

  assign table_heading_size = section.settings.table_heading_size
  assign table_heading_size_mobile = section.settings.table_heading_size_mobile
  assign table_heading_color = section.settings.table_heading_color
  assign table_heading_active_color = section.settings.table_heading_active_color
  assign table_heading_bg_active_color = section.settings.table_heading_bg_active_color
  assign table_heading_custom = section.settings.table_heading_custom
  assign table_heading_font = section.settings.table_heading_font
  assign table_heading_height = section.settings.table_heading_height
  assign table_heading_align = section.settings.table_heading_align
  assign table_heading_padding_horizontal = section.settings.table_heading_padding_horizontal
  assign table_heading_padding_horizontal_mobile = section.settings.table_heading_padding_horizontal_mobile
  assign table_heading_padding_vertical = section.settings.table_heading_padding_vertical
  assign table_heading_padding_vertical_mobile = section.settings.table_heading_padding_vertical_mobile

  assign table_row_padding_horizontal = section.settings.table_row_padding_horizontal
  assign table_row_padding_horizontal_mobile = section.settings.table_row_padding_horizontal_mobile
  assign table_row_padding_vertical = section.settings.table_row_padding_vertical
  assign table_row_padding_vertical_mobile = section.settings.table_row_padding_vertical_mobile

  assign table_row_heading_size = section.settings.table_row_heading_size
  assign table_row_heading_size_mobile = section.settings.table_row_heading_size_mobile
  assign table_row_heading_color = section.settings.table_row_heading_color
  assign table_row_heading_custom = section.settings.table_row_heading_custom
  assign table_row_heading_font = section.settings.table_row_heading_font
  assign table_row_heading_height = section.settings.table_row_heading_height
  assign table_row_heading_align = section.settings.table_row_heading_align

  assign table_row_text_size = section.settings.table_row_text_size
  assign table_row_text_size_mobile = section.settings.table_row_text_size_mobile
  assign table_row_text_color = section.settings.table_row_text_color
  assign table_row_active_text_color = section.settings.table_row_active_text_color
  assign table_row_text_custom = section.settings.table_row_text_custom
  assign table_row_text_font = section.settings.table_row_text_font
  assign table_row_text_height = section.settings.table_row_text_height
  assign table_row_text_align = section.settings.table_row_text_align

  assign table_row_image_size_mobile = section.settings.table_row_image_size_mobile
  assign table_row_image_size = section.settings.table_row_image_size
  
-%}

{%- style -%}

  {{  heading_font | font_face: font_display: 'swap' }}
  {{  text_font | font_face: font_display: 'swap' }}
  {{  list_text_font | font_face: font_display: 'swap' }}
  {{ table_heading_font | font_face: font_display: 'swap' }}
  {{ table_row_heading_font | font_face: font_display: 'swap' }}
  {{ table_row_text_font | font_face: font_display: 'swap' }}
  {{  button_font | font_face: font_display: 'swap' }}
  
  .section-{{ section.id }} {
    border-top: solid {{ border_color }} {{ border_thickness }}px;
    border-bottom: solid {{ border_color }} {{ border_thickness }}px;
    margin-top: {{ margin_top | times: 0.75 | round: 0 }}px;
    margin-bottom: {{ margin_bottom | times: 0.75 | round: 0 }}px;
  }
  
  .section-{{ section.id }}-settings {
    margin: 0 auto;
    padding-top: {{ padding_top | times: 0.75 | round: 0 }}px;
    padding-bottom: {{ padding_bottom | times: 0.75 | round: 0 }}px;
    padding-left: {{ padding_horizontal_mobile }}rem;
    padding-right: {{ padding_horizontal_mobile }}rem;
  }

  .comparison-body-{{ section.id }} {
    display: grid;
    gap: {{ body_gap_mobile }}px;
  }

  .comparison-content-{{ section.id }} {
    display: flex;
    flex-direction: column;
    align-items: {{ content_horizontal_align_mobile }};
  }

  .comparison-heading-{{ section.id }} {
    width: 100%;
    text-align: {{ text_align_mobile }};
  }

  .comparison-heading-{{ section.id }} * {
    margin: 0;
    font-size: {{ heading_size_mobile }}px;
    color: {{ heading_color }};
    line-height: {{ heading_height }}%;
    text-transform: unset;
  }

  .comparison-text-{{ section.id }} {
    text-align: {{ text_align_mobile }};
    margin-top: {{ text_mt | times: 0.75 | round: 0 }}px;
  }

  .comparison-text-{{ section.id }} * {
    margin: 0;
    font-size: {{ text_size_mobile }}px;
    color: {{ text_color }};
    line-height: {{ text_height }}%;
    text-transform: unset;
  }

  .comparison-items-{{ section.id }} {
    margin-top: {{ list_mt_mobile }}px;
    display: flex;
    flex-direction: column;
    gap: {{ list_gap_mobile }}px;
  }

  .comparison-list-item-{{ section.id }} {
    display: flex;
    align-items: center;
    gap: {{ icon_margin_right | times: 0.75 | round: 0 }}px;
    justify-content: {{ content_horizontal_align_mobile }};
  }

  .comparison-icon-{{ section.id }} {
    flex: 0 0 {{ icon_size_mobile }}px;
    height: {{ icon_size_mobile }}px;
  }

  .comparison-icon-{{ section.id }} img,
  .comparison-icon-{{ section.id }} svg {
    display: block;
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .comparison-icon-{{ section.id }} svg path {
    fill: {{ icon_color }};
  }

  .comparison-list-text-{{ section.id }} {
    margin: 0;
    font-size: {{ list_text_size_mobile }}px;
    color: {{ list_text_color }};
    line-height: {{ list_text_height }}%;
    text-transform: unset;
  }

  .comparison-button-{{ section.id }} {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    width: 100%;
    max-width: 100%;
    margin: 0;
    margin-top: {{ button_mt_mobile }}px;
    font-size: {{ button_size_mobile }}px;
    color: {{ button_color }};
    line-height: {{ button_height }}%;
    text-align: center;
    text-transform: unset;
    text-decoration: none;
    padding: {{ button_padding_vertical_mobile }}px {{ button_padding_horizontal_mobile }}px;
    border-radius: {{ button_radius }}px;
    transition: all 0.25s ease 0s;
    position: relative;
    font-weight: 700;
    z-index: 2;
  }

  .comparison-button-{{ section.id }}:hover {
    color: {{ button_hover_color }};
    transition: all 0.25s ease 0s;
  }

  .comparison-button-{{ section.id }} svg {
    width: 14px;
    height: 14px;
  }

  .comparison-button-{{ section.id }} svg path {
    fill: {{ button_color }};
    transition: all 0.25s ease 0s;
  }

  .comparison-button-{{ section.id }}:hover svg path {
    fill: {{ button_hover_color }};
    transition: all 0.25s ease 0s;
  }

  .comparison-grid-{{ section.id }} {
    display: grid;
    grid-template-columns: 1fr {% if table_columns > 0 %}1fr{% endif %} {% if table_columns > 1 %}1fr{% endif %} {% if table_columns > 2 %}1fr{% endif %} {% if table_columns > 3 %}1fr{% endif %};
  }

  .comparison-item-{{ section.id }} {
    display: flex !important;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }

  .comparison-item-{{ section.id }}:first-child {
    flex-direction: row;
    gap: 20px;
  }

  .comparison-grid-header-{{ section.id }} .comparison-item-{{ section.id }} {
    padding: {{ table_heading_padding_vertical_mobile }}px {{ table_heading_padding_horizontal_mobile }}px;
  }

  .comparison-grid-header-{{ section.id }} .comparison-item-{{ section.id }}.active {
    background-color: {{ table_heading_bg_active_color }};
    border-bottom: 0px;
  }

  .comparison-grid-header-{{ section.id }} .comparison-item-{{ section.id }}.active {
    border: 0px;
  }

  .comparison-grid-header-{{ section.id }} .comparison-item-{{ section.id }} h3 {
    margin: 0;
    font-size: {{ table_heading_size_mobile }}px;
    color: {{ table_heading_color }};
    line-height: {{ table_heading_height }}%;
    text-transform: unset;
    text-align: {{ table_heading_align }};
    display: block;
    width: 100%;
    font-weight: 700;
  }

  .comparison-grid-header-{{ section.id }} .comparison-item-{{ section.id }}.active h3 {
    color: {{ table_heading_active_color }};
  }

  .comparison-grid-header-{{ section.id }} .comparison-item-{{ section.id }} img {
    display: block;
    width: 100%;
    max-width: {{ table_heading_image_size_mobile }}px;
    height: 100%;
    object-fit: contain;
  }

  .comparison-grid-rows-{{ section.id }} .comparison-item-{{ section.id }} {
     padding: {{ table_row_padding_vertical_mobile }}px {{ table_row_padding_horizontal_mobile }}px;
  }

  .comparison-grid-header-{{ section.id }} + .comparison-grid-rows-{{ section.id }} .comparison-item-{{ section.id }}:first-child {
    border-top-left-radius: {{ table_radius }}px;
  }

  .comparison-grid-header-{{ section.id }} .comparison-item-{{ section.id }}:nth-child(2) {
    border-top-left-radius: {{ table_radius }}px;
  }

  .comparison-grid-header-{{ section.id }} .comparison-item-{{ section.id }}:last-child {
    border-top-right-radius: {{ table_radius }}px;
  }

  .comparison-grid-rows-{{ section.id }}:last-child .comparison-item-{{ section.id }}:first-child {
    border-bottom-left-radius: {{ table_radius }}px;
  }

  .comparison-grid-rows-{{ section.id }}:last-child .comparison-item-{{ section.id }}:last-child {
    border-bottom-right-radius: {{ table_radius }}px;
  }

  .comparison-grid-rows-{{ section.id }} .comparison-item-{{ section.id }}.active {
    background-color: {{ table_column_active_bg_color }};
  }

  .comparison-item-{{ section.id }} {
    border-top: {{ table_border_thickness }}px solid {{ table_border_color }};
    border-left: {{ table_border_thickness }}px solid {{ table_border_color }};
  }

  .comparison-item-{{ section.id }}.active {
    border-top: {{ table_border_thickness }}px solid {{ table_active_border_color }};
  }

  .comparison-grid-rows-{{ section.id }}:last-child .comparison-item-{{ section.id }} {
    border-bottom: {{ table_border_thickness }}px solid {{ table_border_color }};
  }
  
  .comparison-grid-rows-{{ section.id }}:last-child .comparison-item-{{ section.id }}.active {
    border-bottom: 0px;
  }

  .comparison-item-{{ section.id }}:last-child {
    border-right: {{ table_border_thickness }}px solid {{ table_border_color }};
  }

  .comparison-grid-header-{{ section.id }} .comparison-item-{{ section.id }}:first-child {
    border: 0px;
  }

  .comparison-grid-rows-{{ section.id }} .comparison-item-{{ section.id }}:first-child h3 {
    margin: 0;
    font-size: {{ table_row_heading_size_mobile }}px;
    color: {{ table_row_heading_color }};
    line-height: {{ table_row_heading_height }}%;
    text-transform: unset;
    text-align: {{ table_row_heading_align }};
    display: block;
    width: 100%;
  }

  .comparison-item-text-{{ section.id }} {
    margin: 0;
    font-size: {{ table_row_text_size_mobile }}px;
    color: {{ table_row_text_color }};
    line-height: {{ table_row_text_height }}%;
    text-transform: unset;
    text-align: {{ table_row_text_align }};
    display: block;
    width: 100%;
  }

  .comparison-item-{{ section.id }}.active .comparison-item-text-{{ section.id }} {
    color: {{ table_row_active_text_color }};
  }

  .comparison-item-image-{{ section.id }} {
    width: {{ table_row_image_size_mobile }}px;
    height: {{ table_row_image_size_mobile }}px;
  }

  .comparison-item-image-{{ section.id }} img,
  .comparison-item-image-{{ section.id }} svg {
    display: block;
    object-fit: contain;
    width: 100%;
    height: 100%;
  }
  
  @media(min-width: 1024px) {

    .section-{{ section.id }} {
      margin-top: {{ margin_top }}px;
      margin-bottom: {{ margin_bottom }}px;
    }
    
    .section-{{ section.id }}-settings {
      padding: 0 5rem;
      padding-top: {{ padding_top }}px;
      padding-bottom: {{ padding_bottom }}px;
      padding-left: {{ padding_horizontal }}rem;
      padding-right: {{ padding_horizontal }}rem;
    }

    .comparison-body-{{ section.id }} {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: {{ body_gap }}px;
    }

    .comparison-content-{{ section.id }} {
      align-items: {{ content_horizontal_align }};
    }

    .comparison-heading-{{ section.id }} {
      text-align: {{ text_align }};
    }

    .comparison-heading-{{ section.id }} * {
      font-size: {{ heading_size }}px;
    }

    .comparison-text-{{ section.id }} {
      margin-top: {{ text_mt }}px;
      text-align: {{ text_align }};
    }
  
    .comparison-text-{{ section.id }} * {
      font-size: {{ text_size }}px;
    }

    .comparison-list-text-{{ section.id }} {
      font-size: {{ list_text_size }}px;
    }

    .comparison-items-{{ section.id }} {
      display: flex !important;
      margin-top: {{ list_mt }}px;
      gap: {{ list_gap }}px;
    }

    .comparison-list-item-{{ section.id }} {
      gap: {{ icon_margin_right }}px;
      justify-content: {{ content_horizontal_align }};
    }
  
    .comparison-icon-{{ section.id }} {
      flex: 0 0 {{ icon_size }}px;
      height: {{ icon_size }}px;
    }

    .comparison-button-{{ section.id }} {
      margin-top: {{ button_mt }}px;
      max-width: {{ button_width }}px;
      padding: {{ button_padding_vertical }}px {{ button_padding_horizontal }}px;
      font-size: {{ button_size }}px;
    }

    .comparison-button-top-{{ section.id }} {
      display: flex !important;
    }

    .comparison-button-bottom-{{ section.id }} {
      display: none !important;
    }

    .comparison-grid-{{ section.id }} {
      grid-template-columns: 1fr {% if table_columns > 0 %}1fr{% endif %} {% if table_columns > 1 %}1fr{% endif %} {% if table_columns > 2 %}1fr{% endif %} {% if table_columns > 3 %}1fr{% endif %};
    }

    .comparison-grid-header-{{ section.id }} .comparison-item-{{ section.id }} img {
      max-width: {{ table_heading_image_size }}px;
    }

    .comparison-grid-header-{{ section.id }} .comparison-item-{{ section.id }} {
      padding: {{ table_heading_padding_vertical }}px {{ table_heading_padding_horizontal }}px;
    }

    .comparison-grid-header-{{ section.id }} .comparison-item-{{ section.id }} h3 {
      font-size: {{ table_heading_size }}px;
    }

    .comparison-grid-rows-{{ section.id }} .comparison-item-{{ section.id }} {
      padding: {{ table_row_padding_vertical }}px {{ table_row_padding_horizontal }}px;
    }

    .comparison-grid-rows-{{ section.id }} .comparison-item-{{ section.id }}:first-child h3 {
      font-size: {{ table_row_heading_size }}px;
    }

    .comparison-item-text-{{ section.id }} {
      font-size: {{ table_row_text_size }}px;
    }

    .comparison-item-image-{{ section.id }} {
      width: {{ table_row_image_size }}px;
      height: {{ table_row_image_size }}px;
    }
  }
  
{%- endstyle -%}

{% unless full_width %}
  <style>
    .section-{{ section.id }}-settings {
      max-width: {{ content_width }}rem;
    }
  </style>
{% endunless %}

{% if heading_custom %}
  <style>
    .comparison-heading-{{ section.id }} * {
      font-family: {{ heading_font.family }}, {{ heading_font.fallback_families }};
      font-weight: {{ heading_font.weight }};
      font-style: {{ heading_font.style }};
    }
  </style>
{% endif %}

{% if text_custom %}
  <style>
    .comparison-text-{{ section.id }} * {
      font-family: {{ text_font.family }}, {{ text_font.fallback_families }};
      font-weight: {{ text_font.weight }};
      font-style: {{ text_font.style }};
    }
  </style>
{% endif %}

{% if list_text_custom %}
  <style>
    .comparison-list-text-{{ section.id }} {
      font-family: {{ list_text_font.family }}, {{ list_text_font.fallback_families }};
      font-weight: {{ list_text_font.weight }};
      font-style: {{ list_text_font.style }};
    }
  </style>
{% endif %}

{% if button_custom %}
  <style>
    .comparison-button-{{ section.id }} {
      font-family: {{ button_font.family }}, {{ button_font.fallback_families }};
      font-weight: {{ button_font.weight }};
      font-style: {{ button_font.style }};
    }
  </style>
{% endif %}

{% if table_heading_custom %}
  <style>
    .comparison-grid-header-{{ section.id }} .comparison-item-{{ section.id }} h3 {
      font-family: {{ table_heading_font.family }}, {{ table_heading_font.fallback_families }};
      font-weight: {{ table_heading_font.weight }};
      font-style: {{ table_heading_font.style }};
    }
  </style>
{% endif %}

{% if table_row_heading_custom %}
  <style>
    .comparison-grid-rows-{{ section.id }} .comparison-item-{{ section.id }}:first-child h3 {
      font-family: {{ table_row_heading_font.family }}, {{ table_row_heading_font.fallback_families }};
      font-weight: {{ table_row_heading_font.weight }};
      font-style: {{ table_row_heading_font.style }};
    }
  </style>
{% endif %}

{% if table_row_text_custom %}
  <style>
    .comparison-item-text-{{ section.id }} {
      font-family: {{ table_row_text_font.family }}, {{ table_row_text_font.fallback_families }};
      font-weight: {{ table_row_text_font.weight }};
      font-style: {{ table_row_text_font.style }};
    }
  </style>
{% endif %}

{% if text_align_mobile == "center" %}
  <style>
   .comparison-button-{{ section.id }} {
      margin-left: auto;
      margin-right: auto;
    } 
  </style>
{% elsif text_align_mobile == "right" %}
  <style>
   .comparison-button-{{ section.id }} {
      margin-left: auto;
    } 
  </style>
{% endif %}

{% if text_align == "center" %}
  <style>
    @media(min-width: 1024px) {
     .comparison-button-{{ section.id }} {
        margin-left: auto !important;
        margin-right: auto !important;
      } 
    }
  </style>
{% elsif text_align == "right" %}
  <style>
    @media(min-width: 1024px) {
     .comparison-button-{{ section.id }} {
        margin-left: auto !important;
        margin-right: 0px !important;
      } 
    }
  </style>
{% else %}
  <style>
    @media(min-width: 1024px) {
     .comparison-button-{{ section.id }} {
        margin-left: 0px !important;
        margin-right: 0px !important;
      } 
    }
  </style>
{% endif %}

{% if button_style == "non_outline" or button_style == "non_outline_arrow" %}
  <style>
    .comparison-button-{{ section.id }} {
      background-color: {{ button_bg_color }};
    }

    .comparison-button-{{ section.id }}:hover {
      background-color: {{ button_bg_hover_color }};
    }
  </style>
{% elsif button_style == "outline" or button_style == "outline_arrow" %}
  <style>
    .comparison-button-{{ section.id }} {
      background-color: {{ button_bg_color }};
      border: {{ button_border_thickness }}px solid {{ button_border_color }};
    }

    .comparison-button-{{ section.id }}:hover {
      background-color: {{ button_bg_hover_color }};
      border: {{ button_border_thickness }}px solid {{ button_border_hover_color }};
    }
  </style>
{% endif %}

{% if button_position_mobile == "top" %}
  <style>
    .comparison-button-bottom-{{ section.id }} {
      display: none
    }
  </style>
{% else %}
  <style>
    .comparison-button-top-{{ section.id }} {
      display: none
    }
  </style>
{% endif %}

{% if list_hide_mobile %}
  <style>
    .comparison-items-{{ section.id }} {
      display: none;
    }
  </style>
{% endif %}

<div class="section-{{ section.id }} comparison-{{ section.id }}" style="background-color:{{ background_color }}; background-image: {{ background_gradient }};">
    <div class="section-{{ section.id }}-settings">
      <div class="comparison-body-{{ section.id }}">
        <div class="comparison-content-{{ section.id }}">
          
          {% if heading != blank %}
            <div class="comparison-heading-{{ section.id }}">
              {{ heading }}
            </div>
          {% endif %}

          {% if text != blank %}
            <div class="comparison-text-{{ section.id }}">
              {{ text }}
            </div>
          {% endif %}

          <div class="comparison-items-{{ section.id }}">
            {% for block in section.blocks %}
              {% if block.type == "list_item" %}
                <div class="comparison-list-item-{{ section.id }}">
                  {% if block.settings.image != blank or block.settings.icon != "none" %}
                    <div class="comparison-icon-{{ section.id }}">
                      {% if icon != blank %}
                        <img src="{{ icon | image_url }}" alt="{{ icon.alt }}" {% if lazy %}loading="lazy"{% endif %}>
                      {% else %}
                        <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path d="M15.9993 0C7.17099 0 0 7.17232 0 16.0007C0 24.8291 7.17301 32 16.0007 32C24.8182 32 31.6649 24.9979 31.6649 16.1876C31.6649 13.9113 31.2546 12.2459 30.3966 10.3013C30.3613 10.2178 30.3098 10.1421 30.245 10.0787C30.1802 10.0153 30.1034 9.96546 30.0191 9.93201C29.9349 9.89855 29.8448 9.8822 29.7542 9.88388C29.6635 9.88557 29.5741 9.90527 29.4912 9.94183C29.4082 9.9784 29.3333 10.0311 29.271 10.0969C29.2086 10.1626 29.1599 10.2402 29.1277 10.3249C29.0956 10.4097 29.0806 10.5 29.0837 10.5906C29.0867 10.6812 29.1078 10.7703 29.1456 10.8527C29.9555 12.6881 30.2978 14.0672 30.2978 16.1876C30.2978 24.2951 24.0996 30.6329 16.0007 30.6329C7.91184 30.6329 1.3671 24.0901 1.3671 16.0007C1.3671 7.91116 7.90983 1.3671 15.9993 1.3671C20.5707 1.3671 24.5944 3.5062 27.2779 6.78476C20.614 10.421 15.6561 17.941 13.0061 23.772C10.3992 18.8219 6.69798 14.9526 6.69798 14.9526C6.63564 14.8867 6.56079 14.8338 6.47779 14.7971C6.3948 14.7604 6.30532 14.7406 6.21458 14.7388C6.12385 14.737 6.03366 14.7533 5.94929 14.7868C5.86493 14.8202 5.78806 14.8701 5.72318 14.9336C5.6583 14.997 5.60671 15.0728 5.57141 15.1564C5.53611 15.24 5.51782 15.3298 5.51759 15.4205C5.51736 15.5113 5.5352 15.6012 5.57008 15.685C5.60495 15.7688 5.65617 15.8448 5.72072 15.9085C5.72072 15.9085 9.97141 20.2729 12.4227 25.5236C12.4788 25.6435 12.5685 25.7445 12.6808 25.8144C12.7932 25.8843 12.9235 25.9201 13.0558 25.9174C13.1881 25.9147 13.3168 25.8737 13.4262 25.7993C13.5357 25.7249 13.6212 25.6203 13.6723 25.4983C16.1489 19.5801 21.6708 10.9928 28.609 7.58313C28.7059 7.54455 28.7927 7.4843 28.8628 7.40699C28.9328 7.32968 28.9842 7.23736 29.0131 7.1371C29.042 7.03685 29.0475 6.93131 29.0293 6.82859C29.0111 6.72586 28.9696 6.62867 28.908 6.54445C25.9966 2.57708 21.2935 0 15.9993 0Z" fill="#1A192E"></path>
                        </svg>
                      {% endif %}
                    </div>
                  {% endif %}
                  <p class="comparison-list-text-{{ section.id }}">{{ block.settings.text }}</p>
                </div>
              {% endif %}
            {% endfor %}
          </div>

          {% if button != blank %}
            <a href="{{ button_url }}" class="comparison-button-{{ section.id }} comparison-button-top-{{ section.id }}">
              {{ button }}
              {% if button_style == "non_outline_arrow" or button_style == "outline_arrow" %}
                <svg width="192" height="192" viewBox="0 0 192 192" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M187.827 98.7858C188.123 98.4749 188.123 98.4749 188.123 98.1761C188.419 97.8652 188.419 97.5663 188.704 97.5663C189 96.9566 189 96.6458 189 96.0361C189 95.4263 189 94.8166 188.704 94.5058C188.704 94.195 188.408 93.8961 188.123 93.5852C188.123 93.2744 187.827 93.2744 187.827 92.9755L103.287 4.21945C102.41 3.29889 101.533 3 100.668 3C99.791 3 98.6295 3.31083 98.0488 4.21945C97.1719 5.14 96.8872 6.06058 96.8872 6.96919C96.8872 7.88974 97.1833 9.10918 98.0488 9.7189L175.587 92.0539H6.79206C4.75371 92.0539 3 93.895 3 96.0351C3 98.1751 4.75365 100.016 6.79206 100.016H175.575L97.7543 182.042C96.2967 183.572 96.2967 186.322 97.7543 187.852C99.2119 189.383 101.831 189.383 103.288 187.852L187.827 98.7858Z" fill="black"></path>
                  <path fill-rule="evenodd" clip-rule="evenodd" d="M188.607 97.5657C188.432 97.5657 188.365 97.6788 188.27 97.8382C188.211 97.9378 188.141 98.0554 188.027 98.1748C188.027 98.4734 188.027 98.4734 187.731 98.7839L103.281 187.759C101.825 189.287 99.2085 189.287 97.7524 187.759C96.2963 186.23 96.2963 183.483 97.7524 181.954L175.492 100.013L6.88489 100.013C4.8486 100.013 3.09677 98.1739 3.09677 96.036C3.09677 93.8982 4.84866 92.059 6.88489 92.059L175.504 92.059L98.0465 9.80878C97.182 9.19968 96.8862 7.9815 96.8862 7.0619C96.8862 6.15422 97.1706 5.2346 98.0465 4.315C98.6267 3.40732 99.787 3.0968 100.663 3.0968C101.527 3.0968 102.403 3.39539 103.279 4.315L187.731 92.9796C187.731 93.1274 187.804 93.2021 187.877 93.2774C187.952 93.3543 188.027 93.4319 188.027 93.5887C188.046 93.6098 188.066 93.6308 188.085 93.6518C188.338 93.9267 188.584 94.1935 188.606 94.4689C188.607 94.482 188.607 94.4951 188.607 94.5083C188.903 94.8188 188.903 95.4279 188.903 96.037C188.903 96.6461 188.903 96.9566 188.607 97.5657ZM191.489 93.2767C191.79 93.8661 191.89 94.4204 191.934 94.7363C192.001 95.2226 192 95.7194 192 95.9856L192 96.037C192 96.0544 192 96.0729 192 96.0926C192 96.3523 192.001 96.8096 191.924 97.2931C191.828 97.8884 191.64 98.41 191.393 98.9184L190.546 100.663H190.212C190.127 100.759 190.038 100.852 189.988 100.905L189.974 100.92L105.527 189.891C102.85 192.701 98.1865 192.704 95.51 189.895C94.1476 188.464 93.5636 186.587 93.5636 184.857C93.5636 183.128 94.1468 181.252 95.5071 179.822M191.489 93.2767C191.316 92.7908 191.078 92.4357 190.938 92.2406C190.903 92.1912 190.866 92.142 190.828 92.0939V91.7408L105.522 2.17912C104.076 0.661813 102.397 0 100.663 0C99.3914 0 97.0569 0.401401 95.6212 2.37737C94.3151 3.83819 93.7895 5.45521 93.7895 7.0619C93.7895 8.26663 94.1183 10.6061 95.9608 12.111L168.333 88.9622L6.88489 88.9622C2.9981 88.9622 0 92.3316 0 96.036C0 99.7405 2.99801 103.11 6.88489 103.11L168.285 103.11" fill="black"></path>
                  <path d="M169.5 104L16.5 102.5V90H171L97.5 10.5V7L107.5 4.5C131.167 29.6667 180.2 81.5 187 87.5C193.8 93.5 191.5 99 189.5 101L105.5 184L94 181.5L169.5 104Z" fill="black"></path>
                </svg>
              {% endif %}
            </a>
          {% endif %}
          
        </div>

        <div class="comparison-table-{{ section.id }}" style="align-content: center;">
          <div class="comparison-grid-{{ section.id }} comparison-grid-header-{{ section.id }}">
            <div class="comparison-item-{{ section.id }}"></div>
            {% if first_heading != blank or first_heading_image != blank %}
              <div class="comparison-item-{{ section.id }} {% if table_column_active == "1" %}active{% endif %}">
                {% if first_heading_image != blank %}
                    <img src="{{ first_heading_image | image_url }}" alt="{{ first_heading_image.alt }}" {% if lazy %}loading="lazy"{% endif %}>
                  {% else %}
                    <h3>{{ first_heading }}</h3>
                  {% endif %}
              </div>
            {% endif %}
            {% if table_columns > 1 and second_heading != blank or second_heading_image != blank %}
              <div class="comparison-item-{{ section.id }} {% if table_column_active == "2" %}active{% endif %}">
                {% if second_heading_image != blank %}
                    <img src="{{ second_heading_image | image_url }}" alt="{{ second_heading_image.alt }}" {% if lazy %}loading="lazy"{% endif %}>
                  {% else %}
                    <h3>{{ second_heading }}</h3>
                  {% endif %}
              </div>
            {% endif %}
            {% if table_columns > 2 and third_heading != blank or third_heading_image != blank %}
              <div class="comparison-item-{{ section.id }} {% if table_column_active == "3" %}active{% endif %}">
                {% if third_heading_image != blank %}
                  <img src="{{ third_heading_image | image_url }}" alt="{{ third_heading_image.alt }}" {% if lazy %}loading="lazy"{% endif %}>
                {% else %}
                  <h3>{{ third_heading }}</h3>
                {% endif %}
              </div>
            {% endif %}
            {% if table_columns > 3 and four_heading != blank or four_heading_image != blank %}
              <div class="comparison-item-{{ section.id }} {% if table_column_active == "3" %}active{% endif %}">
                {% if four_heading_image != blank %}
                  <img src="{{ four_heading_image | image_url }}" alt="{{ four_heading_image.alt }}" {% if lazy %}loading="lazy"{% endif %}>
                {% else %}
                  <h3>{{ four_heading }}</h3>
                {% endif %}
              </div>
            {% endif %}
          </div>
          {% for block in section.blocks %}
            {% if block.type == "table_row" %}
              <div class="comparison-grid-{{ section.id }} comparison-grid-rows-{{ section.id }}">
                <div class="comparison-item-{{ section.id }}">
                    <h3>{{ block.settings.row_heading }}</h3>
                </div>
                {% if first_heading != blank or first_heading_image != blank %}
                  <div class="comparison-item-{{ section.id }} {% if table_column_active == "1" %}active{% endif %}">
                    {% if block.settings.first_column_image != blank or block.settings.first_column_icon != "none" %}
                      <div class="comparison-item-image-{{ section.id }}">
                        {% if block.settings.first_column_image != blank %}
                          <img src="{{ block.settings.first_column_image | image_url }}" alt="{{ block.settings.first_column_image.alt }}" {% if lazy %}loading="lazy"{% endif %}>
                        {% else %}
                          {% if block.settings.first_column_icon == "check" %}
                            <svg width="32" height="22" viewBox="0 0 37 27" fill="none" xmlns="http://www.w3.org/2000/svg">
                              <path d="M34.4998 2.5L12.4998 24.5L2.49976 14.5" stroke="#A1D3A2" stroke-width="5" stroke-linecap="round" stroke-linejoin="round"></path>
                            </svg>
                          {% elsif block.settings.first_column_icon == "cross" %}
                            <svg width="24" height="24" viewBox="0 0 49 49" fill="none" xmlns="http://www.w3.org/2000/svg">
                              <path d="M36.5 12.5L12.5 36.5" stroke="#F37E98" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"></path>
                              <path d="M12.5 12.5L36.5 36.5" stroke="#F37E98" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"></path>
                            </svg>
                          {% endif %}
                        {% endif %}
                      </div>
                    {% endif %}
                    {% if block.settings.first_column != blank %}
                      <p class="comparison-item-text-{{ section.id }}">{{ block.settings.first_column }}</p>
                    {% endif %}
                  </div>
                {% endif %}
                {% if table_columns > 1 and second_heading != blank or second_heading_image != blank %}
                  <div class="comparison-item-{{ section.id }} {% if table_column_active == "2" %}active{% endif %}">
                    {% if block.settings.second_column_image != blank or block.settings.second_column_icon != "none" %}
                      <div class="comparison-item-image-{{ section.id }}">
                        {% if block.settings.second_column_image != blank %}
                          <img src="{{ block.settings.second_column_image | image_url }}" alt="{{ block.settings.second_column_image.alt }}" {% if lazy %}loading="lazy"{% endif %}>
                        {% else %}
                          {% if block.settings.second_column_icon == "check" %}
                            <svg width="32" height="22" viewBox="0 0 37 27" fill="none" xmlns="http://www.w3.org/2000/svg">
                              <path d="M34.4998 2.5L12.4998 24.5L2.49976 14.5" stroke="#A1D3A2" stroke-width="5" stroke-linecap="round" stroke-linejoin="round"></path>
                            </svg>
                          {% elsif block.settings.second_column_icon == "cross" %}
                            <svg width="24" height="24" viewBox="0 0 49 49" fill="none" xmlns="http://www.w3.org/2000/svg">
                              <path d="M36.5 12.5L12.5 36.5" stroke="#F37E98" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"></path>
                              <path d="M12.5 12.5L36.5 36.5" stroke="#F37E98" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"></path>
                            </svg>
                          {% endif %}
                        {% endif %}
                      </div>
                    {% endif %}
                    {% if block.settings.second_column != blank %}
                      <p class="comparison-item-text-{{ section.id }}">{{ block.settings.second_column }}</p>
                    {% endif %}
                  </div>
                {% endif %}
                {% if table_columns > 2 and third_heading != blank or third_heading_image != blank %}
                  <div class="comparison-item-{{ section.id }} {% if table_column_active == "3" %}active{% endif %}">
                    {% if block.settings.third_column_image != blank or block.settings.third_column_icon != "none" %}
                      <div class="comparison-item-image-{{ section.id }}">
                        {% if block.settings.third_column_image != blank %}
                          <img src="{{ block.settings.third_column_image | image_url }}" alt="{{ block.settings.third_column_image.alt }}" {% if lazy %}loading="lazy"{% endif %}>
                        {% else %}
                          {% if block.settings.third_column_icon == "check" %}
                            <svg width="32" height="22" viewBox="0 0 37 27" fill="none" xmlns="http://www.w3.org/2000/svg">
                              <path d="M34.4998 2.5L12.4998 24.5L2.49976 14.5" stroke="#A1D3A2" stroke-width="5" stroke-linecap="round" stroke-linejoin="round"></path>
                            </svg>
                          {% elsif block.settings.third_column_icon == "cross" %}
                            <svg width="24" height="24" viewBox="0 0 49 49" fill="none" xmlns="http://www.w3.org/2000/svg">
                              <path d="M36.5 12.5L12.5 36.5" stroke="#F37E98" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"></path>
                              <path d="M12.5 12.5L36.5 36.5" stroke="#F37E98" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"></path>
                            </svg>
                          {% endif %}
                        {% endif %}
                      </div>
                    {% endif %}
                    {% if block.settings.third_column != blank %}
                      <p class="comparison-item-text-{{ section.id }}">{{ block.settings.third_column }}</p>
                    {% endif %}
                  </div>
                {% endif %}
                {% if table_columns > 3 and four_heading != blank or four_heading_image != blank %}
                  <div class="comparison-item-{{ section.id }} {% if table_column_active == "4" %}active{% endif %}">
                   {% if block.settings.four_column_image != blank or block.settings.four_column_icon != "none" %}
                      <div class="comparison-item-image-{{ section.id }}">
                        {% if block.settings.four_column_image != blank %}
                          <img src="{{ block.settings.four_column_image | image_url }}" alt="{{ block.settings.four_column_image.alt }}" {% if lazy %}loading="lazy"{% endif %}>
                        {% else %}
                          {% if block.settings.four_column_icon == "check" %}
                            <svg width="32" height="22" viewBox="0 0 37 27" fill="none" xmlns="http://www.w3.org/2000/svg">
                              <path d="M34.4998 2.5L12.4998 24.5L2.49976 14.5" stroke="#A1D3A2" stroke-width="5" stroke-linecap="round" stroke-linejoin="round"></path>
                            </svg>
                          {% elsif block.settings.four_column_icon == "cross" %}
                            <svg width="24" height="24" viewBox="0 0 49 49" fill="none" xmlns="http://www.w3.org/2000/svg">
                              <path d="M36.5 12.5L12.5 36.5" stroke="#F37E98" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"></path>
                              <path d="M12.5 12.5L36.5 36.5" stroke="#F37E98" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"></path>
                            </svg>
                          {% endif %}
                        {% endif %}
                      </div>
                    {% endif %}
                    {% if block.settings.four_column != blank %}
                      <p class="comparison-item-text-{{ section.id }}">{{ block.settings.four_column }}</p>
                    {% endif %}
                  </div>
                {% endif %}
              </div>
            {% endif %}
          {% endfor %}
        </div>
        
      </div>

      {% if button != blank %}
        <a href="{{ button_url }}" class="comparison-button-{{ section.id }} comparison-button-bottom-{{ section.id }}">
          {{ button }}
          {% if button_style == "non_outline_arrow" or button_style == "outline_arrow" %}
            <svg width="192" height="192" viewBox="0 0 192 192" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M187.827 98.7858C188.123 98.4749 188.123 98.4749 188.123 98.1761C188.419 97.8652 188.419 97.5663 188.704 97.5663C189 96.9566 189 96.6458 189 96.0361C189 95.4263 189 94.8166 188.704 94.5058C188.704 94.195 188.408 93.8961 188.123 93.5852C188.123 93.2744 187.827 93.2744 187.827 92.9755L103.287 4.21945C102.41 3.29889 101.533 3 100.668 3C99.791 3 98.6295 3.31083 98.0488 4.21945C97.1719 5.14 96.8872 6.06058 96.8872 6.96919C96.8872 7.88974 97.1833 9.10918 98.0488 9.7189L175.587 92.0539H6.79206C4.75371 92.0539 3 93.895 3 96.0351C3 98.1751 4.75365 100.016 6.79206 100.016H175.575L97.7543 182.042C96.2967 183.572 96.2967 186.322 97.7543 187.852C99.2119 189.383 101.831 189.383 103.288 187.852L187.827 98.7858Z" fill="black"></path>
              <path fill-rule="evenodd" clip-rule="evenodd" d="M188.607 97.5657C188.432 97.5657 188.365 97.6788 188.27 97.8382C188.211 97.9378 188.141 98.0554 188.027 98.1748C188.027 98.4734 188.027 98.4734 187.731 98.7839L103.281 187.759C101.825 189.287 99.2085 189.287 97.7524 187.759C96.2963 186.23 96.2963 183.483 97.7524 181.954L175.492 100.013L6.88489 100.013C4.8486 100.013 3.09677 98.1739 3.09677 96.036C3.09677 93.8982 4.84866 92.059 6.88489 92.059L175.504 92.059L98.0465 9.80878C97.182 9.19968 96.8862 7.9815 96.8862 7.0619C96.8862 6.15422 97.1706 5.2346 98.0465 4.315C98.6267 3.40732 99.787 3.0968 100.663 3.0968C101.527 3.0968 102.403 3.39539 103.279 4.315L187.731 92.9796C187.731 93.1274 187.804 93.2021 187.877 93.2774C187.952 93.3543 188.027 93.4319 188.027 93.5887C188.046 93.6098 188.066 93.6308 188.085 93.6518C188.338 93.9267 188.584 94.1935 188.606 94.4689C188.607 94.482 188.607 94.4951 188.607 94.5083C188.903 94.8188 188.903 95.4279 188.903 96.037C188.903 96.6461 188.903 96.9566 188.607 97.5657ZM191.489 93.2767C191.79 93.8661 191.89 94.4204 191.934 94.7363C192.001 95.2226 192 95.7194 192 95.9856L192 96.037C192 96.0544 192 96.0729 192 96.0926C192 96.3523 192.001 96.8096 191.924 97.2931C191.828 97.8884 191.64 98.41 191.393 98.9184L190.546 100.663H190.212C190.127 100.759 190.038 100.852 189.988 100.905L189.974 100.92L105.527 189.891C102.85 192.701 98.1865 192.704 95.51 189.895C94.1476 188.464 93.5636 186.587 93.5636 184.857C93.5636 183.128 94.1468 181.252 95.5071 179.822M191.489 93.2767C191.316 92.7908 191.078 92.4357 190.938 92.2406C190.903 92.1912 190.866 92.142 190.828 92.0939V91.7408L105.522 2.17912C104.076 0.661813 102.397 0 100.663 0C99.3914 0 97.0569 0.401401 95.6212 2.37737C94.3151 3.83819 93.7895 5.45521 93.7895 7.0619C93.7895 8.26663 94.1183 10.6061 95.9608 12.111L168.333 88.9622L6.88489 88.9622C2.9981 88.9622 0 92.3316 0 96.036C0 99.7405 2.99801 103.11 6.88489 103.11L168.285 103.11" fill="black"></path>
              <path d="M169.5 104L16.5 102.5V90H171L97.5 10.5V7L107.5 4.5C131.167 29.6667 180.2 81.5 187 87.5C193.8 93.5 191.5 99 189.5 101L105.5 184L94 181.5L169.5 104Z" fill="black"></path>
            </svg>
          {% endif %}
        </a>
      {% endif %}
    </div>
</div>

{% schema %}
  {
    "name": "SS - Comparison Table #6",
    "settings": [
      {
        "type": "header",
        "content": "Body Settings"
      },
      {
        "type": "range",
        "id": "body_gap",
        "min": 0,
        "max": 100,
        "step": 2,
        "unit": "px",
        "label": "Body Gap",
        "default": 32
      },
      {
        "type": "range",
        "id": "body_gap_mobile",
        "min": 0,
        "max": 100,
        "step": 2,
        "unit": "px",
        "label": "Body Gap - Mobile",
        "default": 32
      },
      {
        "type": "header",
        "content": "Content Settings"
      },
      {
        "type": "select",
        "id": "content_horizontal_align",
        "label": "Content Horizontal Align",
        "default": "start",
        "options": [
          {
            "label": "Left",
            "value": "start"
          },
          {
            "label": "Center",
            "value": "center"
          },
          {
            "label": "Right",
            "value": "end"
          }
        ]
      },
      {
        "type": "select",
        "id": "content_horizontal_align_mobile",
        "label": "Content Horizontal Align - Mobile",
        "default": "center",
        "options": [
          {
            "label": "Left",
            "value": "start"
          },
          {
            "label": "Center",
            "value": "center"
          },
          {
            "label": "Right",
            "value": "end"
          }
        ]
      },
      {
        "type": "header",
        "content": "Heading Settings"
      },
      {
        "type": "richtext",
        "id": "heading",
        "label": "Heading",
        "default": "<p><em>US</em> <strong>vs Other Brands</strong></p>"
      },
      {
        "type": "checkbox",
        "id": "heading_custom",
        "label": "Use Custom Font",
        "default": false
      },
      {
        "type": "font_picker",
        "id": "heading_font",
        "label": "Heading Font Family",
        "default": "josefin_sans_n4"
      },
      {
        "type": "range",
        "id": "heading_size",
        "min": 0,
        "max": 72,
        "step": 2,
        "unit": "px",
        "label": "Heading Size",
        "default": 52
      },
      {
        "type": "range",
        "id": "heading_size_mobile",
        "min": 0,
        "max": 72,
        "step": 2,
        "unit": "px",
        "label": "Heading Size - Mobile",
        "default": 32
      },
      {
        "type": "range",
        "id": "heading_height",
        "min": 50,
        "max": 200,
        "step": 10,
        "unit": "%",
        "label": "Heading Line Height",
        "default": 150
      },
      {
        "type": "header",
        "content": "Text Settings"
      },
      {
        "type": "richtext",
        "id": "text",
        "label": "Text",
        "default": "<p>We pride ourselves in our quality and focus on sleep, we cannot say the same for others.</p>"
      },
      {
        "type": "checkbox",
        "id": "text_custom",
        "label": "Use Custom Font",
        "default": false
      },
      {
        "type": "font_picker",
        "id": "text_font",
        "label": "Text Font Family",
        "default": "josefin_sans_n4"
      },
      {
        "type": "range",
        "id": "text_size",
        "min": 0,
        "max": 40,
        "step": 1,
        "unit": "px",
        "label": "Text Size",
        "default": 18
      },
      {
        "type": "range",
        "id": "text_size_mobile",
        "min": 0,
        "max": 40,
        "step": 1,
        "unit": "px",
        "label": "Text Size - Mobile",
        "default": 18
      },
      {
        "type": "range",
        "id": "text_height",
        "min": 50,
        "max": 200,
        "step": 10,
        "unit": "%",
        "label": "Text Line Height",
        "default": 150
      },
      {
        "type": "range",
        "id": "text_mt",
        "min": 0,
        "max": 100,
        "step": 2,
        "unit": "px",
        "label": "Text Margin Top",
        "default": 4
      },
      {
        "type": "header",
        "content": "List Items Settings"
      },
      {
        "type": "range",
        "id": "list_mt",
        "min": 0,
        "max": 100,
        "step": 2,
        "unit": "px",
        "label": "List Margin Top",
        "default": 50
      },
      {
        "type": "range",
        "id": "list_mt_mobile",
        "min": 0,
        "max": 100,
        "step": 2,
        "unit": "px",
        "label": "List Margin Top - Mobile",
        "default": 32
      },
      {
        "type": "range",
        "id": "list_gap",
        "min": 0,
        "max": 100,
        "step": 2,
        "unit": "px",
        "label": "List Gap",
        "default": 30
      },
      {
        "type": "range",
        "id": "list_gap_mobile",
        "min": 0,
        "max": 100,
        "step": 2,
        "unit": "px",
        "label": "List Gap - Mobile",
        "default": 30
      },
      {
        "type": "checkbox",
        "id": "list_hide_mobile",
        "label": "List Hide on Mobile",
        "default": true
      },
      {
        "type": "header",
        "content": "Item Icon Settings"
      },
      {
        "type": "image_picker",
        "id": "icon",
        "label": "Custom List Item Icon"
      },
      {
        "type": "range",
        "id": "icon_size",
        "min": 20,
        "max": 100,
        "step": 2,
        "unit": "px",
        "label": "Icon Size - Desktop",
        "default": 28
      },
      {
        "type": "range",
        "id": "icon_size_mobile",
        "min": 20,
        "max": 100,
        "step": 2,
        "unit": "px",
        "label": "Icon Size - Mobile",
        "default": 28
      },
      {
        "type": "range",
        "id": "icon_margin_right",
        "min": 0,
        "max": 100,
        "step": 2,
        "unit": "px",
        "label": "Icon Margin Right",
        "default": 16
      },
      {
        "type": "header",
        "content": "List Item Text Settings"
      },
      {
        "type": "checkbox",
        "id": "list_text_custom",
        "label": "Use Custom Font",
        "default": false
      },
      {
        "type": "font_picker",
        "id": "list_text_font",
        "label": "Text Font Family",
        "default": "josefin_sans_n4"
      },
      {
        "type": "range",
        "id": "list_text_size",
        "min": 0,
        "max": 40,
        "step": 2,
        "unit": "px",
        "label": "Text Size",
        "default": 20
      },
      {
        "type": "range",
        "id": "list_text_size_mobile",
        "min": 0,
        "max": 40,
        "step": 2,
        "unit": "px",
        "label": "Text Size - Mobile",
        "default": 18
      },
      {
        "type": "range",
        "id": "list_text_height",
        "min": 50,
        "max": 200,
        "step": 10,
        "unit": "%",
        "label": "Text Line Height",
        "default": 150
      },
      {
        "type": "header",
        "content": "Button Settings"
      },
      {
        "type": "text",
        "id": "button",
        "label": "Button Text",
        "default": "TRY DEEP DREAMS"
      },
      {
        "type": "url",
        "id": "button_url",
        "label": "Button URL"
      },
      {
        "type": "checkbox",
        "id": "button_custom",
        "label": "Use Custom Font",
        "default": false
      },
      {
        "type": "font_picker",
        "id": "button_font",
        "label": "Button Font Family",
        "default": "josefin_sans_n4"
      },
      {
        "type": "range",
        "id": "button_size",
        "min": 0,
        "max": 30,
        "step": 1,
        "unit": "px",
        "label": "Button Size",
        "default": 18
      },
      {
        "type": "range",
        "id": "button_size_mobile",
        "min": 0,
        "max": 30,
        "step": 1,
        "unit": "px",
        "label": "Button Size - Mobile",
        "default": 18
      },
      {
        "type": "range",
        "id": "button_height",
        "min": 50,
        "max": 200,
        "step": 10,
        "unit": "%",
        "label": "Button Line Height",
        "default": 100
      },
      {
        "type": "range",
        "id": "button_width",
        "min": 100,
        "max": 600,
        "step": 5,
        "unit": "px",
        "label": "Button Max-Width - Desktop",
        "default": 305
      },
      {
        "type": "range",
        "id": "button_mt",
        "min": 0,
        "max": 100,
        "step": 2,
        "unit": "px",
        "label": "Button Margin Top",
        "default": 50
      },
      {
        "type": "range",
        "id": "button_mt_mobile",
        "min": 0,
        "max": 100,
        "step": 2,
        "unit": "px",
        "label": "Button Margin Top - Mobile",
        "default": 32
      },
      {
        "type": "range",
        "id": "button_padding_vertical",
        "min": 0,
        "max": 100,
        "step": 2,
        "unit": "px",
        "label": "Button Padding Vertical",
        "default": 16
      },
      {
        "type": "range",
        "id": "button_padding_vertical_mobile",
        "min": 0,
        "max": 100,
        "step": 2,
        "unit": "px",
        "label": "Button Padding Vertical - Mobile",
        "default": 16
      },
      {
         "type": "range",
         "id": "button_padding_horizontal",
         "min": 0,
         "max": 100,
         "step": 2,
         "unit": "px",
         "label": "Button Padding Horizontal",
         "default": 48
      },
      {
         "type": "range",
         "id": "button_padding_horizontal_mobile",
         "min": 0,
         "max": 100,
         "step": 2,
         "unit": "px",
         "label": "Button Padding Horizontal - Mobile",
         "default": 48
      },
      {
         "type": "range",
         "id": "button_radius",
         "min": 0,
         "max": 100,
         "step": 2,
         "unit": "px",
         "label": "Button Border Radius",
         "default": 100
      },
      {
         "type": "range",
         "id": "button_border_thickness",
         "min": 0,
         "max": 10,
         "step": 1,
         "unit": "px",
         "label": "Button Border Thickness",
         "default": 0
      },
      {
        "type": "select",
        "id": "button_style",
        "label": "Button Style",
        "default": "non_outline",
        "options": [
          {
            "label": "Link",
            "value": "link"
          },
          {
            "label": "Non-Outline",
            "value": "non_outline"
          },
          {
            "label": "Non-Outline & Arrow",
            "value": "non_outline_arrow"
          },
          {
            "label": "Outline",
            "value": "outline"
          },
          {
            "label": "Outline & Arrow",
            "value": "outline_arrow"
          }
        ]
      },
      {
        "type": "select",
        "id": "button_position_mobile",
        "label": "Button Position - Mobile",
        "default": "bottom",
        "options": [
          {
            "label": "Top",
            "value": "top"
          },
          {
            "label": "Bottom",
            "value": "bottom"
          }
        ]
      },
      {
        "type": "header",
        "content": "Table Settings"
      },
      {
        "type": "range",
        "id": "table_columns",
        "min": 1,
        "max": 4,
        "step": 1,
        "label": "Number of Columns",
        "default": 2
      },
      {
        "type": "range",
        "id": "table_mt_mobile",
        "min": 0,
        "max": 100,
        "step": 2,
        "unit": "px",
        "label": "Table Margin Top - Mobile",
        "default": 32
      },
      {
        "type": "select",
        "id": "table_column_active",
        "label": "Table Column Active",
        "default": "1",
        "options": [
         {
            "label": "1",
            "value": "1"
          },
          {
            "label": "2",
            "value": "2"
          },
          {
            "label": "3",
            "value": "3"
          },
          {
            "label": "4",
            "value": "4"
          }
        ]
      },
      {
        "type": "range",
        "id": "table_radius",
        "min": 0,
        "max": 50,
        "step": 2,
        "unit": "px",
        "label": "Table Border Radius",
        "default": 16
      },
      {
        "type": "range",
        "id": "table_border_thickness",
        "min": 0,
        "max": 10,
        "step": 1,
        "unit": "px",
        "label": "Table Border Thickness",
        "default": 1
      },
      {
        "type": "header",
        "content": "Table Header Image Settings"
      },
      {
        "type": "range",
        "id": "table_heading_image_size",
        "min": 30,
        "max": 200,
        "step": 2,
        "unit": "px",
        "label": "Table Header Image Size",
        "default": 100
      },
      {
        "type": "range",
        "id": "table_heading_image_size_mobile",
        "min": 20,
        "max": 200,
        "step": 2,
        "unit": "px",
        "label": "Table Header Image Size - Mobile",
        "default": 80
      },
      {
        "type": "header",
        "content": "Table Header Settings"
      },
      {
        "type": "text",
        "id": "first_heading",
        "label": "First Heading",
        "default": "SECTION STORE"
      },
      {
        "type": "image_picker",
        "id": "first_heading_image",
        "label": "First Heading Image",
        "info": "The image will replace the heading"
      },
      {
        "type": "text",
        "id": "second_heading",
        "label": "Second Heading",
        "default": "Other Brands"
      },
      {
        "type": "image_picker",
        "id": "second_heading_image",
        "label": "Second Heading Image",
        "info": "The image will replace the heading"
      },
      {
        "type": "text",
        "id": "third_heading",
        "label": "Third Heading",
        "default":"Third"
      },
      {
        "type": "image_picker",
        "id": "third_heading_image",
        "label": "Third Heading Image",
        "info": "The image will replace the heading"
      },
      {
        "type": "text",
        "id": "four_heading",
        "label": "Four Heading",
        "default":"Fourth"
      },
      {
        "type": "image_picker",
        "id": "four_heading_image",
        "label": "Four Heading Image",
        "info": "The image will replace the heading"
      },
      {
        "type": "checkbox",
        "id": "table_heading_custom",
        "label": "Use Custom Font",
        "default": false
      },
      {
        "type": "font_picker",
        "id": "table_heading_font",
        "label": "Table Heading Font Family",
        "default": "assistant_n4"
      },
      {
        "type": "range",
        "id": "table_heading_size",
        "min": 0,
        "max": 72,
        "step": 2,
        "unit": "px",
        "label": "Table Heading Size",
        "default": 20
      },
      {
        "type": "range",
        "id": "table_heading_size_mobile",
        "min": 0,
        "max": 40,
        "step": 1,
        "unit": "px",
        "label": "Table Heading Size - Mobile",
        "default": 16
      },
      {
        "type": "range",
        "id": "table_heading_height",
        "min": 50,
        "max": 200,
        "step": 10,
        "unit": "%",
        "label": "Table Heading Line Height",
        "default": 100
      },
      {
        "type": "select",
        "id": "table_heading_align",
        "label": "Table Heading Text Align",
        "default": "center",
        "options": [
          {
            "label": "Left",
            "value": "left"
          },
         {
            "label": "Center",
            "value": "center"
          },
          {
            "label": "Right",
            "value": "right"
          }
        ]
      },
      {
        "type": "range",
        "id": "table_heading_padding_horizontal",
        "min": 0,
        "max": 100,
        "step": 2,
        "unit": "px",
        "label": "Table Heading Padding sides",
        "default": 18
      },
      {
        "type": "range",
        "id": "table_heading_padding_horizontal_mobile",
        "min": 0,
        "max": 40,
        "step": 2,
        "unit": "px",
        "label": "Table Heading Padding sides - Mobile",
        "default": 18
      },
      {
        "type": "range",
        "id": "table_heading_padding_vertical",
        "min": 0,
        "max": 100,
        "step": 2,
        "unit": "px",
        "label": "Table Heading Padding Vertical",
        "default": 18
      },
      {
        "type": "range",
        "id": "table_heading_padding_vertical_mobile",
        "min": 0,
        "max": 100,
        "step": 2,
        "unit": "px",
        "label": "Table Heading Padding Vertical - Mobile",
        "default": 18
      },
      {
        "type": "header",
        "content": "Table Row Settings"
      },
      {
        "type": "range",
        "id": "table_row_padding_horizontal",
        "min": 0,
        "max": 100,
        "step": 2,
        "unit": "px",
        "label": "Table Row Padding sides",
        "default": 14
      },
      {
        "type": "range",
        "id": "table_row_padding_horizontal_mobile",
        "min": 0,
        "max": 40,
        "step": 2,
        "unit": "px",
        "label": "Table Row Padding sides - Mobile",
        "default": 14
      },
      {
        "type": "range",
        "id": "table_row_padding_vertical",
        "min": 0,
        "max": 100,
        "step": 2,
        "unit": "px",
        "label": "Table Row Padding Vertical",
        "default": 16
      },
      {
        "type": "range",
        "id": "table_row_padding_vertical_mobile",
        "min": 0,
        "max": 100,
        "step": 2,
        "unit": "px",
        "label": "Table Row Padding Vertical - Mobile",
        "default": 16
      },
      {
        "type": "header",
        "content": "Table Row Heading Settings"
      },
      {
        "type": "range",
        "id": "table_row_heading_width_mobile",
        "min": 20,
        "max": 70,
        "step": 2,
        "unit": "%",
        "label": "Table Row Heading Width - Mobile",
        "default": 66
      },
      {
        "type": "checkbox",
        "id": "table_row_heading_custom",
        "label": "Use Custom Font",
        "default": false
      },
      {
        "type": "font_picker",
        "id": "table_row_heading_font",
        "label": "Table Row Heading Font Family",
        "default": "assistant_n4"
      },
      {
        "type": "range",
        "id": "table_row_heading_size",
        "min": 0,
        "max": 72,
        "step": 2,
        "unit": "px",
        "label": "Table Row Heading Size",
        "default": 16
      },
      {
        "type": "range",
        "id": "table_row_heading_size_mobile",
        "min": 0,
        "max": 40,
        "step": 1,
        "unit": "px",
        "label": "Table Row Heading Size - Mobile",
        "default": 16
      },
      {
        "type": "range",
        "id": "table_row_heading_height",
        "min": 50,
        "max": 200,
        "step": 10,
        "unit": "%",
        "label": "Table Row Heading Line Height",
        "default": 130
      },
      {
        "type": "select",
        "id": "table_row_heading_align",
        "label": "Table Row Heading Text Align",
        "default": "left",
        "options": [
          {
            "label": "Left",
            "value": "left"
          },
          {
            "label": "Center",
            "value": "center"
          },
          {
            "label": "Right",
            "value": "right"
          }
        ]
      },
      {
        "type": "header",
        "content": "Table Row Text Settings"
      },
      {
        "type": "checkbox",
        "id": "table_row_text_custom",
        "label": "Use Custom Font",
        "default": false
      },
      {
        "type": "font_picker",
        "id": "table_row_text_font",
        "label": "Table Row Text Font Family",
        "default": "assistant_n4"
      },
      {
        "type": "range",
        "id": "table_row_text_size",
        "min": 0,
        "max": 72,
        "step": 2,
        "unit": "px",
        "label": "Table Row Text Size",
        "default": 20
      },
      {
        "type": "range",
        "id": "table_row_text_size_mobile",
        "min": 0,
        "max": 40,
        "step": 1,
        "unit": "px",
        "label": "Table Row Text Size - Mobile",
        "default": 16
      },
      {
        "type": "range",
        "id": "table_row_text_height",
        "min": 50,
        "max": 200,
        "step": 10,
        "unit": "%",
        "label": "Table Row Text Line Height",
        "default": 130
      },
      {
        "type": "select",
        "id": "table_row_text_align",
        "label": "Table Row Text Align",
        "default": "center",
        "options": [
          {
            "label": "Left",
            "value": "left"
          },
          {
            "label": "Center",
            "value": "center"
          },
          {
            "label": "Right",
            "value": "right"
          }
        ]
      },
      {
        "type": "header",
        "content": "Table Row Image Settings"
      },
      {
        "type": "range",
        "id": "table_row_image_size",
        "min": 20,
        "max": 100,
        "step": 2,
        "unit": "px",
        "label": "Table Row Image Size",
        "default": 24
      },
      {
        "type": "range",
        "id": "table_row_image_size_mobile",
        "min": 20,
        "max": 100,
        "step": 2,
        "unit": "px",
        "label": "Table Row Image Size - Mobile",
        "default": 24
      },
      {
        "type": "header",
        "content": "Content Colors"
      },
      {
        "type": "color",
        "label": "Heading Color",
        "id": "heading_color",
        "default": "#000000"
      },
      {
        "type": "color",
        "label": "Text Color",
        "id": "text_color",
        "default": "#000000"
      },
      {
        "type": "color",
        "label": "Item Icon Color",
        "id": "icon_color",
        "default": "#000000"
      },
      {
        "type": "color",
        "label": "List Item Text Color",
        "id": "list_text_color",
        "default": "#000000"
      },
      {
        "type": "header",
        "content": "Button Colors"
      },
      {
        "type": "color",
        "label": "Button Text Color",
        "id": "button_color",
        "default": "#FFFFFF"
      },
      {
        "type": "color",
        "label": "Button Hover Text Color",
        "id": "button_hover_color",
        "default": "#dad0ff"
      },
      {
        "type": "color",
        "label": "Button Background Color",
        "id": "button_bg_color",
        "default": "#16142B"
      },
      {
        "type": "color",
        "label": "Button Background Hover Color",
        "id": "button_bg_hover_color",
        "default": "#16142B"
      },
      {
        "type": "color",
        "label": "Button Border Color",
        "id": "button_border_color",
        "default": "#16142B"
      },
      {
        "type": "color",
        "label": "Button Border Hover Color",
        "id": "button_border_hover_color",
        "default": "#16142B"
      },
      {
        "type": "header",
        "content": "Table Colors"
      },
      {
        "type": "color",
        "label": "Table Border Color",
        "id": "table_border_color",
        "default": "#2D2B43"
      },
      {
        "type": "color",
        "label": "Table Column Actice Border Color",
        "id": "table_active_border_color",
        "default": "#DAD0FF"
      },
      {
        "type": "color",
        "label": "Table Heading",
        "id": "table_heading_color",
        "default": "#1A192E"
      },
      {
        "type": "color",
        "label": "Table Heading Active",
        "id": "table_heading_active_color",
        "default": "#FFFFFF"
      },
      {
        "type": "color",
        "label": "Table Heading Background Active",
        "id": "table_heading_bg_active_color",
        "default": "#2D2B43"
      },
      {
        "type": "color",
        "label": "Table Column Active Background",
        "id": "table_column_active_bg_color",
        "default": "#2D2B43"
      },
      {
        "type": "color",
        "label": "Table Row Heading",
        "id": "table_row_heading_color",
        "default": "#1A192E"
      },
      {
        "type": "color",
        "label": "Table Row Text",
        "id": "table_row_text_color",
        "default": "#1A192E"
      },
      {
        "type": "color",
        "label": "Table Row Active Text",
        "id": "table_row_active_text_color",
        "default": "#FFFFFF"
      },
      {
        "type": "header",
        "content": "Section Colors"
      },
      {
        "type": "color",
        "label": "Section background",
        "id": "background_color",
        "default": "#F6F3FF"
      },
      {
        "type": "color_background",
        "id": "background_gradient",
        "label": "Section background gradient"
      },
      {
        "type": "color",
        "label": "Border",
        "id": "border_color",
        "default": "#000000"
      },
      {
        "type": "header",
        "content": "Section margin (outside)"
      },
      {
        "type": "range",
        "id": "margin_top",
        "min": 0,
        "max": 100,
        "step": 4,
        "unit": "px",
        "label": "Margin top",
        "default": 0
      },
      {
        "type": "range",
        "id": "margin_bottom",
        "min": 0,
        "max": 100,
        "step": 4,
        "unit": "px",
        "label": "Margin bottom",
        "default": 0
      },
      {
        "type": "header",
        "content": "Section padding (inside)"
      },
      {
        "type": "range",
        "id": "padding_top",
        "min": 0,
        "max": 100,
        "step": 4,
        "unit": "px",
        "label": "Padding top",
        "default": 72
      },
      {
         "type": "range",
         "id": "padding_bottom",
         "min": 0,
         "max": 100,
         "step": 4,
         "unit": "px",
         "label": "Padding bottom",
         "default": 72
      },
      {
        "type": "range",
        "id": "padding_horizontal",
        "min": 0,
        "max": 30,
        "step": 1,
        "unit": "rem",
        "label": "Padding sides",
        "default": 5
      },
      {
        "type": "range",
        "id": "padding_horizontal_mobile",
        "min": 0,
        "max": 15,
        "step": 0.5,
        "unit": "rem",
        "label": "Padding sides mobile",
        "default": 1.5
      },
      {
        "type": "header",
        "content": "Section Settings"
      },
      {
        "type": "checkbox",
        "id": "full_width",
        "label": "Full Width",
        "default": false
      },
      {
        "type": "range",
        "id": "content_width",
        "min": 0,
        "max": 400,
        "step": 10,
        "unit": "rem",
        "label": "Section content width",
        "default": 130
      },
      {
        "type": "range",
        "id": "border_thickness",
        "min": 0,
        "max": 50,
        "step": 1,
        "unit": "px",
        "label": "Border thickness",
        "default": 0
      },
      {
        "type": "checkbox",
        "id": "lazy",
        "label": "Lazy load",
        "info": "Lazy load images for speed optimisation",
        "default": true
      }
    ],
    "blocks": [
      {
        "type": "list_item",
        "name": "List Item",
        "settings": [
          {
            "type": "text",
            "id": "text",
            "label": "Text",
            "default": "Text"
          }
        ]
      },
      {
        "type": "table_row",
        "name": "Table Row",
        "settings": [
          {
            "type": "text",
            "id": "row_heading",
            "label": "Row Heading",
            "default": "Row Heading"
          },
          {
            "type": "text",
            "id": "first_column",
            "label": "First Column Value"
          },
          {
            "type": "image_picker",
            "id": "first_column_image",
            "label": "First Column Image",
            "info":"Custom images replaces icons"
          },
          {
            "type": "select",
            "id": "first_column_icon",
            "label": "First Column Icon",
            "default": "check",
            "options": [
              {
                "label": "Check",
                "value": "check"
              },
              {
                "label": "Cross",
                "value": "cross"
              },
              {
                "label": "None",
                "value": "none"
              }
            ]
          },
          {
            "type": "text",
            "id": "second_column",
            "label": "Second Column Value"
          },
          {
            "type": "image_picker",
            "id": "second_column_image",
            "label": "Second Column Image"
          },
          {
            "type": "select",
            "id": "second_column_icon",
            "label": "Second Column Icon",
            "default": "cross",
            "options": [
              {
                "label": "Check",
                "value": "check"
              },
              {
                "label": "Cross",
                "value": "cross"
              },
              {
                "label": "None",
                "value": "none"
              }
            ]
          },
          {
            "type": "text",
            "id": "third_column",
            "label": "Third Column Value"
          },
          {
            "type": "image_picker",
            "id": "third_column_image",
            "label": "Third Column Image"
          },
          {
            "type": "select",
            "id": "third_column_icon",
            "label": "Third Column Icon",
            "default": "none",
            "options": [
              {
                "label": "Check",
                "value": "check"
              },
              {
                "label": "Cross",
                "value": "cross"
              },
              {
                "label": "None",
                "value": "none"
              }
            ]
          },
          {
            "type": "text",
            "id": "four_column",
            "label": "Four Column Value"
          },
          {
            "type": "image_picker",
            "id": "four_column_image",
            "label": "Four Column Image"
          },
          {
            "type": "select",
            "id": "four_column_icon",
            "label": "Four Column Icon",
            "default": "none",
            "options": [
              {
                "label": "Check",
                "value": "check"
              },
              {
                "label": "Cross",
                "value": "cross"
              },
              {
                "label": "None",
                "value": "none"
              }
            ]
          }
        ]
      }
    ],
    "presets": [
      {
        "name": "SS - Comparison Table #6",
        "blocks": [
          {
            "type": "list_item",
            "settings": {
              "text": "Fall asleep quicker"
            }
          },
          {
            "type": "list_item",
            "settings": {
              "text": "Relax & Restorative sleep"
            }
          },
          {
            "type": "list_item",
            "settings": {
              "text": "Wake up refreshed"
            }
          },
          {
            "type": "list_item",
            "settings": {
              "text": "Plant based"
            }
          },
          {
            "type": "table_row",
            "settings": {
              "row_heading": "Optimized Dosing"
            }
          },
          {
            "type": "table_row",
            "settings": {
              "row_heading": "Plant Based"
            }
          },
          {
            "type": "table_row",
            "settings": {
              "row_heading": "Low Sugar"
            }
          },
          {
            "type": "table_row",
            "settings": {
              "row_heading": "Simple Ingredients"
            }
          },
          {
            "type": "table_row",
            "settings": {
              "row_heading": "3rd Party Tested"
            }
          },
          {
            "type": "table_row",
            "settings": {
              "row_heading": "Delicious Taste"
            }
          }
        ]
      }
    ]
  }
{% endschema %}