{% render 'quiz-js-variables' %}
{% render 'raven-js-variables' %}

<section class="section">

  <div class="container">

    {% if customer %}
      <div class="banner banner--success">
        Customer is Logged in with email: {{ customer.email }}
      </div>
    {% else %}
      <div class="banner banner--error">
        Customer is not logged in.
      </div>
    {% endif %}

    <h3>Test Single Field</h3>
  
    <form action="#" id="raven-test-single-form" class="form">

      <div class="input">
        <input type="email" required class="input__field" id="single-email" value="{% if customer %}{{ customer.email }}{% endif %}">
        <label for="single-email" class="input__label">{{ 'customer.login.email' | t }}</label>
      </div>

      <div class="input">
        <input type="text" required class="input__field" id="single-dog-1-name">
        <label for="single-dog-1-name" class="input__label">Dog Name</label>
      </div>
  
      <div class="input">
        <button class="button button--primary" type="submit">
          Test Single Field
        </button>
      </div>
  
    </form>


    <h3>Test Multiple Fields</h3>

    <form action="#" id="raven-test-multi-form" class="form">

      <div class="input">
        <input type="email" required class="input__field" id="multi-email" value="{% if customer %}{{ customer.email }}{% endif %}">
        <label for="multi-email" class="input__label">{{ 'customer.login.email' | t }}</label>
      </div>

      <div class="input">
        <button class="button button--primary" type="submit">
          Test Multiple Fields
        </button>
      </div>

    </form>

  </div>

  <hr>

  {% comment %} {% render 'raven_example' %} {% endcomment %}

</section>


<script>

  const ravenTestSingleForm = document.querySelector("#raven-test-single-form");
  const ravenTestMultiForm = document.querySelector("#raven-test-multi-form");

  const ravenAPIKey = '{{ shop.metafields.fields_raven.api_secret }}';
  {% comment %} const ravenAPIKey = RavenAPI.storage.raven_api_secret; {% endcomment %}

  document.addEventListener("DOMContentLoaded", ravenPageLoaded);
  ravenTestSingleForm.addEventListener("submit", ravenTestSingleButtonClicked);
  ravenTestMultiForm.addEventListener("submit", ravenTestMultiButtonClicked);

  function ravenPageLoaded(event) {
    
  }

  function ravenTestSingleButtonClicked(event) {

    console.log('ravenTestSingleButtonClicked');

    event.preventDefault();

    const form = event.target;
    const emailInput = form.querySelector("#single-email");
    const email = emailInput.value;

    ravenTestSingle();

  }

  function ravenTestMultiButtonClicked(event) {
    
    console.log('ravenTestMultiButtonClicked');

    event.preventDefault();

    const form = event.target;
    const emailInput = form.querySelector("#multi-email");
    const email = emailInput.value;

    console.log(email);

    ravenTestMultiple();

  }

  function ravenTestSingle() {

    const customer_email = document.querySelector("#single-email").value;
    const dog_1_name = document.querySelector("#single-dog-1-name").value;
    const raven_field = FieldsRaven.definitions.dog_1_name;

    const requestParams = { 
      raven: Object.assign({}, raven_field, {value: dog_1_name}, {customer_email: customer_email}) 
    };
    
    const response = fetch(FieldsRaven.endpoints.create_single, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(requestParams)
    })

    response
      .then(res => res.json())
      .then(resJson => {
        console.log('resJson');
        console.log(resJson);
      })

  }

  function ravenTestMultiple() {

    console.log("ravenTestMultiple");

    const customer_email = document.querySelector("#multi-email").value;

    const dog_1_name = "Raven";
    const dog_1_sex = "Male";
    const dog_1_age_in_months = 24;
    const dog_1_breed = "West Highland White Terrier";
    const dog_1_neutered = "true";
    const dog_1_weight_profile = "Ideal";
    const dog_1_weight = 10;
    const dog_1_ideal_weight = 15;
    const dog_1_activity_level = "Normal";
    const dog_1_has_health_issue = "false";
    const dog_1_prescription_diet = "None";

    const flock = [
      
      Object.assign({}, FieldsRaven.definitions.dog_1_name, { value: dog_1_name }),
      Object.assign({}, FieldsRaven.definitions.dog_1_sex, { value: dog_1_sex }),
      Object.assign({}, FieldsRaven.definitions.dog_1_age_in_months, { value: dog_1_age_in_months }),
      Object.assign({}, FieldsRaven.definitions.dog_1_breed, { value: dog_1_breed }),
      Object.assign({}, FieldsRaven.definitions.dog_1_neutered, { value: dog_1_neutered }),
      Object.assign({}, FieldsRaven.definitions.dog_1_weight_profile, { value: dog_1_weight_profile }),
      Object.assign({}, FieldsRaven.definitions.dog_1_weight, { value: dog_1_weight }),
      Object.assign({}, FieldsRaven.definitions.dog_1_ideal_weight, { value: dog_1_ideal_weight }),
      Object.assign({}, FieldsRaven.definitions.dog_1_activity_level, { value: dog_1_activity_level }),
      Object.assign({}, FieldsRaven.definitions.dog_1_has_health_issue, { value: dog_1_has_health_issue }),
      Object.assign({}, FieldsRaven.definitions.dog_1_prescription_diet, { value: dog_1_prescription_diet }),
      
      Object.assign({}, FieldsRaven.definitions.dog_2_name, { value: dog_1_name }),
      Object.assign({}, FieldsRaven.definitions.dog_2_sex, { value: dog_1_sex }),
      Object.assign({}, FieldsRaven.definitions.dog_2_age_in_months, { value: dog_1_age_in_months }),
      Object.assign({}, FieldsRaven.definitions.dog_2_breed, { value: dog_1_breed }),
      Object.assign({}, FieldsRaven.definitions.dog_2_neutered, { value: dog_1_neutered }),
      Object.assign({}, FieldsRaven.definitions.dog_2_weight_profile, { value: dog_1_weight_profile }),
      Object.assign({}, FieldsRaven.definitions.dog_2_weight, { value: dog_1_weight }),
      Object.assign({}, FieldsRaven.definitions.dog_2_ideal_weight, { value: dog_1_ideal_weight }),
      Object.assign({}, FieldsRaven.definitions.dog_2_activity_level, { value: dog_1_activity_level }),
      Object.assign({}, FieldsRaven.definitions.dog_2_has_health_issue, { value: dog_1_has_health_issue }),
      Object.assign({}, FieldsRaven.definitions.dog_2_prescription_diet, { value: dog_1_prescription_diet }),
      
      Object.assign({}, FieldsRaven.definitions.dog_3_name, { value: dog_1_name }),
      Object.assign({}, FieldsRaven.definitions.dog_3_sex, { value: dog_1_sex }),
      Object.assign({}, FieldsRaven.definitions.dog_3_age_in_months, { value: dog_1_age_in_months }),
      Object.assign({}, FieldsRaven.definitions.dog_3_breed, { value: dog_1_breed }),
      Object.assign({}, FieldsRaven.definitions.dog_3_neutered, { value: dog_1_neutered }),
      Object.assign({}, FieldsRaven.definitions.dog_3_weight_profile, { value: dog_1_weight_profile }),
      Object.assign({}, FieldsRaven.definitions.dog_3_weight, { value: dog_1_weight }),
      Object.assign({}, FieldsRaven.definitions.dog_3_ideal_weight, { value: dog_1_ideal_weight }),
      Object.assign({}, FieldsRaven.definitions.dog_3_activity_level, { value: dog_1_activity_level }),
      Object.assign({}, FieldsRaven.definitions.dog_3_has_health_issue, { value: dog_1_has_health_issue }),
      Object.assign({}, FieldsRaven.definitions.dog_3_prescription_diet, { value: dog_1_prescription_diet }),
      
      Object.assign({}, FieldsRaven.definitions.dog_4_name, { value: dog_1_name }),
      Object.assign({}, FieldsRaven.definitions.dog_4_sex, { value: dog_1_sex }),
      Object.assign({}, FieldsRaven.definitions.dog_4_age_in_months, { value: dog_1_age_in_months }),
      Object.assign({}, FieldsRaven.definitions.dog_4_breed, { value: dog_1_breed }),
      Object.assign({}, FieldsRaven.definitions.dog_4_neutered, { value: dog_1_neutered }),
      Object.assign({}, FieldsRaven.definitions.dog_4_weight_profile, { value: dog_1_weight_profile }),
      Object.assign({}, FieldsRaven.definitions.dog_4_weight, { value: dog_1_weight }),
      Object.assign({}, FieldsRaven.definitions.dog_4_ideal_weight, { value: dog_1_ideal_weight }),
      Object.assign({}, FieldsRaven.definitions.dog_4_activity_level, { value: dog_1_activity_level }),
      Object.assign({}, FieldsRaven.definitions.dog_4_has_health_issue, { value: dog_1_has_health_issue }),
      Object.assign({}, FieldsRaven.definitions.dog_4_prescription_diet, { value: dog_1_prescription_diet }),

      Object.assign({}, FieldsRaven.definitions.dog_5_name, { value: dog_1_name }),
      Object.assign({}, FieldsRaven.definitions.dog_5_sex, { value: dog_1_sex }),
      Object.assign({}, FieldsRaven.definitions.dog_5_age_in_months, { value: dog_1_age_in_months }),
      Object.assign({}, FieldsRaven.definitions.dog_5_breed, { value: dog_1_breed }),
      Object.assign({}, FieldsRaven.definitions.dog_5_neutered, { value: dog_1_neutered }),
      Object.assign({}, FieldsRaven.definitions.dog_5_weight_profile, { value: dog_1_weight_profile }),
      Object.assign({}, FieldsRaven.definitions.dog_5_weight, { value: dog_1_weight }),
      Object.assign({}, FieldsRaven.definitions.dog_5_ideal_weight, { value: dog_1_ideal_weight }),
      Object.assign({}, FieldsRaven.definitions.dog_5_activity_level, { value: dog_1_activity_level }),
      Object.assign({}, FieldsRaven.definitions.dog_5_has_health_issue, { value: dog_1_has_health_issue }),
      Object.assign({}, FieldsRaven.definitions.dog_5_prescription_diet, { value: dog_1_prescription_diet }),

      Object.assign({}, FieldsRaven.definitions.dog_6_name, { value: dog_1_name }),
      Object.assign({}, FieldsRaven.definitions.dog_6_sex, { value: dog_1_sex }),
      Object.assign({}, FieldsRaven.definitions.dog_6_age_in_months, { value: dog_1_age_in_months }),
      Object.assign({}, FieldsRaven.definitions.dog_6_breed, { value: dog_1_breed }),
      Object.assign({}, FieldsRaven.definitions.dog_6_neutered, { value: dog_1_neutered }),
      Object.assign({}, FieldsRaven.definitions.dog_6_weight_profile, { value: dog_1_weight_profile }),
      Object.assign({}, FieldsRaven.definitions.dog_6_weight, { value: dog_1_weight }),
      Object.assign({}, FieldsRaven.definitions.dog_6_ideal_weight, { value: dog_1_ideal_weight }),
      Object.assign({}, FieldsRaven.definitions.dog_6_activity_level, { value: dog_1_activity_level }),
      Object.assign({}, FieldsRaven.definitions.dog_6_has_health_issue, { value: dog_1_has_health_issue }),
      Object.assign({}, FieldsRaven.definitions.dog_6_prescription_diet, { value: dog_1_prescription_diet }),

      Object.assign({}, FieldsRaven.definitions.dog_7_name, { value: dog_1_name }),
      Object.assign({}, FieldsRaven.definitions.dog_7_sex, { value: dog_1_sex }),
      Object.assign({}, FieldsRaven.definitions.dog_7_age_in_months, { value: dog_1_age_in_months }),
      Object.assign({}, FieldsRaven.definitions.dog_7_breed, { value: dog_1_breed }),
      Object.assign({}, FieldsRaven.definitions.dog_7_neutered, { value: dog_1_neutered }),
      Object.assign({}, FieldsRaven.definitions.dog_7_weight_profile, { value: dog_1_weight_profile }),
      Object.assign({}, FieldsRaven.definitions.dog_7_weight, { value: dog_1_weight }),
      Object.assign({}, FieldsRaven.definitions.dog_7_ideal_weight, { value: dog_1_ideal_weight }),
      Object.assign({}, FieldsRaven.definitions.dog_7_activity_level, { value: dog_1_activity_level }),
      Object.assign({}, FieldsRaven.definitions.dog_7_has_health_issue, { value: dog_1_has_health_issue }),
      Object.assign({}, FieldsRaven.definitions.dog_7_prescription_diet, { value: dog_1_prescription_diet }),

      Object.assign({}, FieldsRaven.definitions.dog_8_name, { value: dog_1_name }),
      Object.assign({}, FieldsRaven.definitions.dog_8_sex, { value: dog_1_sex }),
      Object.assign({}, FieldsRaven.definitions.dog_8_age_in_months, { value: dog_1_age_in_months }),
      Object.assign({}, FieldsRaven.definitions.dog_8_breed, { value: dog_1_breed }),
      Object.assign({}, FieldsRaven.definitions.dog_8_neutered, { value: dog_1_neutered }),
      Object.assign({}, FieldsRaven.definitions.dog_8_weight_profile, { value: dog_1_weight_profile }),
      Object.assign({}, FieldsRaven.definitions.dog_8_weight, { value: dog_1_weight }),
      Object.assign({}, FieldsRaven.definitions.dog_8_ideal_weight, { value: dog_1_ideal_weight }),
      Object.assign({}, FieldsRaven.definitions.dog_8_activity_level, { value: dog_1_activity_level }),
      Object.assign({}, FieldsRaven.definitions.dog_8_has_health_issue, { value: dog_1_has_health_issue }),
      Object.assign({}, FieldsRaven.definitions.dog_8_prescription_diet, { value: dog_1_prescription_diet }),
      
      Object.assign({}, FieldsRaven.definitions.dog_9_name, { value: dog_1_name }),
      Object.assign({}, FieldsRaven.definitions.dog_9_sex, { value: dog_1_sex }),
      Object.assign({}, FieldsRaven.definitions.dog_9_age_in_months, { value: dog_1_age_in_months }),
      Object.assign({}, FieldsRaven.definitions.dog_9_breed, { value: dog_1_breed }),
      Object.assign({}, FieldsRaven.definitions.dog_9_neutered, { value: dog_1_neutered }),
      Object.assign({}, FieldsRaven.definitions.dog_9_weight_profile, { value: dog_1_weight_profile }),
      Object.assign({}, FieldsRaven.definitions.dog_9_weight, { value: dog_1_weight }),
      Object.assign({}, FieldsRaven.definitions.dog_9_ideal_weight, { value: dog_1_ideal_weight }),
      Object.assign({}, FieldsRaven.definitions.dog_9_activity_level, { value: dog_1_activity_level }),
      Object.assign({}, FieldsRaven.definitions.dog_9_has_health_issue, { value: dog_1_has_health_issue }),
      Object.assign({}, FieldsRaven.definitions.dog_9_prescription_diet, { value: dog_1_prescription_diet }),

      Object.assign({}, FieldsRaven.definitions.dog_10_name, { value: dog_1_name }),
      Object.assign({}, FieldsRaven.definitions.dog_10_sex, { value: dog_1_sex }),
      Object.assign({}, FieldsRaven.definitions.dog_10_age_in_months, { value: dog_1_age_in_months }),
      Object.assign({}, FieldsRaven.definitions.dog_10_breed, { value: dog_1_breed }),
      Object.assign({}, FieldsRaven.definitions.dog_10_neutered, { value: dog_1_neutered }),
      Object.assign({}, FieldsRaven.definitions.dog_10_weight_profile, { value: dog_1_weight_profile }),
      Object.assign({}, FieldsRaven.definitions.dog_10_weight, { value: dog_1_weight }),
      Object.assign({}, FieldsRaven.definitions.dog_10_ideal_weight, { value: dog_1_ideal_weight }),
      Object.assign({}, FieldsRaven.definitions.dog_10_activity_level, { value: dog_1_activity_level }),
      Object.assign({}, FieldsRaven.definitions.dog_10_has_health_issue, { value: dog_1_has_health_issue }),
      Object.assign({}, FieldsRaven.definitions.dog_10_prescription_diet, { value: dog_1_prescription_diet })

    ];

    const requestParams = { 
      flock: flock,
      customer_email: customer_email
    };
    
    const response = fetch(FieldsRaven.endpoints.create_multiple, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(requestParams)
    });

    response
      .then(res => res.json())
      .then(resJson => {
        console.log('resJson');
        console.log(resJson);
      });

  }

</script>

{% schema %}
{
  "name": "Raven Test",
  "class": "shopify-section--timeline",
  "settings": []
}
{% endschema %}