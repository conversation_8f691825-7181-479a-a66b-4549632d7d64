<style>
  #shopify-section-{{ section.id }} .image-overlay {
    --heading-color: {{ section.settings.image_text_color.red }}, {{ section.settings.image_text_color.green }}, {{ section.settings.image_text_color.blue }};
    --text-color: {{ section.settings.image_text_color.red }}, {{ section.settings.image_text_color.green }}, {{ section.settings.image_text_color.blue }};
    --section-items-alignment: center;
    --section-overlay-color: {{ section.settings.image_overlay_color.red }}, {{ section.settings.image_overlay_color.green }}, {{ section.settings.image_overlay_color.blue }};
    --section-overlay-opacity: {{ section.settings.image_overlay_opacity | divided_by: 100.0 }};
  }
</style>

<section>
  {%- comment -%}
  ------------------------------------------------------------------------------------------
  TOP PART (IMAGE AND BREADCRUMB)
  ------------------------------------------------------------------------------------------
  {%- endcomment -%}

  {%- capture breadcrumb -%}
    <nav aria-label="{{ 'general.breadcrumb.title' | t }}" class="breadcrumb breadcrumb--floating text--xsmall hidden-phone">
      <ol class="breadcrumb__list" role="list">
        <li class="breadcrumb__item">
          <a class="breadcrumb__link" href="{{ routes.root_url }}">{{ 'general.breadcrumb.home' | t }}</a>
        </li>

        <li class="breadcrumb__item">
          <span class="breadcrumb__link" aria-current="page">
            {%- if collection.handle == 'all' -%}
              {{- 'collection.general.all_products' | t -}}
            {%- else -%}
              {{- collection.title -}}
            {%- endif -%}
          </span>
        </li>
      </ol>
    </nav>
  {%- endcapture -%}

  {%- if collection.image and section.settings.show_collection_image -%}
    <image-with-text-overlay reveal-on-scroll parallax class="image-overlay image-overlay--{{ section.settings.section_height }}" {% if section.settings.section_height == 'auto' %}style="--image-aspect-ratio: {{ collection.image.aspect_ratio }}"{% endif %}>
      <div class="image-overlay__image-wrapper" {% if section.settings.section_height == 'auto' %}style="padding-bottom: {{ 100.0 | divided_by: collection.image.aspect_ratio }}%"{% endif %}>
        {%- comment -%}Performance note: this image must not be lazyloaded as it contributes to the LCP{%- endcomment -%}
        <picture>
          {%- if section.settings.section_height != 'auto' -%}
            {{- collection.image | image_url: width: collection.image.width, height: 600, crop: 'center' | image_tag: widths: '400,500,600,700,800,900,1000', media: '(max-width: 740px)' | replace: '<img', '<source' -}}
          {%- else -%}
            {{- collection.image | image_url: width: collection.image.width | image_tag: widths: '400,500,600,700,800,900,1000', media: '(max-width: 740px)' | replace: '<img', '<source' -}}
          {%- endif -%}

          {{- collection.image | image_url: width: collection.image.width | image_tag: widths: '600,700,800,1000,1200,1400,1600,1800,2000,2200,2400,2600', class: 'image-overlay__image', reveal: true -}}
        </picture>
      </div>

      <div class="container">
        <div class="image-overlay__content-wrapper">
          {{- breadcrumb -}}

          <div class="image-overlay__content content-box content-box--{{ section.settings.text_width }} content-box--text-center content-box--center text-container">
            {%- if section.settings.show_collection_title -%}
              <h1 class="heading h1">
                <split-lines reveal>{{ collection.title }}</split-lines>
              </h1>
            {%- endif -%}

            {%- if section.settings.show_collection_description and collection.description != blank -%}
              <div class="image-overlay__text-container" reveal>
                {{- collection.description -}}
              </div>
            {%- endif -%}
          </div>
        </div>
      </div>
    </image-with-text-overlay>
  {%- else -%}
    <div class="container">
      <div class="page-header">
        {{- breadcrumb -}}

        <div class="page-header__text-wrapper text-container">
          {%- if section.settings.show_collection_title -%}
            <h1 class="heading h1">{{ collection.title }}</h1>
          {%- endif -%}

          {%- if section.settings.show_collection_description and collection.description != blank -%}
            <div>
              {{- collection.description -}}
            </div>
          {%- endif -%}
        </div>
      </div>
    </div>
  {%- endif -%}

  {%- comment -%}
  ------------------------------------------------------------------------------------------
  QUICK LINKS PART
  ------------------------------------------------------------------------------------------
  {%- endcomment -%}

  {%- assign quick_links_menu = section.settings.quick_links -%}

  {%- if quick_links_menu.links.size > 0 -%}
    <link-bar class="link-bar">
      <div class="container">
        <div class="link-bar__wrapper">
          <span class="link-bar__title heading heading--small text--subdued">{{ quick_links_menu.title }}</span>

          <div class="link-bar__scroller hide-scrollbar">
            <ul class="link-bar__linklist list--unstyled" role="list">
              {%- for link in quick_links_menu.links -%}
                {%- comment %}<locksmith:5887>{% endcomment -%}
                  {%- capture var %}{% render 'locksmith-variables', scope: 'subject', subject: link, subject_parent: quick_links_menu, variable: 'transparent' %}{% endcapture %}{% if var == "true" %}{% else %}{% continue %}{% endif -%}
                {%- comment %}</locksmith:5887>{% endcomment -%}
                <li class="link-bar__link-item {% if link.active %}link-bar__link-item--selected{% endif %}">
                  <a href="{{ link.url }}" class="link-bar__link link--animated {% if link.active %}text--underlined{% endif %}">{{ link.title }}</a>
                </li>
              {%- endfor -%}
            </ul>
          </div>
        </div>
      </div>
    </link-bar>
  {%- endif -%}
</section>

{% schema %}
{
  "name": "Collection banner",
  "class": "shopify-section--collection-banner",
  "settings": [
    {
      "type": "paragraph",
      "content": "To change collection descriptions or collection images, [edit your collections](/admin/collections)."
    },
    {
      "type": "checkbox",
      "id": "show_collection_image",
      "label": "Show collection image",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "show_collection_title",
      "label": "Show collection title",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "show_collection_description",
      "label": "Show collection description",
      "default": true
    },
    {
      "type": "select",
      "id": "section_height",
      "label": "Image height",
      "options": [
        {
          "value": "auto",
          "label": "Original image ratio"
        },
        {
          "value": "small",
          "label": "Small"
        },
        {
          "value": "medium",
          "label": "Medium"
        },
        {
          "value": "large",
          "label": "Large"
        }
      ],
      "info": "Choose \"Original image ratio\" to not cut images. [Learn more](https://help.shopify.com/en/manual/online-store/images/theme-images#best-practices-for-slideshows-and-full-width-images)",
      "default": "small"
    },
    {
      "type": "select",
      "id": "text_width",
      "label": "Text width",
      "options": [
        {
          "value": "small",
          "label": "Small"
        },
        {
          "value": "medium",
          "label": "Medium"
        },
        {
          "value": "large",
          "label": "Large"
        },
        {
          "value": "fill",
          "label": "Fill screen"
        }
      ],
      "default": "medium"
    },
    {
      "type": "header",
      "content": "Color",
      "info": "Only applicable when collection image is used."
    },
    {
      "type": "color",
      "id": "image_text_color",
      "label": "Text",
      "default": "#ffffff"
    },
    {
      "type": "color",
      "id": "image_overlay_color",
      "label": "Overlay",
      "default": "#000000"
    },
    {
      "type": "range",
      "id": "image_overlay_opacity",
      "label": "Overlay opacity",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "%",
      "default": 30
    },
    {
      "type": "header",
      "content": "Navigation"
    },
    {
      "type": "link_list",
      "id": "quick_links",
      "label": "Quick links",
      "info": "This menu won't show dropdown items."
    }
  ]
}
{% endschema %}